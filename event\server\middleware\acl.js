const Utils = require('../util/utilFunctions');
const HTTPStatus = require('../util/http-status');

module.exports = function (req, res, next) {
    const accessList = {
        'app': [{ method: 'POST', path: '/event/register' }],
        'org_app': [
            { method: 'POST', path: '/event' },
            { method: 'POST', path: '/event/multiple' },
            { method: 'GET', path: '/event' },
            { method: 'PUT', path: '/event' },
            { method: 'DELETE', path: '/event' },
            { method: 'GET', path: '/event/list' },
            { method: 'GET', path: '/event/participant' },
            { method: 'PUT', path: '/event/signup-status' },
            { method: 'PATCH', path: '/event/publish' },
            { method: 'POST', path: '/event/start-chunk-upload' },
            { method: 'POST', path: '/event/upload-chunk' },
            { method: 'POST', path: '/event/complete-chunk-upload' },
            { method: 'POST', path: '/event/abort-chunk-upload' },
            { method: 'GET', path: '/event/generate-presigned-url' }
        ],
        'root': [
            { method: 'POST', path: '/event' },
            { method: 'POST', path: '/event/multiple' },
            { method: 'GET', path: '/event' },
            { method: 'PUT', path: '/event' },
            { method: 'DELETE', path: '/event' },
            { method: 'GET', path: '/event/list' },
            { method: 'GET', path: '/event/participant' },
            { method: 'PUT', path: '/event/signup-status' },
            { method: 'PATCH', path: '/event/publish' },
            { method: 'POST', path: '/event/start-chunk-upload' },
            { method: 'POST', path: '/event/upload-chunk' },
            { method: 'POST', path: '/event/complete-chunk-upload' },
            { method: 'POST', path: '/event/abort-chunk-upload' },
            { method: 'GET', path: '/event/generate-presigned-url' }
        ]
    };
    const accessLevel = res.locals.user.accessLevel;
    const isAllowed = _.find(accessList[accessLevel], { method: req.method, path: req.originalUrl.split('?')[0] });
    if (isAllowed) {
        next();
    } else {
        const responseObject = Utils.errorResponse();
        responseObject.message = res.__('ACCESS_DENIED');
        res.status(HTTPStatus.NOT_ACCEPTABLE).send(responseObject);
        return;
    }
};
