const sinon = require('sinon');
const AWS = require('aws-sdk');
const AWSMock = require('jest-aws-sdk-mock');
const Redis = require('ioredis');
const CONSOLE_LOGGER = require('../server/logger');
const handler = require('../index');
const RedisUtil = require('../server/redisUtil');
const Event = require('../server/models/event.model');
const User = require('../server/models/user.model');
const Post = require('../server/models/post.model');
const Fundraiser = require('../server/models/fundraiser.model');
const ConstantModel = require('../server/models/constant.model');
const GroupMembers = require('../server/models/groupMembers.model');
const Groups = require('../server/models/groups.model');
const { beforeEach, afterEach } = require('mocha');
const ConversationService = require('../server/ConversationService');
const MessageModel = require('../server/models/message.model');
const CONSTANTS = require('../server/constants');
const EventSignups = require('../server/models/eventSignup.model');
const { SQSClient } = require('@aws-sdk/client-sqs');

describe('Child helper service', () => {
    let sandbox;
    let stubRedisConnect;
    let pipelineExecStub;
    let pipelineStub;
    let constantModelGetStub;

    before(() => {
        AWSMock.setSDKInstance(AWS);
        sandbox = sinon.createSandbox();
    });

    after(() => {
        AWSMock.restore();
        sinon.restore();
        sandbox.restore();
        pipelineStub.restore();
    });

    beforeEach(() => {
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
        pipelineExecStub = sinon.stub();
        pipelineStub = sinon.stub(Redis.prototype, 'pipeline').returns({ exec: pipelineExecStub });
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        sinon.stub(ConversationService, 'getReactionsForMessage').resolves({});
        sinon.stub(ConversationService, 'getActiveSocketConnections').resolves([]);
        sinon.stub(ConversationService, 'sendMessagesToConnections').resolves();
        constantModelGetStub = sinon.stub(ConstantModel, 'get');
        constantModelGetStub.withArgs(CONSTANTS.FEED_VERSION_PREFIX).resolves({ value: 'v1' });
    });

    afterEach(() => {
        sinon.restore();
    });

    try {
        it('should insert events for child when child is INSERTED', async () => {
            const addReferenceToSortedSetStub = sinon.stub(RedisUtil, 'addEventReferenceToSortedSet').resolves();
            const childRegisteredEventsStub = sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([
                JSON.stringify({
                    eventId: 'fundraiser3',
                    fundraiserId: 'fundraiser3',
                    score: 1713033600000
                })
            ]);

            const queryStub = sinon.stub(Event, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const whereStub = sinon.stub();
            const whereEqStub = sinon.stub();
            const whereStub1 = sinon.stub();
            const whereEqStub1 = sinon.stub();
            const attributesStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({
                eq: eqStub.returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            exec: sinon.stub().resolves([
                                { id: 'event1', details: { endDateTime: '2037-01-01T00:00:00.000Z' }, eventType: 'calendar' }])
                        })
                    }),
                    using: usingStub.returns({
                        where: whereStub.returns({
                            eq: whereEqStub.returns({
                                where: whereStub1.returns({
                                    eq: whereEqStub1.returns({
                                        attributes: attributesStub.returns({
                                            exec: execStub.resolves(
                                                [
                                                    {
                                                        id: 'event1',
                                                        details: { endDateTime: '2037-01-01T00:00:00.000Z' },
                                                        eventType: 'event'
                                                    },
                                                    {
                                                        id: 'event2',
                                                        details: { endDateTime: '2037-01-01T00:00:00.000Z' },
                                                        eventType: 'calendar'
                                                    }
                                                ]
                                            )
                                        })
                                    })
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ id: 'userId', isFeedsGenerated: true }])
                    })
                }),
                exec: sinon.stub().resolves([{ id: 'userId', isFeedsGenerated: true }])
            });

            sinon.stub(Post, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        attributes: sinon.stub().returns({
                                            exec: sinon.stub().resolves(
                                                [
                                                    {
                                                        id: 'post1', title: 'title', subTitle: 'subTitle',
                                                        content: 'content', publishedDate: '2037-01-01T00:00:00.000Z'
                                                    }
                                                ]
                                            )
                                        })
                                    })
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(Fundraiser, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        attributes: sinon.stub().returns({
                                            exec: sinon.stub().resolves([
                                                {
                                                    id: 'fundraiser1',
                                                    startDate: '2037-01-01T00:00:00.000Z',
                                                    endDate: '2037-01-01T00:00:00.000Z',
                                                    fundraiserType: CONSTANTS.FUNDRAISER_TYPES.SPIRITWEAR
                                                },
                                                {
                                                    id: 'fundraiser3',
                                                    startDate: '2037-01-01T00:00:00.000Z',
                                                    endDate: '2037-01-01T00:00:00.000Z',
                                                    fundraiserType: CONSTANTS.FUNDRAISER_TYPES.BOOSTER
                                                }
                                            ])
                                        })
                                    })
                                })
                            })
                        })
                    })
                })
            });

            constantModelGetStub.withArgs(CONSTANTS.POST_EXPIRATION_DAYS).resolves({ value: '15' });

            sinon.stub(Groups, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        exec: sinon.stub().resolves([{ groupId: 'group1' }])
                                    })
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ groupId: 'group1', childrenIds: ['child1'], save: () => { } }])
                            })
                        }),
                        exec: sinon.stub().resolves([{ groupId: 'group1', childrenIds: ['child1'], save: () => { } }])
                    })
                })
            });

            sinon.stub(MessageModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                exec: sinon.stub().resolves([
                                    {
                                        id: 'messageId1',
                                        message: 'message1',
                                        mediaName: 'mediaName1',
                                        mediaType: 'mediaType1',
                                        mediaDisplayName: 'mediaDisplayName1',
                                        mediaThumbnailName: 'mediaThumbnailName1'
                                    },
                                    {
                                        id: 'messageId1',
                                        message: 'message1',
                                        mediaType: 'mediaType1',
                                        replyMessage: {
                                            id: 'replyMessageId1',
                                            mediaName: 'replyMediaName1'
                                        }
                                    }
                                ])
                            })
                        })
                    })
                })
            });

            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Child/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'associatedColor': { 'S': '#000000' },
                        'associatedOrganizations': { 'L': [{ 'S': '65824922-7a2d-49c0-8f39-ff4d7a729ecd' }] },
                        'lastName': { 'S': 'user' },
                        'updatedAt': { 'S': '2023-11-20T11:20:09.158Z' },
                        'dob': { 'S': '2023-06-20T00:00:00.000Z' },
                        'createdAt': { 'S': '2023-11-20T11:20:09.158Z' },
                        'isDeleted': { 'N': '0' },
                        'id': { 'S': '3e4406aa-b95b-489e-9e58-cfa746eed023' },
                        'zipCode': { 'S': '08820' },
                        'firstName': { 'S': 'Test' },
                        'gender': { 'S': 'boy' },
                        'guardians': { 'L': [{ 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' }] }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(addReferenceToSortedSetStub);
            sinon.assert.called(childRegisteredEventsStub);
        });

        it('should insert events for child when child is INSERTED and feed is not generated for user', async () => {
            const addReferenceToSortedSetStub = sinon.stub(RedisUtil, 'addEventReferenceToSortedSet').resolves();
            const childRegisteredEventsStub = sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([
                JSON.stringify({
                    eventId: 'fundraiser3',
                    fundraiserId: 'fundraiser3',
                    score: 1713033600000
                })
            ]);

            const queryStub = sinon.stub(Event, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const whereStub = sinon.stub();
            const whereEqStub = sinon.stub();
            const whereStub1 = sinon.stub();
            const whereEqStub1 = sinon.stub();
            const attributesStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({
                eq: eqStub.returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            exec: sinon.stub().resolves([
                                { id: 'event1', details: { endDateTime: '2037-01-01T00:00:00.000Z' }, eventType: 'calendar' }])
                        })
                    }),
                    using: usingStub.returns({
                        where: whereStub.returns({
                            eq: whereEqStub.returns({
                                where: whereStub1.returns({
                                    eq: whereEqStub1.returns({
                                        attributes: attributesStub.returns({
                                            exec: execStub.resolves(
                                                [
                                                    {
                                                        id: 'event1',
                                                        details: { endDateTime: '2037-01-01T00:00:00.000Z' },
                                                        eventType: 'event'
                                                    },
                                                    {
                                                        id: 'event2',
                                                        details: { endDateTime: '2037-01-01T00:00:00.000Z' },
                                                        eventType: 'calendar'
                                                    }
                                                ]
                                            )
                                        })
                                    })
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ id: 'userId', isFeedsGenerated: false }])
                    })
                }),
                exec: sinon.stub().resolves([])
            });

            sinon.stub(Post, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        attributes: sinon.stub().returns({
                                            exec: sinon.stub().resolves(
                                                [
                                                    {
                                                        id: 'post1', title: 'title', subTitle: 'subTitle',
                                                        content: 'content', publishedDate: '2037-01-01T00:00:00.000Z'
                                                    }
                                                ]
                                            )
                                        })
                                    })
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(Fundraiser, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        attributes: sinon.stub().returns({
                                            exec: sinon.stub().resolves([
                                                {
                                                    id: 'fundraiser1',
                                                    startDate: '2037-01-01T00:00:00.000Z',
                                                    endDate: '2037-01-01T00:00:00.000Z',
                                                    fundraiserType: CONSTANTS.FUNDRAISER_TYPES.SPIRITWEAR
                                                },
                                                {
                                                    id: 'fundraiser3',
                                                    startDate: '2037-01-01T00:00:00.000Z',
                                                    endDate: '2037-01-01T00:00:00.000Z',
                                                    fundraiserType: CONSTANTS.FUNDRAISER_TYPES.BOOSTER
                                                }
                                            ])
                                        })
                                    })
                                })
                            })
                        })
                    })
                })
            });

            constantModelGetStub.withArgs(CONSTANTS.POST_EXPIRATION_DAYS).resolves({ value: '15' });

            sinon.stub(Groups, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        exec: sinon.stub().resolves([{ groupId: 'group1' }])
                                    })
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ groupId: 'group1', childrenIds: ['child1'], save: () => { } }])
                            })
                        }),
                        exec: sinon.stub().resolves([{ groupId: 'group1', childrenIds: ['child1'], save: () => { } }])
                    })
                })
            });

            sinon.stub(MessageModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                exec: sinon.stub().resolves([
                                    {
                                        id: 'messageId1',
                                        message: 'message1',
                                        mediaName: 'mediaName1',
                                        mediaType: 'mediaType1',
                                        mediaDisplayName: 'mediaDisplayName1',
                                        mediaThumbnailName: 'mediaThumbnailName1'
                                    },
                                    {
                                        id: 'messageId1',
                                        message: 'message1',
                                        mediaType: 'mediaType1',
                                        replyMessage: {
                                            id: 'replyMessageId1',
                                            mediaName: 'replyMediaName1'
                                        }
                                    }
                                ])
                            })
                        })
                    })
                })
            });

            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Child/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'associatedColor': { 'S': '#000000' },
                        'associatedOrganizations': { 'L': [{ 'S': '65824922-7a2d-49c0-8f39-ff4d7a729ecd' }] },
                        'lastName': { 'S': 'user' },
                        'updatedAt': { 'S': '2023-11-20T11:20:09.158Z' },
                        'dob': { 'S': '2023-06-20T00:00:00.000Z' },
                        'createdAt': { 'S': '2023-11-20T11:20:09.158Z' },
                        'isDeleted': { 'N': '0' },
                        'id': { 'S': '3e4406aa-b95b-489e-9e58-cfa746eed023' },
                        'zipCode': { 'S': '08820' },
                        'firstName': { 'S': 'Test' },
                        'gender': { 'S': 'boy' },
                        'guardians': { 'L': [{ 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' }] }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(addReferenceToSortedSetStub);
            sinon.assert.called(childRegisteredEventsStub);
        });

        it('should insert events for child when child is INSERTED and user not found', async () => {
            const addReferenceToSortedSetStub = sinon.stub(RedisUtil, 'addEventReferenceToSortedSet').resolves();
            const childRegisteredEventsStub = sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([
                JSON.stringify({
                    eventId: 'fundraiser3',
                    fundraiserId: 'fundraiser3',
                    score: 1713033600000
                })
            ]);

            const queryStub = sinon.stub(Event, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const whereStub = sinon.stub();
            const whereEqStub = sinon.stub();
            const whereStub1 = sinon.stub();
            const whereEqStub1 = sinon.stub();
            const attributesStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({
                eq: eqStub.returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            exec: sinon.stub().resolves([
                                { id: 'event1', details: { endDateTime: '2037-01-01T00:00:00.000Z' }, eventType: 'calendar' }])
                        })
                    }),
                    using: usingStub.returns({
                        where: whereStub.returns({
                            eq: whereEqStub.returns({
                                where: whereStub1.returns({
                                    eq: whereEqStub1.returns({
                                        attributes: attributesStub.returns({
                                            exec: execStub.resolves(
                                                [
                                                    {
                                                        id: 'event1',
                                                        details: { endDateTime: '2037-01-01T00:00:00.000Z' },
                                                        eventType: 'event'
                                                    },
                                                    {
                                                        id: 'event2',
                                                        details: { endDateTime: '2037-01-01T00:00:00.000Z' },
                                                        eventType: 'calendar'
                                                    }
                                                ]
                                            )
                                        })
                                    })
                                })
                            })
                        })
                    })
                })
            });

            const saveUserStub = sinon.stub();

            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ id: 'userId', isFeedsGenerated: false }])
                    })
                }),
                exec: sinon.stub().resolves([{ id: 'userId', isFeedsGenerated: false, save: saveUserStub }])
            });

            sinon.stub(Post, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        attributes: sinon.stub().returns({
                                            exec: sinon.stub().resolves(
                                                [
                                                    {
                                                        id: 'post1', title: 'title', subTitle: 'subTitle',
                                                        content: 'content', publishedDate: '2037-01-01T00:00:00.000Z'
                                                    }
                                                ]
                                            )
                                        })
                                    })
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(Fundraiser, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        attributes: sinon.stub().returns({
                                            exec: sinon.stub().resolves([
                                                {
                                                    id: 'fundraiser1',
                                                    startDate: '2037-01-01T00:00:00.000Z',
                                                    endDate: '2037-01-01T00:00:00.000Z',
                                                    fundraiserType: CONSTANTS.FUNDRAISER_TYPES.SPIRITWEAR
                                                },
                                                {
                                                    id: 'fundraiser3',
                                                    startDate: '2037-01-01T00:00:00.000Z',
                                                    endDate: '2037-01-01T00:00:00.000Z',
                                                    fundraiserType: CONSTANTS.FUNDRAISER_TYPES.BOOSTER
                                                }
                                            ])
                                        })
                                    })
                                })
                            })
                        })
                    })
                })
            });

            constantModelGetStub.withArgs(CONSTANTS.POST_EXPIRATION_DAYS).resolves({ value: '15' });

            sinon.stub(Groups, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        exec: sinon.stub().resolves([{ groupId: 'group1' }])
                                    })
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ groupId: 'group1', childrenIds: ['child1'], save: () => { } }])
                            })
                        }),
                        exec: sinon.stub().resolves([{ groupId: 'group1', childrenIds: ['child1'], save: () => { } }])
                    })
                })
            });

            sinon.stub(MessageModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                exec: sinon.stub().resolves([
                                    {
                                        id: 'messageId1',
                                        message: 'message1',
                                        mediaName: 'mediaName1',
                                        mediaType: 'mediaType1',
                                        mediaDisplayName: 'mediaDisplayName1',
                                        mediaThumbnailName: 'mediaThumbnailName1'
                                    },
                                    {
                                        id: 'messageId1',
                                        message: 'message1',
                                        mediaType: 'mediaType1',
                                        replyMessage: {
                                            id: 'replyMessageId1',
                                            mediaName: 'replyMediaName1'
                                        }
                                    }
                                ])
                            })
                        })
                    })
                })
            });

            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Child/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'associatedColor': { 'S': '#000000' },
                        'associatedOrganizations': { 'L': [{ 'S': '65824922-7a2d-49c0-8f39-ff4d7a729ecd' }] },
                        'lastName': { 'S': 'user' },
                        'updatedAt': { 'S': '2023-11-20T11:20:09.158Z' },
                        'dob': { 'S': '2023-06-20T00:00:00.000Z' },
                        'createdAt': { 'S': '2023-11-20T11:20:09.158Z' },
                        'isDeleted': { 'N': '0' },
                        'id': { 'S': '3e4406aa-b95b-489e-9e58-cfa746eed023' },
                        'zipCode': { 'S': '08820' },
                        'firstName': { 'S': 'Test' },
                        'gender': { 'S': 'boy' },
                        'guardians': { 'L': [{ 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' }] }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(addReferenceToSortedSetStub);
            sinon.assert.called(childRegisteredEventsStub);
            sinon.assert.called(saveUserStub);
        });

        it('should refresh child feeds when associated organization changes', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Child/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: { S: 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        firstName: { S: 'test' },
                        lastName: { S: 'user' },
                        associatedColor: { S: '#000000' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' },
                        associatedOrganizations: { L: [{ S: '123' }] },
                        connections: { L: [] },
                        guardians: { L: [{ S: 'guardian1' }, { S: 'guardian2' }, { S: 'guardian5' }] }
                    },
                    NewImage: {
                        id: { S: 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        firstName: { S: 'test1' },
                        lastName: { S: 'user' },
                        associatedColor: { S: '#000000' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' },
                        associatedOrganizations: { L: [{ S: '456' }] },
                        connections: { L: [{ M: { childId: { S: 'child1' }, status: { S: 'requestedBy' } } }] },
                        guardians: { L: [{ S: 'guardian3' }, { S: 'guardian2' }, { S: 'guardian4' }] }
                    }
                }
            };

            const queryStub = sinon.stub(Event, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const whereStub = sinon.stub();
            const attributesStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({
                eq: eqStub.withArgs('456').returns({
                    using: usingStub.returns({
                        where: whereStub.withArgs('isDeleted').returns({
                            eq: eqStub.withArgs('0').returns({
                                where: whereStub.withArgs('status').returns({
                                    eq: eqStub.withArgs('published').returns({
                                        attributes: attributesStub.returns({
                                            exec: execStub.resolves([{
                                                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                                                title: 'Test event',
                                                details: { startDateTime: '2050-01-01T00:00:00.000Z' }
                                            }])
                                        })
                                    })
                                })
                            })
                        })
                    }),
                    where: whereStub.withArgs('eventType').returns({
                        eq: eqStub.withArgs('calendar').returns({
                            exec: execStub.resolves([{
                                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                                title: 'Test event',
                                details: { startDateTime: '2050-01-01T00:00:00.000Z' }
                            }])
                        })
                    })
                })
            });

            constantModelGetStub.withArgs(CONSTANTS.POST_EXPIRATION_DAYS).resolves({ value: '15' });

            const queryPostStub = sinon.stub(Post, 'query');
            const eqPostStub = sinon.stub();
            const usingPostStub = sinon.stub();
            const wherePostStub = sinon.stub();
            const attributesPostStub = sinon.stub();
            const execPostStub = sinon.stub();

            queryPostStub.returns({
                eq: eqPostStub.withArgs('456').returns({
                    using: usingPostStub.returns({
                        where: wherePostStub.withArgs('isDeleted').returns({
                            eq: eqPostStub.withArgs('0').returns({
                                where: wherePostStub.withArgs('status').returns({
                                    eq: eqPostStub.withArgs('published').returns({
                                        attributes: attributesPostStub.returns({
                                            exec: execPostStub.resolves([{
                                                id: '0fd6a871-a6f5-4690-b06a-d786f1361eeg',
                                                title: 'Test Post',
                                                subTitle: 'Test SubTitle',
                                                publishedDate: '2050-01-01T00:00:00.000Z'
                                            }])
                                        })
                                    })
                                })
                            })
                        })
                    })
                })
            });

            const queryFundraiserStub = sinon.stub(Fundraiser, 'query');
            const eqFundraiserStub = sinon.stub();
            const usingFundraiserStub = sinon.stub();
            const whereFundraiserStub = sinon.stub();
            const attributesFundraiserStub = sinon.stub();
            const execFundraiserStub = sinon.stub();

            queryFundraiserStub.returns({
                eq: eqFundraiserStub.withArgs('456').returns({
                    using: usingFundraiserStub.returns({
                        where: whereFundraiserStub.withArgs('isDeleted').returns({
                            eq: eqFundraiserStub.withArgs('0').returns({
                                where: whereFundraiserStub.withArgs('status').returns({
                                    eq: eqFundraiserStub.withArgs('published').returns({
                                        attributes: attributesFundraiserStub.returns({
                                            exec: execFundraiserStub.resolves([{
                                                id: '0fd6a871-a6f5-4690-b06a-d786f1361eeh',
                                                title: 'Test Fundraiser',
                                                description: 'Test Description',
                                                startDate: '2050-01-01T00:00:00.000Z',
                                                endDate: '2050-01-01T00:00:00.000Z'
                                            }])
                                        })
                                    })
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(Event, 'batchGet').resolves([{
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                title: 'Test event', details: { startDateTime: '2050-01-01T00:00:00.000Z' }, organizationId: '123'
            }]);

            sinon.stub(Post, 'batchGet').resolves([{
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eeg',
                title: 'Test Post', subTitle: 'subTitle test', organizationId: '123'
            }]);

            sinon.stub(Fundraiser, 'batchGet').resolves([{
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eeh',
                title: 'Test Fundraiser', description: 'Test Description', organizationId: '123'
            }]);

            const sendStub = sinon.stub(SQSClient.prototype, 'send').resolves();

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([
                JSON.stringify({
                    eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                    childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    isSignedUp: false,
                    status: ''
                }),
                JSON.stringify({
                    eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                    childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    isSignedUp: false,
                    status: '',
                    isPost: true,
                    postId: '0fd6a871-a6f5-4690-b06a-d786f1361eeg'
                }),
                JSON.stringify({
                    eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                    childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    isSignedUp: false,
                    status: '',
                    isFundraiser: true,
                    fundraiserId: '0fd6a871-a6f5-4690-b06a-d786f1361eeh'
                })
            ]);

            sinon.stub(RedisUtil, 'addEventReferenceToSortedSet').resolves();
            sinon.stub(RedisUtil, 'getElementsOfSortedSetWithScores').resolves([JSON.stringify({
                eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                isSignedUp: false,
                status: ''
            })]);

            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ id: 'userId', isFeedsGenerated: true }])
                    })
                })
            });

            sinon.stub(MessageModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ id: 'messageId' }])
                            })
                        })
                    })
                })
            });

            sinon.stub(Groups, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        exec: sinon.stub().resolves([{ id: 'group1' }])
                                    })
                                }),
                                exec: sinon.stub()
                                    .onCall(0).resolves([{ id: 'group1' }])
                                    .onCall(1).resolves([{ id: 'group2' }])
                                    .onCall(2).resolves([{ id: 'group4' }])
                                    .onCall(3).resolves([{ id: 'group3' }])
                            }),
                            where: sinon.stub().returns({
                                eq: sinon.stub().returns({
                                    exec: sinon.stub().resolves([{ id: 'group1' }])
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([
                            {
                                id: 'groupMember1',
                                childrenIds: ['ace0149e-447d-4f7f-9402-95a96963de9b'], save: () => { }, userId: 'guardian3'
                            },
                            {
                                id: 'groupMember3',
                                childrenIds: ['ace0149e-447d-4f7f-9402-95a96963de9b'], save: () => { }, userId: 'guardian1'
                            },
                            { id: 'groupMember5', childrenIds: ['childId1'], save: () => { }, userId: 'guardian5' }
                        ])
                    })
                })
            });

            sinon.stub(EventSignups, 'query').returns({
                eq: sinon.stub().returns({
                    exec: sinon.stub().resolves([
                        { id: 'eventSignup1', eventId: 'eventId' },
                        { id: 'eventSignup2', eventId: 'eventId' }])
                })
            });

            sinon.stub(GroupMembers, 'batchPut').resolves();
            sinon.stub(GroupMembers, 'batchDelete').resolves();

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(sendStub);

            sinon.assert.called(RedisUtil.addEventReferenceToSortedSet);
        });

        it('should refresh child feeds when associated organization changes with no future feeds', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Child/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: { S: 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        firstName: { S: 'test' },
                        lastName: { S: 'user' },
                        associatedColor: { S: '#000000' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' },
                        associatedOrganizations: { L: [{ S: '123' }] },
                        connections: { L: [{ M: { childId: { S: 'child2' }, status: { S: 'requestedTo' } } }] }
                    },
                    NewImage: {
                        id: { S: 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        firstName: { S: 'test1' },
                        lastName: { S: 'user' },
                        associatedColor: { S: '#000000' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' },
                        associatedOrganizations: { L: [{ S: '456' }] },
                        connections: { L: [{ M: { childId: { S: 'child2' }, status: { S: 'connected' } } }] }
                    }
                }
            };

            constantModelGetStub.withArgs(CONSTANTS.POST_EXPIRATION_DAYS).resolves({ value: '15' });

            const queryStub = sinon.stub(Event, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const whereStub = sinon.stub();
            const attributesStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({
                eq: eqStub.withArgs('456').returns({
                    using: usingStub.returns({
                        where: whereStub.withArgs('isDeleted').returns({
                            eq: eqStub.withArgs('0').returns({
                                where: whereStub.withArgs('status').returns({
                                    eq: eqStub.withArgs('published').returns({
                                        attributes: attributesStub.returns({
                                            exec: execStub.resolves([{
                                                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                                                title: 'Test event',
                                                details: { startDateTime: '2020-01-01T00:00:00.000Z' }
                                            }])
                                        })
                                    })
                                })
                            })
                        })
                    }),
                    where: whereStub.withArgs('eventType').returns({
                        eq: eqStub.withArgs('calendar').returns({
                            exec: execStub.resolves([{
                                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                                title: 'Test event',
                                details: { startDateTime: '2050-01-01T00:00:00.000Z' }
                            }])
                        })
                    })
                })
            });

            const queryPostStub = sinon.stub(Post, 'query');
            const eqPostStub = sinon.stub();
            const usingPostStub = sinon.stub();
            const wherePostStub = sinon.stub();
            const attributesPostStub = sinon.stub();
            const execPostStub = sinon.stub();

            queryPostStub.returns({
                eq: eqPostStub.withArgs('456').returns({
                    using: usingPostStub.returns({
                        where: wherePostStub.withArgs('isDeleted').returns({
                            eq: eqPostStub.withArgs('0').returns({
                                where: wherePostStub.withArgs('status').returns({
                                    eq: eqPostStub.withArgs('published').returns({
                                        attributes: attributesPostStub.returns({
                                            exec: execPostStub.resolves([{
                                                id: '0fd6a871-a6f5-4690-b06a-d786f1361eeg',
                                                title: 'Test Post',
                                                subTitle: 'Test SubTitle',
                                                publishedDate: '2020-01-01T00:00:00.000Z'
                                            }])
                                        })
                                    })
                                })
                            })
                        })
                    })
                })
            });

            const queryFundraiserStub = sinon.stub(Fundraiser, 'query');
            const eqFundraiserStub = sinon.stub();
            const usingFundraiserStub = sinon.stub();
            const whereFundraiserStub = sinon.stub();
            const attributesFundraiserStub = sinon.stub();
            const execFundraiserStub = sinon.stub();

            queryFundraiserStub.returns({
                eq: eqFundraiserStub.withArgs('456').returns({
                    using: usingFundraiserStub.returns({
                        where: whereFundraiserStub.withArgs('isDeleted').returns({
                            eq: eqFundraiserStub.withArgs('0').returns({
                                where: whereFundraiserStub.withArgs('status').returns({
                                    eq: eqFundraiserStub.withArgs('published').returns({
                                        attributes: attributesFundraiserStub.returns({
                                            exec: execFundraiserStub.resolves([{
                                                id: '0fd6a871-a6f5-4690-b06a-d786f1361eeh',
                                                title: 'Test Fundraiser',
                                                description: 'Test Description',
                                                startDate: '2020-01-01T00:00:00.000Z',
                                                endDate: '2020-01-01T00:00:00.000Z'
                                            }])
                                        })
                                    })
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(Event, 'batchGet').resolves([{
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                title: 'Test event', details: { startDateTime: '2020-01-01T00:00:00.000Z' }, organizationId: '123'
            }]);

            sinon.stub(Post, 'batchGet').resolves([{
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eeg',
                title: 'Test Post', subTitle: 'subTitle test', organizationId: '123'
            }]);

            sinon.stub(Fundraiser, 'batchGet').resolves([{
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eeh',
                title: 'Test Fundraiser', description: 'Test Description', organizationId: '123'
            }]);

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([JSON.stringify({
                eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                isSignedUp: false,
                status: ''
            })]);

            sinon.stub(RedisUtil, 'addEventReferenceToSortedSet').resolves();

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(RedisUtil.getElementsOfSortedSetByScore);
        });

        it('should refresh child feeds when associated organization changes with no feeds', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Child/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: { S: 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        firstName: { S: 'test' },
                        lastName: { S: 'user' },
                        associatedColor: { S: '#000000' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' },
                        associatedOrganizations: { L: [{ S: '123' }] },
                        connections: { L: [] },
                        guardians: { L: [] }
                    },
                    NewImage: {
                        id: { S: 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        firstName: { S: 'test1' },
                        lastName: { S: 'user' },
                        associatedColor: { S: '#000000' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' },
                        associatedOrganizations: { L: [{ S: '456' }] },
                        connections: { L: [] },
                        guardians: { L: [] }
                    }
                }
            };

            const queryStub = sinon.stub(Event, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const whereStub = sinon.stub();
            const attributesStub = sinon.stub();
            const execStub = sinon.stub();

            constantModelGetStub.withArgs(CONSTANTS.POST_EXPIRATION_DAYS).resolves({ value: '15' });

            queryStub.returns({
                eq: eqStub.withArgs('456').returns({
                    using: usingStub.returns({
                        where: whereStub.withArgs('isDeleted').returns({
                            eq: eqStub.withArgs('0').returns({
                                where: whereStub.withArgs('status').returns({
                                    eq: eqStub.withArgs('published').returns({
                                        attributes: attributesStub.returns({
                                            exec: execStub.resolves([])
                                        })
                                    })
                                })
                            })
                        })
                    }),
                    where: whereStub.withArgs('eventType').returns({
                        eq: eqStub.withArgs('calendar').returns({
                            exec: execStub.resolves([{
                                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                                title: 'Test event',
                                details: { startDateTime: '2050-01-01T00:00:00.000Z' }
                            }])
                        })
                    })
                })
            });

            sinon.stub(Post, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        attributes: sinon.stub().returns({
                                            exec: sinon.stub().resolves(
                                                [
                                                    {
                                                        id: 'post1', title: 'title', subTitle: 'subTitle',
                                                        content: 'content', publishedDate: '2037-01-01T00:00:00.000Z'
                                                    }
                                                ]
                                            )
                                        })
                                    })
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(Fundraiser, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        attributes: sinon.stub().returns({
                                            exec: sinon.stub().resolves([
                                                {
                                                    id: 'fundraiser1',
                                                    startDate: '2037-01-01T00:00:00.000Z',
                                                    endDate: '2037-01-01T00:00:00.000Z',
                                                    fundraiserType: CONSTANTS.FUNDRAISER_TYPES.SPIRITWEAR
                                                },
                                                {
                                                    id: 'fundraiser3',
                                                    startDate: '2037-01-01T00:00:00.000Z',
                                                    endDate: '2037-01-01T00:00:00.000Z',
                                                    fundraiserType: CONSTANTS.FUNDRAISER_TYPES.BOOSTER
                                                }
                                            ])
                                        })
                                    })
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([]);

            sinon.stub(RedisUtil, 'getElementsOfSortedSetWithScores').resolves([JSON.stringify({
                eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                isSignedUp: false,
                status: ''
            })]);

            sinon.stub(Groups, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        exec: sinon.stub().resolves([{ id: 'group1' }])
                                    })
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([{
                            id: 'groupMember1',
                            childrenIds: ['ace0149e-447d-4f7f-9402-95a96963de9b'], save: () => { }
                        }])
                    })
                })
            });

            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ id: 'userId', isFeedsGenerated: true }])
                    })
                }),
                exec: sinon.stub().resolves([])
            });

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(RedisUtil.getElementsOfSortedSetByScore);
        });

        it('should refresh child feeds when associated organization changes and remove old feed with organization id', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Child/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: { S: 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        firstName: { S: 'test' },
                        lastName: { S: 'user' },
                        associatedColor: { S: '#000000' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' },
                        associatedOrganizations: { L: [{ S: '123' }] },
                        connections: { L: [] },
                        guardians: { L: [] }
                    },
                    NewImage: {
                        id: { S: 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        firstName: { S: 'test1' },
                        lastName: { S: 'user' },
                        associatedColor: { S: '#000000' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' },
                        associatedOrganizations: { L: [{ S: '456' }] },
                        connections: { L: [] },
                        guardians: { L: [] }
                    }
                }
            };

            constantModelGetStub.withArgs(CONSTANTS.POST_EXPIRATION_DAYS).resolves({ value: '15' });


            const queryStub = sinon.stub(Event, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const whereStub = sinon.stub();
            const attributesStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({
                eq: eqStub.withArgs('456').returns({
                    using: usingStub.returns({
                        where: whereStub.withArgs('isDeleted').returns({
                            eq: eqStub.withArgs('0').returns({
                                where: whereStub.withArgs('status').returns({
                                    eq: eqStub.withArgs('published').returns({
                                        attributes: attributesStub.returns({
                                            exec: execStub.resolves([{
                                                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                                                title: 'Test event',
                                                details: { startDateTime: '2020-01-01T00:00:00.000Z' }
                                            }])
                                        })
                                    })
                                })
                            })
                        })
                    }),
                    where: whereStub.withArgs('eventType').returns({
                        eq: eqStub.withArgs('calendar').returns({
                            exec: execStub.resolves([{
                                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                                title: 'Test event',
                                details: { startDateTime: '2050-01-01T00:00:00.000Z' }
                            }])
                        })
                    })
                })
            });

            sinon.stub(Post, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        attributes: sinon.stub().returns({
                                            exec: sinon.stub().resolves(
                                                [
                                                    {
                                                        id: 'post1', title: 'title', subTitle: 'subTitle',
                                                        content: 'content', publishedDate: '2037-01-01T00:00:00.000Z'
                                                    }
                                                ]
                                            )
                                        })
                                    })
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(Fundraiser, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        attributes: sinon.stub().returns({
                                            exec: sinon.stub().resolves([
                                                {
                                                    id: 'fundraiser1',
                                                    startDate: '2037-01-01T00:00:00.000Z',
                                                    endDate: '2037-01-01T00:00:00.000Z',
                                                    fundraiserType: CONSTANTS.FUNDRAISER_TYPES.SPIRITWEAR
                                                },
                                                {
                                                    id: 'fundraiser3',
                                                    startDate: '2037-01-01T00:00:00.000Z',
                                                    endDate: '2037-01-01T00:00:00.000Z',
                                                    fundraiserType: CONSTANTS.FUNDRAISER_TYPES.BOOSTER
                                                }
                                            ])
                                        })
                                    })
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(Event, 'batchGet').resolves([{
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                title: 'Test event', details: { startDateTime: '2020-01-01T00:00:00.000Z' }, organizationId: '456'
            }]);

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([JSON.stringify({
                eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                isSignedUp: false,
                status: ''
            })]);

            sinon.stub(RedisUtil, 'getElementsOfSortedSetWithScores').resolves([JSON.stringify({
                eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                isSignedUp: false,
                status: ''
            })]);

            sinon.stub(RedisUtil, 'addEventReferenceToSortedSet').resolves();

            sinon.stub(Groups, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        exec: sinon.stub().resolves([{ id: 'group1' }])
                                    })
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([{
                            id: 'groupMember1',
                            childrenIds: ['ace0149e-447d-4f7f-9402-95a96963de9b'], save: () => { }
                        }])
                    })
                })
            });

            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ id: 'userId', isFeedsGenerated: true }])
                    })
                }),
                exec: sinon.stub().resolves([])
            });

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(RedisUtil.addEventReferenceToSortedSet);
        });

        // eslint-disable-next-line max-len
        it('should refresh child feeds when associated organization changes and remove old feed with organization id if event exists', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Child/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: { S: 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        firstName: { S: 'test' },
                        lastName: { S: 'user' },
                        associatedColor: { S: '#000000' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' },
                        associatedOrganizations: { L: [{ S: '123' }] },
                        connections: { L: [] },
                        guardians: { L: [] }
                    },
                    NewImage: {
                        id: { S: 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        firstName: { S: 'test1' },
                        lastName: { S: 'user' },
                        associatedColor: { S: '#000000' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' },
                        associatedOrganizations: { L: [{ S: '456' }] },
                        connections: { L: [] },
                        guardians: { L: [] }
                    }
                }
            };

            sinon.stub(RedisUtil, 'getElementsOfSortedSetWithScores').resolves([JSON.stringify({
                eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                isSignedUp: false,
                status: ''
            })]);

            constantModelGetStub.withArgs(CONSTANTS.POST_EXPIRATION_DAYS).resolves({ value: 'abc' });

            const queryStub = sinon.stub(Event, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const whereStub = sinon.stub();
            const attributesStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({
                eq: eqStub.withArgs('456').returns({
                    using: usingStub.returns({
                        where: whereStub.withArgs('isDeleted').returns({
                            eq: eqStub.withArgs('0').returns({
                                where: whereStub.withArgs('status').returns({
                                    eq: eqStub.withArgs('published').returns({
                                        attributes: attributesStub.returns({
                                            exec: execStub.resolves([{
                                                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                                                title: 'Test event',
                                                details: { startDateTime: '2020-01-01T00:00:00.000Z' }
                                            }])
                                        })
                                    })
                                })
                            })
                        })
                    }),
                    where: whereStub.withArgs('eventType').returns({
                        eq: eqStub.withArgs('calendar').returns({
                            exec: execStub.resolves([{
                                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                                title: 'Test event',
                                details: { startDateTime: '2050-01-01T00:00:00.000Z' }
                            }])
                        })
                    })
                })
            });

            sinon.stub(Post, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        attributes: sinon.stub().returns({
                                            exec: sinon.stub().resolves(
                                                [
                                                    {
                                                        id: 'post1', title: 'title', subTitle: 'subTitle',
                                                        content: 'content', publishedDate: '2037-01-01T00:00:00.000Z'
                                                    }
                                                ]
                                            )
                                        })
                                    })
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(Fundraiser, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        attributes: sinon.stub().returns({
                                            exec: sinon.stub().resolves([
                                                {
                                                    id: 'fundraiser1',
                                                    startDate: '2037-01-01T00:00:00.000Z',
                                                    endDate: '2037-01-01T00:00:00.000Z',
                                                    fundraiserType: CONSTANTS.FUNDRAISER_TYPES.SPIRITWEAR
                                                },
                                                {
                                                    id: 'fundraiser3',
                                                    startDate: '2037-01-01T00:00:00.000Z',
                                                    endDate: '2037-01-01T00:00:00.000Z',
                                                    fundraiserType: CONSTANTS.FUNDRAISER_TYPES.BOOSTER
                                                }
                                            ])
                                        })
                                    })
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(Event, 'batchGet').resolves([{
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                title: 'Test event', details: { startDateTime: '2020-01-01T00:00:00.000Z' }, organizationId: '123'
            }]);

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([JSON.stringify({
                eventId: '1fd6a871-a6f5-4690-b06a-d786f1361eef',
                childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                isSignedUp: false,
                status: ''
            })]);

            sinon.stub(RedisUtil, 'addEventReferenceToSortedSet').resolves();

            sinon.stub(Groups, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        exec: sinon.stub().resolves([{ id: 'group1' }])
                                    })
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([{
                            id: 'groupMember1',
                            childrenIds: ['ace0149e-447d-4f7f-9402-95a96963de9b'], save: () => { }
                        }])
                    })
                })
            });

            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ id: 'userId', isFeedsGenerated: true }])
                    })
                }),
                exec: sinon.stub().resolves([])
            });

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(RedisUtil.addEventReferenceToSortedSet);
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
