const dynamoose = require('dynamoose');
const { v4: uuidv4 } = require('uuid');

const childOrganizationMappingSchema = new dynamoose.Schema({
    childOrganizationMappingId: {
        hashKey: true,
        type: String,
        default: () => uuidv4()
    },
    organizationId: {
        type: String,
        index: {
            global: true,
            name: 'organizationId-index',
            project: true
        },
        required: true
    },
    childId: {
        type: String,
        required: true,
        index: {
            global: true,
            name: 'childId-index',
            project: true
        }
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('ChildOrganizationMapping', childOrganizationMappingSchema);
