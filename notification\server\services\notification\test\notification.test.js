const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const sinon = require('sinon');
const User = require('../../../models/user.model');
const jwt = require('jsonwebtoken');
const Organization = require('../../../models/organization.model');
const Child = require('../../../models/child.model');
const Notification = require('../../../models/notification.model');
const ChildOrganizationMapping = require('../../../models/childOrganizationMapping.model');
const EventSignup = require('../../../models/eventSignup.model');
const Event = require('../../../models/event.model');
const TestCase = require('./testcaseNotification');
const axios = require('axios');
const { google } = require('googleapis');
const Utils = require('../../../util/utilFunctions');


const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
/* User Token */
const user = {
    id: '95285616-3769-4b2e-b36b-898597b8146e',
    email: '<EMAIL>'
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'testingjwt', tokenOptionalInfo)
};

const child = {
    id: '5f5f7e5f7e5f7e5f7e5f7e5f',
    firstName: 'test',
    lastName: 'test',
    associatedColor: 'red',
    school: 'test school'
};

const childWithPhotoUrl = {
    id: '5f5f7e5f7e5f7e5f7e5f7e5f',
    firstName: 'test',
    lastName: 'test',
    associatedColor: 'red',
    school: 'test school',
    photoURL: 'test url',
    homeRoom: 'test home room'
};

const notification = {
    id: '5f5f7e5f7e5f7e5f7e5f7e5f',
    title: 'test',
    description: 'test',
    userId: '95285616-3769-4b2e-b36b-898597b8146e',
    associatedChildId: '5f5f7e5f7e5f7e5f7e5f7e5f',
    payload: 'test'
};

Utils.addCommonReqTokenForHMac(request);
describe('Notification List', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should handle error if token is not passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .get('/notification//list')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and correct token is passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .get('/notification//list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and is verified', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 0, isDeleted: 0, role: 3,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', role: 'org super admin' }]
            });
            request(process.env.BASE_URL)
                .get('/notification//list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 423);
                    done();
                });
        });

        it('As a user I should handle error in getting notification list', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1,
                children: ['5f5f7e5f7e5f7e5f7e5f7e5f']
            });

            const notificationQueryStub = sinon.stub(Notification, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const sortStub = sinon.stub();
            const limitStub = sinon.stub();
            const execStub = sinon.stub();

            notificationQueryStub.returns({
                eq: eqStub.returns({
                    using: usingStub.returns({
                        sort: sortStub.returns({
                            limit: limitStub.returns({
                                exec: execStub.throws(new Error('error'))
                            })
                        })
                    })
                })
            });

            request(process.env.BASE_URL)
                .get('/notification/list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    notificationQueryStub.restore();
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get notification list', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, children: ['5f5f7e5f7e5f7e5f7e5f7e5f'] });

            const notificationQueryStub = sinon.stub(Notification, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const sortStub = sinon.stub();
            const limitStub = sinon.stub();
            const execStub = sinon.stub();

            notificationQueryStub.returns({
                eq: eqStub.returns({
                    using: usingStub.returns({
                        sort: sortStub.returns({
                            limit: limitStub.returns({
                                exec: execStub.returns([notification])
                            })
                        })
                    })
                })
            });

            sinon.stub(Child, 'get').resolves(child);

            sinon.stub(Organization, 'get').resolves({ name: 'test org' });

            request(process.env.BASE_URL)
                .get('/notification//list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    notificationQueryStub.restore();
                    Child.get.restore();
                    Organization.get.restore();
                    done();
                });
        });

        it('As a user I should get notification list with photo url', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, children: ['5f5f7e5f7e5f7e5f7e5f7e5f'] });

            const notificationQueryStub = sinon.stub(Notification, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const sortStub = sinon.stub();
            const limitStub = sinon.stub();
            const execStub = sinon.stub();

            notificationQueryStub.returns({
                eq: eqStub.returns({
                    using: usingStub.returns({
                        sort: sortStub.returns({
                            limit: limitStub.returns({
                                exec: execStub.returns([{ ...notification, payload: undefined }])
                            })
                        })
                    })
                })
            });

            sinon.stub(Child, 'get').resolves(childWithPhotoUrl);

            sinon.stub(Organization, 'get').resolves({ name: 'test org' });

            request(process.env.BASE_URL)
                .get('/notification//list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    notificationQueryStub.restore();
                    Child.get.restore();
                    Organization.get.restore();
                    done();
                });
        });

        it('As a user I should get 2nd page of notification list', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, children: ['5f5f7e5f7e5f7e5f7e5f7e5f'] });

            const notificationQueryStub = sinon.stub(Notification, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const sortStub = sinon.stub();
            const limitStub = sinon.stub();
            const startAtStub = sinon.stub();
            const execStub = sinon.stub();

            notificationQueryStub.returns({
                eq: eqStub.returns({
                    using: usingStub.returns({
                        sort: sortStub.returns({
                            limit: limitStub.returns({
                                startAt: startAtStub.returns({
                                    exec: execStub.returns([
                                        { ...notification, payload: { child: { id: 'child-id1' } } },
                                        { ...notification }])
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(Child, 'get').resolves(child);

            sinon.stub(Organization, 'get').resolves({ name: 'test org' });

            request(process.env.BASE_URL)
                .get('/notification//list?nextIndex=5f5f7e5f7e5f7e5f7e5f7e5f&nextCreatedAt=1706772659654&pageSize=1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    notificationQueryStub.restore();
                    Child.get.restore();
                    Organization.get.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Create Notification', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.addNotification.forEach(testCase => {
            it(testCase.it, (done) => {
                getStub.resolves({
                    status: 'active', isVerified: 1, isDeleted: 0, role: 1,
                    children: ['5f5f7e5f7e5f7e5f7e5f7e5f']
                });
                request(process.env.BASE_URL)
                    .post('/notification')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(testCase.request)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.statusCode, 400);
                        done();
                    });
            });
        });

        it('As a user I should handle error if token is not passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .post('/notification')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and correct token is passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .post('/notification')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and is verified', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 0, isDeleted: 0, role: 3,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', role: 'org super admin' }]
            });
            request(process.env.BASE_URL)
                .post('/notification')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 423);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and is verified', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 0, isDeleted: 0, role: 3,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', role: 'org super admin' }]
            });
            request(process.env.BASE_URL)
                .post('/notification')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    title: 'test',
                    description: 'test',
                    associatedChildId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                    notificationAction: 'test',
                    payload: 'test'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 423);
                    done();
                });
        });

        it('As a user I should validate that the child is associated with user', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1,
                children: ['5f5f7e5f7e5f7e5f7e5f7e5f']
            });

            request(process.env.BASE_URL)
                .post('/notification')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    title: 'test',
                    description: 'test',
                    associatedChildId: '5f5f7e5f7e5f7e5f7e5f7e5a',
                    notificationAction: 'test',
                    payload: 'test'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 404);
                    done();
                });
        });

        it('As a user I should be able to create notification', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1,
                children: ['5f5f7e5f7e5f7e5f7e5f7e5f']
            });
            const notificationSaveStub = sinon.stub(Notification.prototype, 'save');
            notificationSaveStub.resolves(notification);

            sinon.stub(Child, 'get').resolves(child);
            sinon.stub(Organization, 'get').resolves({ name: 'test org' });

            request(process.env.BASE_URL)
                .post('/notification')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    title: 'test',
                    description: 'test',
                    associatedChildId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                    notificationAction: 'test',
                    payload: 'test'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    notificationSaveStub.restore();
                    Child.get.restore();
                    Organization.get.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Update notification read status', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should handle error if token is not passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .patch('/notification')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and correct token is passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .patch('/notification')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and is verified', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 0, isDeleted: 0, role: 3,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', role: 'org super admin' }]
            });
            request(process.env.BASE_URL)
                .patch('/notification')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 423);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and is verified', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 0, isDeleted: 0, role: 3,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', role: 'org super admin' }]
            });
            request(process.env.BASE_URL)
                .patch('/notification?notificationId=5f5f7e5f7e5f7e5f7e5f7e5f')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 423);
                    done();
                });
        });

        it('As a user I should validate if the notification is not found', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1,
                children: ['5f5f7e5f7e5f7e5f7e5f7e5f']
            });
            sinon.stub(Notification, 'get').resolves(null);
            request(process.env.BASE_URL)
                .patch('/notification?notificationId=5f5f7e5f7e5f7e5f7e5f7e5f')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 404);
                    Notification.get.restore();
                    done();
                });
        });

        it('As a user I should validate if the notification is not associated with user', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1,
                children: ['5f5f7e5f7e5f7e5f7e5f7e5f']
            });

            sinon.stub(Notification, 'get').resolves({ ...notification, userId: '5f5f7e5f7e5f7e5f7e5f7e5a' });

            request(process.env.BASE_URL)
                .patch('/notification?notificationId=5f5f7e5f7e5f7e5f7e5f7e5f')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 404);
                    Notification.get.restore();
                    done();
                });
        });

        it('As a user I should validate if the notification is already read', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1,
                children: ['5f5f7e5f7e5f7e5f7e5f7e5f'], id: '95285616-3769-4b2e-b36b-898597b8146e'
            });

            sinon.stub(Notification, 'get').resolves({ ...notification, readStatus: true });

            request(process.env.BASE_URL)
                .patch('/notification?notificationId=5f5f7e5f7e5f7e5f7e5f7e5f')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Notification.get.restore();
                    done();
                });
        });

        it('As a user I should be able to update read status of a notification', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1,
                children: ['5f5f7e5f7e5f7e5f7e5f7e5f'], id: '95285616-3769-4b2e-b36b-898597b8146e'
            });

            sinon.stub(Notification, 'get').resolves({ ...notification, save: () => { return notification; } });
            sinon.stub(Child, 'get').resolves(child);
            sinon.stub(Organization, 'get').resolves({ name: 'test org' });

            request(process.env.BASE_URL)
                .patch('/notification?notificationId=5f5f7e5f7e5f7e5f7e5f7e5f')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Notification.get.restore();
                    Child.get.restore();
                    Organization.get.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});


describe('Send Notification', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
            process.env.NOTIFICATION_PRIVATE_KEY = 'testkey';
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.sendNotification.forEach(testCase => {
            it(testCase.it, (done) => {
                getStub.resolves({
                    status: 'active', isVerified: 1, isDeleted: 0, role: 3,
                    children: ['5f5f7e5f7e5f7e5f7e5f7e5f']
                });
                request(process.env.BASE_URL)
                    .post('/notification/send')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(testCase.request)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.statusCode, 400);
                        done();
                    });
            });
        });

        it('As a user I should handle error if token is not passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .post('/notification/send')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and correct token is passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .post('/notification/send')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and is verified', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 0, isDeleted: 0, role: 3,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', role: 'org super admin' }]
            });
            request(process.env.BASE_URL)
                .post('/notification/send')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 423);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and is verified', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 0, isDeleted: 0, role: 3,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', role: 'org super admin' }]
            });
            request(process.env.BASE_URL)
                .post('/notification/send')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    title: 'test',
                    description: 'Notification description',
                    organizationId: 'organization-id',
                    allParticipants: true,
                    eventId: 'event-id'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 423);
                    done();
                });
        });

        it('As a user I should get error if I am not associated with passed organization', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 3,
                children: ['5f5f7e5f7e5f7e5f7e5f7e5f'],
                associatedOrganizations: [{ organizationId: 'organization-id', role: 'super admin' }]
            });
            request(process.env.BASE_URL)
                .post('/notification/send')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    title: 'Notification Title',
                    description: 'Notification description',
                    organizationId: 'organization-id1',
                    allOrgChildren: true
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 404);
                    done();
                });
        });

        it('As a user I should get error if I have editor role in passed organization', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 3,
                children: ['5f5f7e5f7e5f7e5f7e5f7e5f'],
                associatedOrganizations: [{ organizationId: 'organization-id', role: 'editor' }]
            });
            request(process.env.BASE_URL)
                .post('/notification/send')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    title: 'Notification Title',
                    description: 'Notification description',
                    organizationId: 'organization-id',
                    allOrgChildren: true
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 403);
                    done();
                });
        });

        it('As a user I should be able to send notification to all children associated with passed org', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 3,
                children: ['5f5f7e5f7e5f7e5f7e5f7e5f'],
                associatedOrganizations: [{ organizationId: 'organization-id', role: 'super admin' }]
            });

            sinon.stub(ChildOrganizationMapping, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ childId: '5f5f7e5f7e5f7e5f7e5f7e5f' }])
                    })
                })
            });

            sinon.stub(Child, 'batchGet').resolves([{ id: 'child-id', guardians: ['user-id1', 'user-id2'] }]);
            sinon.stub(Notification, 'batchPut').resolves();

            const axiosPostStub = sinon.stub(axios, 'post').resolves({ data: {} });
            const jwtClientAuthorizeStub = sinon.stub(google.auth.JWT.prototype, 'authorize').callsFake((callback) => {
                callback(null, { access_token: 'fake_token' });
            });

            request(process.env.BASE_URL)
                .post('/notification/send')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    title: 'Notification Title',
                    description: 'Notification description',
                    organizationId: 'organization-id',
                    allOrgChildren: true
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    ChildOrganizationMapping.query.restore();
                    Child.batchGet.restore();
                    Notification.batchPut.restore();
                    axiosPostStub.restore();
                    jwtClientAuthorizeStub.restore();
                    done();
                });
        });

        it('As a user I should be able to send notification to all participants of the event passed', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 3,
                children: ['child1'],
                associatedOrganizations: [{ organizationId: 'organization-id', role: 'super admin' }]
            });

            sinon.stub(EventSignup, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([
                            { childId: 'child1', paymentDetails: { paymentStatus: 'approved' } },
                            { childId: 'child2', paymentDetails: { paymentStatus: 'payment-initiated' } }
                        ])
                    })
                })
            });

            sinon.stub(Event, 'get').resolves({ id: 'event-id', details: {
                startDateTime: '1709181000000', endDateTime: '1709181000000' }
            });

            sinon.stub(Child, 'batchGet').resolves([{ id: 'child-id', guardians: ['user-id1', 'user-id2'] }]);
            sinon.stub(Notification, 'batchPut').resolves();

            const axiosPostStub = sinon.stub(axios, 'post').resolves({ data: {} });
            const jwtClientAuthorizeStub = sinon.stub(google.auth.JWT.prototype, 'authorize').callsFake((callback) => {
                callback(null, { access_token: 'fake_token' });
            });

            request(process.env.BASE_URL)
                .post('/notification/send')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    description: 'Notification description',
                    organizationId: 'organization-id',
                    allParticipants: true,
                    eventId: 'event-id'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    EventSignup.query.restore();
                    Child.batchGet.restore();
                    Notification.batchPut.restore();
                    axiosPostStub.restore();
                    jwtClientAuthorizeStub.restore();
                    done();
                });
        });

        it('As a user I should be able to send notification to all the children passed', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 3,
                children: ['child1'],
                associatedOrganizations: [{ organizationId: 'organization-id', role: 'super admin' }]
            });

            sinon.stub(Child, 'batchGet').resolves([{ id: 'child-id', guardians: ['user-id1', 'user-id2'] }]);
            sinon.stub(Notification, 'batchPut').resolves();

            const axiosPostStub = sinon.stub(axios, 'post').resolves({ data: {} });
            const jwtClientAuthorizeStub = sinon.stub(google.auth.JWT.prototype, 'authorize').callsFake((callback) => {
                callback(null, { access_token: 'fake_token' });
            });

            request(process.env.BASE_URL)
                .post('/notification/send')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    description: 'Notification description',
                    organizationId: 'organization-id',
                    children: ['child-id1', 'child-id2']
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.batchGet.restore();
                    Notification.batchPut.restore();
                    axiosPostStub.restore();
                    jwtClientAuthorizeStub.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Send Notification via topic', () => {
    try {
        it('As a user I should be able to send notification to the topic', (done) => {
            const axiosPostStub = sinon.stub(axios, 'post').resolves({ data: {} });
            const jwtClientAuthorizeStub = sinon.stub(google.auth.JWT.prototype, 'authorize').callsFake((callback) => {
                callback(null, { access_token: 'fake_token' });
            });

            request(process.env.BASE_URL)
                .post('/notification/notify')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    topic: 'childId-fc19a407-321f-447f-8e46-adsf5455',
                    data: {
                        childId: 'childId',
                        route: 'childDetails',
                        activeTab: 'requestedBy'
                    },
                    notification: {
                        title: 'Title',
                        body: 'Description'
                    },
                    android: { notification: {} },
                    apns: { payload: { aps: [Object] } }
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    axiosPostStub.restore();
                    jwtClientAuthorizeStub.restore();
                    done();
                });
        });

        it('As a user I should get error when sending notification to the topic', (done) => {
            request(process.env.BASE_URL)
                .post('/notification/notify')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    topic: 'childId-fc19a407-321f-447f-8e46-adsf5455',
                    data: {
                        childId: 'childId',
                        route: 'childDetails',
                        activeTab: 'requestedBy'
                    },
                    notification: {
                        title: 'Title',
                        body: 'Description'
                    },
                    android: { notification: {} },
                    apns: { payload: { aps: [Object] } }
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
