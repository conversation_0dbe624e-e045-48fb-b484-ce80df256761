const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const sinon = require('sinon');
const User = require('../../../models/user.model');
const Event = require('../../../models/event.model');
const TestCase = require('./testcaseComment');
const jwt = require('jsonwebtoken');
const RedisUtil = require('../../../util/redisUtil');
const Utils = require('../../../util/utilFunctions');
const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
/* User Token */
const user = {
    id: '95285616-3769-4b2e-b36b-898597b8146e',
    email: '<EMAIL>'
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'testingjwt', tokenOptionalInfo)
};

const addComment = {
    eventId: '95285616-3769-4b2e-b36b-898597b8146f',
    childId: '95285616-3769-4b2e-b36b-898597b81461',
    message: 'my first comment'
};

Utils.addCommonReqTokenForHMac(request);
describe('Add Comment', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.addComment.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({
                    status: 'active',
                    isVerified: 1,
                    id: '95213616-3769-4b2e-b36b-898597b8146f',
                    isDeleted: 0,
                    role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                    associatedOrganizations: [
                        {
                            organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                            role: 'org super admin'
                        }
                    ]
                });
                request(process.env.BASE_URL)
                    .post('/comment')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should validate that event exists or not', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP, id: '95285616-3769-411e-b36b-898597b8146e'
            });
            const eventGetStub = sinon.stub(Event, 'get');
            eventGetStub.returns({ isDeleted: 1, details: { endDateTime: new Date() } });
            request(process.env.BASE_URL)
                .post('/comment')
                .set({ Authorization: requestPayloadUser.token })
                .send(addComment)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 404);
                    eventGetStub.restore();
                    done();
                });
        });

        it('As a user I should comment on event', (done) => {
            const currentDate = new Date();
            currentDate.setDate(currentDate.getDate() + 10);
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP, id: '95285616-3769-411e-b36b-898597b8146e'
            });
            const eventGetStub = sinon.stub(Event, 'get');
            eventGetStub.returns({ isDeleted: 0, details: { endDateTime: currentDate }, save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .post('/comment')
                .set({ Authorization: requestPayloadUser.token })
                .send(addComment)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('List Comment', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should validate that event exists or not', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP, id: '95285616-3769-411e-b36b-898597b8146e'
            });
            request(process.env.BASE_URL)
                .get('/comment/list?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should list comment', (done) => {
            const currentDate = new Date();
            currentDate.setDate(currentDate.getDate() + 10);
            const comments = [];
            for (let i = 0; i < 26; i++) {
                comments.push(i % 2 === 0 ? JSON.stringify({
                    message: `my ${i} comment`,
                    id: `${i}`,
                    createdAt: new Date(),
                    firstName: 'Test',
                    lastName: 'User',
                    associatedColorOrImage: '#2772ED'
                }) : JSON.stringify(`${i}`));
            }
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP, id: '95285616-3769-411e-b36b-898597b8146e'
            });
            const redisUtilStub = sinon.stub(RedisUtil, 'getMembersByScore');
            redisUtilStub.returns(comments);
            request(process.env.BASE_URL)
                .get('/comment/list?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Delete Comment', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should validate that event exists or not', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP, id: '95285616-3769-411e-b36b-898597b8146e'
            });
            const eventGetStub = sinon.stub(Event, 'get');
            eventGetStub.returns({ isDeleted: 1, details: { endDateTime: new Date() } });
            request(process.env.BASE_URL)
                .delete('/comment?eventId=84b3c49a-2ca6-454c-9be1-405cfff40d33&commentId=e68ec38a-89e0-4966-baee-9a5d4df682f1&childId=a4972df2-43eb-45b8-82ce-262d929d073f')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 404);
                    eventGetStub.restore();
                    done();
                });
        });

        it('As a user I should delete comment', (done) => {
            const currentDate = new Date();
            currentDate.setDate(currentDate.getDate() + 10);
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP, id: '95285616-3769-411e-b36b-898597b8146e'
            });
            const eventGetStub = sinon.stub(Event, 'get');
            eventGetStub.returns({
                isDeleted: 0, details: { endDateTime: currentDate }, save: () => Promise.resolve(), comments: [
                    {
                        isDeleted: 0, id: 'e68ec38a-89e0-4966-baee-9a5d4df682f1', parentId: '95285616-3769-411e-b36b-898597b8146e',
                        childId: 'a4972df2-43eb-45b8-82ce-262d929d073f'
                    }
                ]
            });
            request(process.env.BASE_URL)
                .delete('/comment?eventId=84b3c49a-2ca6-454c-9be1-405cfff40d33&commentId=e68ec38a-89e0-4966-baee-9a5d4df682f1&childId=a4972df2-43eb-45b8-82ce-262d929d073f')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    eventGetStub.restore();
                    done();
                });
        });

        it('As a user I should validate logged in user can delete their comment', (done) => {
            const currentDate = new Date();
            currentDate.setDate(currentDate.getDate() + 10);
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP, id: '95285616-3769-411e-b36b-898597b8146e'
            });
            const eventGetStub = sinon.stub(Event, 'get');
            eventGetStub.returns({
                isDeleted: 0, details: { endDateTime: currentDate }, save: () => Promise.resolve(), comments: [
                    {
                        isDeleted: 0, id: 'e68ec38a-89e0-4966-baee-9a5d4df682f1',
                        parentId: '95285616-3769-411e-b36b-898597b8146e', childId: 'a4972df2-43eb-45b8-82ce-262d929d073f'
                    }
                ]
            });
            request(process.env.BASE_URL)
                .delete('/comment?eventId=84b3c49a-2ca6-454c-9be1-405cfff40d33&commentId=e68ec38a-89e0-4966-baee-9a5d4df682f1&childId=b4972df2-43eb-45b8-82ce-262d929d073f')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 403);
                    eventGetStub.restore();
                    done();
                });
        });

        it('As a user I should validate comment exists', (done) => {
            const currentDate = new Date();
            currentDate.setDate(currentDate.getDate() + 10);
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP, id: '95285616-3769-411e-b36b-898597b8146e'
            });
            const eventGetStub = sinon.stub(Event, 'get');
            eventGetStub.returns({
                isDeleted: 0, details: { endDateTime: currentDate }, save: () => Promise.resolve(), comments: [
                    {
                        isDeleted: 1, id: 'e68ec38a-89e0-4966-baee-9a5d4df682f1',
                        parentId: '95285616-3769-411e-b36b-898597b8146e', childId: 'a4972df2-43eb-45b8-82ce-262d929d073f'
                    }
                ]
            });
            request(process.env.BASE_URL)
                .delete('/comment?eventId=84b3c49a-2ca6-454c-9be1-405cfff40d33&commentId=e68ec38a-89e0-4966-baee-9a5d4df682f1&childId=b4972df2-43eb-45b8-82ce-262d929d073f')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 404);
                    eventGetStub.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
