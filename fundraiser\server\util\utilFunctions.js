/* eslint-disable max-len */
const HTTPStatus = require('../util/http-status');
const crypto = require('crypto');
const secretKey = process.env.HMAC_SECRET_KEY;

/**
 * This class reprasents common utilities for application
 */
class Utils {
    static errorResponse () {
        return JSON.parse(
            JSON.stringify({
                status: 0,
                data: {},
                message: ''
            })
        );
    }

    static successResponse () {
        return JSON.parse(
            JSON.stringify({
                status: 1,
                data: {},
                message: ''
            })
        );
    }

    /**
   * This function is being used to add pagination for user table
   * @auther Growexx
   * @param {string} error Error Message
   * @param {Object} data Object to send in response
   * @param {Object} res Response Object
   * @param {string} successMessage success message
   * @param {Object} additionalData additional data outside of data object in response
   * @param {string} successMessageVars
   * @since 01/03/2021
   */
    static sendResponse (error, data, res, successMessage, successMessageVars) {
        let responseObject;

        if (error) {
            let status;
            responseObject = Utils.errorResponse();
            if (typeof error === 'object') {
                responseObject.message = error.message
                    ? error.message
                    : res.__('ERROR_MSG');
                status = error.statusCode ? error.statusCode : HTTPStatus.BAD_REQUEST;
            } else {
                responseObject.message = res.__(error);
                status = HTTPStatus.BAD_REQUEST;
            }

            responseObject.data = error.data;
            res.status(status).send(responseObject);
        } else {
            responseObject = Utils.successResponse();
            responseObject.message = successMessageVars
                ? res.__.apply('', [successMessage].concat(successMessageVars))
                : successMessage;
            responseObject.data = data;
            res.status(HTTPStatus.OK).send(responseObject);
        }
    }

    static generateHmac (url) {
        const currentDate = MOMENT().utc().format('MMYYYYDD'); // 03202426
        const reqURL = url; // /feed/path
        const message = currentDate + reqURL;
        return crypto.createHmac('sha256', secretKey).update(message).digest('hex');
    }

    /**
   * This function is being used to overwrite request function for adding reqtoken
   * @auther Growexx
   * @param {import('supertest')} request
   * @since 28/03/2024
   */
    static addCommonReqTokenForHMac (request) {
        const originalEnd = request.Test.prototype.end;

        request.Test.prototype.end = function (callback) {
            const currentParsedUrl = new URL(this.url);
            this.set('reqtoken', Utils.generateHmac(`${currentParsedUrl.pathname}`));
            return originalEnd.call(this, callback);
        };
    }

    /**
     * Checks if the child has a membership with the specified organization.
     * <AUTHOR>
     * @since 29/07/2024
     * @param {string} organizationId - The ID of the organization.
     * @param {Object} child - The child object.
     * @param {string} eventStartDate - The start date of the event.
     * @param {boolean} isGuestSignup - Whether the signup is for a guest.
     * @returns {boolean} - Returns true if the child has a membership with the specified organization, false otherwise.
    */
    static getMembershipsOfChild ({ organizationId, child, eventStartDate, isGuestSignup }) {
        if (isGuestSignup) {
            return null;
        }
        const activeMembershipsForOrg = child.membershipsPurchased?.filter(
            (membership) =>
                membership.organizationId === organizationId &&
            MOMENT(membership.endDate).isAfter(MOMENT().utc()) &&
            MOMENT(membership.startDate).isBefore(eventStartDate)
        );
        if (activeMembershipsForOrg?.length) {
            return activeMembershipsForOrg[0];
        } else {
            return null;
        }
    }

    /**
     * checks if the string is empty
     * @param {*} str
     * @returns
     */
    static isEmpty (str) {
        if (str === 'undefined') {
            return true;
        }
        if (str === undefined) {
            return true;
        }
        if (!str) {
            return true;
        }
        if (str.trim().length === 0) {
            return true;
        }
        return false;
    }
}

module.exports = Utils;
