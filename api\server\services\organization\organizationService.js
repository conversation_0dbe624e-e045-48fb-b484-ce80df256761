const generatePassword = require('generate-password');
const OrganizationValidator = require('./organizationValidator');
const Cognito = require('../../util/cognito');
const User = require('../../models/user.model');
const Event = require('../../models/event.model');
const Organization = require('../../models/organization.model');
const Utils = require('../../util/utilFunctions');
const OrganizationMember = require('../../models/organizationMember.model');
const Stripe = require('../../util/Stripe');
const Validation = require('../../util/validation');
const AwsOpenSearchService = require('../../util/opensearch');
const childModel = require('../../models/child.model');
const ChildOrganizationMapping = require('../../models/childOrganizationMapping.model');
const UploadService = require('../../util/uploadService');
const { v4: uuidv4 } = require('uuid');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

/**
 * Class represents services for organisation.
 */
class OrganizationService {
    /**
     * @desc This function is being used to add organization admin by super admin
     * <AUTHOR>
     * @since 07/11/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {Object} loggedInUser loggedInUser
     * @param {Object} res Response
     */
    static async addOrganization (req, loggedInUser, locale) {
        this.parseRequest(req);
        const orgDetails = req.body;
        const Validator = new OrganizationValidator(
            orgDetails,
            locale,
            req.query
        );
        Validator.validate();
        orgDetails.email = orgDetails.email.toLowerCase();
        orgDetails.firstName = orgDetails.adminFirstName;
        orgDetails.lastName = orgDetails.adminLastName;

        const org = await this.orgExists(orgDetails);

        if (!org.count) {
            await this.createAndSaveOrganization(orgDetails, loggedInUser);
            return;
        }
        throw {
            message: MESSAGES.ORG_EXISTS,
            statusCode: 400
        };
    }

    static async createAndSaveOrganization (orgDetails, loggedInUser) {
        const {
            category,
            orgName,
            address,
            country,
            state,
            city,
            zipCode,
            email,
            parentOrganization,
            parentOrganizationName,
            paymentInstructions,
            allowedPaymentType,
            paymentDetails,
            platformFee,
            platformFeeCoveredBy,
            minPlatformFeeAmount,
            bannerImageName,
            orgId: organizationId
        } = orgDetails;
        const organizationIdsForSuperAdmin = [];
        const orgId = organizationId || uuidv4();

        const orgObj = {
            id: orgId,
            name: orgName,
            createdBy: loggedInUser.id,
            paymentInstructions,
            allowedPaymentType,
            paymentDetails,
            category,
            address,
            country,
            state,
            city,
            zipCode,
            email,
            platformFee,
            platformFeeCoveredBy,
            minPlatformFeeAmount,
            parentOrganization,
            parentOrganizationName
        };

        if (category !== CONSTANTS.CATEGORIES.SCHOOL && category !== CONSTANTS.CATEGORIES.CLUB) {
            orgObj.parentOrganization = parentOrganization;
            orgObj.parentOrganizationName = parentOrganizationName;
        }

        if (bannerImageName) {
            orgObj.logo = bannerImageName;
        }

        if (category === CONSTANTS.CATEGORIES.SCHOOL) {
            const pto = {
                name: `${orgName} ${CONSTANTS.CATEGORIES.PTO}`,
                createdBy: loggedInUser.id,
                category: CONSTANTS.CATEGORIES.PTO,
                parentOrganization: orgId,
                parentOrganizationName: orgName,
                paymentInstructions: {
                    cashInstruction: MESSAGES.CASH_INSTRUCTION
                },
                address,
                country,
                state,
                city,
                zipCode,
                email
            };
            if (bannerImageName) {
                pto.logo = bannerImageName;
            }
            const createdPTO = await Organization.create(pto);
            organizationIdsForSuperAdmin.push(createdPTO.id);
            this.insertOrganizationInOpenSearch(createdPTO);

            await this.addOrUpdateUserToOrg({
                orgId: createdPTO.id,
                user: loggedInUser,
                newOrg: true,
                orgMembers: null,
                role: CONSTANTS.ROLE.ORG_SUPER_ADMIN,
                organization: createdPTO,
                orgDetails
            });
            orgObj.associatedOrganizations = [createdPTO.id];
        }

        const organization = await Organization.create(orgObj);

        organizationIdsForSuperAdmin.push(organization.id);
        this.insertOrganizationInOpenSearch(organization);

        await this.addOrUpdateUserToOrg({
            orgId: organization.id,
            user: loggedInUser,
            newOrg: true,
            orgMembers: null,
            role: CONSTANTS.ROLE.ORG_SUPER_ADMIN,
            orgDetails,
            organization
        });
        await this.addOrgToSuperAdmin(organizationIdsForSuperAdmin);
        await this.addAssociatedOrganization(parentOrganization, orgId, undefined);
    }

    /**
     * @desc This function is being used to upload child's profile picture
     * @param {Object} file File from Request
     * @param {Object} loggedInUser Logged in user
     * @param {String} childId Child ID
     * @returns {String} File name
    */
    static async uploadOrgLogo (file, orgId) {
        let fileName;
        if (file) {
            fileName = `${process.env.NODE_ENV}-organization-logo/${orgId}`;
            await UploadService.uploadFile(file, fileName, true);
        }

        return fileName;
    }

    static async getSuperAdminUser () {
        return await User.query('accessLevel').eq(CONSTANTS.ACCESS_LEVEL.ROOT).using('accessLevel-index').exec();
    }

    static async addOrgToSuperAdmin (organizationIds) {
        const superAdminUsers = await this.getSuperAdminUser();
        for (const superAdminUser of superAdminUsers) {
            for (const orgId of organizationIds) {
                const org = superAdminUser.associatedOrganizations.find(org => org.organizationId === orgId);
                if (!org) {
                    superAdminUser.associatedOrganizations.push({ organizationId: orgId, role: CONSTANTS.ROLE.ORG_SUPER_ADMIN });
                }
                const orgMember = await OrganizationMember.get(orgId);
                const user = orgMember.users.find(user => user.id === superAdminUser.id);
                if (!user) {
                    orgMember.users.push({
                        id: superAdminUser.id,
                        email: superAdminUser.email,
                        associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN,
                        status: CONSTANTS.STATUS.ACTIVE,
                        firstName: superAdminUser.firstName,
                        lastName: superAdminUser.lastName
                    });
                } else {
                    user.associatedOrgRole = CONSTANTS.ROLE.ORG_SUPER_ADMIN;
                }
                await orgMember.save();
            }
            await superAdminUser.save();
        }
    }

    static async filterOrgForSuperAdmin (orgId) {
        const superAdminUsers = await this.getSuperAdminUser();
        for (const superAdminUser of superAdminUsers) {
            superAdminUser.associatedOrganizations = superAdminUser.associatedOrganizations.filter(org => org.organizationId !== orgId);
            await superAdminUser.save();
        }
    }

    static async insertOrganizationInOpenSearch (organization) {
        await AwsOpenSearchService.create(CONSTANTS.OPEN_SEARCH.COLLECTION.ORGANIZATION, organization.id, organization);
    }

    static async updateOrganizationInOpenSearch (organization) {
        await AwsOpenSearchService.updateField(CONSTANTS.OPEN_SEARCH.COLLECTION.ORGANIZATION, organization.id, organization);
    }

    /**
     * @desc This function is being used to update organization details
     * <AUTHOR>
     * @since 15/11/2023
     * @param {Object} req Request
     * @param {Object} loggedInUser loggedInUser
     * @param {Object} locale locale passed from request
     */

    static parseRequest ( req ) {
        req.body.platformFee = parseFloat(req.body.platformFee);
        req.body.isDeleted = parseInt(req.body.isDeleted);
        req.body.isEnabled = parseInt(req.body.isEnabled);
        req.body.allowedPaymentType.cash = req.body.allowedPaymentType.cash === 'true';
        req.body.allowedPaymentType.cheque = req.body.allowedPaymentType.cheque === 'true';
        req.body.allowedPaymentType.venmo = req.body.allowedPaymentType.venmo === 'true';
        req.body.allowedPaymentType.stripe = req.body.allowedPaymentType.stripe === 'true';
        req.body.minPlatformFeeAmount = parseFloat(req.body.minPlatformFeeAmount);
    }
    static async updateOrganizationDetails (req, loggedInUser, locale) {
        this.parseRequest(req);
        const Validator = new OrganizationValidator(req.body, locale, req.query);
        Validator.validateUpdateOrganizationDetails();

        const { orgId, parentOrganization } = req.body;
        let organization = await Organization.get({ id: orgId });
        if (!organization || organization.isDeleted) {
            throw {
                message: MESSAGES.ORGANIZATION_DOES_NOT_EXIST,
                statusCode: 404
            };
        }
        if (organization.category !== CONSTANTS.CATEGORIES.SCHOOL
            && organization.category !== CONSTANTS.CATEGORIES.CLUB) {
            const Validator = new Validation(locale);
            Validator.field(req.body.parentOrganization, 'Parent Organization Id');
            Validator.uuid(req.body.parentOrganization, 'Parent Organization Id');
            Validator.field(req.body.parentOrganizationName, 'Parent Organization Name');
        }

        await this.validateOrgUserRole(loggedInUser, orgId);

        organization = await organization.populate();

        await this.validateStripeAllowed(req.body, loggedInUser, locale);

        if (req.body.allowedPaymentType.stripe && organization.paymentDetails.stripeConnectAccountId.length === 0
        ) {
            const account = await Stripe.createAccount(stripe);
            const accountLink = await Stripe.createOnboardingLink(stripe, account);
            await this.updateOrganization(organization, req.body, loggedInUser, account.id);
            const orgManager = await OrganizationMember.get({ organizationId: orgId });
            const associatedOrgAdmin = orgManager.users;
            await Promise.all(associatedOrgAdmin.map(async (orgAdmin) => {
                if (orgAdmin.associatedOrgRole === CONSTANTS.ROLE.ORG_SUPER_ADMIN) {
                    await Utils.sendOrgToMail({ url: accountLink.url, email: orgAdmin.email },
                        'stripeLoginUrl.html', `Stripe Onboarding URL for ${CONSTANTS.APP_NAME}`);
                }
            }));
            return;
        }
        if (req.body.allowedPaymentType.stripe && organization.paymentDetails.stripeOnboardingStatus === 'inactive') {
            const accountLink = await Stripe.createOnboardingLink(stripe, { id: organization.paymentDetails.stripeConnectAccountId });
            const orgManager = await OrganizationMember.get({ organizationId: orgId });
            const associatedOrgAdmin = orgManager.users;
            await Promise.all(associatedOrgAdmin.map(async (orgAdmin) => {
                if (orgAdmin.associatedOrgRole === CONSTANTS.ROLE.ORG_SUPER_ADMIN) {
                    await Utils.sendOrgToMail({ url: accountLink.url, email: orgAdmin.email },
                        'stripeLoginUrl.html', `Stripe Onboarding URL for ${CONSTANTS.APP_NAME}`);
                }
            }));
        }

        await this.addAssociatedOrganization(parentOrganization, orgId, organization.parentOrganization);
        await this.updateOrganization(organization, req.body, loggedInUser);
    }

    static async addAssociatedOrganization (parentOrgId, associatedOrgId, oldParentOrgId) {
        if (parentOrgId === oldParentOrgId) {
            return;
        }

        if (oldParentOrgId) {
            const oldParentOrgDetails = await Organization.get({ id: oldParentOrgId });
            if (oldParentOrgDetails) {
                let associatedOrganizations = oldParentOrgDetails.associatedOrganizations ?? [];
                if (associatedOrganizations.includes(associatedOrgId)) {
                    associatedOrganizations = associatedOrganizations.filter(orgId => orgId !== associatedOrgId);
                    oldParentOrgDetails.associatedOrganizations = associatedOrganizations;
                    await oldParentOrgDetails.save();
                }
            }
        }
        const parentOrgDetails = await Organization.get({ id: parentOrgId });

        if (parentOrgDetails) {
            const associatedOrganizations = parentOrgDetails.associatedOrganizations ?? [];
            if (!associatedOrganizations.includes(associatedOrgId)) {
                associatedOrganizations.push(associatedOrgId);
                parentOrgDetails.associatedOrganizations = associatedOrganizations;
                await parentOrgDetails.save();
            }
        }
    }

    static async validateStripeAllowed (reqObj, loggedInUser) {
        if (!reqObj.stripeAllowed) {
            return;
        }

        if (reqObj.platformFee) {
            if (isNaN(reqObj.platformFee) || reqObj.platformFee < 0 || reqObj.platformFee > 100) {
                throw {
                    message: MESSAGES.INVALID_PLATFORM_FEE,
                    statusCode: 400
                };
            }

            if (loggedInUser.accessLevel !== CONSTANTS.ACCESS_LEVEL.ROOT) {
                throw {
                    message: MESSAGES.CANNOT_MODIFY_PLATFORM_FEE,
                    statusCode: 403
                };
            }

            if (!reqObj.platformFeeCoveredBy) {
                throw {
                    message: MESSAGES.MISSING_PLATFORM_FEE_COVERED_BY,
                    statusCode: 400
                };
            }

            if (!['organization', 'parent', 'optional'].includes(reqObj.platformFeeCoveredBy)) {
                throw {
                    message: MESSAGES.INVALID_PLATFORM_FEE_COVERED_BY,
                    statusCode: 400
                };
            }
        }
    }

    /**
     * @desc This function is being used to validate user role in organization
     * <AUTHOR>
     * @since 12/02/2024
     * @param {Object} loggedInUser logged in user
     * @param {String} orgId organization id
     */
    static async validateOrgUserRole (loggedInUser, orgId) {
        if (loggedInUser.accessLevel === CONSTANTS.ACCESS_LEVEL.ORG_APP) {
            const allowedRoles = [CONSTANTS.ROLE.ORG_SUPER_ADMIN, CONSTANTS.ROLE.ORG_ADMIN];
            const org = loggedInUser.associatedOrganizations.find(org => org.organizationId === orgId);

            if (!org) {
                throw {
                    message: MESSAGES.ORGANIZATION_DOES_NOT_EXIST,
                    statusCode: 400
                };
            }

            if (!allowedRoles.includes(org.role)) {
                throw {
                    message: MESSAGES.ACCESS_DENIED,
                    statusCode: 403
                };
            }
        }
    }

    /**
     * @desc This function is being used to update organisation
     * <AUTHOR>
     * @since 16/11/2023
     * @param {Object} organization organization db object
     * @param {Object} reqObj reqObj
     * @param {Object} loggedInUser logged in user
     */
    static async updateOrganization (organization, reqObj, loggedInUser, stripeId) {
        const { orgName, address, country, state, city, zipCode, parentOrganization, parentOrganizationName, paymentInstructions,
            paymentDetails, platformFee, platformFeeCoveredBy, minPlatformFeeAmount, allowedPaymentType, bannerImageName } = reqObj;
        organization.name = orgName;
        organization.address = address;
        organization.city = city;
        organization.state = state;
        organization.country = country;
        organization.zipCode = zipCode;
        organization.updatedBy = loggedInUser.id;
        organization.allowedPaymentType.cash = allowedPaymentType.cash;
        organization.allowedPaymentType.cheque = allowedPaymentType.cheque;
        organization.allowedPaymentType.venmo = allowedPaymentType.venmo;
        organization.allowedPaymentType.stripe = allowedPaymentType.stripe;
        if (bannerImageName !== undefined) {
            organization.logo = bannerImageName;
        }

        if (allowedPaymentType.venmo) {
            organization.paymentDetails.venmoPaymentURL = paymentDetails.venmoPaymentURL;
        }

        if (stripeId) {
            organization.paymentDetails.stripeConnectAccountId = stripeId;
        }
        if (paymentDetails.venmoPaymentURL) {
            organization.paymentDetails.venmoPaymentURL = paymentDetails.venmoPaymentURL;
        }
        if (this.hasValidPaymentInstructions(paymentInstructions)) {
            organization.paymentInstructions = {
                cashInstruction: paymentInstructions.cashInstruction ? paymentInstructions.cashInstruction : '',
                chequeInstruction: paymentInstructions.chequeInstruction ? paymentInstructions.chequeInstruction : '',
                venmoInstruction: paymentInstructions.venmoInstruction ? paymentInstructions.venmoInstruction : ''
            };
        }
        if (parentOrganization) {
            organization.parentOrganization = parentOrganization;
            organization.parentOrganizationName = parentOrganizationName;
        }
        if (platformFee >= 0) {
            organization.platformFee = platformFee;
            organization.platformFeeCoveredBy = platformFeeCoveredBy;
        }
        if (minPlatformFeeAmount >= 0) {
            organization.minPlatformFeeAmount = minPlatformFeeAmount;
        }
        const updatedOrg = await organization.save();

        this.updateOrganizationInOpenSearch(updatedOrg);
    }

    static hasValidPaymentInstructions (paymentInstructions) {
        return paymentInstructions.cashInstruction || paymentInstructions.chequeInstruction || paymentInstructions.venmoInstruction;
    }

    /**
     * @desc This function is being used to check organization exists
     * <AUTHOR>
     * @since 08/11/2023
     * @param {Object} reqObj reqObj
     */
    static async orgExists (reqObj) {
        return await Organization.scan()
            .where('name').eq(reqObj.orgName)
            .where('zipCode').eq(reqObj.zipCode)
            .exec();
    }

    /**
     * @desc This function is being used to check email already exists for sign-up of organization
     * <AUTHOR>
     * @since 07/11/2023
     * @param {Object} reqObj reqObj
     */
    static async userExists (reqObj) {
        return await User.query('email').eq(reqObj.email).exec();
    }

    /**
     * @desc This function is being used to save or update organization details
     * <AUTHOR>
     * @since 07/11/2023
     * @param {Object} reqObj Request
     * @param {Object} reqObj.body RequestBody
     * @param {Object} reqObj reqObj
     * @param {String} userType userType
     * @param {String} id id
     * @param {String} token token
     * @param {String} refreshToken refreshToken
     * @param {String} orgId orgId
     */
    static async saveOrUpdateUser (reqObj, { id, token, refreshToken }, orgId, superAdminId,
        userType = CONSTANTS.ACCESS_LEVEL.ORG_APP, role = CONSTANTS.ROLE.ORG_SUPER_ADMIN) {
        const { phoneNumber, firstName, lastName, email, countryCode } = reqObj;
        const obj = {
            firstName: firstName.trim(),
            lastName: lastName.trim(),
            email: email.trim(),
            countryCode: phoneNumber && countryCode.trim(),
            phoneNumber: phoneNumber && phoneNumber.trim(),
            isVerified: 1,
            status: 'active',
            associatedOrganizations: [{ organizationId: orgId, role: role.trim().toLowerCase() }],
            accessLevel: userType,
            createdBy: superAdminId,
            isFeedsGenerated: false,
            id,
            token,
            refreshToken
        };
        const user = new User(obj);
        return await user.save();
    }

    /**
     * @desc This function is being used to get list of organizations
     * <AUTHOR>
     * @since 08/11/2023
     */
    static async getOrganizationList (req, locale) {
        if (req.query.category) {
            const Validator = new OrganizationValidator(req.body, locale, req.query);
            Validator.verifyCategoryParams();
            const { category } = req.query;
            const organizationAttributes = ['id', 'name', 'country', 'zipCode',
                'address', 'city', 'state', 'category', 'isDeleted', 'isEnabled', 'paymentDetails', 'parentOrganization'];

            let organizations = await Organization.query('category')
                .eq(category).using('category-index').attributes(organizationAttributes).exec();
            if (organizations.length === 0) {
                return [];
            }

            organizations = await organizations.populate();
            return organizations;
        } else {
            return await Organization.scan().exec();
        }
    }

    static getOrganizationAttributes () {
        return [
            'id',
            'name',
            'country',
            'zipCode',
            'address',
            'city',
            'state',
            'category',
            'isEnabled',
            'paymentDetails',
            'parentOrganization',
            'parentOrganizationName',
            'paymentInstructions',
            'allowedPaymentType',
            'platformFee',
            'logo',
            'organizationType',
            'platformFeeCoveredBy',
            'isDeleted',
            'minPlatformFeeAmount'
        ];
    }

    /**
     * @desc This function is being used to get organization details
     * <AUTHOR>
     * @since 09/11/2023
     * @param {Object} req Request
     * @param {Object} locale Locale passed from request
     */
    static async getOrganizationDetails (req, loggedInUser, locale) {
        const Validator = new OrganizationValidator(req.body, locale, req.query);
        Validator.validateOrganizationId();

        const { orgId } = req.query;

        await this.validateOrgUserRole(loggedInUser, orgId);

        let orgDetails = await Organization.get({ id: orgId }, {
            attributes: this.getOrganizationAttributes()
        });
        if (orgDetails === undefined) {
            return {};
        }
        orgDetails = await orgDetails.populate();

        if (orgDetails.logo) {
            orgDetails.logo = await UploadService.getSignedUrl(orgDetails.logo);
        }

        if (loggedInUser.accessLevel === CONSTANTS.ACCESS_LEVEL.ORG_APP) {
            return orgDetails;
        } else {
            return {
                ...orgDetails,
                canBeDisabled: await this.canOrganizationBeDisabled(orgId),
                canBeDeleted: await this.canOrganizationBeDeleted(orgId)
            };
        }
    }

    /**
     * @desc This function is being used to update organization status to active or inactive
     * <AUTHOR>
     * @since 10/11/2023
     * @param {Object} req Request
     * @param {Object} locale Locale passed from request
     */
    static async updateOrganizationStatus (req, locale) {
        const Validator = new OrganizationValidator(req.body, locale, req.query);
        Validator.validateUpdateOrganizationStatus();

        const { orgId, status } = req.body;

        if (status === CONSTANTS.STATUS.ORGANIZATION_DISABLED) {
            const canBeDisabled = await this.canOrganizationBeDisabled(orgId);
            if (!canBeDisabled) {
                throw {
                    message: MESSAGES.ORGANIZATION_STATUS_CHANGE_ERROR,
                    statusCode: 400
                };
            }
        }
        await Organization.update({ id: orgId }, {
            $SET: {
                isEnabled: status
            }
        });

        return;
    }

    /**
     * @desc This function is being used to delete organization
     * <AUTHOR>
     * @since 10/11/2023
     * @param {Object} req Request
     * @param {Object} locale Locale passed from request
     */
    static async deleteOrganization (req, locale) {
        const Validator = new OrganizationValidator(req.body, locale, req.query);
        Validator.validateOrganizationId();

        const { orgId } = req.query;

        const canBeDeleted = await this.canOrganizationBeDeleted(orgId);
        if (canBeDeleted) {
            await Organization.update({ id: orgId }, {
                $SET: {
                    isEnabled: CONSTANTS.STATUS.ORGANIZATION_DISABLED,
                    isDeleted: CONSTANTS.STATUS.ORGANIZATION_DELETED
                }
            });
            const orgDetails = await Organization.get({ id: orgId });
            const parentOrgId = orgDetails.parentOrganization;
            const parentOrgDetails = await Organization.get({ id: parentOrgId });
            if (parentOrgDetails) {
                let associatedOrganizations = parentOrgDetails.associatedOrganizations ?? [];
                associatedOrganizations = parentOrgDetails.associatedOrganizations.filter(id => id !== orgId);
                parentOrgDetails.associatedOrganizations = associatedOrganizations;
                const archivedAssociatedOrganizations = parentOrgDetails.archivedAssociatedOrganizations ?? [];
                archivedAssociatedOrganizations.push(orgId);
                parentOrgDetails.archivedAssociatedOrganizations = archivedAssociatedOrganizations;
                await parentOrgDetails.save();
            }
            this.deleteOrganizationInOpenSearch(orgId);
            await this.filterOrgForSuperAdmin(orgId);
        } else {
            throw {
                message: MESSAGES.DELETE_ORGANIZATION_ERROR,
                statusCode: 400
            };
        }
        return;
    }

    static async deleteOrganizationInOpenSearch (orgId) {
        await AwsOpenSearchService.delete(CONSTANTS.OPEN_SEARCH.COLLECTION.ORGANIZATION, orgId);
    }

    /**
     * @desc This function is being used to check if we can disable the organization
     * <AUTHOR>
     * @since 10/11/2023
     * @param {String} orgId organization id
     */
    static async canOrganizationBeDisabled (orgId) {
        const publishedEvents = await Event.query('organizationId').eq(orgId).where('status').eq(CONSTANTS.STATUS.PUBLISHED).exec();
        if (publishedEvents && publishedEvents.length > 0) {
            return 0;
        }
        return 1;
    }

    /**
     * @desc This function is being used to check if we can delete the organization
     * <AUTHOR>
     * @since 10/11/2023
     * @param {String} orgId organization id
     */
    static async canOrganizationBeDeleted (orgId) {
        const canOrganizationBeDisabled = await this.canOrganizationBeDisabled(orgId);
        if (!canOrganizationBeDisabled) {
            return 0;
        }
        // TO DO - check if students are subscribed to this ORGANIZATION after event signup story is completed
        return 1;
    }

    /**
     * @desc This function is being used to get organization users list
     * <AUTHOR>
     * @since 14/12/2023
     * @param {String} organizationId organization id
     */
    static async getOrganizationUsers (req, user, locale) {
        const Validator = new OrganizationValidator(req.body, locale, req.query);
        Validator.validateOrganizationId();

        const { orgId } = req.query;

        const { orgMembers } = await this.getOrgMembers(orgId, user);
        const superAdmins = await this.getSuperAdminUser();
        orgMembers.users = orgMembers.users.filter(member => !superAdmins.find(admin => admin.id === member.id));
        return this.formatOrgUsers(orgMembers.users, user.id);
    }
    /**
     * @desc This function is being used to get organization search result
     * <AUTHOR>
     * @since 14/12/2023
     * @param {String} organizationId organization id
     */
    static async getOrganizationSearch (req) {
        const { searchValue, page = 1, limit = 100 } = req.query;
        const searchResult = await AwsOpenSearchService.searchOrganization(CONSTANTS.OPEN_SEARCH.COLLECTION.ORGANIZATION, {
            searchValue, page, limit
        });
        return searchResult;
    }

    /**
     * @desc This function is being used to get organization members
     * <AUTHOR>
     * @since 14/12/2023
     * @param {String} organizationId organization id
     */
    static async getOrgMembers (organizationId, user) {
        const orgMembers = await OrganizationMember.get(organizationId);
        if (!orgMembers) {
            throw {
                message: MESSAGES.ORGANIZATION_DOES_NOT_EXIST,
                statusCode: 400
            };
        }

        const orgAssociation = orgMembers.users.find(member => member.id === user.id);
        if (user.accessLevel !== CONSTANTS.ACCESS_LEVEL.ROOT && !orgAssociation) {
            throw {
                message: MESSAGES.ORGANIZATION_DOES_NOT_EXIST,
                statusCode: 400
            };
        }
        if (
            user.accessLevel !== CONSTANTS.ACCESS_LEVEL.ROOT &&
            ([CONSTANTS.ROLE.ORG_SUPER_ADMIN, CONSTANTS.ROLE.ORG_ADMIN].indexOf(orgAssociation.associatedOrgRole) === -1
                || orgAssociation.status !== CONSTANTS.STATUS.ACTIVE)
        ) {
            throw {
                message: MESSAGES.ACCESS_DENIED,
                statusCode: 400
            };
        }
        return { orgMembers, orgAssociation };
    }

    /**
     * @desc This function is being used to add organization users
     * <AUTHOR>
     * @since 14/12/2023
     * @param {Object} req Request
     * @param {Object} user User
     * @param {Object} locale Locale passed from request
     */
    static async addOrganizationUser (req, user, locale) {
        const Validator = new OrganizationValidator(req.body, locale, req.query);
        Validator.validateAddOrganizationUser();
        req.body.email = req.body.email.toLowerCase();
        const { orgId, role, password } = req.body;

        const { orgMembers, orgAssociation } = await this.getOrgMembers(orgId, user);

        const orgUserExists = orgMembers.users.find(member => member.email === req.body.email);
        if (orgUserExists) {
            if (orgUserExists.status === CONSTANTS.STATUS.DELETED) {
                user.accessLevel !== CONSTANTS.ACCESS_LEVEL.ROOT && this.validateRolePassed(
                    orgAssociation.associatedOrgRole,
                    role
                );
                await this.updateUserToActive(orgUserExists.id, orgMembers, role, orgId, password, user);
                return;
            }
            throw {
                message: MESSAGES.ORGANIZATION_USER_EXISTS,
                statusCode: 400
            };
        }

        user.accessLevel !== CONSTANTS.ACCESS_LEVEL.ROOT && this.validateRolePassed(orgAssociation.associatedOrgRole, role);

        await this.addOrUpdateUserToOrg({
            newOrg: false,
            organization: false,
            newPassword: password,
            orgMembers,
            orgId,
            orgDetails: req.body,
            user,
            role
        });
    }

    /** @desc This function is being used to update user status to active
     * @param {String} userId userId
     * @param {Object} orgMembers orgMembers
     * @param {String} role role
    */
    static async updateUserToActive (userId, orgMembers, role, orgId, newPassword, loggedInUser) {
        const updatedOrgUsers = orgMembers.users.map(member =>
            member.id === userId
                ? { ...member, status: CONSTANTS.STATUS.ACTIVE, associatedOrgRole: role.trim().toLowerCase() }
                : member
        );

        orgMembers.users = updatedOrgUsers;

        const userToUpdate = await User.query('id').eq(userId).exec();

        await this.updateUserPasswordAndAddOrg(userToUpdate[0], orgId, role, newPassword, loggedInUser);
        await orgMembers.save();
    }

    /**
     * @desc This function is being used to update organization user
     * <AUTHOR>
     * @since 14/12/2023
     * @param {Object} req Request
     * @param {Object} user User
     * @param {Object} locale Locale passed from request
     */
    static async updateOrganizationUser (req, user, locale) {
        const Validator = new OrganizationValidator(req.body, locale, req.query);
        Validator.validateUpdateOrganizationUser();
        const { orgId, userId, role, status, password, email } = req.body;

        if (userId === user.id) {
            throw {
                message: MESSAGES.ORGANIZATION_USER_CANNOT_BE_UPDATED,
                statusCode: 400
            };
        }

        const { orgMembers, orgAssociation } = await this.getOrgMembers(orgId, user);

        const orgUserExists = orgMembers.users.find(member => member.id === userId);
        const userToUpdate = await User.query('id').eq(userId).exec();

        if (!orgUserExists || userToUpdate.length === 0) {
            throw {
                message: MESSAGES.ORGANIZATION_USER_DOES_NOT_EXIST,
                statusCode: 400
            };
        }

        user.accessLevel !== CONSTANTS.ACCESS_LEVEL.ROOT
            && this.validateRolePassed(orgAssociation.associatedOrgRole, orgUserExists.associatedOrgRole);
        user.accessLevel !== CONSTANTS.ACCESS_LEVEL.ROOT && this.validateRolePassed(orgAssociation.associatedOrgRole, role);

        if (password && email) {
            await Cognito.setCognitoUserPassword(email, password);
        }

        if (status === CONSTANTS.STATUS.DELETED || status === CONSTANTS.STATUS.INACTIVE) {
            userToUpdate[0].associatedOrganizations = userToUpdate[0].associatedOrganizations.filter(org => org.organizationId !== orgId);
        } else {
            const org = userToUpdate[0].associatedOrganizations.find(org => org.organizationId === orgId);
            if (org) {
                Object.assign(org, { ...org, role: role.trim().toLowerCase() });
            } else {
                userToUpdate[0].associatedOrganizations.push({ organizationId: orgId, role: role.trim().toLowerCase() });
            }
        }

        const updatedOrgUsers = orgMembers.users.map(member =>
            member.id === userId
                ? { ...member, associatedOrgRole: role.trim().toLowerCase(), status: status.trim().toLowerCase() }
                : member
        );

        orgMembers.users = updatedOrgUsers;

        await userToUpdate[0].save();
        await orgMembers.save();
    }

    /**
     * @desc This function is being used to delete organization user
     * <AUTHOR>
     * @since 14/12/2023
     * @param {Object} req Request
     * @param {Object} user User
     * @param {Object} locale Locale passed from request
     */
    static async deleteOrganizationUser (req, user, locale) {
        const Validator = new OrganizationValidator(req.body, locale, req.query);
        Validator.validateDeleteOrganizationUser();
        const { orgId, userId } = req.body;

        if (userId === user.id) {
            throw {
                message: MESSAGES.ORGANIZATION_USER_CANNOT_BE_DELETED,
                statusCode: 400
            };
        }

        const { orgMembers, orgAssociation } = await this.getOrgMembers(orgId, user);

        const orgUserExists = orgMembers.users.find(member => member.id === userId);
        const userToDelete = await User.query('id').eq(userId).exec();
        if (!orgUserExists || userToDelete.length === 0) {
            throw {
                message: MESSAGES.ORGANIZATION_USER_DOES_NOT_EXIST,
                statusCode: 400
            };
        }

        user.accessLevel !== CONSTANTS.ACCESS_LEVEL.ROOT
            && this.validateRolePassed(orgAssociation.associatedOrgRole, orgUserExists.associatedOrgRole);

        userToDelete[0].associatedOrganizations = userToDelete[0].associatedOrganizations.filter(org => org.organizationId !== orgId);

        const updatedOrgUsers = orgMembers.users.map(member =>
            member.id === userId
                ? { ...member, status: CONSTANTS.STATUS.DELETED }
                : member
        );

        orgMembers.users = updatedOrgUsers;
        await userToDelete[0].save();
        await orgMembers.save();
    }

    /**
     *  @desc This function is being used to validate role passed
     *  <AUTHOR>
     *  @since 14/12/2023
     *  @param {String} userOrgRole associated user role of org
     *  @param {String} role role passed
    */
    static validateRolePassed (userOrgRole, role) {
        if (userOrgRole === CONSTANTS.ROLE.ORG_ADMIN && role === CONSTANTS.ROLE.ORG_SUPER_ADMIN) {
            throw {
                message: MESSAGES.ROLE_NOT_ALLOWED,
                statusCode: 400
            };
        }
    }

    static async addOrUpdateUserToOrg (params) {
        const { orgDetails, orgId, user, newOrg, orgMembers, role, organization, newPassword } = params;
        const existingUser = await this.userExists(orgDetails);
        const { email, phoneNumber } = orgDetails;
        if (!existingUser.count) {
            let password = generatePassword.generate({
                length: 8,
                numbers: true,
                symbols: true,
                uppercase: true,
                lowercase: true,
                exclude: '$!%*?&{}~`"()|#;,>.<^[]/_-+=:',
                strict: true,
                excludeSimilarCharacters: true
            });
            if (newPassword) {
                password = newPassword;
            }
            orgDetails.password = password;
            const cognitoSignupUser = await Cognito.signup(orgDetails);
            await Cognito.updateConfirmStatus(email);
            await Cognito.updateUserToActive(email, phoneNumber);
            const cognitoUser = await Cognito.login({ email, password });

            const newOrgAdmin = await this.saveOrUpdateUser(
                orgDetails,
                {
                    id: cognitoSignupUser.userSub,
                    token: cognitoUser.AuthenticationResult.IdToken,
                    refreshToken: cognitoUser.AuthenticationResult.RefreshToken
                },
                orgId,
                user.id,
                undefined,
                role
            );
            if (newOrg) {
                await OrganizationMember.create({
                    organizationId: organization.id,
                    users: [{
                        id: newOrgAdmin.id,
                        email: newOrgAdmin.email,
                        firstName: newOrgAdmin.firstName,
                        lastName: newOrgAdmin.lastName,
                        associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN
                    }]
                });
            } else {
                orgMembers.users.push({
                    id: newOrgAdmin.id,
                    email: newOrgAdmin.email,
                    firstName: newOrgAdmin.firstName,
                    lastName: newOrgAdmin.lastName,
                    associatedOrgRole: role.trim().toLowerCase()
                });
                await orgMembers.save();
            }
            await Utils.sendOrgToMail(orgDetails, 'orgPasswordCred.html', `Login Credentials ${CONSTANTS.APP_NAME}`);
        } else {
            const updateUser = await User.get({ id: existingUser.toJSON()[0].id, email });
            await this.updateUserPasswordAndAddOrg(
                updateUser, orgId, role, newPassword, user);
            if (newOrg) {
                await OrganizationMember.create({
                    organizationId: organization.id,
                    users: [{
                        id: updateUser.id,
                        email: updateUser.email,
                        firstName: updateUser.firstName,
                        lastName: updateUser.lastName,
                        associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN
                    }]
                });
            } else {
                orgMembers.users.push({
                    id: updateUser.id,
                    email: updateUser.email,
                    firstName: updateUser.firstName,
                    lastName: updateUser.lastName,
                    associatedOrgRole: role.trim().toLowerCase()
                });
                await orgMembers.save();
            }
        }
    }

    /**
     * @desc This function is being used to update user password and add organization to user
     * @param {Object} updateUser user
     * @param {String} orgId orgId
     * @param {String} role role
     * @param {String} newPassword newPassword
     * @param {Object} loggedInUser loggedInUser
     */
    static async updateUserPasswordAndAddOrg (updateUser, orgId, role, newPassword, loggedInUser) {
        updateUser.provider === CONSTANTS.PROVIDER.EMAIL &&
        newPassword && await Cognito.setCognitoUserPassword(updateUser.email, newPassword);
        if (!updateUser.associatedOrganizations) {
            updateUser.associatedOrganizations = [];
        }
        updateUser.updatedBy = loggedInUser.id;
        updateUser.associatedOrganizations.push({ organizationId: orgId, role: role.trim().toLowerCase() });
        if (updateUser.accessLevel !== CONSTANTS.ACCESS_LEVEL.ROOT) {
            updateUser.accessLevel = CONSTANTS.ACCESS_LEVEL.ORG_APP;
        }
        await updateUser.save();
    }

    /**
     * @desc This function is being used to format organization users
     * <AUTHOR>
     * @since 10/11/2023
     * @param {Array} orgUsers organization users
     */
    static formatOrgUsers (orgUsers, userId) {
        return orgUsers
            .filter(orgUser => orgUser.status !== CONSTANTS.STATUS.DELETED && orgUser.id !== userId)
            .map(orgUser => {
                return {
                    id: orgUser.id,
                    firstName: orgUser.firstName,
                    lastName: orgUser.lastName,
                    email: orgUser.email,
                    associatedOrgRole: orgUser.associatedOrgRole,
                    status: orgUser.status
                };
            });
    }

    /**
     * @desc This function is being used to join organization for multiple child
     * <AUTHOR>
     * @since 03/06/2024
     * @param {Object} req Request
     * @param {Object} user User
     * @param {Object} locale Locale passed from request

     */
    static async addChildrenToOrganization (req, locale) {
        const Validator = new OrganizationValidator(req.body, locale, req.query);
        await Validator.validateChildrenToOrganization();

        const { orgId, addedChildIds, removedChildIds } = req.body;
        if (addedChildIds.length > 0) {
            for (const childId of addedChildIds) {
                const child = await childModel.get({ id: childId });
                if (child && !child.associatedOrganizations.some(org => org === orgId)) {
                    child.associatedOrganizations.push(orgId);
                    await child.save();
                    await ChildOrganizationMapping.create({ childId, organizationId: orgId });
                }
            }
        }

        if (removedChildIds.length > 0) {
            for (const childId of removedChildIds) {
                const child = await childModel.get({ id: childId });
                if (child && child.associatedOrganizations.some(org => org === orgId)) {
                    child.associatedOrganizations = child.associatedOrganizations.filter(org => org !== orgId);
                    await child.save();
                    const childOrgs = await ChildOrganizationMapping
                        .query('organizationId').eq(orgId).using('organizationId-index').where('childId').eq(childId).exec();
                    await Promise.all(childOrgs.map(async childOrg => await childOrg.delete()));
                }
            }
        }
    }

    static async getAssociatedOrganizations (req, locale) {
        const Validator = new OrganizationValidator(req.body, locale, req.query);
        Validator.validateOrganizationId();
        const { orgId } = req.query;
        const organizationAttributes = [
            'id',
            'name',
            'category',
            'isEnabled'
        ];

        let orgDetails = await Organization.get({ id: orgId }, {
            attributes: [...this.getOrganizationAttributes(), 'associatedOrganizations']
        });

        if (orgDetails === undefined) {
            throw {
                message: MESSAGES.ORGANIZATION_DOES_NOT_EXIST,
                statusCode: 400
            };
        }
        orgDetails = await orgDetails.populate();
        let associatedOrganizations = [];

        if ( orgDetails.associatedOrganizations ) {
            associatedOrganizations = await Promise.all(
                (orgDetails.associatedOrganizations || []).map(async (id) =>
                    await Organization.get({ id }, { attributes: organizationAttributes })
                )
            );
        }

        return { ...orgDetails, associatedOrganizations };
    }

    /**
     * @desc This function is being used to validate organization association
     * <AUTHOR>
     * @since 20/12/2024
     * @param {String} organizationId organization id
     */
    static async validateOrganizationAssociation (organizationId, user) {
        const orgMembers = await OrganizationMember.get(organizationId);
        if (!orgMembers) {
            throw {
                message: MESSAGES.UNAUTHORIZED_ACTION_FOR_ORGANIZATION,
                statusCode: 400
            };
        }

        const orgAssociation = orgMembers.users.find(
            (member) => member.id === user.id
        );
        if (!orgAssociation) {
            throw {
                message: MESSAGES.UNAUTHORIZED_ACTION_FOR_ORGANIZATION,
                statusCode: 400
            };
        }
        if (orgAssociation.status !== CONSTANTS.STATUS.ACTIVE) {
            throw {
                message: MESSAGES.UNAUTHORIZED_ACTION_FOR_ORGANIZATION,
                statusCode: 400
            };
        }
        return orgAssociation;
    }

    /**
     * @desc This function is being used to generate presigned url
     * <AUTHOR>
     * @since 20/12/2024
     */
    static async generatePresignedUrl (req, user) {
        const { organizationId } = req.query;

        if (organizationId) {
            await this.validateOrganizationAssociation(
                organizationId,
                user
            );
        }

        const checkedOrganizationId = organizationId ?? uuidv4();

        const generatedFileName = `${process.env.NODE_ENV}-organization-logo/${checkedOrganizationId}`;

        const presignedUrl = await UploadService.getPreSignedUrlForUpload(
            generatedFileName
        );
        return {
            presignedUrl,
            fileName: generatedFileName,
            organizationId: checkedOrganizationId
        };
    }
}

module.exports = OrganizationService;
