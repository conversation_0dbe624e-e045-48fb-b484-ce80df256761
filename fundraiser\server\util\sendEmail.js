const fs = require('fs');
const { promisify } = require('util');
const Constants = require('./constants');
const readFileAsync = promisify(fs.readFile);
const { SESClient, SendEmailCommand } = require('@aws-sdk/client-ses');
const ses = new SESClient({ region: process.env.SES_REGION });
class EmailService {
    /**
     * @desc This function is used to send email without attachments
     * <AUTHOR>
     * @param {Array} email array of email addresses
     * @param {String} subject subject of the email
     * @param {String} template location of the email template
     * @param {Object} templateVariables dynamic variables included in the email template
     * @since 26/09/2023
     */
    static async prepareAndSendEmail (email, subject, template, templateVariables) {
        if (process.env.NODE_ENV !== 'testing') {
            let htmlMessage = await readFileAsync(template, 'utf8');
            templateVariables.year = MOMENT().year();
            for (const [key, value] of Object.entries(templateVariables)) {
                htmlMessage = htmlMessage.replace(new RegExp(`##${key.toUpperCase()}`, 'g'), value);
            }
            const command = new SendEmailCommand({
                Destination: {
                    ToAddresses: email
                },
                Message: {
                    Body: {
                        Html: {
                            Charset: 'UTF-8',
                            Data: htmlMessage
                        }
                    },
                    Subject: {
                        Charset: 'UTF-8',
                        Data: subject
                    }
                },
                ReturnPath: Constants.DEVELOPERS_EMAIL,
                Source: Constants.DEVELOPERS_EMAIL
            });
            await ses.send(command);
        }
    }
}

module.exports = EmailService;
