const sinon = require('sinon');
const AWS = require('aws-sdk');
const AWSMock = require('jest-aws-sdk-mock');
const Redis = require('ioredis');
const CONSOLE_LOGGER = require('../server/logger');
const { beforeEach, afterEach } = require('mocha');
const PopulateConversations = require('../server/PopulateConversations');
const GroupsModel = require('../server/models/groups.model');
const GroupMembersModel = require('../server/models/groupMembers.model');
const UserModel = require('../server/models/user.model');
const PersonalConversationModel = require('../server/models/personalConversation.model');
const MessageModel = require('../server/models/message.model');
const PersonalMessageModel = require('../server/models/personalMessage.model');
const { expect } = require('chai');

const assert = sinon.assert;

const groups = [
    {
        groupId: 'g1',
        groupType: 'event',
        organizationId: 'o1',
        organizationMetaData: {
            name: 'Organization 1'
        },
        status: 'active'
    },
    {
        groupId: 'g2',
        groupType: 'event',
        organizationId: 'o2',
        eventId: 'e2',
        eventMetaData: {
            name: 'Event 2'
        },
        status: 'active'
    }
];

const groupMembers = [
    {
        id: 'gm1',
        groupId: 'g1',
        userId: 'u1',
        isAdmin: false,
        childrenIds: [],
        lastReadMessage: null,
        muteConversation: false
    },
    {
        id: 'gm2',
        groupId: 'g1',
        userId: 'u2',
        isAdmin: false,
        childrenIds: []
    }
];

const users = [
    {
        id: 'u1',
        email: '<EMAIL>',
        firstName: 'User',
        lastName: '1',
        isDeleted: 0,
        children: []
    },
    {
        id: 'u2',
        email: '<EMAIL>',
        firstName: 'User',
        lastName: '2',
        isDeleted: 0,
        children: []
    }
];

const personalConversations = [
    {
        userAId: 'u1',
        userBId: 'u2',
        conversationId: 'c1',
        lastReadMessage: null
    },
    {
        userAId: 'u2',
        userBId: 'u1',
        conversationId: 'c2',
        lastReadMessage: null
    }
];

const messages = [
    {
        id: 'm1',
        groupId: 'g1',
        senderId: 'u1',
        message: 'Hello, how are you?',
        createdAt: new Date('2021-01-01T00:00:00Z')
    },
    {
        id: 'm2',
        groupId: 'g1',
        senderId: 'u2',
        message: 'I am fine, thank you!',
        createdAt: new Date('2021-01-01T00:00:01Z')
    }
];

const personalMessages = [
    {
        id: 'pm1',
        conversationId: 'c1',
        senderId: 'u1',
        message: 'Hello, how are you?',
        createdAt: new Date('2021-01-01T00:00:00Z')
    },
    {
        id: 'pm2',
        conversationId: 'c1',
        senderId: 'u2',
        message: 'I am fine, thank you!',
        createdAt: new Date('2021-01-01T00:00:01Z')
    }
];

describe('Populate Conversation', () => {
    let sandbox;
    let stubRedisConnect;
    let pipelineExecStub;
    let pipelineStub;

    before(() => {
        AWSMock.setSDKInstance(AWS);
        sandbox = sinon.createSandbox();
    });

    after(() => {
        AWSMock.restore();
        sinon.restore();
        sandbox.restore();
        pipelineStub.restore();
    });

    beforeEach(() => {
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        pipelineExecStub = sinon.stub();
        pipelineStub = sinon.stub(Redis.prototype, 'pipeline').returns({ exec: pipelineExecStub });
    });

    afterEach(() => {
        sinon.restore();
    });

    try {
        it('should populate conversation', async () => {
            sinon.stub(GroupsModel, 'scan').returns({
                exec: sinon.stub().resolves(groups)
            });

            sinon.stub(GroupMembersModel, 'scan').returns({
                exec: sinon.stub().resolves(groupMembers)
            });

            sinon.stub(UserModel, 'scan').returns({
                exec: sinon.stub().resolves(users)
            });

            sinon.stub(PersonalConversationModel, 'scan').returns({
                exec: sinon.stub().resolves(personalConversations)
            });

            sinon.stub(MessageModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                exec: sinon.stub().resolves(messages)
                            })
                        })
                    })
                })
            });

            sinon.stub(PersonalMessageModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                exec: sinon.stub().resolves(personalMessages)
                            })
                        })
                    })
                })
            });

            await PopulateConversations.populateConversations();

            assert.called(GroupsModel.scan);
            assert.called(GroupMembersModel.scan);
            assert.called(UserModel.scan);
            assert.called(PersonalConversationModel.scan);
            assert.called(MessageModel.query);
            assert.called(PersonalMessageModel.query);
        });

        it('should handle error if something goes wrong while populating conversations', async () => {
            sinon.stub(GroupsModel, 'scan').returns({
                exec: sinon.stub().rejects(new Error('Something went wrong'))
            });

            try {
                await PopulateConversations.populateConversations();
            } catch (error) {
                expect(error.message).to.eq('Something went wrong');
            }

            assert.called(GroupsModel.scan);
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
