/* eslint-disable max-len */
const { Client } = require('@opensearch-project/opensearch');
const CONSOLE_LOGGER = require('./logger');
let client;
if (process.env.NODE_ENV !== 'testing') {
    client = new Client({
        node: process.env.OPENSEARCH_ENDPOINT,
        auth: {
            username: process.env.OPENSEARCH_USERNAME,
            password: process.env.OPENSEARCH_PASSWORD
        },
        ssl: { rejectUnauthorized: false }
    });
}

/**
 * Class represents Utilities function for AWS Opensearch.
 */
class AwsOpenSearchService {
    /**
     * Function to create a new document in AWS Opensearch.
     * @param {String} collectionName Name of the collection.
     * @param {String} id Unique id of the document.
     * @param {Object} body Body of the document.
     * @returns {Object} Response from AWS Opensearch.
     */
    static async create (collectionName, id, body) {
        if (process.env.NODE_ENV !== 'testing') {
            CONSOLE_LOGGER.info(process.env.NODE_ENV, 'Creating index', collectionName, id, body, process.env.OPENSEARCH_ENDPOINT, process.env.OPENSEARCH_USERNAME, process.env.OPENSEARCH_PASSWORD);
            CONSOLE_LOGGER.info('Client', JSON.stringify(client));
            try {
                // const { body } = await client.cat.indices({ format: 'json' });
                // for (const index of body) {
                //     CONSOLE_LOGGER.info('--> index',index.index);
                // }
                const createdIndex = await client.index({
                    index: collectionName,
                    id,
                    body
                });
                CONSOLE_LOGGER.info('Created index', createdIndex);
                return createdIndex;
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error creating index', collectionName, id, body);
                return error;
            }
        } else {
            return '';
        }
    }

    /**
     * Function to get a document from AWS Opensearch.
     * @param {String} collectionName Name of the collection.
     * @param {String} id Unique id of the document.
     * @returns {Object} Response from AWS Opensearch.
     */
    static async updateField (collectionName, searchId, fieldUpdates) {
        if (process.env.NODE_ENV !== 'testing') {
            try {
                const isConnected = await client.ping();
                CONSOLE_LOGGER.info('Is connected', isConnected);
                const { body } = await client.cat.indices({ format: 'json' });
                for (const index of body) {
                    CONSOLE_LOGGER.info(index.index);
                }
                return await client.update({
                    index: collectionName,
                    id: searchId,
                    body: {
                        doc: fieldUpdates,
                        doc_as_upsert: true
                    }
                });
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error updating index', collectionName, searchId, fieldUpdates);
                return error;
            }
        } else {
            return '';
        }
    }

    static async registerMultipleEvents (collectionName, childId, eventIds) {
        if (process.env.NODE_ENV !== 'testing') {
            try {
                return await client.update({
                    index: collectionName,
                    id: childId,
                    body: {
                        script: {
                            source: `
                                if (!ctx._source.containsKey("childCalendarEvents")) { 
                                    ctx._source.childCalendarEvents = new ArrayList(); 
                                } 
                                for (def eventId : params.tags) {
                                    if (!ctx._source.childCalendarEvents.contains(eventId)) { 
                                        ctx._source.childCalendarEvents.add(eventId); 
                                    }
                                }
                            `,
                            lang: 'painless',
                            params: {
                                tags: eventIds
                            }
                        }
                    }
                });
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error registering multiple events', collectionName, childId, eventIds);
                return error;
            }
        } else {
            return '';
        }
    }

    static async emptyChildCalendarEvents (collectionName, childId) {
        if (process.env.NODE_ENV !== 'testing') {
            try {
                return await client.update({
                    index: collectionName,
                    id: childId,
                    body: {
                        script: {
                            source: `
                                if (!ctx._source.containsKey("childCalendarEvents")) { 
                                    ctx._source.childCalendarEvents = new ArrayList(); 
                                } else {
                                    ctx._source.childCalendarEvents.clear();
                                }
                            `,
                            lang: 'painless'
                        }
                    }
                });
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error emptying child calendar events', collectionName, childId);
                return error;
            }
        } else {
            return '';
        }
    }

    static async emptyField (collectionName, fieldId, fieldName) {
        if (process.env.NODE_ENV !== 'testing') {
            try {
                return await client.update({
                    index: collectionName,
                    id: fieldId,
                    body: {
                        script: {
                            source: `
                                if (!ctx._source.containsKey(params.field)) { 
                                    ctx._source[params.field] = new ArrayList(); 
                                } else {
                                    ctx._source[params.field].clear();
                                }
                            `,
                            lang: 'painless',
                            params: {
                                field: fieldName
                            }
                        }
                    }
                });
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error emptying field', collectionName, fieldId, fieldName);
                return error;
            }
        } else {
            return '';
        }
    }

    static async addItemsInField (collectionName, documentId, itemIds, fieldName) {
        if (process.env.NODE_ENV !== 'testing') {
            try {
                CONSOLE_LOGGER.info('-->', collectionName, documentId, itemIds, fieldName);
                return await client.update({
                    index: collectionName,
                    id: documentId,
                    body: {
                        script: {
                            source: `
                                if (!ctx._source.containsKey(params.field)) { 
                                    ctx._source[params.field] = new ArrayList(params.tags); 
                                } else {
                                    for (def tag : params.tags) {
                                        if (!ctx._source[params.field].contains(tag)) {
                                            ctx._source[params.field].add(tag);
                                        }
                                    }
                                }
                            `,
                            lang: 'painless',
                            params: {
                                tags: itemIds,
                                field: fieldName
                            }
                        }
                    }
                });
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error updating index', collectionName, documentId, itemIds);
                return error;
            }
        } else {
            return '';
        }
    }
}

module.exports = AwsOpenSearchService;
