const { childQueryPlanParser, buildChildQueryPlanPrompt } = require('../../util/companionUtils');
const { ChatOpenAI } = require('@langchain/openai');
const { SystemMessage, HumanMessage } = require('@langchain/core/messages');

class ChildQueryPlanningService {
    constructor (model = new ChatOpenAI({
        temperature: CONSTANTS.AI_TEMPERATURE,
        modelName: CONSTANTS.AI_MODELS.DEFAULT
    })) {
        this.model = model;
    }

    formatChildrenDetails (childrenList) {
        return childrenList.map(child => ({
            fullName: `${child.firstName} ${child.lastName}`,
            associatedOrganizations: child.associatedOrganizations
        }));
    }

    async generateQueryPlan (childrenList, queryMessage, context) {
        const childrenDetailsJSON = JSON.stringify(
            this.formatChildrenDetails(childrenList),
            null,
            2
        );

        const systemPrompt = buildChildQueryPlanPrompt(childrenDetailsJSON, context);
        const response = await this.executeAIQuery(systemPrompt, queryMessage);
        return this.parseResponse(response);
    }

    async executeAIQuery (systemPrompt, queryMessage) {
        const response = await this.model.invoke([
            new SystemMessage(systemPrompt),
            new HumanMessage(queryMessage)
        ]);
        return response.content.toString();
    }

    async parseResponse (text) {
        try {
            return await childQueryPlanParser.parse(text);
        } catch (error) {
            CONSOLE_LOGGER.error('Query plan parsing failed:', error);
            return error.message;
        }
    }
}

module.exports = ChildQueryPlanningService;
