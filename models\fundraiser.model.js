const dynamoose = require('dynamoose');
const { v4: uuidv4 } = require('uuid');

const homeroomDonationsSchema = new dynamoose.Schema({
    homeRoomId: String,
    donationsAmount: Number
});

const homeroomRegistrationsSchema = new dynamoose.Schema({
    homeRoomId: String,
    registrationsCount: Number
});

const childLevelDonationsSchema = new dynamoose.Schema({
    childId: String,
    donationsAmount: Number
});

const fundraiserSchema = new dynamoose.Schema({
    id: {
        hashKey: true,
        type: String,
        default: () => uuidv4()
    },
    title: {
        type: String,
        required: true
    },
    description: {
        type: String,
        required: true
    },
    products: {
        type: String,
        required: true
    },
    imageURL: {
        type: String
    },
    fundraiserType: {
        type: String,
        enum: ['spiritwearFundraiser', 'annualFundraiser', 'membershipFundraiser', 'other', 'booster'],
        default: 'spiritwearFundraiser'
    },
    startDate: {
        type: Date,
        required: true
    },
    endDate: {
        type: Date,
        required: true
    },
    status: {
        type: String,
        enum: ['published', 'draft', 'cancelled'],
        default: 'draft'
    },
    organizationId: {
        type: String,
        required: true,
        index: {
            name: 'organizationId-index',
            global: true,
            project: true
        }
    },
    membershipBenefitDetails: {
        type: Object,
        schema: {
            'benefitDiscount': {
                'type': String
            },
            'isOnlyForMembers': {
                'type': Boolean
            }
        }
    },
    boosterGoal: {
        type: Number,
        default: 0
    },
    boosterGoalForChild: {
        type: Number
    },
    boosterMessageForChild: {
        type: String
    },
    homeroomStats: {
        type: Object,
        schema: {
            'donations': {
                type: Array,
                schema: [{
                    type: Object,
                    schema: homeroomDonationsSchema
                }]
            },
            'registrations': {
                type: Array,
                schema: [{
                    type: Object,
                    schema: homeroomRegistrationsSchema
                }]
            }
        }
    },
    childLevelStats: {
        type: Object,
        schema: {
            'donations': {
                type: Array,
                schema: [{
                    type: Object,
                    schema: childLevelDonationsSchema
                }]
            }
        }
    },
    raisedDonationsAmountForOrg: {
        type: Number,
        default: 0
    },
    createdBy: {
        type: String,
        required: true
    },
    updatedBy: {
        type: String
    },
    isDeleted: {
        type: Number,
        enum: [0, 1],
        default: 0
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('Fundraisers', fundraiserSchema);
