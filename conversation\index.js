#!/usr/bin/env node
'use strict';

const dotenv = require('dotenv');
const env = process.env.NODE_ENV || 'local';
dotenv.config({ path: env + '.env' });
const app = require('./server/server');
const DynamoDBConnection = require('./server/connection');

const connectDB = async () => await DynamoDBConnection.connectToDB();
if (process.env.NODE_ENV !== 'testing') {
    connectDB();
}

if (process.env.SERVERLESS === 'false') {
    const WebSocket = require('ws');
    const handleConnect = require('./server/services/webSocketHandlers/handleConnect');
    const handleDisconnect = require('./server/services/webSocketHandlers/handleDisconnect');
    const handleSendGroupMessage = require('./server/services/webSocketHandlers/handleSendGroupMessage');

    const { v4: uuidv4 } = require('uuid');
    const server = app.listen(process.env.PORT, () => {
        CONSOLE_LOGGER.info('Server is started at : %s', process.env.PORT);
    });
    const wss = new WebSocket.Server({ server });
    const connections = new Map();

    wss.on('connection', async (ws, req) => {
        const connectionId = uuidv4();
        const userId = req.headers.userid;
        connections.set(connectionId, ws);
        CONSOLE_LOGGER.info('WebSocket connection established');
        await handleConnect({ requestContext: { connectionId }, headers: { userId } });

        ws.on('message', async (body) => {
            await handleSendGroupMessage({ requestContext: { connectionId }, body });
            CONSOLE_LOGGER.info('Received message: %s', body.message);
        });

        ws.on('close', async () => {
            await handleDisconnect({ requestContext: { connectionId } });
            connections.delete(connectionId);
            CONSOLE_LOGGER.info('WebSocket connection closed');
        });
    });

    module.exports = server;
} else {
    module.exports.handler = async (event, context) => {
        if (event.httpMethod) {
            const serverless = require('serverless-http');
            const expressHandler = serverless(app);
            return await expressHandler(event, context);
        }

        const webSocketHandler = require('./server/webSocketHandler');
        return await webSocketHandler(event);
    };
    CONSOLE_LOGGER.info('Serverless project is started');
}
