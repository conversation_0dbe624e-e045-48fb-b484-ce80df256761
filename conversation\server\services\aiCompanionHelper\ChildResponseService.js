const { Chat, CustomPrompts } = require('@incubyte/ai');
const CONSTANTS = require('../../util/constants');
const {
    buildChildSpecificPrompt,
    buildGeneralPromptForAllChildren,
    getRewriteQueryPrompt,
    getChatMessages
} = require('../../util/companionUtils');

class ChildResponseService {
    constructor (userDetails, context) {
        this.userDetails = userDetails;
        this.chatMessages = getChatMessages(context);
    }

    getUniqueOrganizationsForAllChild () {
        return this.userDetails.children.reduce((acc, child) => {
            child.associatedOrganizations.forEach(org => {
                if (!acc.some(existingOrg => existingOrg.id === org.id)) {
                    acc.push({ id: org.id, name: org.name });
                }
            });
            return acc;
        }, []);
    }

    async handleSpecificChildResponse (childName, associatedOrganizations, queryMessage) {
        const chat = this.createChildChat(childName, associatedOrganizations);
        const chatParams = this.buildChatParams(queryMessage, associatedOrganizations);
        return await this.sendChatRequest(chat, chatParams);
    }

    async handleGeneralResponse (queryMessage) {
        const organizations = this.getUniqueOrganizationsForAllChild();
        const chat = this.createGeneralChat(organizations);
        const chatParams = this.buildChatParams(queryMessage, organizations);
        return await this.sendChatRequest(chat, chatParams);
    }

    createChildChat (childName, associatedOrganizations) {
        const systemPrompt = buildChildSpecificPrompt(this.userDetails, childName, associatedOrganizations);
        const customPrompts = new CustomPrompts({
            systemPrompts: [systemPrompt],
            queryRewritingPrompt: getRewriteQueryPrompt(associatedOrganizations)
        });
        return new Chat(undefined, customPrompts);
    }

    createGeneralChat (organizations) {
        const systemPrompt = buildGeneralPromptForAllChildren(this.userDetails);
        const customPrompts = new CustomPrompts({
            systemPrompts: [systemPrompt],
            queryRewritingPrompt: getRewriteQueryPrompt(organizations)
        });
        return new Chat(undefined, customPrompts);
    }

    buildChatParams (queryMessage, organizations) {
        return {
            query: queryMessage,
            context: { messages: this.chatMessages },
            metadataFilters: [{ organizationid: organizations.map(({ id }) => id) }]
        };
    }

    async sendChatRequest (chat, chatParams) {
        const { chatResponse } = await chat.sendChat(
            CONSTANTS.AI_COMPANION_GLOBAL_INDEX,
            null,
            chatParams
        );
        return chatResponse;
    }
}

module.exports = ChildResponseService;
