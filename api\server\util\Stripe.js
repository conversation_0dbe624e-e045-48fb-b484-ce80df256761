
/**
 * This class represents all the stripe services functions
 */
class Stripe {
    /**
     * @desc This function is being used to create a stripe account for the connect user
     * <AUTHOR>
     * @since 08/11/2023
     */
    static async createAccount (stripe) {
        return await stripe.accounts.create({
            'type': 'express',
            'country': 'US'
        });
    }

    /**
     * @desc This function is being used to create a onboarding link for the connect user
     * <AUTHOR>
     * @since 08/11/2023
     * @param {Object} account account
     */
    static async createOnboardingLink (stripe, account) {
        return await stripe.accountLinks.create({
            account: account.id,
            // failure
            refresh_url: process.env.FE_URL,
            // success url
            return_url: process.env.FE_URL,
            type: 'account_onboarding'
        });
    }

    /**
     * @desc This function is being used to create stripe webhooks
     * <AUTHOR>
     * @since 10/11/2023
     * @param {Object} body
     * @param {String} signature
     */
    static async constructWebhookEvent (body, signature, stripe) {
        return await stripe.webhooks.constructEvent(body, signature, process.env.STRIPE_WEBHOOK_SECRET_CONNECT);
    }
}

module.exports = Stripe;
