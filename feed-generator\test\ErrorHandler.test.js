const handler = require('../index');
const AWS = require('aws-sdk');
const AWSMock = require('jest-aws-sdk-mock');
const sinon = require('sinon');
const Organization = require('../server/models/organization.model');
const User = require('../server/models/user.model');
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const Redis = require('ioredis');
const CONSOLE_LOGGER = require('../server/logger');
const FeedService = require('../server/feedService');
const { afterEach, beforeEach } = require('mocha');
const PopulateCacheService = require('../server/PopulateCacheService');
const { expect } = require('chai');
const CONSTANTS = require('../server/constants');
const ConstantModel = require('../server/models/constant.model');
const ConversationService = require('../server/ConversationService');
const UserHelperService = require('../server/UserHelperService');
const constants = require('../server/constants');
const fundraiserModel = require('../server/models/fundraiser.model');
const childModel = require('../server/models/child.model');

describe('Handle Error', () => {
    let sandbox;
    let stubRedisConnect;
    let loggerStub;

    before(() => {
        AWSMock.setSDKInstance(AWS);
        sandbox = sinon.createSandbox();
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
        loggerStub = sinon.stub(CONSOLE_LOGGER, 'error');
        sinon.stub(ConstantModel, 'get').withArgs(CONSTANTS.FEED_VERSION_PREFIX).resolves({ value: 'v1' });
    });

    after(() => {
        AWSMock.restore();
        sinon.restore();
        sandbox.restore();
        loggerStub.restore();
    });

    it('handle redis connection error in INSERT Event', async () => {
        sinon.stub(Organization, 'get').resolves({ name: 'Test' });
        const sendStub = sandbox.stub(DynamoDBClient.prototype, 'send');
        stubRedisConnect.callsFake(async function () {
            this.setStatus('error');
        });
        const mockedResponse = {
            Items: [
                { id: { S: 'parent1' }, associatedOrganizations: { L: [{ S: '456' }] } },
                { id: { S: 'child1' }, associatedOrganizations: { L: [{ S: '123' }] } }
            ]
        };

        sendStub.resolves(mockedResponse);
        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Events/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                    title: { S: 'Test event' },
                    details: { M: { startDateTime: { S: '2020-01-01T00:00:00.000Z' } } },
                    comments: { S: 'Test comment' },
                    organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                    eventType: { S: 'Test event type' },
                    eventScope: { S: 'Test event scope' },
                    sheetUrl: { S: 'Test sheet url' },
                    fee: { S: 'Test fee' },
                    status: { S: 'published' },
                    photoURL: { S: 'Test photo url' },
                    createdAt: { S: '2020-01-01T00:00:00.000Z' },
                    updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle error if something goes wrong while inserting fundraiser', async () => {
        sinon.stub(FeedService, 'generateFeedAndStoreToCache').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Fundraisers/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        FeedService.generateFeedAndStoreToCache.restore();
    });

    it('should handle error if something goes wrong while inserting post', async () => {
        sinon.stub(FeedService, 'generatePostFeedAndStoreToCache').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Posts/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        FeedService.generatePostFeedAndStoreToCache.restore();
    });

    it('should handle redis connection error in MODIFY event', async () => {
        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Events/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                OldImage: {
                    id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                    title: { S: 'Test event' },
                    details: { M: { startDateTime: { S: '2020-01-01T00:00:00.000Z' } } },
                    comments: { S: 'Test comment' },
                    organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                    eventType: { S: 'Test event type' },
                    eventScope: { S: 'Test event scope' },
                    sheetUrl: { S: 'Test sheet url' },
                    fee: { S: '100' },
                    status: { S: 'unpublished' },
                    photoURL: { S: 'Test photo url' },
                    createdAt: { S: '2020-01-01T00:00:00.000Z' },
                    updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                },
                NewImage: {
                    id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                    title: { S: 'Test event' },
                    details: { M: { startDateTime: { S: '2020-01-01T00:00:00.000Z' } } },
                    comments: { S: 'Test comment' },
                    organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                    eventType: { S: 'Test event type' },
                    eventScope: { S: 'Test event scope' },
                    sheetUrl: { S: 'Test sheet url' },
                    fee: { S: 'Test fee' },
                    status: { S: 'in-review' },
                    photoURL: { S: 'Test photo url' },
                    createdAt: { S: '2020-01-01T00:00:00.000Z' },
                    updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle redis connection error in MODIFY fundraiser', async () => {
        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Fundraisers/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                OldImage: {
                    id: { S: 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                },
                NewImage: {
                    id: { S: 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle redis connection error in INSERT child event', async () => {
        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Child/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'associatedColor': { 'S': '#000000' },
                    'associatedOrganizations': { 'L': [{ 'S': '65824922-7a2d-49c0-8f39-ff4d7a729ecd' }] },
                    'lastName': { 'S': 'user' },
                    'updatedAt': { 'S': '2023-11-20T11:20:09.158Z' },
                    'dob': { 'S': '2023-06-20T00:00:00.000Z' },
                    'createdAt': { 'S': '2023-11-20T11:20:09.158Z' },
                    'isDeleted': { 'N': '0' },
                    'id': { 'S': '3e4406aa-b95b-489e-9e58-cfa746eed023' },
                    'zipCode': { 'S': '08820' },
                    'firstName': { 'S': 'Test' },
                    'gender': { 'S': 'boy' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle redis connection error in INSERT fundraiser', async () => {
        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Fundraisers/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle redis connection error in INSERT event sign up', async () => {
        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/EventSignups/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'eventId': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' },
                    'organizationId': { 'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc' },
                    'paymentDetails': {
                        'M': {
                            'stripeConnectAccountId': { 'S': 'accountId' },
                            'stripeCustomerId': { 'S': 'customerId' },
                            'stripePaymentIntentId': { 'S': 'intentId' },
                            'paymentStatus': { 'S': 'pending' },
                            'paymentType': { 'S': 'stripe' }
                        }
                    },
                    'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                    'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                    'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                    'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                    'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                    'createdBy': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle redis connection error in INSERT fundraiser sign up', async () => {
        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/FundraiserSignup/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'eventId': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' },
                    'organizationId': {
                        'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc'
                    },
                    'paymentDetails': {
                        'M': {
                            'stripeConnectAccountId': { 'S': 'accountId' },
                            'stripeCustomerId': { 'S': 'customerId' },
                            'stripePaymentIntentId': { 'S': 'intentId' },
                            'paymentStatus': { 'S': 'pending' },
                            'paymentType': { 'S': 'stripe' }
                        }
                    },
                    'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                    'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                    'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                    'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                    'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                    'createdBy': {
                        'S':
                            'a967e334-60d4-4efe-8a9d-c71ecc525977'
                    }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle error if something goes wrong while inserting organization', async () => {
        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Organization/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle redis connection error in MODIFY event signup', async () => {
        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/EventSignups/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                OldImage: {
                    id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' }
                },
                NewImage: {
                    id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle redis connection error in MODIFY fundraiser signup', async () => {
        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/FundraiserSignup/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                OldImage: {
                    id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' }
                },
                NewImage: {
                    id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle redis connection error in MODIFY posts', async () => {
        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Posts/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                OldImage: {
                    id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' }
                },
                NewImage: {
                    id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle redis connection error in MODIFY child event', async () => {
        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Child/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                OldImage: {
                    id: { S: 'ace0149e-447d-4f7f-9402-95a96963de9b' }
                },
                NewImage: {
                    id: { S: 'ace0149e-447d-4f7f-9402-95a96963de9b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle redis connection error in REMOVE event signup', async () => {
        const record = {
            eventName: 'REMOVE',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/EventSignups/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                OldImage: {
                    id: { S: 'ace0149e-447d-4f7f-9402-95a96963de9b' }
                },
                NewImage: {
                    id: { S: 'ace0149e-447d-4f7f-9402-95a96963de9b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle redis connection error in REMOVE fundraiser signup', async () => {
        const record = {
            eventName: 'REMOVE',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/FundraiserSignup/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                OldImage: {
                    id: { S: 'ace0149e-447d-4f7f-9402-95a96963de9b' }
                },
                NewImage: {
                    id: { S: 'ace0149e-447d-4f7f-9402-95a96963de9b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle redis connection error in INSERT org managed fundraiser booster donation', async () => {
        const record = {
            eventName: 'INSERT',
            // eslint-disable-next-line max-len
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/OrgManagedFundraiserBoosterDonation/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };
        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle default case while INSERT', async () => {
        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Organizations/stream/2023-10-27T13:01:18.061'
        };
        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle default case while MODIFY', async () => {
        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Organizations/stream/2023-10-27T13:01:18.061'
        };
        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle default case while DELETE', async () => {
        const record = {
            eventName: 'REMOVE',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Organizations/stream/2023-10-27T13:01:18.061'
        };
        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle redis connection error in INSERT User', async () => {
        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/User/stream/2025-03-03T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'userId1' },
                    'firstName': { 'S': 'John' },
                    'lastName': { 'S': 'Doe' }
                }
            }
        };
        const event = {
            Records: [record]
        };

        await handler.handler(event);




        sinon.assert.called(loggerStub);




    });

    it('should handle redis connection error in MODIFY User', async () => {
        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/User/stream/2025-03-03T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'userId1' },
                    'firstName': { 'S': 'John' },
                    'lastName': { 'S': 'Doe' }
                },
                OldImage: {
                    'id': { 'S': 'userId1' },
                    'firstName': { 'S': 'John' },
                    'lastName': { 'S': 'Doe' }
                }
            }
        };
        const event = {
            Records: [record]
        };

        await handler.handler(event);




        sinon.assert.called(loggerStub);
    });

    it('should handle redis connection error in REMOVE User', async () => {
        const record = {
            eventName: 'REMOVE',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/User/stream/2025-03-03T13:01:18.061',
            dynamodb: {
                OldImage: {
                    'id': { 'S': 'userId1' },
                    'firstName': { 'S': 'John' },
                    'lastName': { 'S': 'Doe' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);


    });

    it('should handle redis connection error in INSERT Group', async () => {


        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Groups/stream/2025-03-03T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'groupId1' },
                    'name': { 'S': 'Group 1' },
                    'description': { 'S': 'Group 1 description' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);



    });

    it('should handle redis connection error in MODIFY Group', async () => {






        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Groups/stream/2025-03-03T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'groupId1' },
                    'name': { 'S': 'Group 1' },
                    'description': { 'S': 'Group 1 description' }
                },
                OldImage: {
                    'id': { 'S': 'groupId1' },
                    'name': { 'S': 'Group 1' },
                    'description': { 'S': 'Group 1 description' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);



    });

    it('should handle redis connection error in INSERT GroupMember', async () => {


        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/GroupMembers/stream/2025-03-03T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'groupMemberId1' },
                    'groupId': { 'S': 'groupId1' },
                    'userId': { 'S': 'userId1' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);


    });

    it('should handle redis connection error in MODIFY GroupMember', async () => {


        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/GroupMembers/stream/2025-03-03T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'groupMemberId1' },
                    'groupId': { 'S': 'groupId1' },
                    'userId': { 'S': 'userId1' }
                },
                OldImage: {
                    'id': { 'S': 'groupMemberId1' },
                    'groupId': { 'S': 'groupId1' },
                    'userId': { 'S': 'userId1' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);


    });

    it('should handle redis connection error in REMOVE GroupMember', async () => {


        const record = {
            eventName: 'REMOVE',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/GroupMembers/stream/2025-03-03T13:01:18.061',
            dynamodb: {



                OldImage: {
                    'id': { 'S': 'groupMemberId1' },
                    'groupId': { 'S': 'groupId1' },
                    'userId': { 'S': 'userId1' }
                }

            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle redis connection error in INSERT PersonalConversation', async () => {
        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/PersonalConversation/stream/2025-03-03T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'personalConversationId1' },
                    'userAId': { 'S': 'userId1' },
                    'userBId': { 'S': 'userId2' }
                }
            }
        };
        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle redis connection error in MODIFY PersonalConversation', async () => {
        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/PersonalConversation/stream/2025-03-03T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'personalConversationId1' },
                    'userAId': { 'S': 'userId1' },
                    'userBId': { 'S': 'userId2' }
                },
                OldImage: {
                    'id': { 'S': 'personalConversationId1' },
                    'userAId': { 'S': 'userId1' },
                    'userBId': { 'S': 'userId2' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle redis connection error in REMOVE PersonalConversation', async () => {
        const record = {
            eventName: 'REMOVE',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/PersonalConversation/stream/2025-03-03T13:01:18.061',
            dynamodb: {
                OldImage: {
                    'id': { 'S': 'personalConversationId1' },
                    'userAId': { 'S': 'userId1' },
                    'userBId': { 'S': 'userId2' }
                }
            }
        };
        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle redis connection error in INSERT Group Message', async () => {
        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Message/stream/2025-03-03T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'messageId1' },
                    'groupId': { 'S': 'groupId1' },
                    'senderId': { 'S': 'userId1' },
                    'message': { 'S': 'messageContent1' }
                }
            }
        };
        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle redis connection error in MODIFY Group Message', async () => {
        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Message/stream/2025-03-03T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'messageId1' },
                    'groupId': { 'S': 'groupId1' },
                    'senderId': { 'S': 'userId1' },
                    'message': { 'S': 'messageContent1' }
                },
                OldImage: {
                    'id': { 'S': 'messageId1' },
                    'groupId': { 'S': 'groupId1' },
                    'senderId': { 'S': 'userId1' },
                    'message': { 'S': 'messageContent1' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);


    });

    it('should handle redis connection error in INSERT Personal Message', async () => {
        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/PersonalMessage/stream/2025-03-03T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'messageId1' },
                    'conversationId': { 'S': 'conversationId1' },
                    'senderId': { 'S': 'userId1' },
                    'message': { 'S': 'messageContent1' }
                }
            }
        };
        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });

    it('should handle redis connection error in MODIFY Personal Message', async () => {
        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/PersonalMessage/stream/2025-03-03T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'messageId1' },
                    'conversationId': { 'S': 'conversationId1' },
                    'senderId': { 'S': 'userId1' },
                    'message': { 'S': 'messageContent1' }
                },
                OldImage: {
                    'id': { 'S': 'messageId1' },
                    'conversationId': { 'S': 'conversationId1' },
                    'senderId': { 'S': 'userId1' },
                    'message': { 'S': 'messageContent1' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
    });
});

describe('Handle Error while processing', () => {
    let sandbox;
    let stubRedisConnect;
    let loggerStub;

    before(() => {
        AWSMock.setSDKInstance(AWS);
        sandbox = sinon.createSandbox();
    });

    after(() => {
        AWSMock.restore();
        sinon.restore();
        sandbox.restore();
        loggerStub.restore();
    });

    afterEach(() => {
        sinon.restore();
    });

    beforeEach(() => {
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
        loggerStub = sinon.stub(CONSOLE_LOGGER, 'error');
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        sinon.stub(ConstantModel, 'get').withArgs(CONSTANTS.FEED_VERSION_PREFIX).resolves({ value: 'v1' });
    });

    it('should handle error is something goes wrong while inserting event', async () => {
        sinon.stub(FeedService, 'generateFeedAndStoreToCache').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Events/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        FeedService.generateFeedAndStoreToCache.restore();
    });

    it('should handle error is something goes wrong while inserting fundraiser', async () => {
        sinon.stub(FeedService, 'generateFundraiserFeedAndStoreToCache').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Fundraisers/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        FeedService.generateFundraiserFeedAndStoreToCache.restore();

    });

    it('should handle error if something goes wrong while inserting post', async () => {
        sinon.stub(FeedService, 'generatePostFeedAndStoreToCache').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Posts/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        FeedService.generatePostFeedAndStoreToCache.restore();
    });

    it('should handle error if something goes wrong while modifying post', async () => {
        sinon.stub(FeedService, 'updatePostFeedAndStoreToCache').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Posts/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                },
                OldImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        FeedService.updatePostFeedAndStoreToCache.restore();
    });

    it('should handle error if something goes wrong while inserting fundraiser signup', async () => {
        sinon.stub(fundraiserModel, 'get').resolves({ fundraiserType: constants.FUNDRAISER_TYPES.BOOSTER, save: () => { } });
        sinon.stub(childModel, 'get').resolves({ homeRoom: 'homeRoomId' });
        sinon.stub(FeedService, 'addToFundraiserSignupFeed').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/FundraiserSignup/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        FeedService.addToFundraiserSignupFeed.restore();
    });

    it('should handle error if something goes wrong while modifying fundraiser signup', async () => {
        sinon.stub(FeedService, 'updateFundraiserSignupFeed').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/FundraiserSignup/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                },
                OldImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        FeedService.updateFundraiserSignupFeed.restore();
    });

    it('should handle error is something goes wrong while inserting child feeds', async () => {
        sinon.stub(FeedService, 'generateFeedForChild').rejects(new Error('Something went wrong'));

        sinon.stub(User, 'query').returns({
            exec: sinon.stub().resolves([{ id: '123' }])
        });

        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Child/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        FeedService.generateFeedForChild.restore();
        User.query.restore();
    });

    it('should handle error is something goes wrong while inserting event participants', async () => {
        sinon.stub(FeedService, 'addToRegisteredEvents').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/EventSignups/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        FeedService.addToRegisteredEvents.restore();
    });

    it('should handle error is something goes wrong while updating event details', async () => {
        sinon.stub(FeedService, 'updateFeedAndStoreToCache').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Events/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                },
                OldImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        FeedService.updateFeedAndStoreToCache.restore();
    });

    it('should handle error is something goes wrong while updating fundraiser details', async () => {
        sinon.stub(FeedService, 'updateFundraiserFeedAndStoreToCache').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Fundraisers/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                },
                OldImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }

            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        FeedService.updateFundraiserFeedAndStoreToCache.restore();

    });

    it('should handle error is something goes wrong while updating event participants', async () => {
        sinon.stub(FeedService, 'updateRegisteredEvents').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/EventSignups/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                },
                OldImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        FeedService.updateRegisteredEvents.restore();
    });

    it('should handle error is something goes wrong while updating child feeds', async () => {
        sinon.stub(FeedService, 'updateFeedForChild').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Child/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                },
                OldImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        FeedService.updateFeedForChild.restore();
    });

    it('should handle error if something goes wrong while removing feeds if event is deleted', async () => {
        sinon.stub(FeedService, 'removeRegisteredEvents').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'REMOVE',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/EventSignups/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                },
                OldImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        FeedService.removeRegisteredEvents.restore();
    });

    it('should handle error if something goes wrong while removing feeds if fundraiser is deleted', async () => {
        sinon.stub(FeedService, 'removeFundraiserSignupFeed').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'REMOVE',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/FundraiserSignup/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                },
                OldImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        FeedService.removeFundraiserSignupFeed.restore();
    });

    it('should handle error is something goes wrong while inserting org managed fundraiser booster donation', async () => {
        sinon.stub(FeedService, 'updateDonationAmountRaisedAndSendNotification').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'INSERT',
            // eslint-disable-next-line max-len
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/OrgManagedFundraiserBoosterDonation/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        FeedService.updateDonationAmountRaisedAndSendNotification.restore();
    });

    it('should handle error in populating cache', async () => {
        try {
            sinon.stub(PopulateCacheService, 'populateOrganizationEvents').rejects(new Error('Something went wrong'));

            await PopulateCacheService.populateCache();
        } catch (error) {
            expect(error.message).to.eq('Something went wrong');
        }

        sinon.assert.called(loggerStub);
    });

    it('should handle error if something goes wrong while inserting user details to cache', async () => {
        sinon.stub(UserHelperService, 'addOrUpdateUserDetails').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/User/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        UserHelperService.addOrUpdateUserDetails.restore();
    });

    it('should handle error if something goes wrong while inserting group details to cache', async () => {
        sinon.stub(ConversationService, 'addOrUpdateGroupDetailsToHashSet').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Groups/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        ConversationService.addOrUpdateGroupDetailsToHashSet.restore();
    });

    it('should handle error if something goes wrong while inserting group member details to cache', async () => {
        sinon.stub(ConversationService, 'addOrUpdateGroupMemberToHashSet').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/GroupMembers/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        ConversationService.addOrUpdateGroupMemberToHashSet.restore();
    });

    it('should handle error if something goes wrong while inserting personal conversation details to cache', async () => {
        sinon.stub(ConversationService, 'addOrUpdatePersonalConversationDetailsToHashSet').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/PersonalConversation/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        ConversationService.addOrUpdatePersonalConversationDetailsToHashSet.restore();
    });

    it('should handle error if something goes wrong while inserting group message details to cache', async () => {
        sinon.stub(ConversationService, 'addGroupMessageToCache').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Message/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        ConversationService.addGroupMessageToCache.restore();
    });

    it('should handle error if something goes wrong while inserting personal message details to cache', async () => {
        sinon.stub(ConversationService, 'addPersonalMessageToCache').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/PersonalMessage/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        ConversationService.addPersonalMessageToCache.restore();
    });

    it('should handle error if something goes wrong while updating user details to cache', async () => {
        sinon.stub(UserHelperService, 'handleUserDetailsUpdate').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/User/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                },
                OldImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        UserHelperService.handleUserDetailsUpdate.restore();
    });

    it('should handle error if something goes wrong while updating group details to cache', async () => {
        sinon.stub(ConversationService, 'handleGroupDetailsUpdate').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Groups/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                },
                OldImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        ConversationService.handleGroupDetailsUpdate.restore();
    });

    it('should handle error if something goes wrong while updating group member details to cache', async () => {
        sinon.stub(ConversationService, 'addOrUpdateGroupMemberToHashSet').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/GroupMembers/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                },
                OldImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        ConversationService.addOrUpdateGroupMemberToHashSet.restore();
    });

    it('should handle error if something goes wrong while updating personal conversation details to cache', async () => {
        sinon.stub(ConversationService, 'addOrUpdatePersonalConversationDetailsToHashSet').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/PersonalConversation/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                },
                OldImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        ConversationService.addOrUpdatePersonalConversationDetailsToHashSet.restore();
    });

    it('should handle error if something goes wrong while updating group message details to cache', async () => {
        sinon.stub(ConversationService, 'updateGroupMessageDetailsInCache').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/Message/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                },
                OldImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        ConversationService.updateGroupMessageDetailsInCache.restore();
    });

    it('should handle error if something goes wrong while updating personal message details to cache', async () => {
        sinon.stub(ConversationService, 'updatePersonalMessageDetailsInCache').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/PersonalMessage/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                },
                OldImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        ConversationService.updatePersonalMessageDetailsInCache.restore();
    });

    it('should handle error if something goes wrong while deleting user details from cache', async () => {
        sinon.stub(UserHelperService, 'removeUserDetails').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'REMOVE',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/User/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                OldImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        UserHelperService.removeUserDetails.restore();
    });

    it('should handle error if something goes wrong while deleting group member details from cache', async () => {
        sinon.stub(ConversationService, 'removeGroupMemberFromHashSet').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'REMOVE',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/GroupMembers/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                OldImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        ConversationService.removeGroupMemberFromHashSet.restore();
    });

    it('should handle error if something goes wrong while deleting personal conversation details from cache', async () => {
        sinon.stub(ConversationService, 'removePersonalConversationDetailsFromHashSet').rejects(new Error('Something went wrong'));

        const record = {
            eventName: 'REMOVE',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/PersonalConversation/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                OldImage: {
                    'id': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        ConversationService.removePersonalConversationDetailsFromHashSet.restore();
    });
});
