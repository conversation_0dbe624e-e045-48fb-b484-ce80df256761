sonar.javascript.globals=DB_CONNECTION,CONSOLE_LOGGER,CONSTANTS,MESSAGES,MOMENT
sonar.sources=./
sonar.language=js
sonar.exclusions=**/crons/**,**/node_modules/**,**/coverage/**,**/test/**,**/jsdocs/**,**/db/**,**/migrations/**,**/testmigrations/**,**/target/zap-report/**,**/.eslintrc.js,**/getEnvs.js,**/retrieveSecrets.js,**/sendEmail.js,**/sendSMS.js,**/cognito.js,**/migrate-mongo-config.js,**/logger.js,**/util/country.js,**/util/currency.js,**/util/timeZone.js,**/util/languageISO.js,**/util/http-status.js, **/connection.js, **/util/constants.js, **/*.html, **/index.js, **/serverless.js, **/migrate-dynamodb-config.js, **/util/uploadService.js, **/redisUtil.js, **/util/opensearch.js, **/opensearchUtil.js
sonar.javascript.lcov.reportPaths=./coverage/lcov.info
sonar.zaproxy.reportPath=target/zap-report/security_report.xml
sonar.zaproxy.report.dir=target/zap-report
sonar.zaproxy.htmlReportPath=target/zap-report/security_report.html
