const validation = require('../../util/validation');

/**
 * Class represents validations for comment.
 */
class CommentValidator extends validation {
    constructor (body, locale) {
        super(locale);
        this.body = body;
    }

    /**
     * @desc This function is being used to validate request for add comment
     * <AUTHOR>
     * @since 27/11/2023
     */
    commentValidate () {
        const { eventId, childId, message } = this.body;
        super.uuid(eventId, 'Event Id');
        super.uuid(childId, 'Child Id');
        super.field(message, 'Message');
    }
}


module.exports = CommentValidator;
