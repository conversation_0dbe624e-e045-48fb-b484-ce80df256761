/* eslint-disable max-len */
const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const sinon = require('sinon');
const User = require('../../../models/user.model');
const jwt = require('jsonwebtoken');
const Organization = require('../../../models/organization.model');
const EventSignups = require('../../../models/eventSignup.model');
const Event = require('../../../models/event.model');
const Child = require('../../../models/child.model');
const PostView = require('../../../models/postView.model');
const RedisUtil = require('../../../util/redisUtil');
const Utils = require('../../../util/utilFunctions');
const AwsOpenSearchService = require('../../../util/opensearch');
const ConstantModel = require('../../../models/constant.model');
const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
/* User Token */
const user = {
    id: '95285616-3769-4b2e-b36b-898597b8146e',
    email: '<EMAIL>'
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'testingjwt', tokenOptionalInfo)
};

const children = [
    {
        associatedOrganizations: ['e9380c58-b172-468d-aff0-10a06f940d7a'],
        firstName: 'Test',
        lastName: 'User',
        zipCode: '12345',
        associatedColor: '#2772ED',
        createdAt: '2023-11-17T08:53:49.024Z',
        gender: 'boy',
        isDeleted: 0,
        dob: '2005-10-18T18:30:00.000Z',
        id: '0b69dd16-37ab-4843-a6af-11c81436e1cf',
        updatedAt: '2023-11-17T08:53:49.024Z'
    }
];

const events = [{
    eventScope: 'public',
    comments: [],
    fee: 100,
    eventType: 'event',
    title: 'Test Event',
    organizationId: 'e9380c58-b172-468d-aff0-10a06f940d7a',
    participantsLimit: 100,
    createdAt: '2023-11-16T06:42:01.315Z',
    isDeleted: 0,
    createdBy: '03c31d67-f28a-404a-801b-0d7e2f41b884',
    details: {
        venue: 'venue',
        startDateTime: '2000-11-10T18:30:00.000Z',
        recurringFrequency: 'week',
        faq: [],
        details: 'This is test event',
        isRecurring: true,
        endDateTime: '2000-11-19T18:30:00.000Z'
    },
    id: '66771020-42ae-4b62-a621-adb5c65772cc',
    volunteerSignupUrl: 'https://test-gmailsheet.com',
    updatedAt: '2023-11-16T06:42:01.315Z',
    status: 'unpublished'
}];

Utils.addCommonReqTokenForHMac(request);
const event = {
    eventScope: 'public',
    comments: [],
    fee: 100,
    eventType: 'event',
    title: 'Test Event',
    organizationId: 'e9380c58-b172-468d-aff0-10a06f940d7a',
    participantsLimit: 100,
    createdAt: '2023-11-16T06:42:01.315Z',
    isDeleted: 0,
    createdBy: '03c31d67-f28a-404a-801b-0d7e2f41b884',
    details: {
        venue: 'venue',
        startDateTime: '2000-11-10T18:30:00.000Z',
        recurringFrequency: 'week',
        faq: [],
        details: 'This is test event',
        isRecurring: true,
        endDateTime: '2000-11-19T18:30:00.000Z'
    },
    id: '66771020-42ae-4b62-a621-adb5c65772cc',
    volunteerSignupUrl: 'https://test-gmailsheet.com',
    updatedAt: '2023-11-16T06:42:01.315Z',
    status: 'unpublished'
};

const post = {
    eventType: 'post',
    isPost: true,
    title: 'Test Event',
    subTitle: 'Testing',
    organizationId: 'e9380c58-b172-468d-aff0-10a06f940d7a',
    createdBy: '03c31d67-f28a-404a-801b-0d7e2f41b884',
    id: '66771020-42ae-4b62-a621-adb5c65772cb',
    status: 'unpublished'
};

const dummyFreeMembershipProducts = [
    {
        'itemId': 0,
        'options': [
            {
                'optionId': 0,
                'variants': [
                    'Family Membership',
                    'Child Membership'
                ],
                'name': 'Membership option'
            }
        ],
        'optionPrices': [
            {
                'itemName': 'Membership',
                'id': '78aa5ce6-e82e-4d97-91d6-54e5dc659437',
                'itemCost': '0',
                'membershipOption': 'Child Membership',
                'membershipType': 'child'
            },
            {
                'itemName': 'Membership',
                'id': '5d5fa601-4165-41cf-80bb-0c5edb45b0d7',
                'itemCost': '0',
                'membershipOption': 'Family Membership',
                'membershipType': 'family'
            }
        ],
        'itemName': 'Membership'
    }
];

const fundraiser = {
    isFundraiser: true,
    title: 'Test Event',
    description: 'Testing',
    organizationId: 'e9380c58-b172-468d-aff0-10a06f940d7a',
    createdBy: '03c31d67-f28a-404a-801b-0d7e2f41b884',
    id: '66771020-42ae-4b62-a621-adb5c65772cb',
    status: 'unpublished',
    startDate: '2000-11-10T18:30:00.000Z',
    endDate: '2000-11-10T18:30:00.000Z',
    products: JSON.stringify(dummyFreeMembershipProducts)
};

describe('Feed List', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should handle error if token is not passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .get('/list')
                .set({ reqtoken: Utils.generateHmac('/list') })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and correct token is passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .get('/list')
                .set({ reqtoken: Utils.generateHmac('/list') })
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and is verified', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 0, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', role: 'org super admin' }]
            });
            request(process.env.BASE_URL)
                .get('/list')
                .set({ reqtoken: Utils.generateHmac('/list') })
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 423);
                    done();
                });
        });

        it('As a user I should validate the role before getting feed list', (done) => {
            getStub.resolves({
                status: 'active',
                isVerified: 1,
                isDeleted: 0,
                role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [
                    {
                        organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                        role: 'org super admin'
                    }
                ]
            });
            request(process.env.BASE_URL)
                .get('/list')
                .set({ reqtoken: Utils.generateHmac('/list') })
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 406);
                    done();
                });
        });

        it('As a user I should get feed list of all children by default', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, populate: () => { }, children });

            const scanStub = sinon.stub();
            const inStub = sinon.stub();
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const anotherWhereStub = sinon.stub();
            const anotherEqStub = sinon.stub();
            const execStub = sinon.stub();

            scanStub.returns({ in: inStub });
            inStub.returns({ where: whereStub });
            whereStub.returns({ eq: eqStub });
            eqStub.returns({ where: anotherWhereStub });
            anotherWhereStub.returns({ eq: anotherEqStub });
            anotherEqStub.returns({ exec: execStub });
            execStub.resolves(events);

            sinon.replace(Event, 'scan', scanStub);

            sinon.stub(Organization, 'get').resolves({ name: 'Test' });

            const signupScanStub = sinon.stub();
            const signupEqStub = sinon.stub();
            const signupExecStub = sinon.stub();

            signupScanStub.returns({ eq: signupEqStub });
            signupEqStub.returns({ exec: signupExecStub });
            signupExecStub.resolves([]);
            sinon.replace(EventSignups, 'scan', signupScanStub);

            request(process.env.BASE_URL)
                .get('/list')
                .set({ reqtoken: Utils.generateHmac('/list') })
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Feed List of specific child', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });
        it('As a user I should throw error if passed child id is not associated with me', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                populate: () => { }, children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca']
            });

            request(process.env.BASE_URL)
                .get('/list?childId=0b69dd16-37ab-4843-a6af-11c81436e1cf')
                .set({ reqtoken: Utils.generateHmac('/list?childId=0b69dd16-37ab-4843-a6af-11c81436e1cf') })
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get feed list of a specific child', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                populate: () => { }, children: ['0b69dd16-37ab-4843-a6af-11c81436e1cf']
            });

            const scanStub = sinon.stub();
            const inStub = sinon.stub();
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const anotherWhereStub = sinon.stub();
            const anotherEqStub = sinon.stub();
            const execStub = sinon.stub();

            scanStub.returns({ in: inStub });
            inStub.returns({ where: whereStub });
            whereStub.returns({ eq: eqStub });
            eqStub.returns({ where: anotherWhereStub });
            anotherWhereStub.returns({ eq: anotherEqStub });
            anotherEqStub.returns({ exec: execStub });
            execStub.resolves(events);

            sinon.replace(Event, 'scan', scanStub);

            sinon.stub(Organization, 'get').resolves({ name: 'Test' });

            const signupScanStub = sinon.stub();
            const signupEqStub = sinon.stub();
            const signupExecStub = sinon.stub();

            signupScanStub.returns({ eq: signupEqStub });
            signupEqStub.returns({ exec: signupExecStub });
            signupExecStub.resolves([]);
            sinon.replace(EventSignups, 'scan', signupScanStub);

            sinon.stub(Child, 'get').resolves({ associatedOrganizations: ['5f5f7e5f7e5f7e5f7e5f7e5f'] });

            request(process.env.BASE_URL)
                .get('/list?childId=0b69dd16-37ab-4843-a6af-11c81436e1cf')
                .set({ reqtoken: Utils.generateHmac('/list?childId=0b69dd16-37ab-4843-a6af-11c81436e1cf') })
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('My feeds list', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get feed list of all children by default where I am registered', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                children: ['0b69dd16-37ab-4843-a6af-11c81436e1cf']
            });

            const scanStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            scanStub.returns({ eq: eqStub });
            eqStub.returns({ exec: execStub });
            execStub.resolves([
                { id: '5f5f7e5f7e5f7e5f7e5f7e5f', eventId: '4f4f7e5f7e5f7e5f7e5f7e5f' },
                { id: '6f6f7e5f7e5f7e5f7e5f7e5f', eventId: '7f5f7e5f7e5f7e5f7e5f7e5f' }
            ]);

            sinon.replace(EventSignups, 'scan', scanStub);

            const eventScanStub = sinon.stub();
            const eventEqStub = sinon.stub();
            const whereStub = sinon.stub();
            const whereEqStub = sinon.stub();
            const anotherWhereStub = sinon.stub();
            const anotherEqStub = sinon.stub();
            const eventExecStub = sinon.stub();

            eventScanStub.returns({ eq: eventEqStub });
            eventEqStub.returns({ where: whereStub });
            whereStub.returns({ eq: whereEqStub });
            whereEqStub.returns({ where: anotherWhereStub });
            anotherWhereStub.returns({ eq: anotherEqStub });
            anotherEqStub.returns({ exec: eventExecStub });
            eventExecStub.resolves({ count: 1, toJSON: () => events });

            sinon.replace(Event, 'scan', eventScanStub);

            sinon.stub(Organization, 'get').resolves({ name: 'Test' });

            sinon.stub(Child, 'get').resolves(children[0]);

            request(process.env.BASE_URL)
                .get('/registered')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

        it('As a user I should get feed list of specific child', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                children: ['0b69dd16-37ab-4843-a6af-11c81436e1cf']
            });

            request(process.env.BASE_URL)
                .get('/registered?childId=0b69dd16-37ab-4843-a6af-11c81436e1cf')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

        it('As a user I should get error if childid is not associated with me', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                children: ['0b69dd16-37ab-4843-a6af-11c81436e1ce']
            });

            request(process.env.BASE_URL)
                .get('/registered?childId=0b69dd16-37ab-4843-a6af-11c81436e1cf')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('My feeds list empty', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get empty feed list If i am not registered to any events', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                children: ['0b69dd16-37ab-4843-a6af-11c81436e1cf']
            });

            const scanStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            scanStub.returns({ eq: eqStub });
            eqStub.returns({ exec: execStub });
            execStub.resolves([
                { id: '5f5f7e5f7e5f7e5f7e5f7e5f', eventId: '4f4f7e5f7e5f7e5f7e5f7e5f' },
                { id: '6f6f7e5f7e5f7e5f7e5f7e5f', eventId: '7f5f7e5f7e5f7e5f7e5f7e5f' }
            ]);

            sinon.replace(EventSignups, 'scan', scanStub);

            const eventScanStub = sinon.stub();
            const eventEqStub = sinon.stub();
            const whereStub = sinon.stub();
            const whereEqStub = sinon.stub();
            const anotherWhereStub = sinon.stub();
            const anotherEqStub = sinon.stub();
            const eventExecStub = sinon.stub();

            eventScanStub.returns({ eq: eventEqStub });
            eventEqStub.returns({ where: whereStub });
            whereStub.returns({ eq: whereEqStub });
            whereEqStub.returns({ where: anotherWhereStub });
            anotherWhereStub.returns({ eq: anotherEqStub });
            anotherEqStub.returns({ exec: eventExecStub });
            eventExecStub.resolves({ count: 0 });

            sinon.replace(Event, 'scan', eventScanStub);

            request(process.env.BASE_URL)
                .get('/registered')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    sinon.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Feed List Redis', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
            sinon.stub(ConstantModel, 'get').resolves({ value: 'v1' });
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should handle error if token is not passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .get('/events')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and correct token is passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .get('/events')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and is verified', (done) => {
            getStub.resolves({
                status: 'active',
                isVerified: 0,
                isDeleted: 0,
                role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [
                    {
                        organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                        role: 'org super admin'
                    }
                ]
            });
            request(process.env.BASE_URL)
                .get('/events')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 423);
                    done();
                });
        });

        it('As a user I should get error message if i dont have any children added', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: [] });

            request(process.env.BASE_URL)
                .get('/events')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error message if i dont have any children added and org is is passed', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: [] });

            request(process.env.BASE_URL)
                .get('/events?organizationId=organization-id1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get feed list of all children by default', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['5f5f7e5f7e5f7e5f7e5f7e5f'] });

            const userEvents = [];
            for (let i = 0; i < 10; i++) {
                userEvents.push(i % 2 === 0 ? JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED'
                    },
                    isSignedUp: false,
                    status: ''
                }) : JSON.stringify(`${i}`));
            }

            sinon.stub(RedisUtil, 'getMembersByScore').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                ...event,
                photoURL: 'https://test-gmailsheet.com',
                documentURLs: []
            }));

            request(process.env.BASE_URL)
                .get('/events')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getMembersByScore.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });

        it('As a user I should get feed and post list of all children by default', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['5f5f7e5f7e5f7e5f7e5f7e5f'] });

            const userEvents = [];
            for (let i = 0; i < 10; i++) {
                userEvents.push(i % 2 === 0 ? JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED'
                    },
                    isSignedUp: false,
                    status: '',
                    isPost: true
                }) : JSON.stringify(`${i}`));
            }

            sinon.stub(RedisUtil, 'getMembersByScore').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                ...event,
                photoURL: 'https://test-gmailsheet.com',
                documentURLs: []
            }));

            request(process.env.BASE_URL)
                .get('/events')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getMembersByScore.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });

        it('As a user I should get feed and fundraiser list of all children by default', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['5f5f7e5f7e5f7e5f7e5f7e5f'] });

            const userEvents = [];
            for (let i = 0; i < 10; i++) {
                userEvents.push(i % 2 === 0 ? JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED'
                    },
                    isSignedUp: false,
                    status: '',
                    isFundraiser: true
                }) : JSON.stringify(`${i}`));
            }

            sinon.stub(RedisUtil, 'getMembersByScore').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                ...event,
                photoURL: 'https://test-gmailsheet.com',
                documentURLs: []
            }));

            request(process.env.BASE_URL)
                .get('/events')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getMembersByScore.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });

        it('As a user I should get feed list of all children by default filtered by passed org id', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['5f5f7e5f7e5f7e5f7e5f7e5f'] });

            const userEvents = [];
            for (let i = 0; i < 10; i++) {
                userEvents.push(i % 2 === 0 ? JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED'
                    },
                    isSignedUp: false,
                    status: ''
                }) : JSON.stringify(`${i}`));
            }

            sinon.stub(RedisUtil, 'getAllMembersWithScores').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                ...event,
                photoURL: 'https://test-gmailsheet.com',
                documentURLs: [],
                organizationId: 'organization-id1'
            }));

            request(process.env.BASE_URL)
                .get('/events?organizationId=organization-id1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getAllMembersWithScores.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });

        it('As a user I should get feed list of all children by default and send next cursor and next childId', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['5f5f7e5f7e5f7e5f7e5f7e5f'] });

            const userEvents = [];
            for (let i = 0; i < 26; i++) {
                userEvents.push(i % 2 === 0 ? JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED'
                    },
                    isSignedUp: false,
                    status: ''
                }) : JSON.stringify(`${i}`));
            }

            sinon.stub(RedisUtil, 'getMembersByScore').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify(event));

            request(process.env.BASE_URL)
                .get('/events')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getMembersByScore.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });

        it('As a user I should get feed list filtered by passed organization id of all children by default and send next cursor and next childId', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['5f5f7e5f7e5f7e5f7e5f7e5f'] });

            const userEvents = [];
            for (let i = 0; i < 26; i++) {
                userEvents.push(i % 2 === 0 ? JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED'
                    },
                    isSignedUp: false,
                    status: ''
                }) : JSON.stringify(`${i}`));
            }

            sinon.stub(RedisUtil, 'getAllMembersWithScores').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({ ...event, organizationId: 'organization-id1' }));

            request(process.env.BASE_URL)
                .get('/events?organizationId=organization-id1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getAllMembersWithScores.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });

        it('As a user I should get feed list of next page by next cursor and next childId', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['5f5f7e5f7e5f7e5f7e5f7e5f'] });

            const userEvents = [];
            for (let i = 0; i < 8; i++) {
                userEvents.push(i % 2 === 0 ? JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED',
                        associatedColor: '#2772ED',
                        associatedImage: 'https://test-gmailsheet.com'
                    },
                    isSignedUp: false,
                    status: ''
                }) : JSON.stringify(`${i}`));
            }

            sinon.stub(RedisUtil, 'getMembersByScore').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify(event));

            request(process.env.BASE_URL)
                .get('/events?nextChildId=5f5f7e5f7e5f7e5f7e5f7e5f&nextIndex=1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getMembersByScore.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });

        it('As a user I should get feed list filtered by passed organizationId of next page by next cursor and next childId', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['5f5f7e5f7e5f7e5f7e5f7e5f'] });

            const userEvents = [];
            for (let i = 0; i < 8; i++) {
                userEvents.push(i % 2 === 0 ? JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED',
                        associatedColor: '#2772ED',
                        associatedImage: 'https://test-gmailsheet.com'
                    },
                    isSignedUp: false,
                    status: ''
                }) : JSON.stringify(`${i}`));
            }

            sinon.stub(RedisUtil, 'getAllMembersWithScores').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({ ...event, organizationId: 'organization-id1' }));

            request(process.env.BASE_URL)
                .get('/events?nextChildId=5f5f7e5f7e5f7e5f7e5f7e5f&nextIndex=1&organizationId=organization-id1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getAllMembersWithScores.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });

        it('As a user I should get error message of next childId passed is not associated with me', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['5f5f7e5f7e5f7e5f7e5f7e5a'] });

            request(process.env.BASE_URL)
                .get('/events?childId=5f5f7e5f7e5f7e5f7e5f7e5f&nextIndex=1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error message of next childId passed is not associated with me if orgIs is passed', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['5f5f7e5f7e5f7e5f7e5f7e5a'] });

            request(process.env.BASE_URL)
                .get('/events?nextChildId=5f5f7e5f7e5f7e5f7e5f7e5f&nextIndex=1&organizationId=organization-id1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error if passed child id is not associated with me', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca']
            });

            request(process.env.BASE_URL)
                .get('/events?childId=0b69dd16-37ab-4843-a6af-11c81436e1cf')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error if passed child id is not associated with me and orgId is passed', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca']
            });

            request(process.env.BASE_URL)
                .get('/events?childId=0b69dd16-37ab-4843-a6af-11c81436e1cf&organizationId=organization-id1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get feed list of all children without next cursor if list size is less than page size', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca']
            });

            const userEvents = [];
            for (let i = 0; i < 10; i++) {
                userEvents.push(i % 2 === 0 ? JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED'
                    },
                    isSignedUp: false,
                    status: ''
                }) : JSON.stringify(`${i}`));
            }

            sinon.stub(RedisUtil, 'getMembersByScore').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify(event));

            request(process.env.BASE_URL)
                .get('/events?childId=0b69dd16-37ab-4843-a6af-11c81436e1ca')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getMembersByScore.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });

        it('As a user I should get feed list filtered by organization id of all children without next cursor if list size is less than page size', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca']
            });

            const userEvents = [];
            for (let i = 0; i < 10; i++) {
                userEvents.push(i % 2 === 0 ? JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED'
                    },
                    isSignedUp: false,
                    status: ''
                }) : JSON.stringify(`${i}`));
            }

            sinon.stub(RedisUtil, 'getAllMembersWithScores').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({ ...event, organizationId: 'organization-id1' }));

            request(process.env.BASE_URL)
                .get('/events?childId=0b69dd16-37ab-4843-a6af-11c81436e1ca&organizationId=organization-id1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getAllMembersWithScores.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });

        it('As a user I should get feed list of all children with next cursor if list size is greater than page size', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca']
            });

            const userEvents = [];
            for (let i = 0; i < 26; i++) {
                userEvents.push(i % 2 === 0 ? JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED'
                    },
                    isSignedUp: false,
                    status: ''
                }) : JSON.stringify(`${i}`));
            }

            sinon.stub(RedisUtil, 'getMembersByScore').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify(event));

            request(process.env.BASE_URL)
                .get('/events?childId=0b69dd16-37ab-4843-a6af-11c81436e1ca')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getMembersByScore.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });

        it('As a user I should get feed list filtered by organization of all children with next cursor if list size is greater than page size', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca']
            });

            const userEvents = [];
            for (let i = 0; i < 26; i++) {
                userEvents.push(i % 2 === 0 ? JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED'
                    },
                    isSignedUp: false,
                    status: ''
                }) : JSON.stringify(`${i}`));
            }

            sinon.stub(RedisUtil, 'getAllMembersWithScores').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({ ...event, organizationId: 'organization-id1' }));

            request(process.env.BASE_URL)
                .get('/events?childId=0b69dd16-37ab-4843-a6af-11c81436e1ca&organizationId=organization-id1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getAllMembersWithScores.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('My Feed List Redis', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
            sinon.stub(ConstantModel, 'get').resolves({ value: 'v1' });
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get feed list of all children by default that have registered in events', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['5f5f7e5f7e5f7e5f7e5f7e5f'] });

            const userEvents = [];
            for (let i = 0; i < 10; i++) {
                userEvents.push(i % 2 === 0 ? JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED'
                    },
                    isSignedUp: false,
                    status: ''
                }) : JSON.stringify(`${i}`));
            }

            sinon.stub(RedisUtil, 'getMembersByScore').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify(event));

            request(process.env.BASE_URL)
                .get('/registered-events')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getMembersByScore.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });

        it('As a user I should get feed list filtered by organization of all children by default that have registered in events', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['5f5f7e5f7e5f7e5f7e5f7e5f'] });

            const userEvents = [];
            for (let i = 0; i < 10; i++) {
                userEvents.push(i % 2 === 0 ? JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED'
                    },
                    isSignedUp: false,
                    status: ''
                }) : JSON.stringify(`${i}`));
            }

            sinon.stub(RedisUtil, 'getAllMembersWithScores').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({ ...event, organizationId: 'organization-id1' }));

            request(process.env.BASE_URL)
                .get('/registered-events?organizationId=organization-id1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getAllMembersWithScores.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });

        it('As a user I should get error if passed child id is not associated with me for registered events', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca']
            });

            request(process.env.BASE_URL)
                .get('/registered-events?childId=0b69dd16-37ab-4843-a6af-11c81436e1cf')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error message if i dont have any children added', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: [] });

            request(process.env.BASE_URL)
                .get('/registered-events')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get feed list of all children without next cursor if list size is less than page size for registered events', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca']
            });

            const userEvents = [];
            for (let i = 0; i < 10; i++) {
                userEvents.push(i % 2 === 0 ? JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED'
                    },
                    isSignedUp: false,
                    status: ''
                }) : JSON.stringify(`${i}`));
            }

            sinon.stub(RedisUtil, 'getMembersByScore').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify(event));

            request(process.env.BASE_URL)
                .get('/registered-events?childId=0b69dd16-37ab-4843-a6af-11c81436e1ca')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getMembersByScore.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });

        it('As a user I should get feed list filtered by organization of all children without next cursor if list size is less than page size for registered events', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca']
            });

            const userEvents = [];
            for (let i = 0; i < 10; i++) {
                userEvents.push(i % 2 === 0 ? JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED'
                    },
                    isSignedUp: false,
                    status: ''
                }) : JSON.stringify(`${i}`));
            }

            sinon.stub(RedisUtil, 'getAllMembersWithScores').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({ ...event, organizationId: 'organization-id1' }));

            request(process.env.BASE_URL)
                .get('/registered-events?childId=0b69dd16-37ab-4843-a6af-11c81436e1ca&organizationId=organization-id1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getAllMembersWithScores.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });


        it('As a user I should get feed list of next page by next cursor and next childId', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['5f5f7e5f7e5f7e5f7e5f7e5f'] });

            const userEvents = [];
            for (let i = 0; i < 8; i++) {
                userEvents.push(i % 2 === 0 ? JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED'
                    },
                    isSignedUp: false,
                    status: ''
                }) : JSON.stringify(`${i}`));
            }

            sinon.stub(RedisUtil, 'getMembersByScore').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify(event));

            request(process.env.BASE_URL)
                .get('/registered-events?nextChildId=5f5f7e5f7e5f7e5f7e5f7e5f&nextIndex=1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getMembersByScore.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });

        it('As a user I should get feed list filtered by organization of next page by next cursor and next childId', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['5f5f7e5f7e5f7e5f7e5f7e5f'] });

            const userEvents = [];
            for (let i = 0; i < 8; i++) {
                userEvents.push(i % 2 === 0 ? JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED'
                    },
                    isSignedUp: false,
                    status: ''
                }) : JSON.stringify(`${i}`));
            }

            sinon.stub(RedisUtil, 'getAllMembersWithScores').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({ ...event, organizationId: 'organization-id1' }));

            request(process.env.BASE_URL)
                .get('/registered-events?nextChildId=5f5f7e5f7e5f7e5f7e5f7e5f&nextIndex=1&organizationId=organization-id1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getAllMembersWithScores.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });

        it('As a user I should get error message of next childId passed is not associated with me', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['5f5f7e5f7e5f7e5f7e5f7e5a'] });

            request(process.env.BASE_URL)
                .get('/registered-events?childId=5f5f7e5f7e5f7e5f7e5f7e5f&nextIndex=1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Feed Details', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
            sinon.stub(ConstantModel, 'get').resolves({ value: 'v1' });
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get error if event id is not passed', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1 });

            request(process.env.BASE_URL)
                .get('/details')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error if event id is not passed', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1 });

            request(process.env.BASE_URL)
                .get('/details')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error if passed child id is not associated with me', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['0b69dd16-37ab-4843-a6af-11c81436e1cb'] });

            request(process.env.BASE_URL)
                .get('/details?eventId=66771020-42ae-4b62-a621-adb5c65772cc&childId=0b69dd16-37ab-4843-a6af-11c81436e1ca&score=2025-11-10T00:00:00.000Z')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error if feed details doesnt exists', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca'] });

            sinon.stub(RedisUtil, 'getHashValue').resolves(null);

            request(process.env.BASE_URL)
                .get('/details?eventId=66771020-42ae-4b62-a621-adb5c65772cc&childId=0b69dd16-37ab-4843-a6af-11c81436e1ca&score=2025-11-10T00:00:00.000Z')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 404);
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });

        it('As a user I should get feed details', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca'] });

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                ...event,
                products: [
                    {
                        'itemId': 0,
                        'options': [{ 'optionId': '123' }],
                        'data': [{
                            'itemName': 'Test',
                            'id': '123',
                            'itemCost': '35'
                        }],
                        'images': ['test.jpg'],
                        'imageCount': 1,
                        'itemName': 'Test',
                        'imageURLs': ['test image']
                    },
                    {
                        'itemId': 1,
                        'options': [{ 'optionId': '123' }],
                        'data': [{ 'itemName': 'Test', 'id': '123', 'itemCost': '35' }],
                        'images': [],
                        'imageCount': 0
                    }
                ]
            }));
            const getElementsByScoreStub = sinon.stub(RedisUtil, 'getElementsByScore');
            getElementsByScoreStub
                .onCall(11)
                .resolves([
                    JSON.stringify({
                        eventId: event.id,
                        child: {
                            id: '1',
                            firstName: 'Test',
                            lastName: 'User',
                            associatedColorOrImage: '#2772ED'
                        },
                        isSignedUp: false,
                        status: ''
                    })
                ]);
            getElementsByScoreStub.resolves([]);

            sinon.stub(Organization, 'get').resolves({
                id: event.organizationId,
                name: 'Test Organization'
            });

            request(process.env.BASE_URL)
                .get('/details?eventId=66771020-42ae-4b62-a621-adb5c65772cc&childId=0b69dd16-37ab-4843-a6af-11c81436e1ca&score=2025-11-10T00:00:00.000Z')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getHashValue.restore();
                    Organization.get.restore();
                    getElementsByScoreStub.restore();
                    done();
                });
        });

        it('As a user I should get feed details for public urls', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca'] });

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify(event));
            sinon.stub(RedisUtil, 'getElementsByScore')
                .onCall(1)
                .resolves([JSON.stringify({
                    eventId: event.id,
                    childId: '0b69dd16-37ab-4843-a6af-11c81436e1ca',
                    isSignedUp: false,
                    status: ''
                })])
                .resolves([]);

            sinon.stub(Organization, 'get').resolves({
                id: event.organizationId,
                name: 'Test Organization'
            });

            request(process.env.BASE_URL)
                .get('/details?eventId=66771020-42ae-4b62-a621-adb5c65772cc')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getHashValue.restore();
                    RedisUtil.getElementsByScore.restore();
                    Organization.get.restore();
                    done();
                });
        });

        it('As a user I should get feed details for public urls for multiple feeds', (done) => {
            getStub.resolves({ id: 'user-id', status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca'] });

            const getHashValueStub = sinon.stub(RedisUtil, 'getHashValue');

            getHashValueStub.withArgs(Utils.getChildDetailsKey({ versionPrefix: 'v1', childId: '0b69dd16-37ab-4843-a6af-11c81436e1ca' }))
                .resolves(JSON.stringify({
                    id: '0b69dd16-37ab-4843-a6af-11c81436e1ca',
                    firstName: 'Test',
                    lastName: 'User',
                    associatedColorOrImage: '#2772ED'
                }));
            getHashValueStub.withArgs(Utils.getChildDetailsKey({ versionPrefix: 'v1', childId: '0b69dd16-37ab-4843-a6af-11c81436e1cb' }))
                .resolves(JSON.stringify({
                    id: '0b69dd16-37ab-4843-a6af-11c81436e1cb',
                    firstName: 'Test',
                    lastName: 'User',
                    associatedColorOrImage: '#2772ED',
                    photoURL: 'https://test.com'
                }));
            getHashValueStub.resolves(JSON.stringify(event));


            sinon.stub(RedisUtil, 'getElementsByScore').resolves([
                JSON.stringify({
                    eventId: event.id,
                    childId: '0b69dd16-37ab-4843-a6af-11c81436e1ca',
                    isSignedUp: false,
                    status: ''
                }),
                JSON.stringify({
                    eventId: event.id,
                    childId: '0b69dd16-37ab-4843-a6af-11c81436e1cb',
                    isSignedUp: true,
                    status: 'pending'
                })]);


            sinon.stub(Organization, 'get').resolves({
                id: event.organizationId,
                name: 'Test Organization'
            });

            request(process.env.BASE_URL)
                .get('/details?eventId=66771020-42ae-4b62-a621-adb5c65772cc')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getHashValue.restore();
                    RedisUtil.getElementsByScore.restore();
                    Organization.get.restore();
                    done();
                });
        });

        it('As a user I should get feed details with Post', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca'] });
            const childIdForPost = '0b69dd16-37ab-4843-a6af-11c81436e1ca';
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify(post));
            sinon.stub(RedisUtil, 'getElementsByScore').resolves([JSON.stringify({
                eventId: post.id,
                child: {
                    id: '1',
                    firstName: 'Test',
                    lastName: 'User',
                    associatedColorOrImage: '#2772ED'
                },
                isSignedUp: false,
                status: ''
            })]);
            sinon.stub(RedisUtil, 'setHashValue').resolves([JSON.stringify({
                postId: post.id,
                chaildId: childIdForPost
            })]);
            sinon.stub(Organization, 'get').resolves({
                id: post.organizationId,
                name: 'Test Organization'
            });
            sinon.stub(PostView, 'create').resolves();

            request(process.env.BASE_URL)
                .get(`/details?eventId=${post.id}&childId=${childIdForPost}&score=2025-11-10T00:00:00.000Z&isPost=true`)
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getHashValue.restore();
                    RedisUtil.getElementsByScore.restore();
                    Organization.get.restore();
                    done();
                });
        });

        it('As a user I should get feed details with fundraiser with family membership enabled', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca'],
                membershipsPurchased: [{ organizationId: 'org-id', membershipType: 'family', startDate: '11/10/2000', endDate: '11/18/2027', membershipId: '0' }]
            });
            sinon.stub(Child, 'get').resolves({
                id: 'childId1', membershipsPurchased:
                    [{ organizationId: 'org-id', membershipType: 'family', startDate: '11/10/2000', endDate: '11/18/2027', membershipId: '0' }]
            });
            const childIdForFundraiser = '0b69dd16-37ab-4843-a6af-11c81436e1ca';
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                ...fundraiser, isMembershipEnabled: true, organizationId: 'org-id', membershipBenefitDetails:
                    { benefitDiscount: JSON.stringify([{ id: 0 }]) }
            }));
            sinon.stub(RedisUtil, 'getElementsByScore').resolves([JSON.stringify({
                fundraiserId: fundraiser.id,
                child: {
                    id: '1',
                    firstName: 'Test',
                    lastName: 'User',
                    associatedColorOrImage: '#2772ED'
                },
                isSignedUp: false,
                status: ''
            })]);
            sinon.stub(Organization, 'get').resolves({
                id: fundraiser.organizationId,
                name: 'Test Organization'
            });

            request(process.env.BASE_URL)
                .get(`/details?eventId=${fundraiser.id}&childId=${childIdForFundraiser}&score=2025-11-10T00:00:00.000Z&isFundraiser=true&isMyFeed=false`)
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getHashValue.restore();
                    RedisUtil.getElementsByScore.restore();
                    Organization.get.restore();
                    Child.get.restore();
                    done();
                });
        });

        it('As a user I should get signed up feed details with fundraiser with family membership enabled', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca'],
                membershipsPurchased: [{ organizationId: 'org-id', membershipType: 'family', startDate: '11/10/2000', endDate: '11/18/2027', membershipId: '0' }]
            });
            sinon.stub(Child, 'get').resolves({
                id: 'childId1', membershipsPurchased:
                    [{ organizationId: 'org-id', membershipType: 'family', startDate: '11/10/2000', endDate: '11/18/2027', membershipId: '0' }]
            });
            const childIdForFundraiser = '0b69dd16-37ab-4843-a6af-11c81436e1ca';
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                ...fundraiser, isMembershipEnabled: true, organizationId: 'org-id', membershipBenefitDetails:
                    { benefitDiscount: JSON.stringify([{ id: 0 }]) }
            }));
            sinon.stub(RedisUtil, 'getElementsByScore').resolves([JSON.stringify({
                fundraiserId: fundraiser.id,
                child: {
                    id: '1',
                    firstName: 'Test',
                    lastName: 'User',
                    associatedColorOrImage: '#2772ED'
                },
                isSignedUp: true,
                status: 'pending',
                purchasedProducts: JSON.stringify([{
                    id: '78aa5ce6-e82e-4d97-91d6-54e5dc659437'
                }])
            })]);
            sinon.stub(Organization, 'get').resolves({
                id: fundraiser.organizationId,
                name: 'Test Organization'
            });

            request(process.env.BASE_URL)
                .get(`/details?eventId=${fundraiser.id}&childId=${childIdForFundraiser}&score=2025-11-10T00:00:00.000Z&isFundraiser=true&isMyFeed=false`)
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getHashValue.restore();
                    RedisUtil.getElementsByScore.restore();
                    Organization.get.restore();
                    Child.get.restore();
                    done();
                });
        });

        it('As a user I should get feed details with fundraiser and child not found', (done) => {
            const childIdForFundraiser = '0b69dd16-37ab-4843-a6af-11c81436e1ca';
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca'],
                membershipsPurchased: []
            });
            sinon.stub(Child, 'get').resolves(null);
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({ ...fundraiser, isMembershipEnabled: true, organizationId: 'org-id' }));
            sinon.stub(RedisUtil, 'getElementsByScore').resolves([JSON.stringify({
                fundraiserId: fundraiser.id,
                child: {
                    id: '1',
                    firstName: 'Test',
                    lastName: 'User',
                    associatedColorOrImage: '#2772ED'
                },
                isSignedUp: false,
                status: ''
            })]);
            sinon.stub(Organization, 'get').resolves({
                id: fundraiser.organizationId,
                name: 'Test Organization'
            });

            request(process.env.BASE_URL)
                .get(`/details?eventId=${fundraiser.id}&childId=${childIdForFundraiser}&score=2025-11-10T00:00:00.000Z&isFundraiser=true&isMyFeed=false`)
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getHashValue.restore();
                    RedisUtil.getElementsByScore.restore();
                    Organization.get.restore();
                    Child.get.restore();
                    done();
                });
        });

        it('As a user I should get feed details with fundraiser and is my feed', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca'] });
            const childIdForFundraiser = '0b69dd16-37ab-4843-a6af-11c81436e1ca';
            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                ...fundraiser,
                products: '[{"itemId":0,"options":[{"optionId":"123"}],"data":[{"itemName":"Test","id":"123","itemCost":"35"}],"images":["test.jpg"],"imageCount":1,"itemName":"Test","imageURLs":["test image"]}]'
            }));
            sinon.stub(RedisUtil, 'getElementsByScore').resolves([JSON.stringify({
                fundraiserId: fundraiser.id,
                child: {
                    id: '1',
                    firstName: 'Test',
                    lastName: 'User',
                    associatedColorOrImage: '#2772ED'
                },
                isSignedUp: false,
                status: '',
                fundraiserSignupId: childIdForFundraiser
            })]);
            sinon.stub(Organization, 'get').resolves({
                id: fundraiser.organizationId,
                name: 'Test Organization'
            });

            request(process.env.BASE_URL)
                .get(`/details?eventId=${fundraiser.id}&childId=${childIdForFundraiser}&score=2025-11-10T00:00:00.000Z&isFundraiser=true&isMyFeed=true&fundraiserSignupId=${childIdForFundraiser}`)
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getHashValue.restore();
                    RedisUtil.getElementsByScore.restore();
                    Organization.get.restore();
                    done();
                });
        });

        it('As a user I should get error if feed details doesnt exists', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca'] });

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify(event));
            sinon.stub(RedisUtil, 'getElementsByScore').resolves([JSON.stringify({
                eventId: '1',
                child: {
                    id: '1',
                    firstName: 'Test',
                    lastName: 'User',
                    associatedColorOrImage: '#2772ED'
                },
                isSignedUp: false,
                status: ''
            })]);

            request(process.env.BASE_URL)
                .get('/details?eventId=66771020-42ae-4b62-a621-adb5c65772cc&childId=0b69dd16-37ab-4843-a6af-11c81436e1ca&score=2025-11-10T00:00:00.000Z')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 404);
                    RedisUtil.getHashValue.restore();
                    RedisUtil.getElementsByScore.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Calendar event list', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
            sinon.stub(ConstantModel, 'get').resolves({ value: 'v1' });
        });

        after(async () => {
            sinon.restore();
        });
        it('As a user I should get error message if I dont have any children added', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: [] });

            request(process.env.BASE_URL)
                .get('/calendar-events')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error message if passed date is not valid', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: [] });

            request(process.env.BASE_URL)
                .get('/calendar-events?date=13/13/2020')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get event list of all children in calendar', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['5f5f7e5f7e5f7e5f7e5f7e5f'] });

            const userEvents = [];
            for (let i = 0; i < 10; i++) {
                userEvents.push(JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED',
                        associatedColor: '#2772ED',
                        associatedImage: 'https://test-gmailsheet.com'
                    },
                    isSignedUp: false,
                    status: 'published'
                }));
            }
            userEvents.push(JSON.stringify({
                eventId: '10',
                child: {
                    id: '10',
                    firstName: 'Test',
                    lastName: 'User',
                    associatedColorOrImage: '#2772ED',
                    associatedColor: '#2772ED'
                },
                isSignedUp: true,
                status: 'approved'
            }));
            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(
                JSON.stringify({
                    ...event,
                    startDateTime: '2020-11-10T00:00:00.000Z',
                    endDateTime: '2020-11-10T00:10:00.000Z',
                    eventType: 'calendar',
                    photoURL: 'https://test-gmailsheet.com',
                    documentURLs: []
                })
            );

            request(process.env.BASE_URL)
                .get('/calendar-events')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getElementsOfSortedSetByScore.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });

        it('As a user I should get event list of all children in calendar with calendar events from another key', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['5f5f7e5f7e5f7e5f7e5f7e5f'] });

            const userEvents = [];
            for (let i = 0; i < 10; i++) {
                userEvents.push(JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED',
                        associatedColor: '#2772ED',
                        associatedImage: 'https://test-gmailsheet.com'
                    },
                    isSignedUp: false,
                    status: 'published'
                }));
            }
            userEvents.push(JSON.stringify({
                eventId: '10',
                child: {
                    id: '10',
                    firstName: 'Test',
                    lastName: 'User',
                    associatedColorOrImage: '#2772ED',
                    associatedColor: '#2772ED'
                },
                isSignedUp: true,
                status: 'approved'
            }));
            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(
                JSON.stringify({
                    ...event,
                    startDateTime: '2020-11-10T00:00:00.000Z',
                    endDateTime: '2020-11-15T00:10:00.000Z',
                    photoURL: 'https://test-gmailsheet.com',
                    documentURLs: [],
                    eventType: 'calendar'
                })
            );

            request(process.env.BASE_URL)
                .get('/calendar-events?childId=5f5f7e5f7e5f7e5f7e5f7e5f')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getElementsOfSortedSetByScore.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });

        it('As a user I should get event list of all children in calendar with registered events from another key', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['5f5f7e5f7e5f7e5f7e5f7e5f'] });

            const userEvents = [];
            for (let i = 0; i < 10; i++) {
                userEvents.push(JSON.stringify({
                    eventId: `${i}`,
                    child: {
                        id: `${i}`,
                        firstName: 'Test',
                        lastName: 'User',
                        associatedColorOrImage: '#2772ED',
                        associatedColor: '#2772ED',
                        associatedImage: 'https://test-gmailsheet.com'
                    },
                    isSignedUp: false,
                    status: 'published'
                }));
            }
            userEvents.push(JSON.stringify({
                eventId: '10',
                child: {
                    id: '10',
                    firstName: 'Test',
                    lastName: 'User',
                    associatedColorOrImage: '#2772ED',
                    associatedColor: '#2772ED'
                },
                isSignedUp: true,
                status: 'approved'
            }));
            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves(userEvents);
            sinon.stub(RedisUtil, 'getHashValue').resolves(
                JSON.stringify({
                    ...event,
                    startDateTime: '2020-11-10T00:00:00.000Z',
                    endDateTime: '2020-11-15T00:10:00.000Z',
                    photoURL: 'https://test-gmailsheet.com',
                    documentURLs: []
                })
            );

            request(process.env.BASE_URL)
                .get(`/calendar-events?date=${MOMENT().month(9).format('MM/DD/YYYY')}`)
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    RedisUtil.getElementsOfSortedSetByScore.restore();
                    RedisUtil.getHashValue.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Search Feeds', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get error message if searchValue is empty', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: [] });
            request(process.env.BASE_URL)
                .get('/search?searchValue}')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should not search for my child if child is not valid', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca']
            });
            request(process.env.BASE_URL)
                .get('/search?searchValue=Test&childId=0b69dd16-37ab-4843-a6af-11c81436e1c1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should search for my child', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca']
            });
            sinon.stub(AwsOpenSearchService, 'getAllEventsWithChild').resolves({});

            request(process.env.BASE_URL)
                .get('/search?searchValue=Test&childId=0b69dd16-37ab-4843-a6af-11c81436e1ca')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    AwsOpenSearchService.getAllEventsWithChild.restore();
                    done();
                });
        });

        it('As a user I should search for my all children', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, isDeleted: 0, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                children: ['0b69dd16-37ab-4843-a6af-11c81436e1ca']
            });
            sinon.stub(AwsOpenSearchService, 'getAllEventsWithChild').resolves({});

            request(process.env.BASE_URL)
                .get('/search?searchValue=Test')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    AwsOpenSearchService.getAllEventsWithChild.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
