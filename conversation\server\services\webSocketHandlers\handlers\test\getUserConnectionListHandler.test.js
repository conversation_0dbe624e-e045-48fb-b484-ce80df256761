const { assert } = require('chai');
const sinon = require('sinon');
const User = require('../../../../models/user.model');
const Child = require('../../../../models/child.model');
const Organization = require('../../../../models/organization.model');
const handleGetPersonalMessage = require('../../handleGetPersonalMessage');
const PersonalConversation = require('../../../../models/personalConversation.model');

describe('getUserConnectionListHandler', () => {
    it('should throw error if user does not exist', async () => {
        sinon.stub(User, 'query').returns({
            eq: sinon.stub().returns({
                exec: sinon.stub().resolves([])
            })
        });

        const response = await handleGetPersonalMessage({
            body: {
                actionType: 'GET_USER_CONNECTIONS_LIST',
                userId: 'userId1'
            }
        });

        assert.equal(response.statusCode, 404);
        sinon.restore();
    });

    it('should return empty connections array if user have no children', async () => {
        sinon.stub(User, 'query').returns({
            eq: sinon.stub().returns({
                exec: sinon.stub().resolves([
                    {
                        children: []
                    }
                ])
            })
        });

        const response = await handleGetPersonalMessage({
            body: {
                actionType: 'GET_USER_CONNECTIONS_LIST',
                userId: 'userId1'
            }
        });

        const parsedResponse = JSON.parse(response.data);

        assert.equal(response.statusCode, 200);
        assert.equal(parsedResponse.connections.length, 0);
        sinon.restore();
    });

    it('should return empty connections array if user have children but no connections', async () => {
        sinon.stub(User, 'query').returns({
            eq: sinon.stub().returns({
                exec: sinon.stub().resolves([{ children: ['childId1'] }])
            })
        });

        sinon.stub(Child, 'batchGet').resolves([
            {
                id: 'childId1',
                school: 'schoolId1'
            }
        ]);

        const response = await handleGetPersonalMessage({
            body: {
                actionType: 'GET_USER_CONNECTIONS_LIST',
                userId: 'userId1'
            }
        });

        const parsedResponse = JSON.parse(response.data);

        assert.equal(response.statusCode, 200);
        assert.equal(parsedResponse.connections.length, 0);
        sinon.restore();
    });

    it('should return empty connections array if user have children with connections not accepted', async () => {
        sinon.stub(User, 'query').returns({
            eq: sinon.stub().returns({
                exec: sinon.stub().resolves([{ children: ['childId1'] }])
            })
        });

        sinon.stub(Child, 'batchGet').resolves([
            {
                id: 'childId1',
                school: 'schoolId1',
                connections: [
                    {
                        childId: 'childId1',
                        status: 'pending'
                    }
                ]
            }
        ]);

        const response = await handleGetPersonalMessage({
            body: {
                actionType: 'GET_USER_CONNECTIONS_LIST',
                userId: 'userId1'
            }
        });

        const parsedResponse = JSON.parse(response.data);

        assert.equal(response.statusCode, 200);
        assert.equal(parsedResponse.connections.length, 0);
        sinon.restore();
    });

    it('should return connections array if user have children with connections accepted', async () => {
        sinon.stub(User, 'query').returns({
            eq: sinon.stub().returns({
                exec: sinon.stub()
                    .onFirstCall().resolves([{ children: ['childId1', 'childId2'] }])
                    .onSecondCall().resolves([{ id: 'guardianId1', children: [] }])
                    .onThirdCall().resolves([{ id: 'guardianId2', children: [] }])
            })
        });

        sinon.stub(PersonalConversation, 'get')
            .onFirstCall().resolves({ conversationId: 'conversationId1' })
            .onSecondCall().resolves(null)
            .onThirdCall().resolves(null);

        sinon.stub(Child, 'batchGet').resolves([
            {
                id: 'childId1',
                school: 'schoolId1',
                connections: [
                    {
                        childId: 'childId1',
                        status: 'connected'
                    }
                ],
                guardians: ['guardianId1'],
                photoURL: 'photoURL1'
            },
            {
                id: 'childId2',
                school: 'schoolId2',
                homeRoom: 'homeRoomId2',
                connections: [
                    {
                        childId: 'childId2',
                        status: 'connected'
                    }
                ],
                guardians: ['guardianId1', 'guardianId2']
            },
            {
                id: 'childId3',
                school: 'schoolId3'
            }
        ]);

        sinon.stub(Organization, 'batchGet').resolves([
            {
                id: 'schoolId1',
                name: 'schoolName1'
            },
            {
                id: 'schoolId2',
                name: 'schoolName2'
            },
            {
                id: 'homeRoomId2',
                name: 'homeRoomName2'
            }
        ]);

        const response = await handleGetPersonalMessage({
            body: {
                actionType: 'GET_USER_CONNECTIONS_LIST',
                userId: 'userId1'
            }
        });

        const parsedResponse = JSON.parse(response.data);

        assert.equal(response.statusCode, 200);
        assert.equal(parsedResponse.connections.length, 2);
        sinon.restore();
    });

    it('should handle error in handleGetMessage', async () => {
        sinon.stub(User, 'query').throws(new Error('test error'));

        const response = await handleGetPersonalMessage({
            body: {
                actionType: 'GET_USER_CONNECTIONS_LIST',
                userId: 'userId1'
            }
        });

        assert.equal(response.statusCode, 500);
        sinon.restore();
    });
});
