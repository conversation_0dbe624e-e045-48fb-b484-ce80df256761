const GeneralError = require('../../util/GeneralError');
const validation = require('../../util/validation');
const CONSTANTS = require('../../util/constants');

/**
 * Class represents validations for AI Companion Group.
 */
class AiCompanionGroupValidator extends validation {
    constructor ({ body, query, locale }) {
        super(locale);
        this.body = body;
        this.locale = locale;
        this.query = query;
    }

    /**
     * @desc This function is being used to validate add/update AI Companion Group Feedback
     * <AUTHOR>
     * @since 11/06/2025
     */
    validateAddUpdateAiCompanionGroupFeedback () {
        const { aiResponseMessageId, userQueryMessageId, groupId, feedback } = this.body;
        super.field(aiResponseMessageId, 'AI Response Message Id');
        super.uuid(aiResponseMessageId, 'AI Response Message Id');
        super.field(userQueryMessageId, 'User Query Message Id');
        super.uuid(userQueryMessageId, 'User Query Message Id');
        super.field(groupId, 'Group Id');
        super.uuid(groupId, 'Group Id');
        super.field(feedback, 'Feedback');
        super.enum(feedback, CONSTANTS.AI_COMPANION_FEEDBACK_VALUES, 'Feedback');
        if (aiResponseMessageId === userQueryMessageId) {
            throw new GeneralError(this.locale(MESSAGES.AI_COMPANION_GROUP_FEEDBACK_NOT_FOUND), 400);
        }
    }

    /**
     * @desc This function is being used to validate get AI Companion Group Feedback List
     * <AUTHOR>
     * @since 11/06/2025
     */
    validateGetAiCompanionGroupFeedbackList () {
        const { groupId, startAiResponseMessageId, startUserId } = this.query;
        super.field(groupId, 'Group Id');
        super.uuid(groupId, 'Group Id');
        if (startAiResponseMessageId) {
            super.field(startAiResponseMessageId, 'Start AI Response Message Id');
            super.uuid(startAiResponseMessageId, 'Start AI Response Message Id');
            super.field(startUserId, 'Start User Id');
        }
    }
}

module.exports = AiCompanionGroupValidator;
