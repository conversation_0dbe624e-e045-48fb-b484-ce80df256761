
const FeedService = require('./feedService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for feed routes.
 */
class FeedController {
    /**
     * @desc This function is being used to get list of fee
     * <AUTHOR>
     * @since 15/11/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async getFeedList (req, res) {
        try {
            const data = await FeedService.getFeedList(req, res.locals.user);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error fetching feed list in getFeedList: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    static async getRegisteredFeeds (req, res) {
        try {
            const data = await FeedService.getRegisteredFeeds(req, res.locals.user);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error fetching registered feed list in getRegisteredFeeds:', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to get list of fee
     * <AUTHOR>
     * @since 15/11/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async getFeedListRedis (req, res) {
        try {
            const data = await FeedService.getFeedListRedis(req, res.locals.user);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error fetching feed list in getFeedListRedis:', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    static async getRegisteredFeedsRedis (req, res) {
        try {
            const data = await FeedService.getRegisteredFeedsRedis(req, res.locals.user);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error fetching registered feed list in getRegisteredFeedsRedis:', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to get list of fee
     * <AUTHOR>
     * @since 15/11/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async getFeedDetails (req, res) {
        try {
            const data = await FeedService.getFeedDetails(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in fetching feed details in getFeedDetails:', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to get guest signup feed details
     * <AUTHOR>
     * @since 08/07/2025
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async getGuestSignupFeedDetails (req, res) {
        try {
            const data = await FeedService.getGuestSignupFeedDetails(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in fetching guest signup feed details in getGuestSignupFeedDetails:', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is used to get list of calendar feeds from Redis
     * <AUTHOR>
     * @since 05/12/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async getCalendarFeedsFromRedis (req, res) {
        try {
            const data = await FeedService.getCalendarFeedsFromRedis(req, res.locals.user);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error fetching calendar event list in getCalendarFeedsFromRedis:', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is used to search feed list from OpenSearch
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async searchFeed (req, res) {
        try {
            const data = await FeedService.searchFeed(req, res.locals.user);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error fetching search feed in searchFeed:', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = FeedController;
