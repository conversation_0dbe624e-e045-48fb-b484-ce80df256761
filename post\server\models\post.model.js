const dynamoose = require('dynamoose');
const { v4: uuidv4 } = require('uuid');

const postSchema = new dynamoose.Schema({
    id: {
        hashKey: true,
        type: String,
        default: () => uuidv4()
    },
    title: {
        type: String,
        required: true
    },
    subTitle: {
        type: String,
        required: true
    },
    content: {
        type: String,
        required: true
    },
    publishedDate: {
        type: Date,
        required: false
    },
    status: {
        type: String,
        enum: ['published', 'draft'],
        default: 'draft'
    },
    organizationId: {
        type: String,
        required: true,
        index: {
            name: 'organizationId-index',
            global: true,
            project: true
        }
    },
    createdBy: {
        type: String,
        required: true
    },
    updatedBy: {
        type: String
    },
    isDeleted: {
        type: Number,
        enum: [0, 1],
        default: 0
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('Posts', postSchema);
