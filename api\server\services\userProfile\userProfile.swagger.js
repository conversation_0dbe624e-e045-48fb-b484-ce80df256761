/**
 *  routes and schema for User
 */

/**
 * @openapi
 * components:
 *  schemas:
 *      successUserDetails:
 *          type: object
 *          properties:
 *              status:
 *                  type: boolean
 *                  description: status if user is present
 *              data:
 *                  type: string
 *                  description: details of the user
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: true
 *              data:
 *                  id : id
 *                  firstName : Sam
 *                  lastName: Jones
 *                  email : email
 *                  phoneNumber : number
 *                  countryCode : country code
 *                  photoURL : url
 *                  role: 1
 *                  isVerified: 1
 *                  children: [Children]
 *                  associatedOrganizations: [associatedOrganizations]
 *              message: Success
 *
 *      changePassword:
 *          type: object
 *          properties:
 *              oldPassword:
 *                  type: string
 *                  description: The old password
 *              newPassword:
 *                  type: string
 *                  description: The new password
 *          example:
 *              oldPassword: "oldPassword123"
 *              newPassword: "newPassword123"
 *
 *      successDeleteUser:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: "Success"
 *
 *      successChangePassword:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: "Password changed successfully"
 *
 *      successConnectionsList:
 *          type: array
 *          items:
 *                  type: object
 *                  properties:
 *                      childId:
 *                          type: string
 *                          description: The ID of the child
 *                      connections:
 *                          type: array
 *                          items:
 *                              type: object
 *                              properties:
 *                                  firstName:
 *                                      type: string
 *                                      description: The first name of the connected child
 *                                  lastName:
 *                                      type: string
 *                                      description: The last name of the connected child
 *                                  photoURL:
 *                                      type: string
 *                                      description: The photo URL of the connected child
 *                                  school:
 *                                      type: string
 *                                      description: The school of the connected child
 *                                  homeRoom:
 *                                      type: string
 *                                      description: The home room of the connected child
 *                                  associatedColor:
 *                                      type: string
 *                                      description: The associated color of the connected child
 *                                  childId:
 *                                      type: string
 *                                      description: The ID of the connected child
 */

/**
 * @openapi
 *  /user/details:
 *      get:
 *          security:
 *              - bearerAuth: []
 *          tags: [User]
 *          summary: User Details
 *          parameters:
 *              - in: query
 *                name: userId
 *                schema:
 *                    type: string
 *                description: uuid of the user
 *          responses:
 *              200:
 *                  description: User get details
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#/components/schemas/successUserDetails'
 *              400:
 *                  description: Invalid Request
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#/components/schemas/validationError'
 *              401:
 *                  description: Unauthorised Access
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#/components/schemas/unauthorisedAccessUser'
 *              500:
 *                  description: internal server error
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /user/change-password:
 *  put:
 *      security:
 *          - bearerAuth: []
 *      tags: [User]
 *      summary: Change Password
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/changePassword'
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successChangePassword'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Wrong Credentials
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessLogin'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /user/update-user:
 *  patch:
 *      tags: [User]
 *      security:
 *          - bearerAuth: []
 *      summary: Update user profile
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      type: object
 *                      properties:
 *                          firstName:
 *                              type: string
 *                              description: The first name of the user
 *                          lastName:
 *                              type: string
 *                              description: The last name of the user
 *      responses:
 *          200:
 *              description: Your profile updated successfully
 *              content:
 *                  application/json:
 *                      schema:
 *                          type: object
 *                          properties:
 *                              message:
 *                                  type: string
 *                                  example: Your profile updated successfully
 *          400:
 *              description: Bad Request
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /user/connections:
 *  get:
 *      security:
 *          - bearerAuth: []
 *      tags: [User]
 *      summary: Get Connections List
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successConnectionsList'
 *          400:
 *              description: Bad Request
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /user/:
 *  delete:
 *      security:
 *          - bearerAuth: []
 *      tags: [User]
 *      summary: Delete User
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successDeleteUser'
 *          401:
 *              description: Bad Request
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /user/membership-list:
 *  get:
 *      security:
 *          - bearerAuth: []
 *      tags: [User]
 *      summary: Get Purchased Memberships List
 *      responses:
 *          200:
 *              description: Successfully retrieved purchased memberships list
 *              content:
 *                  application/json:
 *                      schema:
 *                          type: array
 *                          items:
 *                              type: object
 *                              properties:
 *                                  organizationId:
 *                                      type: string
 *                                  membershipType:
 *                                      type: string
 *                                      enum: ['child', 'family']
 *                                  childId:
 *                                      type: string
 *                                  startDate:
 *                                      type: string
 *                                      format: date-time
 *                                  endDate:
 *                                      type: string
 *                                      format: date-time
 *                                  organizationDetails:
 *                                      type: object
 *                                      properties:
 *                                          id:
 *                                              type: string
 *                                          name:
 *                                              type: string
 *                                          zipCode:
 *                                              type: string
 *                                          category:
 *                                              type: string
 *                                          address:
 *                                              type: string
 *                                          country:
 *                                              type: string
 *                                          state:
 *                                              type: string
 *                                          city:
 *                                              type: string
 *                                  associatedChild:
 *                                      type: object
 *                                      properties:
 *                                          id:
 *                                              type: string
 *                                          firstName:
 *                                              type: string
 *                                          lastName:
 *                                              type: string
 *                                          associatedColor:
 *                                              type: string
 *                                          photoURL:
 *                                              type: string
 *          401:
 *              description: Unauthorized
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /user/chat-guidelines-read:
 *  patch:
 *      security:
 *         - bearerAuth: []
 *      tags: [User]
 *      summary: Update chat guidelines read status
 *      responses:
 *          200:
 *              description: Successfully updated chat guidelines read status
 *              content:
 *                  application/json:
 *                      schema:
 *                          type: object
 *                      properties:
 *                          message:
 *                              type: string
 *                          status:
 *                              $ref: '#/components/messageDefinition/properties/status'
 *                      example:
 *                          message: Successfully updated chat guidelines read status
 *                          status: 1
 *          401:
 *              description: Unauthorized
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */
