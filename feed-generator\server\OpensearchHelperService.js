const AwsOpenSearchService = require('./opensearchUtil');
const Event = require('./models/event.model');
const CONSOLE_LOGGER = require('./logger');

class OpensearchHelperService {
    /**
     * @description Adds events to the opensearch
     * <AUTHOR>
     * @param {Object} eventIds - The event ids
     * @param {Object} child - The child
     * @param {Boolean} isUpdate - Whether the events are being updated
     * @returns {Promise<Array>} The event ids
     */
    static async addEventsInOpensearch (eventIds, childId, child, isUpdate) {
        CONSOLE_LOGGER.info('Adding child feeds in opensearch', eventIds, childId);
        if (!isUpdate) {
            CONSOLE_LOGGER.info(
                'Creating child in opensearch',
                child.id,
                JSON.stringify(child),
                typeof child
            );
            await AwsOpenSearchService.create('children', child.id, child);
        } else {
            CONSOLE_LOGGER.info(
                'Updating child in opensearch',
                child.id,
                JSON.stringify(child)
            );
            await AwsOpenSearchService.updateField('children', child.id, child);
            await AwsOpenSearchService.emptyChildCalendarEvents('children', child.id);
        }

        await this.setCalendarEventsInOpensearch(child);
        CONSOLE_LOGGER.info(
            'Updating child in opensearch',
            child.id,
            JSON.stringify(child)
        );

        const emptiedFields = await AwsOpenSearchService.emptyField(
            'children',
            childId,
            'childFeeds'
        );
        CONSOLE_LOGGER.info('Emptied fields', JSON.stringify(emptiedFields));
        const addedItems = await AwsOpenSearchService.addItemsInField(
            'children',
            childId,
            eventIds,
            'childFeeds'
        );
        CONSOLE_LOGGER.info('Added items', JSON.stringify(addedItems));
        return addedItems;
    }

    /**
     * @description Sets the calendar events in opensearch
     * <AUTHOR>
     * @param {Object} child - The child
     * @returns {Promise<Array>} The event ids
     */
    static async setCalendarEventsInOpensearch (child) {
        let calendarEventIds = [];
        for (const organizationId of child.associatedOrganizations) {
            const orgEvents = await Event.query('organizationId')
                .eq(organizationId)
                .where('eventType')
                .eq('calendar')
                .exec();
            const eventIds = orgEvents.map((event) => event.id);
            calendarEventIds.push(...eventIds);
        }
        calendarEventIds = [...new Set(calendarEventIds)];
        return await AwsOpenSearchService.registerMultipleEvents(
            'children',
            child.id,
            Array.from(calendarEventIds)
        );
    }

    /**
     * @description Adds the posts in opensearch
     * <AUTHOR>
     * @param {Array} postIds - The post ids
     * @param {String} childId - The child id
     * @returns {Promise<Array>} The post ids
     */
    static async addPostsInOpensearch (postIds, childId) {
        CONSOLE_LOGGER.info(
            'Adding post feeds for child in opensearch',
            postIds,
            childId
        );
        const emptiedFields = await AwsOpenSearchService.emptyField(
            'children',
            childId,
            'childPosts'
        );
        CONSOLE_LOGGER.info(
            'Emptied childPosts fields',
            JSON.stringify(emptiedFields)
        );
        const addedItems = await AwsOpenSearchService.addItemsInField(
            'children',
            childId,
            postIds,
            'childPosts'
        );
        CONSOLE_LOGGER.info('Added postIds items', JSON.stringify(addedItems));
        return addedItems;
    }

    /**
     * @description Adds the fundraisers in opensearch
     * <AUTHOR>
     * @param {Array} fundraiserIds - The fundraiser ids
     * @param {String} childId - The child id
     * @returns {Promise<Array>} The fundraiser ids
     */
    static async addFundRaisersInOpensearch (fundraiserIds, childId) {
        CONSOLE_LOGGER.info(
            'Adding fundraiser feeds for child in opensearch',
            fundraiserIds,
            childId
        );
        const emptiedFields = await AwsOpenSearchService.emptyField(
            'children',
            childId,
            'childFundraisers'
        );
        CONSOLE_LOGGER.info(
            'Emptied childFundraisers fields',
            JSON.stringify(emptiedFields)
        );
        const addedItems = await AwsOpenSearchService.addItemsInField(
            'children',
            childId,
            fundraiserIds,
            'childFundraisers'
        );
        CONSOLE_LOGGER.info(
            'Added fundraiserIds items',
            JSON.stringify(addedItems)
        );
        return addedItems;
    }

    /**
     * @description Adds the participants in opensearch
     * <AUTHOR>
     * @param {String} eventId - The event id
     * @param {Array} participants - The participants
     * @returns {Promise<Array>} The participants
     */
    static async addParticipantsInOpensearch (eventId, participants) {
        const participantsIds = participants.map(
            (participant) => participant.childId
        );
        CONSOLE_LOGGER.info(
            'Adding participants in opensearch',
            eventId,
            participantsIds
        );
        await AwsOpenSearchService.emptyField('events', eventId, 'participants');
        return await AwsOpenSearchService.addItemsInField(
            'events',
            eventId,
            participantsIds,
            'participants'
        );
    }
}

module.exports = OpensearchHelperService;
