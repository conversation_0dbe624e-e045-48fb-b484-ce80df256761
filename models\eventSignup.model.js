const dynamoose = require('dynamoose');
const { v4: uuidv4 } = require('uuid');

const eventSignupSchema = new dynamoose.Schema({
    id: {
        hashKey: true,
        type: String,
        default: () => uuidv4()
    },
    eventId: {
        type: String,
        required: true,
        index: {
            name: 'eventId-index',
            global: true,
            project: true
        }
    },
    organizationId: {
        type: String,
        required: true
    },
    parentId: {
        type: String,
        required: true
    },
    childId: {
        type: String,
        required: true,
        index: {
            name: 'childId-index',
            global: true,
            project: true
        }
    },
    donationAmount: {
        type: Number,
        default: 0
    },
    volunteerDetails: {
        type: Set,
        schema: [String]
    },
    stripePaymentIntentId: {
        type: String,
        index: {
            global: true,
            name: 'stripePaymentIntentId-index',
            project: true
        }
    },
    paymentDetails: {
        type: Object,
        schema: {
            stripeCustomerId: {
                type: String
            },
            stripeConnectAccountId: {
                type: String
            },
            transactionFee: {
                type: Number,
                default: 0
            },
            platformFeeCoveredBy: {
                type: String,
                enum: ['parent', 'organization']
            },
            paymentStatus: {
                type: String,
                enum: ['pending', 'approved', 'cancelled', 'payment-initiated'],
                required: true
            },
            paymentType: {
                type: String,
                enum: ['stripe', 'cash', 'cheque', 'venmo', 'free']
            },
            membershipDiscount: {
                type: Number,
                default: 0
            }
        },
        default: { stripeCustomerId: '', stripeConnectAccountId: '', transactionFee: 0 }
    },
    purchasedProducts: {
        type: Array,
        schema: [
            {
                type: Object,
                schema: {
                    id: {
                        type: String,
                        required: true
                    },
                    itemName: {
                        type: String,
                        required: true
                    },
                    itemCost: {
                        type: Number,
                        required: true
                    },
                    quantity: {
                        type: Number,
                        required: true
                    }
                }
            }
        ]
    },
    quantityCount: {
        type: Number,
        default: 1
    },
    isGuestSignup: {
        type: Boolean,
        default: false
    },
    createdBy: {
        type: String,
        required: true
    },
    updatedBy: {
        type: String
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('EventSignups', eventSignupSchema);
