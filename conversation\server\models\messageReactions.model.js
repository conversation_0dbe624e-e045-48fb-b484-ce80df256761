const dynamoose = require('dynamoose');
const CONSTANTS = require('../util/constants');

const messageReactionSchema = new dynamoose.Schema({
    messageId: {
        type: String,
        required: true,
        hashKey: true
    },
    userId: {
        type: String,
        required: true,
        rangeKey: true
    },
    reaction: {
        type: String,
        required: true,
        enum: CONSTANTS.ALLOWED_REACTIONS
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('MessageReactions', messageReactionSchema);
