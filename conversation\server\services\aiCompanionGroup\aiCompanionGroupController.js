/**
 * This file contains controller for AI Companion Group.
 * <AUTHOR>
 * @since 11/06/2025
 * @name aiCompanionGroupController
 */

const AiCompanionGroupService = require('./aiCompanionGroupService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for AI Companion Group.
 */
class AiCompanionGroupController {
    /**
     * @desc This function is being used to add/update feedback for AI Companion Group
     * <AUTHOR>
     * @since 11/06/2025
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async addUpdateAiCompanionGroupFeedback (req, res) {
        try {
            const data = await AiCompanionGroupService.addUpdateAiCompanionGroupFeedback(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in add/update feedback for AI Companion Group', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to get feedback list for AI Companion Group
     * <AUTHOR>
     * @since 11/06/2025
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async getAiCompanionGroupFeedbackList (req, res) {
        try {
            const data = await AiCompanionGroupService.getAiCompanionGroupFeedbackList(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get feedback list for AI Companion Group', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = AiCompanionGroupController;
