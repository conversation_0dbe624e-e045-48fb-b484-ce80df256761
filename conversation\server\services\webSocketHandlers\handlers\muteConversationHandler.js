const GroupMembers = require('../../../models/groupMembers.model');
const PersonalConversation = require('../../../models/personalConversation.model');
const SendMessageService = require('../../sendSocketMessageService');

/**
 * @description Mute group conversation.
 * <AUTHOR>
 * @since 08/01/2025
 * @param {Object} eventBody - The event body.
 * @returns {Promise<Object>} - A promise that resolves to an object containing the result of the operation.
 */
module.exports.handleMuteGroupConversation = async (eventBody) => {
    const muteConversation = eventBody?.muteConversation;
    const action = muteConversation ? 'mute' : 'unmute';
    try {
        const { groupId, userId } = eventBody;

        const groupMember = await getGroupMemberObject(groupId, userId);

        await GroupMembers.update({ id: groupMember.id }, { muteConversation });
        const data = {
            groupId,
            muteConversation
        };

        await SendMessageService.sendMessagesToUsers([userId], { data, actionType: 'MUTE_GROUP_CONVERSATION' });

        return {
            statusCode: 200,
            message: `Group conversation ${action}d successfully`,
            data: JSON.stringify(data),
            action: 'sendPersonalMessage',
            actionType: 'MUTE_GROUP_CONVERSATION'
        };
    } catch (error) {
        if (error.statusCode) {
            return error;
        }

        return {
            statusCode: 500,
            message: `Failed to ${action} group conversation`,
            action: 'sendPersonalMessage',
            actionType: 'MUTE_GROUP_CONVERSATION'
        };
    }
};

/**
 * @description Mute personal conversation.
 * <AUTHOR>
 * @since 08/01/2025
 * @param {Object} eventBody - The event body.
 * @returns {Promise<Object>} - A promise that resolves to an object containing the result of the operation.
 */
module.exports.handleMutePersonalConversation = async (eventBody) => {
    const muteConversation = eventBody?.muteConversation;
    const action = muteConversation ? 'mute' : 'unmute';
    try {
        const { conversationId, userId } = eventBody;

        const personalConversation = await getPersonalConversationObject(conversationId, userId);

        await PersonalConversation.update(
            { userAId: personalConversation.userAId, userBId: personalConversation.userBId },
            { isMuted: muteConversation }
        );

        const data = {
            conversationId,
            muteConversation
        };

        await SendMessageService.sendMessagesToUsers([userId], { data, actionType: 'MUTE_PERSONAL_CONVERSATION' });

        return {
            statusCode: 200,
            message: `Personal conversation ${action}d successfully`,
            data: JSON.stringify(data),
            action: 'sendPersonalMessage',
            actionType: 'MUTE_PERSONAL_CONVERSATION'
        };
    } catch (error) {
        if (error.statusCode) {
            return error;
        }

        return {
            statusCode: 500,
            message: `Failed to ${action} personal conversation`,
            action: 'sendPersonalMessage',
            actionType: 'MUTE_PERSONAL_CONVERSATION'
        };
    }
};

/**
 * @description Get the group member object.
 * <AUTHOR>
 * @since 08/01/2025
 * @param {String} groupId - The group id.
 * @param {String} userId - The user id.
 * @returns {Promise<Object>} - A promise that resolves to an object containing the group member.
 */
const getGroupMemberObject = async (groupId, userId) => {
    const groupMember = await GroupMembers.query('groupId')
        .eq(groupId)
        .using('groupId-index')
        .where('userId')
        .eq(userId)
        .exec();

    if (!groupMember.length) {
        throw {
            statusCode: 404,
            message: 'User not part of group',
            action: 'sendPersonalMessage',
            actionType: 'MUTE_GROUP_CONVERSATION'
        };
    }

    return groupMember[0];
};

/**
 * @description Get the personal conversation object.
 * <AUTHOR>
 * @since 08/01/2025
 * @param {String} conversationId - The conversation id.
 * @param {String} userId - The user id.
 * @returns {Promise<Object>} - A promise that resolves to an object containing the personal conversation.
 */
const getPersonalConversationObject = async (conversationId, userId) => {
    const personalConversation = await PersonalConversation.query('conversationId')
        .eq(conversationId)
        .using('conversationId-index')
        .where('userAId')
        .eq(userId)
        .exec();

    if (!personalConversation.length) {
        throw {
            statusCode: 404,
            message: 'Conversation not found',
            action: 'sendPersonalMessage',
            actionType: 'MUTE_PERSONAL_CONVERSATION'
        };
    }

    return personalConversation[0];
};
