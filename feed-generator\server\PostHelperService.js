const CONSOLE_LOGGER = require('./logger');
const CONSTANTS = require('./constants');
const RedisUtil = require('./redisUtil');
const MOMENT = require('moment');
const DBModelHelperService = require('./DBModelHelperService');
const NotificationHelperService = require('./NotificationHelperService');
const Utils = require('./Utils');
const FeedHelperService = require('./FeedHelperService');

class PostHelperService {
    /**
     * @description Adds a post reference to user and children
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} post - The post object
     * @param {Number} timestamp - The timestamp
     * @param {Boolean} isCreated - Whether the post is created
     * @param {Object} oldPost - The old post object
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<Boolean>} True if the post reference was added, false otherwise
     */
    static async addPostReferenceToUserAndChildren ({
        redis,
        post,
        timestamp,
        isCreated,
        oldPost,
        versionPrefix
    }) {
        const { status, organizationId, publishedDate, id: postId } = post;
        CONSOLE_LOGGER.debug(
            'Adding post reference to user and children',
            post,
            oldPost,
            timestamp
        );
        if (status === CONSTANTS.POST_STATUS.PUBLISHED) {
            const children = await DBModelHelperService.getChildOrganizationMapping(organizationId);
            const childrenDetailsMap = await Utils.getChildrenDetailsMapWithAttributes(children);

            await this.handlePost({
                redis,
                children,
                childrenDetailsMap,
                publishedDate,
                postId,
                isCreated,
                timestamp,
                organizationId,
                post,
                versionPrefix
            });

            return true;
        } else if (
            !isCreated &&
            oldPost.status === CONSTANTS.POST_STATUS.PUBLISHED
        ) {
            await FeedHelperService.deleteAllPostReferences(redis, post, versionPrefix);
            return false;
        } else {
            return true;
        }
    }

    /**
     * @description Handles a post
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Array} children - The children
     * @param {Object} childrenDetailsMap - The children details map
     * @param {Date} publishedDate - The published date
     * @param {String} postId - The post id
     * @param {Boolean} isCreated - Whether the post is created
     * @param {Number} timestamp - The timestamp
     * @param {String} organizationId - The organization id
     * @param {Object} post - The post
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<Number>} The number of children processed
     */
    static async handlePost ({
        redis,
        children,
        childrenDetailsMap,
        publishedDate,
        postId,
        isCreated,
        timestamp,
        organizationId,
        post,
        versionPrefix
    }) {
        for (const child of children) {
            const { childId } = child;
            const childKey = Utils.getChildKey({ versionPrefix, childId });
            const score = MOMENT(publishedDate).toDate().getTime();

            const childDetails = childrenDetailsMap[childId] ?? {};

            const childPosts = await RedisUtil.getElementsOfSortedSetByScore(
                redis,
                childKey,
                timestamp,
                CONSTANTS.PLUS_INF
            );

            const childPost = childPosts.find((childPost) => {
                const feed = JSON.parse(childPost);
                return postId === feed.postId;
            });

            const { id, guardians = [] } = childDetails;

            if (childPost) {
                await Utils.upsertChildAndUserEventsToSortedSet({
                    guardians,
                    redis,
                    score,
                    value: childPost,
                    keyForGuardian: Utils.getUserKey({ versionPrefix, shouldGenerateKeyPrefix: true }),
                    keyForChild: childKey
                });
            } else {
                const value = JSON.stringify({
                    postId,
                    isPost: true,
                    eventId: postId,
                    childId: id
                });

                await Utils.upsertChildAndUserEventsToSortedSet({
                    guardians,
                    redis,
                    score,
                    value,
                    keyForGuardian: Utils.getUserKey({ versionPrefix, shouldGenerateKeyPrefix: true }),
                    keyForChild: childKey
                });
            }

            await Utils.upsertChildDetailsToHashSet({
                redis,
                childDetails,
                key: Utils.getChildDetailsKey({ versionPrefix, childId: id }),
                field: 'details'
            });

            if (isCreated) {
                const organizationName = await DBModelHelperService.getOrganizationName(organizationId);
                await NotificationHelperService.sendPostNotification(
                    post,
                    childDetails,
                    organizationName
                );
            }
        }

        return children.length;
    }
}

module.exports = PostHelperService;
