/**
 *  routes and schema for comment routes
 */

/**
 * @openapi
 * components:
 *  schemas:
 *
 *      addComment:
 *          type: object
 *          required:
 *              - eventId
 *              - childId
 *              - message
 *          properties:
 *              eventId:
 *                  type: string
 *                  description: id of the event
 *              childId:
 *                  type: string
 *                  description: id of the child
 *              message:
 *                  type: string
 *                  description: comment message
 *          example:
 *                  eventId: eventId
 *                  childId: childId
 *                  message: comment message
 *
 *      successAddComment:
 *          type: object
 *          properties:
 *              status:
 *                  type: number
 *                  description: status if data exists
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Comment added successfully
 *
 *      successCommentList:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: array
 *                  description: array of comment Object
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data: {
 *                  comments: [{
 *                  id: comment id,
 *                  firstName: child first name,
 *                  lastName: child last name,
 *                  message: comment message,
 *                  associatedColorOrImage: associatedColorOrImage,
 *                  createdAt: createdAt,
 *                  }],
 *                  nextIndex: next cursor pointer for pagination }
 *              message: Success
 *
 *      successDeleteComment:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Comment deleted successfully
 *
 *      failDeleteComment:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: You are not authorized to delete this comment
 *
 */

/**
 * @openapi
 * /comment:
 *  post:
 *      security:
 *        - bearerAuth: []
 *      tags: [Comment]
 *      summary: add comment
 *      requestBody:
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/addComment'
 *
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successAddComment'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */


/**
 * @openapi
 * /comment/list:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Comment]
 *      summary: get comment list
 *      parameters:
 *          - in: query
 *            name: eventId
 *            schema:
 *                type: string
 *            description: uuid of the event
 *          - in: query
 *            name: nextIndex
 *            schema:
 *                type: string
 *            description: index for the pagination
 *
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successCommentList'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /comment:
 *  delete:
 *      security:
 *        - bearerAuth: []
 *      tags: [Comment]
 *      summary: delete comment
 *      parameters:
 *          - in: query
 *            name: eventId
 *            schema:
 *                type: string
 *            description: uuid of the event
 *          - in: query
 *            name: commentId
 *            schema:
 *                type: string
 *            description: uuid of the comment
 *          - in: query
 *            name: childId
 *            schema:
 *                type: string
 *            description: uuid of the child
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successDeleteComment'
 *          403:
 *              description: Can not be deleted
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/failDeleteComment'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */
