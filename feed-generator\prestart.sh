#!/bin/bash
ln -f ../models/child.model.js ./server/models/child.model.js
ln -f ../models/childOrganizationMapping.model.js ./server/models/childOrganizationMapping.model.js
ln -f ../models/event.model.js ./server/models/event.model.js
ln -f ../models/eventSignup.model.js ./server/models/eventSignup.model.js
ln -f ../models/organization.model.js ./server/models/organization.model.js
ln -f ../models/pendingPushNotification.model.js ./server/models/pendingPushNotification.model.js
ln -f ../models/post.model.js ./server/models/post.model.js
ln -f ../models/user.model.js ./server/models/user.model.js
ln -f ../models/fundraiser.model.js ./server/models/fundraiser.model.js
ln -f ../models/orgManagedFundraiserBoosterDonation.model.js ./server/models/orgManagedFundraiserBoosterDonation.model.js
ln -f ../models/groupMembers.model.js ./server/models/groupMembers.model.js
ln -f ../models/groups.model.js ./server/models/groups.model.js
ln -f ../models/organizationMember.model.js ./server/models/organizationMember.model.js
ln -f ../models/message.model.js ./server/models/message.model.js
ln -f ../models/personalConversation.model.js ./server/models/personalConversation.model.js
ln -f ../models/personalMessage.model.js ./server/models/personalMessage.model.js
ln -f ../models/fundraiserSignup.model.js ./server/models/fundraiserSignup.model.js

ln -f ../shared/childSchema.js ./server/opensearchDB/opensearchIndexes/childSchema.js
ln -f ../shared/eventSchema.js ./server/opensearchDB/opensearchIndexes/eventSchema.js
ln -f ../shared/postSchema.js ./server/opensearchDB/opensearchIndexes/postSchema.js
ln -f ../shared/fundraiserSchema.js ./server/opensearchDB/opensearchIndexes/fundraiserSchema.js
