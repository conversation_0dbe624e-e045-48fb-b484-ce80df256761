const validation = require('../../util/validation');

/**
 * Class represents validations for signin.
 */
class SignInValidator extends validation {
    constructor (body, locale) {
        super(locale);
        this.body = body;
    }

    /**
     * @desc This function is being used to validate request for sign in
     * <AUTHOR>
     * @since 17/10/2023
     */
    validate () {
        super.field(this.body.email, 'Email');
        super.field(this.body.password, 'Password');
    }
}


module.exports = SignInValidator;
