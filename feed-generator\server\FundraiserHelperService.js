const CONSTANTS = require('./constants');
const RedisUtil = require('./redisUtil');
const MOMENT = require('moment');
const CONSOLE_LOGGER = require('./logger');
const DBModelHelperService = require('./DBModelHelperService');
const Utils = require('./Utils');
const FeedHelperService = require('./FeedHelperService');

class FundraiserHelperService {
    /**
     * @description Adds a fundraiser reference to the user and children
     * <AUTHOR>
     * @param {Object} redis - The Redis client
     * @param {Object} fundraiser - The fundraiser object
     * @param {Number} timestamp - The timestamp
     * @param {Boolean} isCreated - Whether the fundraiser is created
     * @param {Object} oldFundraiser - The old fundraiser object
     * @param {String} versionPrefix - The version prefix
     */
    static async addFundraiserReferenceToUserAndChildren ({
        redis,
        fundraiser,
        timestamp,
        isCreated,
        oldFundraiser,
        versionPrefix
    }) {
        CONSOLE_LOGGER.debug(
            'Adding fundraiser reference to user and children',
            fundraiser,
            timestamp
        );

        const { organizationId, id: fundraiserId, status, startDate, fundraiserType } = fundraiser;

        if (status === CONSTANTS.FUNDRAISER_STATUS.PUBLISHED) {
            const children = await DBModelHelperService.getChildOrganizationMapping(organizationId);
            const childrenDetailsMap = await Utils.getChildrenDetailsMapWithAttributes(children);

            await this.handleFundraiser({
                redis,
                timestamp,
                fundraiserId,
                fundraiserType,
                children,
                startDate,
                childrenDetailsMap,
                versionPrefix
            });

            return true;
        } else if (
            !isCreated &&
            oldFundraiser.status === CONSTANTS.FUNDRAISER_STATUS.PUBLISHED
        ) {
            await FeedHelperService.deleteAllFundraiserReferences({
                redis,
                versionPrefix,
                shouldDeleteRegisteredEvents: false,
                fundraiser: oldFundraiser
            });
            return false;
        } else {
            return true;
        }
    }

    /**
     * @description Adds a fundraiser reference to the child feed
     * <AUTHOR>
     * @param {Object} redis - The Redis client
     * @param {Object} child - The child object
     * @param {Object} childKey - The child key
     * @param {Number} score - The score
     * @param {String} childId - The child id
     * @param {Array} guardians - The guardians
     * @param {String} versionPrefix - The version prefix
     */
    static async addFundraiserReferenceToChildFeed ({ redis, fundraiserId, childKey, score, childId, guardians, versionPrefix }) {
        const value = JSON.stringify({
            fundraiserId,
            isFundraiser: true,
            eventId: fundraiserId,
            childId
        });

        await Utils.upsertChildAndUserEventsToSortedSet({
            guardians,
            redis,
            score,
            value,
            keyForGuardian: Utils.getUserKey({ versionPrefix, shouldGenerateKeyPrefix: true }),
            keyForChild: childKey
        });
    }

    /**
     * @description Handles the fundraiser
     * <AUTHOR>
     * @param {Object} redis - The Redis client
     * @param {Number} timestamp - The timestamp
     * @param {String} fundraiserId - The fundraiser id
     * @param {String} fundraiserType - The fundraiser type
     * @param {Array} children - The children
     * @param {Number} startDate - The start date
     * @param {Object} childrenDetailsMap - The children details map
     * @param {String} versionPrefix - The version prefix
     */
    static async handleFundraiser ({
        redis,
        timestamp,
        fundraiserId,
        fundraiserType,
        children,
        startDate,
        childrenDetailsMap,
        versionPrefix
    }) {
        for (const child of children) {
            const { childId } = child;
            const childKey = Utils.getChildKey({ versionPrefix, childId });
            const childRegisteredKey = Utils.getRegisteredChildKey({ versionPrefix, childId });
            const score = MOMENT(startDate).toDate().getTime();
            const childDetails = childrenDetailsMap[childId] ?? {};

            const { id, guardians = [] } = childDetails;

            const childFundraisers =
                await RedisUtil.getElementsOfSortedSetByScore(
                    redis,
                    childKey,
                    timestamp,
                    CONSTANTS.PLUS_INF
                );

            const childFundraiser = childFundraisers.find((childFundraiser) => {
                const feed = JSON.parse(childFundraiser);
                return fundraiserId === feed.fundraiserId;
            });

            if (childFundraiser) {
                await this.handleFundraiserInChildKey({
                    redis,
                    childKey,
                    childRegisteredKey,
                    timestamp,
                    score,
                    fundraiserId,
                    childFundraiser,
                    guardians,
                    versionPrefix
                });
            } else {
                await this.handleFundraiserNotInChildKey({
                    redis,
                    childRegisteredKey,
                    timestamp,
                    score,
                    fundraiserId,
                    guardians,
                    fundraiserType,
                    childKey,
                    versionPrefix,
                    childId: id
                });
            }

            await Utils.upsertChildDetailsToHashSet({
                redis,
                childDetails,
                key: Utils.getChildDetailsKey({ versionPrefix, childId: id }),
                field: 'details'
            });
        }
    }

    /**
     * @description Handles the fundraiser in the child key
     * <AUTHOR>
     * @param {Object} redis - The Redis client
     * @param {String} childKey - The child key
     * @param {String} childRegisteredKey - The child registered key
     * @param {Number} timestamp - The timestamp
     * @param {Number} score - The score
     * @param {String} fundraiserId - The fundraiser id
     * @param {Object} childFundraiser - The child fundraiser
     * @param {Array} guardians - The guardians
     * @param {String} versionPrefix - The version prefix
     */
    static async handleFundraiserInChildKey ({
        redis,
        childKey,
        childRegisteredKey,
        timestamp,
        score,
        fundraiserId,
        childFundraiser,
        guardians,
        versionPrefix
    }) {
        await Utils.upsertChildAndUserEventsToSortedSet({
            guardians,
            redis,
            score,
            value: childFundraiser,
            keyForGuardian: Utils.getUserKey({ versionPrefix, shouldGenerateKeyPrefix: true }),
            keyForChild: childKey
        });

        const childRegisteredFundraisers =
            await RedisUtil.getElementsOfSortedSetByScore(
                redis,
                childRegisteredKey,
                timestamp,
                CONSTANTS.PLUS_INF
            );

        const childRegisteredFundraisersWithSameFundraiserId = childRegisteredFundraisers.filter(
            (childFundraiser) => {
                const feed = JSON.parse(childFundraiser);
                return fundraiserId === feed.fundraiserId;
            }
        );

        if (childRegisteredFundraisersWithSameFundraiserId?.length > 0) {
            for (const childRegisteredFundraiser of childRegisteredFundraisersWithSameFundraiserId) {
                await Utils.upsertChildAndUserEventsToSortedSet({
                    guardians,
                    redis,
                    score,
                    value: childRegisteredFundraiser,
                    keyForGuardian: Utils.getRegisteredUserKey({ versionPrefix, shouldGenerateKeyPrefix: true }),
                    keyForChild: childRegisteredKey
                });
            }
        }
    }

    /**
     * @description Handles the fundraiser not in the child key
     * <AUTHOR>
     * @param {Object} redis - The Redis client
     * @param {String} childRegisteredKey - The child registered key
     * @param {Number} timestamp - The timestamp
     * @param {Number} score - The score
     * @param {String} fundraiserId - The fundraiser id
     * @param {Array} guardians - The guardians
     * @param {String} fundraiserType - The fundraiser type
     * @param {String} childId - The child id
     * @param {String} childKey - The child key
     * @param {String} versionPrefix - The version prefix
     */
    static async handleFundraiserNotInChildKey ({
        redis,
        childRegisteredKey,
        timestamp,
        score,
        fundraiserId,
        guardians,
        fundraiserType,
        childId,
        childKey,
        versionPrefix
    }) {
        const childRegisteredFundraisers =
            await RedisUtil.getElementsOfSortedSetByScore(
                redis,
                childRegisteredKey,
                timestamp,
                CONSTANTS.PLUS_INF
            );

        const childRegisteredFundraisersWithSameFundraiserId = childRegisteredFundraisers.filter(
            (childFundraiser) => {
                const feed = JSON.parse(childFundraiser);
                return fundraiserId === feed.fundraiserId;
            }
        );

        if (childRegisteredFundraisersWithSameFundraiserId?.length > 0) {
            for (const childRegisteredFundraiser of childRegisteredFundraisersWithSameFundraiserId) {
                await Utils.upsertChildAndUserEventsToSortedSet({
                    guardians,
                    redis,
                    score,
                    value: childRegisteredFundraiser,
                    keyForGuardian: Utils.getRegisteredUserKey({ versionPrefix, shouldGenerateKeyPrefix: true }),
                    keyForChild: childRegisteredKey
                });
            }

            if (fundraiserType !== CONSTANTS.FUNDRAISER_TYPES.BOOSTER) {
                await this.addFundraiserReferenceToChildFeed({
                    redis,
                    fundraiserId,
                    childKey,
                    score,
                    guardians,
                    childId,
                    versionPrefix
                });
            }
        } else {
            await this.addFundraiserReferenceToChildFeed({
                redis,
                fundraiserId,
                childKey,
                score,
                guardians,
                childId,
                versionPrefix
            });
        }
    }
}

module.exports = FundraiserHelperService;
