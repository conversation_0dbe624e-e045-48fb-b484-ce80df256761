const axios = require('axios');
const HTTPStatus = require('../util/http-status');
const Utils = require('../util/utilFunctions');

/**
 * This middleware is used to verify the reCAPTCHA token.
 * @param {Object} req - The request object.
 * @param {Object} res - The response object.
 * @param {Function} next - The next function.
 */
module.exports = async function (req, res, next) {
    const recaptchaToken = req.body.recaptchaToken || req.query.recaptchaToken;
    const recaptchaAction = req.body.recaptchaAction || req.query.recaptchaAction;
    const responseObject = Utils.errorResponse();

    if (!recaptchaToken || !recaptchaAction) {
        responseObject.message = res.__('NO_RECAPTCHA_TOKEN_OR_ACTION_PROVIDED');
        res.status(HTTPStatus.BAD_REQUEST).json(responseObject);

        return;
    }
    const secretKey = process.env.RECAPTCHA_SECRET_KEY;
    try {
        const response = await axios.post(
            `https://www.google.com/recaptcha/api/siteverify?secret=${secretKey}&response=${recaptchaToken}`
        );
        if (!response.data.success) {
            responseObject.message = res.__('INVALID_RECAPTCHA_TOKEN');
            res.status(HTTPStatus.BAD_REQUEST).json(responseObject);
            return;
        }
        if (response.data.score < 0.5) {
            responseObject.message = res.__('RECAPTCHA_SCORE_TOO_LOW');
            res.status(HTTPStatus.FORBIDDEN).json(responseObject);
            return;
        }
        if (response.data.action !== recaptchaAction) {
            responseObject.message = res.__('INVALID_RECAPTCHA_ACTION');
            res.status(HTTPStatus.BAD_REQUEST).json(responseObject);
            return;
        }
        next();
    } catch (err) {
        responseObject.message = res.__('RECAPTCHA_VERIFICATION_FAILED');
        res.status(HTTPStatus.INTERNAL_SERVER_ERROR).json(responseObject);
        return;
    }
};
