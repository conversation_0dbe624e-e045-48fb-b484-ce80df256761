module.exports = {
    addEvent: [{
        it: 'As a user I should validate if title is not passed',
        options: {
            title: ''
        },
        status: 0
    },
    {
        it: 'As a user I should validate if startDateTime is not passed',
        options: {
            title: 'Test',
            startDateTime: ''
        },
        status: 0
    },
    {
        it: 'As a user I should validate if endDateTime is not passed',
        options: {
            title: 'Test',
            startDateTime: '11/18/2023 11:00'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if venue is not passed',
        options: {
            title: 'Test',
            startDateTime: '11/18/2023 11:00',
            endDateTime: '11/18/2023 20:00'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if fee is not passed',
        options: {
            title: 'Test',
            startDateTime: '11/18/2023 11:00',
            endDateTime: '11/18/2023 20:00',
            venue: 'Ahmedabad'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if participantsLimit is not passed',
        options: {
            title: 'Test',
            startDateTime: '11/18/2023 11:00',
            endDateTime: '11/18/2023 20:00',
            venue: 'Ahmedabad',
            fee: '200'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if organizationId is not passed',
        options: {
            title: 'Test',
            startDateTime: '11/18/2023 11:00',
            endDateTime: '11/18/2023 20:00',
            venue: 'Ahmedabad',
            fee: '200',
            participantsLimit: '10'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if eventDescription is not passed',
        options: {
            title: 'Test',
            startDateTime: '11/18/2023 11:00',
            endDateTime: '11/18/2023 20:00',
            venue: 'Ahmedabad',
            fee: '200',
            participantsLimit: '10',
            organizationId: '123'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if isRecurring is not passed',
        options: {
            title: 'Test',
            startDateTime: '11/18/2023 11:00',
            endDateTime: '11/18/2023 20:00',
            venue: 'Ahmedabad',
            fee: '200',
            participantsLimit: '10',
            organizationId: '123',
            eventDescription: 'Test'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if event status is not passed',
        options: {
            title: 'Test',
            startDate: '12/12/3000',
            endDate: '12/12/3001',
            startTime: '12:00',
            endTime: '12:00',
            venue: 'Ahmedabad',
            eventDescription: 'Test',
            isRecurring: 'false',
            isPaid: 'false',
            volunteerRequired: 'false'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if event type is not passed',
        options: {
            title: 'Test',
            startDateTime: '11/18/2023 11:00',
            endDateTime: '11/18/2023 20:00',
            venue: 'Ahmedabad',
            fee: '200',
            participantsLimit: '10',
            organizationId: '123',
            eventDescription: 'Test',
            eventType: ' '
        },
        status: 0
    },
    {
        it: 'As a user I should validate if event type is not valid',
        options: {
            title: 'Test',
            startDate: '12/12/3000',
            endDate: '12/12/3001',
            startTime: '12:00',
            endTime: '12:00',
            venue: 'Ahmedabad',
            fee: '200',
            participantsLimit: '10',
            organizationId: '123',
            isPaid: 'false',
            isRecurring: 'false',
            eventDescription: 'Test  ',
            eventType: 'test'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if recurringFrequency is not passed',
        options: {
            title: 'Test',
            startDateTime: '11/18/2023 11:00',
            endDateTime: '11/18/2023 20:00',
            venue: 'Ahmedabad',
            fee: '200',
            participantsLimit: '10',
            organizationId: '123',
            eventDescription: 'Test',
            isRecurring: 'true'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if event fee is valid when isPaid is true',
        options: {
            title: 'Test',
            startDate: '12/12/3000',
            endDate: '12/12/3001',
            startTime: '12:00',
            endTime: '12:00',
            venue: 'Ahmedabad',
            eventDescription: 'Test',
            isRecurring: 'false',
            isPaid: 'true',
            fee: 'invalid fee',
            volunteerRequired: 'false'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if volunteer signup URL is passed when volunteerRequired is true',
        options: {
            title: 'Test',
            startDate: '12/12/3000',
            endDate: '12/12/3001',
            startTime: '12:00',
            endTime: '12:00',
            venue: 'Ahmedabad',
            eventDescription: 'Test',
            isRecurring: 'false',
            isPaid: 'false',
            volunteerRequired: 'true',
            volunteerSignupURL: ''
        },
        status: 0
    },
    {
        it: 'As a user I should validate if recurring frequency is passed when isRecurring is true',
        options: {
            title: 'Test',
            startDate: '12/12/3000',
            endDate: '12/12/3001',
            startTime: '12:00',
            endTime: '12:00',
            venue: 'Ahmedabad',
            eventDescription: 'Test',
            isRecurring: 'true',
            recurringFrequency: '',
            isPaid: 'false',
            volunteerRequired: 'false'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if event status is valid',
        options: {
            title: 'Test',
            startDate: '12/12/3000',
            endDate: '12/12/3001',
            startTime: '12:00',
            endTime: '12:00',
            venue: 'Ahmedabad',
            eventDescription: 'Test',
            isRecurring: 'false',
            isPaid: 'false',
            volunteerRequired: 'false',
            eventStatus: 'invalid status'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if participants limit is valid',
        options: {
            title: 'Test',
            startDate: '12/12/3000',
            endDate: '12/12/3001',
            startTime: '12:00',
            endTime: '12:00',
            venue: 'Ahmedabad',
            eventDescription: 'Test',
            isRecurring: 'false',
            isPaid: 'false',
            volunteerRequired: 'false',
            participantsLimit: 'invalid limit'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if quantityType is passed and not valid',
        options: {
            title: 'Test',
            startDate: '12/12/3000',
            endDate: '12/12/3001',
            startTime: '12:00',
            endTime: '12:00',
            venue: 'Ahmedabad',
            eventDescription: 'Test',
            isRecurring: 'false',
            isPaid: 'false',
            volunteerRequired: 'false',
            participantsLimit: '10',
            quantityType: 'People'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if quantityInstruction is passed',
        options: {
            title: 'Test',
            startDate: '12/12/3000',
            endDate: '12/12/3001',
            startTime: '12:00',
            endTime: '12:00',
            venue: 'Ahmedabad',
            eventDescription: 'Test',
            isRecurring: 'false',
            isPaid: 'false',
            volunteerRequired: 'false',
            participantsLimit: '10',
            quantityType: 'Peoples',
            quantityInstruction: 'test instruction'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if quantityInstruction is not valid',
        options: {
            title: 'Test',
            startDate: '12/12/3000',
            endDate: '12/12/3001',
            startTime: '12:00',
            endTime: '12:00',
            venue: 'Ahmedabad',
            eventDescription: 'Test',
            isRecurring: 'false',
            isPaid: 'false',
            volunteerRequired: 'false',
            participantsLimit: '10',
            quantityType: 'Peoples',
            quantityInstruction: 'tes'
        },
        status: 0
    }],
    addMultipleEvent: [{
        it: 'As a user I should validate if title is not passed',
        options: [{
            title: ''
        }],
        status: 0
    },
    {
        it: 'As a user I should validate if startDateTime is not passed',
        options: [{
            title: 'Test',
            startDateTime: ''
        }],
        status: 0
    },
    {
        it: 'As a user I should validate if endDateTime is not passed',
        options: [{
            title: 'Test',
            startDateTime: '11/18/2023 11:00'
        }],
        status: 0
    },
    {
        it: 'As a user I should validate if venue is not passed',
        options: [{
            title: 'Test',
            startDateTime: '11/18/2023 11:00',
            endDateTime: '11/18/2023 20:00'
        }],
        status: 0
    },
    {
        it: 'As a user I should validate if event status is not passed',
        options: [{
            title: 'Test',
            startDate: '12/12/3000',
            endDate: '12/12/3001',
            startTime: '12:00',
            endTime: '12:00',
            eventDescription: 'Test'
        }],
        status: 0
    },
    {
        it: 'As a user I should validate if event type is not passed',
        options: [{
            ttitle: 'Test',
            startDate: '12/12/3000',
            endDate: '12/12/3001',
            startTime: '12:00',
            endTime: '12:00',
            eventDescription: 'Test',
            eventType: ' '
        }],
        status: 0
    },
    {
        it: 'As a user I should validate if event type is not valid',
        options: [{
            title: 'Test',
            startDate: '12/12/3000',
            endDate: '12/12/3001',
            startTime: '12:00',
            endTime: '12:00',
            eventDescription: 'Test',
            eventType: 'test'
        }],
        status: 0
    },
    {
        it: 'As a user I should validate if event status is valid',
        options: [{
            title: 'Test',
            startDate: '12/12/3000',
            endDate: '12/12/3001',
            startTime: '12:00',
            endTime: '12:00',
            eventDescription: 'Test',
            eventStatus: 'invalid status'
        }],
        status: 0
    }
    ],
    generatePresignedUrl: [
        {
            it: 'As a user I should validate if fileName is not passed',
            options: {},
            status: 0
        },
        {
            it: 'As a user I should validate if organizationId is not passed',
            options: {
                fileName: 'fileName'
            },
            status: 0
        }
    ],
    otherFunctions: [
        {
            it: 'As a user I should validate if fundraiser type is not valid',
            options: {
                title: 'test',
                description: 'test',
                startDate: '2023-11-16T12:00:00Z',
                endDate: '2023-11-16T14:00:00Z',
                fundraiserType: 'test'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if bannerImageName is passed',
            options: {
                title: 'test',
                description: 'test',
                startDate: '2023-11-16T12:00:00Z',
                endDate: '2023-11-16T14:00:00Z',
                bannerImageName: ''
            },
            status: 0
        }
    ],
    validateFileType: [
        {
            it: 'As a user I should validate if fileType is not valid for image',
            options: [{
                fieldname: 'image',
                mimetype: 'application/pdf'
            }],
            status: 0
        },
        {
            it: 'As a user I should validate if fileType is not valid for document',
            options: [{
                fieldname: 'documents',
                mimetype: 'application/apk'
            }],
            status: 0
        }
    ],
    isStringEmpty: [
        {
            it: 'As a user I should validate if string is undefined',
            options: 'undefined',
            response: true
        },
        {
            it: 'As a user I should validate if string is null',
            options: null,
            response: true
        },
        {
            it: 'As a user I should validate if string is empty',
            options: ' ',
            response: true
        },
        {
            it: 'As a user I should validate if string is not empty',
            options: 'test',
            response: false
        }
    ]
};
