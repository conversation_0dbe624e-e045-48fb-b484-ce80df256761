const { expect } = require('chai');
const { getAssociatedOrganizations } = require('../organizationService');

describe('getAssociatedOrganizations', () => {

    it('should throw an error if org Id is not present', async () => {
        const req = {
            body: {},
            query: {}
        };

        try {
            await getAssociatedOrganizations(req);
            expect.fail('Expected error was not thrown');
        } catch (error) {
            expect(error).to.be.an('error');
            expect(error.message).to
                .equal('Organization Id is required');
        }
    });

    it('should throw an error if org Id is invalid', async () => {
        const req = {
            body: {},
            query: { orgId: 'invalid-org-id' }
        };

        try {
            await getAssociatedOrganizations(req);
            expect.fail('Expected error was not thrown');
        } catch (error) {
            expect(error).to.be.an('error');
            expect(error.message).to
                .equal('Organization Id is not valid.');
        }
    });
});
