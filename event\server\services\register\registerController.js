const RegisterService = require('./registerService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for Register.
 */
class RegisterController {
    /**
     * @desc This function is being used to register user and payment for event registration.
     * <AUTHOR>
     * @since 15/11/2023
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async registerUserAndPayment (req, res) {
        try {
            const data = await RegisterService.registerUserAndPayment(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, res.__('EVENT_REGISTER_SUCCESS'));
        } catch (error) {
            CONSOLE_LOGGER.error('Error in registerUserAndPayment: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used for Register webhooks for stripe connect account creation and updation.
     * <AUTHOR>
     * @since 16/11/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async stripeWebhook (req, res) {
        try {
            await RegisterService.stripeWebhook(req, res, req.locale);
        } catch (error) {
            CONSOLE_LOGGER.error('Error from stripe webhook at payment time: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = RegisterController;
