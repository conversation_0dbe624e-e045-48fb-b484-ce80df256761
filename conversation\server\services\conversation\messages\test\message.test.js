const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const sinon = require('sinon');
const jwt = require('jsonwebtoken');
const Utils = require('../../../../util/utilFunctions');
const User = require('../../../../models/user.model');
const TestCase = require('./testcaseMessage');
const Message = require('../../../../models/message.model');
const Groups = require('../../../../models/groups.model');
const FlaggedMessage = require('../../../../models/flaggedMessage.model');
const { beforeEach, afterEach } = require('mocha');
const MessageService = require('../messageService');
const GroupMembers = require('../../../../models/groupMembers.model');
const SendMessageService = require('../../../sendSocketMessageService');

const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
/* User Token */
const user = {
    id: '95285616-3769-4b2e-b36b-898597b8146e',
    sub: '95285616-3769-4b2e-b36b-898597b8146e',
    email: '<EMAIL>',
    isVerified: 1,
    role: 1,
    status: 'active',
    isDeleted: 0,
    accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'testToken', tokenOptionalInfo)
};

Utils.addCommonReqTokenForHMac(request);

const validOptions = {
    conversationId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
    senderId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
    receiverId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
    messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
    replyMessage: JSON.stringify({
        messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
        senderId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
    })
};

const validOptionsWithoutReplyMessage = {
    conversationId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
    senderId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
    receiverId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
    messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
};

const validOptionsWithNewConversation = {
    senderId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
    receiverId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
    messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
};

describe('Conversation Service', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.sendPersonalMessageWithMedia.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                    accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
                });

                request(process.env.BASE_URL)
                    .post('/conversation/personal-message-with-media')
                    .set({ Authorization: requestPayloadUser.token })
                    .field(data.options)
                    .attach('media', 'test/mock-data/valid_profile_pic.jpg')
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('should be able to send message without media thumbnail', (done) => {
            request(process.env.BASE_URL)
                .post('/conversation/personal-message-with-media')
                .set({ Authorization: requestPayloadUser.token })
                .field(validOptions)
                .attach('media', 'test/mock-data/valid_profile_pic.jpg')
                .attach('mediaNew', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

        it('should be able to send message with media thumbnail', (done) => {
            request(process.env.BASE_URL)
                .post('/conversation/personal-message-with-media')
                .set({ Authorization: requestPayloadUser.token })
                .field(validOptions)
                .attach('mediaThumbnail', 'test/mock-data/valid_profile_pic.jpg')
                .attach('media', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

        it('should be able to send message without replyMessage', (done) => {
            request(process.env.BASE_URL)
                .post('/conversation/personal-message-with-media')
                .set({ Authorization: requestPayloadUser.token })
                .field(validOptionsWithoutReplyMessage)
                .attach('media', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

        it('should be able to send message with new conversation', (done) => {
            request(process.env.BASE_URL)
                .post('/conversation/personal-message-with-media')
                .set({ Authorization: requestPayloadUser.token })
                .field(validOptionsWithNewConversation)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

        describe('Get User Children List', () => {
            it('should be able to get user children list', async () => {
                const groupMemberQueryStub = sinon.stub(GroupMembers, 'query').returns({
                    eq: sinon.stub().returns({
                        using: sinon.stub().returns({
                            where: sinon.stub().returns({
                                eq: sinon.stub().returns({
                                    using: sinon.stub().returns({
                                        attributes: sinon.stub().returns({
                                            exec: sinon.stub().resolves([{ id: '38325e0b-fed4-42a9-8d45-36f9a445c609' }])
                                        })
                                    })
                                })
                            })
                        })
                    })
                });
                const getChildrenListStub = sinon.stub(MessageService, 'getChildrenList').
                    resolves([{ id: '38325e0b-fed4-42a9-8d45-36f9a445c609' }]);
                const res = await request(process.env.BASE_URL)
                    .get('/conversation/get-children-list')
                    .set({ Authorization: requestPayloadUser.token })
                    .query({ userId: '38325e0b-fed6-42a9-8d45-36f9a445c609', groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609' });

                expect(res.statusCode).to.equal(200);
                expect(groupMemberQueryStub.calledOnce).to.be.true;
                expect(getChildrenListStub.calledOnce).to.be.true;

                groupMemberQueryStub.restore();
                getChildrenListStub.restore();
            });

            it('should handle error when getting user children list', async () => {
                const groupMemberQueryStub = sinon.stub(GroupMembers, 'query').rejects(new Error('Error'));

                const res = await request(process.env.BASE_URL)
                    .get('/conversation/get-children-list')
                    .set({ Authorization: requestPayloadUser.token })
                    .query({ userId: '38325e0b-fed6-42a9-8d45-36f9a445c609', groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609' });

                expect(res.statusCode).to.equal(400);
                expect(groupMemberQueryStub.calledOnce).to.be.true;

                groupMemberQueryStub.restore();
            });
        });

        describe('Flagged Message', () => {
            let messageStub;
            let groupStub;
            let flaggedMessageStub;
            let messageUpdateStub;

            beforeEach(() => {
                messageStub = sinon.stub(Message, 'get');
                messageStub.resolves({
                    id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    groupId: '38325e0b-fed5-42a9-8d45-36f9a445c609',
                    senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609',
                    message: 'Hello',
                    reactions: [],
                    flagCount: 0
                });
                messageUpdateStub = sinon.stub(Message, 'update');
                messageUpdateStub.resolves({
                    id: '38325e0b-fed4-42a9-8d45-36f9a45c6091',
                    flagCount: 1
                });

                groupStub = sinon.stub(Groups, 'get');
                groupStub.resolves({
                    id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    groupType: CONSTANTS.GROUP_TYPES.ORGANIZATION,
                    organizationMetaData: {
                        name: 'Test Organization'
                    }
                });

                flaggedMessageStub = sinon.stub(FlaggedMessage.prototype, 'save');
                flaggedMessageStub.resolves();
            });

            afterEach(() => {
                messageStub.restore();
                messageUpdateStub.restore();
                groupStub.restore();
                flaggedMessageStub.restore();
            });

            TestCase.flagMessage.forEach((data) => {
                it(data.it, async () => {
                    const res = await request(process.env.BASE_URL)
                        .post('/conversation/flag-message')
                        .set({ Authorization: requestPayloadUser.token })
                        .send(data.options);

                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, data.status);
                });
            });

            it('should throw error when message is not found', async () => {
                messageStub.resolves(null);

                const res = await request(process.env.BASE_URL)
                    .post('/conversation/flag-message')
                    .set({ Authorization: requestPayloadUser.token })
                    .send({ messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609', reasons: ['Spam'] });

                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 404);
            });

            it('should throw error when group is not found', async () => {
                groupStub.resolves(null);

                const res = await request(process.env.BASE_URL)
                    .post('/conversation/flag-message')
                    .set({ Authorization: requestPayloadUser.token })
                    .send({ messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609', reasons: ['Spam'] });

                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 404);
            });

            it('should not throw error when flagCount is not present', async () => {
                messageStub.resolves({
                    id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    groupId: '38325e0b-fed5-42a9-8d45-36f9a445c609',
                    senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609',
                    message: 'Hello',
                    reactions: [],
                    save: sinon.stub().resolves()
                });

                const res = await request(process.env.BASE_URL)
                    .post('/conversation/flag-message')
                    .set({ Authorization: requestPayloadUser.token })
                    .send({ messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609', reasons: ['Spam'] });

                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
            });

            it('should be able to get flag message list', (done) => {

                const flaggedMessage = {
                    messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    reasons: [CONSTANTS.FLAG_MESSAGE_REASON.SPAM],
                    status: CONSTANTS.FLAG_MESSAGE_STATUS.PENDING,
                    orgId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
                };
                const flaggedMessage2 = {
                    messageId: '38325e0b-fed4-42a9-8d45-36f9a445c689',
                    reasons: [CONSTANTS.FLAG_MESSAGE_REASON.SPAM, CONSTANTS.FLAG_MESSAGE_REASON.INAPPROPRIATE_CONTENT],
                    status: CONSTANTS.FLAG_MESSAGE_STATUS.PENDING,
                    orgId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
                };
                const flaggedMessage3 = {
                    messageId: '38325e0b-fed4-42a9-8d45-36f9a445c999',
                    reasons: [CONSTANTS.FLAG_MESSAGE_REASON.SPAM],
                    status: CONSTANTS.FLAG_MESSAGE_STATUS.PENDING,
                    orgId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
                };

                const flaggedMessageQueryStub = sinon.stub(FlaggedMessage, 'query').returns({
                    eq: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                using: sinon.stub().returns({
                                    exec: sinon.stub().resolves([flaggedMessage, flaggedMessage2, flaggedMessage3])
                                })
                            })
                        })
                    })
                });

                const messageStub = sinon.stub(Message, 'batchGet').resolves([
                    {
                        id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                        message: 'Hello',
                        senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609',
                        groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                        mediaName: 'test.jpg',
                        mediaThumbnailName: 'test.jpg',
                        replyMessage: {
                            mediaName: 'test.jpg',
                            senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609'
                        }
                    },
                    {
                        id: '38325e0b-fed4-42a9-8d45-36f9a445c999',
                        message: 'Hello World!',
                        senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609',
                        groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
                    },
                    {
                        id: '38325e0b-fed4-42a9-8d45-36f9a445c689',
                        message: 'Hello World!',
                        senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609',
                        groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                        replyMessage: {
                            senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609'
                        }
                    }
                ]);

                const userQueryStub = sinon.stub(User, 'query').returns({
                    eq: sinon.stub().returns({
                        exec: sinon.stub().resolves([{
                            id: '38325e0b-fed6-42a9-8d45-36f9a445c609',
                            firstName: 'John',
                            lastName: 'Doe',
                            toJSON: sinon.stub().returns({
                                id: '38325e0b-fed6-42a9-8d45-36f9a445c609',
                                firstName: 'John',
                                lastName: 'Doe'
                            })
                        }])
                    })
                });

                const groupBatchGetStub = sinon.stub(Groups, 'batchGet').resolves([{
                    groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    groupType: CONSTANTS.GROUP_TYPES.ORGANIZATION,
                    organizationMetaData: {
                        name: 'Test Organization'
                    }
                }]);

                request(process.env.BASE_URL)
                    .get('/conversation/flag-message-list')
                    .set({ Authorization: requestPayloadUser.token })
                    .query({ orgId: '38325e0b-fed4-42a9-8d45-36f9a445c609' })
                    .end((err, res) => {
                        try {
                            expect(res.body.status).to.be.status;
                            assert.equal(res.statusCode, 200);
                            done();
                        } catch (error) {
                            done(error);
                        }
                        finally {
                            flaggedMessageQueryStub.restore();
                            messageStub.restore();
                            userQueryStub.restore();
                            groupBatchGetStub.restore();
                        }
                    });
            });

            it('should handle error when getting flag message list', (done) => {
                const flaggedMessageQueryStub = sinon.stub(FlaggedMessage, 'query').rejects(new Error('Error'));

                request(process.env.BASE_URL)
                    .get('/conversation/flag-message-list')
                    .set({ Authorization: requestPayloadUser.token })
                    .query({ orgId: '38325e0b-fed4-42a9-8d45-36f9a445c609' })
                    .end((err, res) => {
                        try {
                            expect(res.body.status).to.be.status;
                            assert.equal(res.statusCode, 400);
                            done();
                        } catch (error) {
                            done(error);
                        }
                        finally {
                            flaggedMessageQueryStub.restore();
                        }
                    });
            });

            it('should be able to update flag message status', async () => {
                const flaggedMessageQueryStub = createFlaggedMessageQueryStub([{
                    id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    status: CONSTANTS.FLAG_MESSAGE_STATUS.PENDING,
                    conversationId: '38325e0b-fed5-42a9-8d45-36f9a445c609',
                    messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
                }]);
                const flaggedMessageUpdateStub = createFlaggedMessageUpdateStub();

                const res = await request(process.env.BASE_URL)
                    .patch('/conversation/update-flagged-message-status')
                    .set({ Authorization: requestPayloadUser.token })
                    .send({
                        messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                        status: CONSTANTS.FLAG_MESSAGE_STATUS.DISMISSED,
                        senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609'
                    });

                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);

                flaggedMessageQueryStub.restore();
                flaggedMessageUpdateStub.restore();
            });

            it('should throw error when flagged message is not found', async () => {
                const flaggedMessageQueryStub = createFlaggedMessageQueryStub([]);

                const res = await request(process.env.BASE_URL)
                    .patch('/conversation/update-flagged-message-status')
                    .set({ Authorization: requestPayloadUser.token })
                    .send({
                        messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                        status: CONSTANTS.FLAG_MESSAGE_STATUS.DISMISSED,
                        senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609'
                    });

                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 404);
                flaggedMessageQueryStub.restore();
            });

            it('should be able to delete group message of a flagged message', async () => {
                const flaggedMessageQueryStub = createFlaggedMessageQueryStub([{
                    id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    status: CONSTANTS.FLAG_MESSAGE_STATUS.PENDING,
                    conversationId: '38325e0b-fed5-42a9-8d45-36f9a445c609',
                    messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
                }]);
                const messageServiceStub = sinon.stub(MessageService, 'handleDeleteGroupMessage').resolves({
                    statusCode: 200,
                    message: 'Message deleted successfully!'
                });
                const flaggedMessageUpdateStub = createFlaggedMessageUpdateStub();

                const res = await request(process.env.BASE_URL)
                    .patch('/conversation/update-flagged-message-status')
                    .set({ Authorization: requestPayloadUser.token })
                    .send({
                        messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                        status: CONSTANTS.FLAG_MESSAGE_STATUS.MESSAGE_DELETED,
                        senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609'
                    });

                expect(res.statusCode).to.equal(200);
                expect(messageServiceStub.calledOnce).to.be.true;
                expect(flaggedMessageUpdateStub.calledOnce).to.be.true;

                messageServiceStub.restore();
                flaggedMessageQueryStub.restore();
                flaggedMessageUpdateStub.restore();
            });

            it('should handle error when deleting group message of a flagged message', async () => {
                const flaggedMessageQueryStub = createFlaggedMessageQueryStub([{
                    id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    status: CONSTANTS.FLAG_MESSAGE_STATUS.PENDING,
                    conversationId: '38325e0b-fed5-42a9-8d45-36f9a445c609',
                    messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
                }]);
                const messageServiceStub = sinon.stub(MessageService, 'handleDeleteGroupMessage').resolves({
                    statusCode: 500,
                    message: 'Error deleting message!'
                });
                const flaggedMessageUpdateStub = createFlaggedMessageUpdateStub();

                const res = await request(process.env.BASE_URL)
                    .patch('/conversation/update-flagged-message-status')
                    .set({ Authorization: requestPayloadUser.token })
                    .send({
                        messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                        status: CONSTANTS.FLAG_MESSAGE_STATUS.MESSAGE_DELETED,
                        senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609'
                    });

                expect(res.statusCode).to.equal(500);
                expect(messageServiceStub.calledOnce).to.be.true;
                expect(flaggedMessageUpdateStub.calledOnce).to.be.false;

                messageServiceStub.restore();
                flaggedMessageQueryStub.restore();
                flaggedMessageUpdateStub.restore();
            });

            it('should be able to disable commenting of a flagged message', async () => {
                const flaggedMessageQueryStub = createFlaggedMessageQueryStub([{
                    id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    status: CONSTANTS.FLAG_MESSAGE_STATUS.PENDING,
                    conversationId: '38325e0b-fed5-42a9-8d45-36f9a445c609',
                    messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
                }]);

                const groupMembersQueryStub = sinon.stub(GroupMembers, 'query').returns({
                    eq: sinon.stub().returns({
                        using: sinon.stub().returns({
                            where: sinon.stub().returns({
                                eq: sinon.stub().returns({
                                    using: sinon.stub().returns({
                                        exec: sinon.stub().resolves([{
                                            groupId: '38325e0b-fed5-42a9-8d45-36f9a445c609',
                                            userId: '38325e0b-fed6-42a9-8d45-36f9a445c609'
                                        }])
                                    })
                                })
                            }),
                            attributes: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    in: sinon.stub().returns({
                                        exec: sinon.stub().resolves([{
                                            status: 'active',
                                            userId: '38325e0b-fed6-42a9-8d45-36f9a445c609'
                                        }])
                                    })
                                })
                            })
                        })
                    })
                });

                const flaggedMessageUpdateStub = createFlaggedMessageUpdateStub();

                const sendMessagesToUsersStub = sinon.stub(SendMessageService, 'sendMessagesToUsers');
                sendMessagesToUsersStub.resolves();

                const deleteGroupMessageStub = sinon.stub(MessageService, 'handleDeleteGroupMessage');
                deleteGroupMessageStub.resolves({
                    statusCode: 200,
                    body: 'Message deleted successfully!'
                });

                const updateGroupMemberStub = sinon.stub(GroupMembers, 'update');
                updateGroupMemberStub.resolves();

                const res = await request(process.env.BASE_URL)
                    .patch('/conversation/update-flagged-message-status')
                    .set({ Authorization: requestPayloadUser.token })
                    .send({
                        messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                        status: CONSTANTS.FLAG_MESSAGE_STATUS.USER_DISABLED_COMMENTING,
                        senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609'
                    });

                expect(res.statusCode).to.equal(200);
                expect(flaggedMessageUpdateStub.calledOnce).to.be.true;
                expect(groupMembersQueryStub.called).to.be.true;
                expect(sendMessagesToUsersStub.calledOnce).to.be.true;
                expect(deleteGroupMessageStub.calledOnce).to.be.true;
                expect(updateGroupMemberStub.calledOnce).to.be.true;

                flaggedMessageQueryStub.restore();
                flaggedMessageUpdateStub.restore();
                groupMembersQueryStub.restore();
                sendMessagesToUsersStub.restore();
                deleteGroupMessageStub.restore();
                updateGroupMemberStub.restore();
            });

            it('should handle if group member is not found', async () => {
                const flaggedMessageQueryStub = createFlaggedMessageQueryStub([{
                    id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    status: CONSTANTS.FLAG_MESSAGE_STATUS.PENDING,
                    conversationId: '38325e0b-fed5-42a9-8d45-36f9a445c609',
                    messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
                }]);

                const groupMembersQueryStub = sinon.stub(GroupMembers, 'query').returns({
                    eq: sinon.stub().returns({
                        using: sinon.stub().returns({
                            where: sinon.stub().returns({
                                eq: sinon.stub().returns({
                                    using: sinon.stub().returns({
                                        exec: sinon.stub().resolves([])
                                    })
                                })
                            })
                        })
                    })
                });

                const res = await request(process.env.BASE_URL)
                    .patch('/conversation/update-flagged-message-status')
                    .set({ Authorization: requestPayloadUser.token })
                    .send({
                        messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                        status: CONSTANTS.FLAG_MESSAGE_STATUS.USER_DISABLED_COMMENTING,
                        senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609'
                    });

                expect(res.statusCode).to.equal(404);
                expect(groupMembersQueryStub.called).to.be.true;

                flaggedMessageQueryStub.restore();
                groupMembersQueryStub.restore();
            });

            it('should handle error while deleting group message', async () => {
                const flaggedMessageQueryStub = createFlaggedMessageQueryStub([{
                    id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    status: CONSTANTS.FLAG_MESSAGE_STATUS.PENDING,
                    conversationId: '38325e0b-fed5-42a9-8d45-36f9a445c609',
                    messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
                }]);

                const groupMembersQueryStub = sinon.stub(GroupMembers, 'query').returns({
                    eq: sinon.stub().returns({
                        using: sinon.stub().returns({
                            where: sinon.stub().returns({
                                eq: sinon.stub().returns({
                                    using: sinon.stub().returns({
                                        exec: sinon.stub().resolves([{
                                            groupId: '38325e0b-fed5-42a9-8d45-36f9a445c609',
                                            userId: '38325e0b-fed6-42a9-8d45-36f9a445c609'
                                        }])
                                    })
                                })
                            }),
                            attributes: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    in: sinon.stub().returns({
                                        exec: sinon.stub().resolves([{
                                            status: 'active',
                                            userId: '38325e0b-fed6-42a9-8d45-36f9a445c609'
                                        }])
                                    })
                                })
                            })
                        })
                    })
                });

                const flaggedMessageUpdateStub = createFlaggedMessageUpdateStub();

                const sendMessagesToUsersStub = sinon.stub(SendMessageService, 'sendMessagesToUsers');
                sendMessagesToUsersStub.resolves();

                const deleteGroupMessageStub = sinon.stub(MessageService, 'handleDeleteGroupMessage');
                deleteGroupMessageStub.resolves({
                    statusCode: 500,
                    body: 'Error deleting message!'
                });

                const res = await request(process.env.BASE_URL)
                    .patch('/conversation/update-flagged-message-status')
                    .set({ Authorization: requestPayloadUser.token })
                    .send({
                        messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                        status: CONSTANTS.FLAG_MESSAGE_STATUS.USER_DISABLED_COMMENTING,
                        senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609'
                    });

                expect(res.statusCode).to.equal(500);
                expect(flaggedMessageUpdateStub.calledOnce).to.be.false;
                expect(groupMembersQueryStub.called).to.be.true;
                expect(sendMessagesToUsersStub.calledOnce).to.be.true;
                expect(deleteGroupMessageStub.calledOnce).to.be.true;

                flaggedMessageQueryStub.restore();
                flaggedMessageUpdateStub.restore();
                groupMembersQueryStub.restore();
                sendMessagesToUsersStub.restore();
                deleteGroupMessageStub.restore();
            });

            it('should dismiss all flagged messages when status is dismissed', async () => {
                const flaggedMessageQueryStub = createFlaggedMessageQueryStub([
                    {
                        id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                        status: CONSTANTS.FLAG_MESSAGE_STATUS.PENDING,
                        conversationId: '38325e0b-fed5-42a9-8d45-36f9a445c609',
                        messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
                    },
                    {
                        id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                        status: CONSTANTS.FLAG_MESSAGE_STATUS.PENDING,
                        conversationId: '38325e0b-fed5-42a9-8d45-36f9a445c609',
                        messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
                    }
                ]);

                const flaggedMessageUpdateStub = createFlaggedMessageUpdateStub();

                const res = await request(process.env.BASE_URL)
                    .patch('/conversation/update-flagged-message-status')
                    .set({ Authorization: requestPayloadUser.token })
                    .send({
                        messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                        status: CONSTANTS.FLAG_MESSAGE_STATUS.DISMISSED,
                        senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609'
                    });

                expect(res.statusCode).to.equal(200);
                expect(flaggedMessageUpdateStub.calledTwice).to.be.true;

                flaggedMessageQueryStub.restore();
                flaggedMessageUpdateStub.restore();
            });
        });

        const createFlaggedMessageQueryStub = (resolvedValue) => {
            return sinon.stub(FlaggedMessage, 'query').returns({
                eq: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            using: sinon.stub().returns({
                                exec: sinon.stub().resolves(resolvedValue)
                            })
                        })
                    })
                })
            });
        };

        const createFlaggedMessageUpdateStub = () => {
            return sinon.stub(FlaggedMessage, 'update').resolves();
        };

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Flag Message Reasons', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('should throw error if messageId is not passed', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
            });

            request(process.env.BASE_URL)
                .get('/conversation/flag-message-reasons')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('should throw error if user is not associated with the organization', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '38325e0b-fed4-42a9-8d45-36f9a445c608', role: 'admin' }]
            });

            request(process.env.BASE_URL)
                // eslint-disable-next-line max-len
                .get('/conversation/flag-message-reasons?messageId=38325e0b-fed4-42a9-8d45-36f9a445c609&organizationId=38325e0b-fed4-42a9-8d45-36f9a445c609')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 403);
                    done();
                });
        });

        it('should throw error if message is not found', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '38325e0b-fed4-42a9-8d45-36f9a445c609', role: 'admin' }]
            });

            sinon.stub(Message, 'get').resolves(null);

            request(process.env.BASE_URL)
                // eslint-disable-next-line max-len
                .get('/conversation/flag-message-reasons?messageId=38325e0b-fed4-42a9-8d45-36f9a445c609&organizationId=38325e0b-fed4-42a9-8d45-36f9a445c609')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 404);
                    Message.get.restore();
                    done();
                });
        });

        it('should throw error if group not found', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '38325e0b-fed4-42a9-8d45-36f9a445c609', role: 'admin' }]
            });

            sinon.stub(Message, 'get').resolves({
                id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                message: 'Hello',
                senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609',
                groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
            });

            sinon.stub(Groups, 'get').resolves(null);

            request(process.env.BASE_URL)
                // eslint-disable-next-line max-len
                .get('/conversation/flag-message-reasons?messageId=38325e0b-fed4-42a9-8d45-36f9a445c609&organizationId=38325e0b-fed4-42a9-8d45-36f9a445c609')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 404);
                    Message.get.restore();
                    Groups.get.restore();
                    done();
                });
        });

        it('should throw error if organization id does not match with group organization id', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '38325e0b-fed4-42a9-8d45-36f9a445c609', role: 'admin' }]
            });

            sinon.stub(Message, 'get').resolves({
                id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                message: 'Hello',
                senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609',
                groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
            });

            sinon.stub(Groups, 'get').resolves({
                groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                groupType: CONSTANTS.GROUP_TYPES.ORGANIZATION,
                organizationId: '38325e0b-fed4-42a9-8d45-36f9a445c608'
            });

            request(process.env.BASE_URL)
                // eslint-disable-next-line max-len
                .get('/conversation/flag-message-reasons?messageId=38325e0b-fed4-42a9-8d45-36f9a445c609&organizationId=38325e0b-fed4-42a9-8d45-36f9a445c609')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 404);
                    Message.get.restore();
                    Groups.get.restore();
                    done();
                });
        });

        it('should be able to get flag message reasons', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '38325e0b-fed4-42a9-8d45-36f9a445c609', role: 'admin' }]
            });

            sinon.stub(Message, 'get').resolves({
                id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                message: 'Hello',
                senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609',
                replyMessage: {
                    messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609'
                },
                flagCount: 2
            });

            sinon.stub(Groups, 'get').resolves({
                groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                groupType: CONSTANTS.GROUP_TYPES.ORGANIZATION,
                organizationId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
            });

            sinon.stub(FlaggedMessage, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([
                            {
                                id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                                flaggedBy: '38325e0b-fed6-42a9-8d45-36f9a445c609'
                            },
                            {
                                id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                                flaggedBy: '38325e0b-fed6-42a9-8d45-36f9a445c608'
                            }
                        ])
                    })
                })
            });

            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub()
                            .onFirstCall().resolves([{
                                id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                                firstName: 'John',
                                lastName: 'Doe'
                            }])
                            .onSecondCall().resolves([])

                    }),
                    exec: sinon.stub().resolves([{
                        id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                        firstName: 'John',
                        lastName: 'Doe'
                    }])
                })
            });

            request(process.env.BASE_URL)
                // eslint-disable-next-line max-len
                .get('/conversation/flag-message-reasons?messageId=38325e0b-fed4-42a9-8d45-36f9a445c609&organizationId=38325e0b-fed4-42a9-8d45-36f9a445c609')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Message.get.restore();
                    Groups.get.restore();
                    FlaggedMessage.query.restore();
                    User.query.restore();
                    done();
                });
        });

        it('should be able to get flag message reasons for message in event group', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '38325e0b-fed4-42a9-8d45-36f9a445c609', role: 'admin' }]
            });

            sinon.stub(Message, 'get').resolves({
                id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                message: 'Hello',
                senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609',
                mediaName: 'image.jpg',
                mediaThumbnailName: 'image.jpg',
                replyMessage: {
                    mediaName: 'image.jpg',
                    messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    senderId: '38325e0b-fed6-42a9-8d45-36f9a445c609'
                },
                flagCount: 2
            });

            sinon.stub(Groups, 'get').resolves({
                groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                groupType: CONSTANTS.GROUP_TYPES.EVENT,
                organizationId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
            });

            sinon.stub(FlaggedMessage, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([
                            {
                                id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                                flaggedBy: '38325e0b-fed6-42a9-8d45-36f9a445c609'
                            },
                            {
                                id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                                flaggedBy: '38325e0b-fed6-42a9-8d45-36f9a445c608'
                            }
                        ])
                    })
                })
            });

            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub()
                            .onFirstCall().resolves([{
                                id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                                firstName: 'John',
                                lastName: 'Doe'
                            }])
                            .onSecondCall().resolves([])

                    }),
                    exec: sinon.stub().resolves([{
                        id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                        firstName: 'John',
                        lastName: 'Doe'
                    }])
                })
            });

            request(process.env.BASE_URL)
                // eslint-disable-next-line max-len
                .get('/conversation/flag-message-reasons?messageId=38325e0b-fed4-42a9-8d45-36f9a445c609&organizationId=38325e0b-fed4-42a9-8d45-36f9a445c609')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Message.get.restore();
                    Groups.get.restore();
                    FlaggedMessage.query.restore();
                    User.query.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Disabled Commenter List', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('should throw error if organization id is not passed', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '38325e0b-fed4-42a9-8d45-36f9a445c609', role: 'admin' }]
            });

            request(process.env.BASE_URL)
                .get('/conversation/disabled-commenter-list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('should throw error if user is not associated with the organization', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '38325e0b-fed4-42a9-8d45-36f9a445c608', role: 'admin' }]
            });

            request(process.env.BASE_URL)
                .get('/conversation/disabled-commenter-list?organizationId=38325e0b-fed4-42a9-8d45-36f9a445c609')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 403);
                    done();
                });
        });

        it('should fetch disabled commenter list', (done) => {
            const orgId = 'test-org-id';
            const groupId = 'test-group-id';
            const userId = 'test-user-id';
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: orgId, role: 'admin' }]
            });

            sinon.stub(FlaggedMessage, 'query').returns({
                eq: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            exec: sinon.stub().resolves([{
                                id: 'flagged-message-id-1',
                                conversationId: groupId,
                                orgId,
                                status: CONSTANTS.FLAG_MESSAGE_STATUS.USER_DISABLED_COMMENTING
                            }])
                        })
                    })
                })
            });

            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([{
                                    userId,
                                    groupId,
                                    status: CONSTANTS.GROUP_MEMBER_STATUS.DISABLED
                                }])
                            })
                        })
                    })
                })
            });

            sinon.stub(Groups, 'batchGet').resolves([
                {
                    groupId,
                    groupType: CONSTANTS.GROUP_TYPES.ORGANIZATION,
                    organizationId: orgId,
                    organizationMetaData: {
                        name: 'Test Organization'
                    }
                }
            ]);

            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{
                            id: userId,
                            firstName: 'John',
                            lastName: 'Doe'
                        }])
                    })
                })
            });

            request(process.env.BASE_URL)
                .get('/conversation/disabled-commenter-list?organizationId=test-org-id')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    assert.equal(res.body.data.length, 1);
                    const responseData = res.body.data[0];
                    assert.equal(responseData.groupId, groupId);
                    assert.equal(responseData.groupType, CONSTANTS.GROUP_TYPES.ORGANIZATION);
                    assert.equal(responseData.groupName, 'Test Organization');
                    assert.equal(responseData.users.length, 1);
                    assert.equal(responseData.users[0].id, userId);
                    assert.equal(responseData.users[0].firstName, 'John');
                    assert.equal(responseData.users[0].lastName, 'Doe');
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Access level error scenarios for enabling disabled commenter', () => {
    try {
        afterEach(async () => {
            sinon.restore();
        });

        it('should throw error when user access level is app and not org_app or root user', (done) => {
            sinon.stub(User, 'get')
                .resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0,
                    accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                    associatedOrganizations: [{ organizationId: 'org-id', role: 'admin' }]
                });

            request(process.env.BASE_URL)
                .patch('/conversation/enable-commenter')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 406);
                    assert.equal(res.body.message, 'You are not authorized to access this resource.');
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Enable Disabled Commenter', () => {
    try {
        before(async () => {
            sinon.stub(User, 'get')
                .resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0,
                    accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT,
                    associatedOrganizations: [{ organizationId: 'org-id', role: 'admin' }]
                });
        });

        after(async () => {
            sinon.restore();
        });

        it('should throw error if user id is not passed', (done) => {
            request(process.env.BASE_URL)
                .patch('/conversation/enable-commenter')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    assert.equal(res.body.message, 'User Id can\'t be blank');
                    done();
                });
        });

        it('should throw error if group id is not passed', (done) => {
            request(process.env.BASE_URL)
                .patch('/conversation/enable-commenter')
                .set({ Authorization: requestPayloadUser.token })
                .send({ userId: 'user-id' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    assert.equal(res.body.message, 'Group Id can\'t be blank');
                    done();
                });
        });

        it('should throw error if group id is not UUID', (done) => {
            request(process.env.BASE_URL)
                .patch('/conversation/enable-commenter')
                .set({ Authorization: requestPayloadUser.token })
                .send({ userId: 'user-id', groupId: 'invalid-group-id' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    assert.equal(res.body.message, 'Group Id is not valid.');
                    done();
                });
        });

        it('Should handle if group member is not found', (done) => {
            const groupId = '38325e0b-fed4-42a9-8d45-36f9a445c609';
            const groupMemberQueryStub = sinon.stub(GroupMembers, 'query');
            groupMemberQueryStub.returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                using: sinon.stub().returns({
                                    exec: sinon.stub().resolves([])
                                })
                            })
                        })
                    })
                })
            });

            request(process.env.BASE_URL)
                .patch('/conversation/enable-commenter')
                .set({ Authorization: requestPayloadUser.token })
                .send({ userId: 'user-id-1', groupId })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 404);

                    const groupMemberQueryStubCall = groupMemberQueryStub.getCall(0);
                    sinon.assert.calledWithExactly(groupMemberQueryStubCall, 'groupId');
                    sinon.assert.calledWithExactly(groupMemberQueryStubCall.returnValue.eq, groupId);
                    sinon.assert.calledWithExactly(groupMemberQueryStubCall.returnValue.eq().using, 'groupId-index');
                    sinon.assert.calledWithExactly(groupMemberQueryStubCall.returnValue.eq().using().where, 'userId');
                    sinon.assert.calledWithExactly(groupMemberQueryStubCall.returnValue.eq().using().where().eq, 'user-id-1');
                    sinon.assert.calledWithExactly(groupMemberQueryStubCall.returnValue.eq()
                        .using().where().eq().using, 'userId-index');
                    sinon.assert.calledWithExactly(groupMemberQueryStubCall.returnValue.eq()
                        .using().where().eq().using().exec);

                    groupMemberQueryStub.restore();
                    done();
                });
        });

        it('Should enable disabled commenter', (done) => {
            const groupId = '38325e0b-fed4-42a9-8d45-36f9a445c609';
            const groupMemberQueryStub = sinon.stub(GroupMembers, 'query');
            groupMemberQueryStub.onFirstCall().returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                using: sinon.stub().returns({
                                    exec: sinon.stub().resolves([{
                                        id: 'id-1',
                                        userId: 'user-id-1',
                                        groupId,
                                        status: CONSTANTS.GROUP_MEMBER_STATUS.DISABLED
                                    }])
                                })
                            })
                        })
                    })
                })
            });
            sinon.stub(GroupMembers, 'update').resolves();
            groupMemberQueryStub.onSecondCall().returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        attributes: sinon.stub().returns({
                            where: sinon.stub().returns({
                                in: sinon.stub().returns({
                                    exec: sinon.stub().resolves([{
                                        id: 'id-2',
                                        userId: 'user-id-2',
                                        groupId,
                                        status: CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE
                                    }])
                                })
                            })
                        })
                    })
                })
            });
            const sendMessagesToUsersStub = sinon.stub(SendMessageService, 'sendMessagesToUsers');
            sendMessagesToUsersStub.resolves();

            request(process.env.BASE_URL)
                .patch('/conversation/enable-commenter')
                .set({ Authorization: requestPayloadUser.token })
                .send({ userId: 'user-id-1', groupId })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);

                    const groupMemberQueryStubFirstCall = groupMemberQueryStub.getCall(0);
                    sinon.assert.calledWithExactly(groupMemberQueryStubFirstCall, 'groupId');
                    sinon.assert.calledWithExactly(groupMemberQueryStubFirstCall.returnValue.eq, groupId);
                    sinon.assert.calledWithExactly(groupMemberQueryStubFirstCall.returnValue.eq().using, 'groupId-index');
                    sinon.assert.calledWithExactly(groupMemberQueryStubFirstCall.returnValue.eq().using().where, 'userId');
                    sinon.assert.calledWithExactly(groupMemberQueryStubFirstCall.returnValue.eq().using().where().eq, 'user-id-1');
                    sinon.assert.calledWithExactly(groupMemberQueryStubFirstCall.returnValue.eq()
                        .using().where().eq().using, 'userId-index');
                    sinon.assert.calledWithExactly(groupMemberQueryStubFirstCall.returnValue.eq()
                        .using().where().eq().using().exec);
                    sinon.assert.calledWithExactly(GroupMembers.update, { id: 'id-1' }, { status: CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE });

                    const groupMemberQueryStubSecondCall = groupMemberQueryStub.getCall(1);
                    sinon.assert.calledWithExactly(groupMemberQueryStubSecondCall, 'groupId');
                    sinon.assert.calledWithExactly(groupMemberQueryStubSecondCall.returnValue.eq, groupId);
                    sinon.assert.calledWithExactly(groupMemberQueryStubSecondCall.returnValue.eq().using, 'groupId-index');
                    sinon.assert.calledWithExactly(groupMemberQueryStubSecondCall.returnValue.eq().using()
                        .attributes, ['userId', 'status']);
                    sinon.assert.calledWithExactly(groupMemberQueryStubSecondCall.returnValue.eq().using().attributes().where, 'status');
                    sinon.assert.calledWithExactly(groupMemberQueryStubSecondCall.returnValue.eq().using().attributes().where().in,
                        [CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE, CONSTANTS.GROUP_MEMBER_STATUS.DISABLED]);
                    sinon.assert.calledWithExactly(groupMemberQueryStubSecondCall.returnValue.eq().using().attributes().where().in().exec);

                    const messageData = {
                        action: 'sendMessage',
                        actionType: 'USER_ENABLED_FROM_GROUP',
                        message: 'User\'s commenting has been enabled',
                        data: { id: 'id-1', userId: 'user-id-1', groupId }
                    };
                    sinon.assert.calledWithExactly(sendMessagesToUsersStub, ['user-id-2'], messageData);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});


describe('Send Message With Media', () => {
    try {
        before(async () => {
            sinon.stub(User, 'get')
                .resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0,
                    accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT,
                    associatedOrganizations: [{ organizationId: 'org-id', role: 'admin' }]
                });
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.sendMessageWithMedia.forEach((data) => {
            it(data.it, (done) => {
                request(process.env.BASE_URL)
                    .post('/conversation/messageWithMedia')
                    .set({ Authorization: requestPayloadUser.token })
                    .field(data.options)
                    .attach('media', 'test/mock-data/valid_profile_pic.jpg')
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.statusCode, data.status);
                        done();
                    });
            });
        });

        it('should upload message with the media urls without reply message', (done) => {
            request(process.env.BASE_URL)
                .post('/conversation/messageWithMedia')
                .set({ Authorization: requestPayloadUser.token })
                .field({
                    messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    organizationId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    senderId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

        it('should upload message with the media and reply message', (done) => {
            request(process.env.BASE_URL)
                .post('/conversation/messageWithMedia')
                .set({ Authorization: requestPayloadUser.token })
                .field({
                    messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    organizationId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    senderId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    replyMessage: JSON.stringify({
                        senderId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                        messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
                    })
                })
                .attach('media', 'test/mock-data/valid_profile_pic.jpg')
                .attach('mediaThumbnail', 'test/mock-data/valid_profile_pic.jpg')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
