const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const sinon = require('sinon');
const User = require('../../../models/user.model');
const Event = require('../../../models/event.model');
const TestCase = require('./testcaseEvent');
const jwt = require('jsonwebtoken');
const Stripe = require('../../../util/Stripe');
const organizationModel = require('../../../models/organization.model');
const OrganizationMembers = require('../../../models/organizationMember.model');
const EventSignup = require('../../../models/eventSignup.model');
const Children = require('../../../models/child.model');
const AwsOpenSearchService = require('../../../util/opensearch');
const childOrganizationMappingModel = require('../../../models/childOrganizationMapping.model');
const Utils = require('../../../util/utilFunctions');

const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
/* User Token */
const user = {
    id: '95285616-3769-4b2e-b36b-898597b8146e',
    email: '<EMAIL>'
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'testingjwt', tokenOptionalInfo)
};

const data = {
    eventType: 'public',
    title: 'Test',
    startDate: '11/18/2027',
    startTime: '11:00',
    endDate: '11/18/2027',
    endTime: '20:00',
    venue: 'Ahmedabad',
    isPaid: 'true',
    isDonatable: 'true',
    fee: '200',
    participantsLimit: '10',
    volunteerRequired: 'true',
    volunteerSignupURL: 'https://docs.google.com/spreadsheets/test.com',
    eventDescription: 'Test',
    isRecurring: 'true',
    organizationId: '123',
    recurringFrequency: 'Weekly',
    documentURLs: '["test document"]'
};

const eventList = [{
    'eventScope': 'public',
    'fee': 200,
    'eventType': 'event',
    'title': 'Community Cleanup Day',
    'isDeleted': 0,
    'isDonatable': false,
    'details': {
        'venue': 'City Park',
        'startDateTime': '2023-11-10T09:00:00.000Z',
        'recurringFrequency': 'month',
        'details': 'Join us for a community cleanup day at City Park. Let\'s make our city cleaner and greener!',
        'isRecurring': true,
        'endDateTime': '2023-11-10T17:00:00.000Z',
        'documentURLs': ['test document']
    },
    'id': '54404ec7-15af-4ab8-afb3-8db63203e05e',
    'status': 'unpublished',
    'organizationId': '5f5f7e5f7e5f7e5f7e5f7e5f',
    'photoURL': 'test image'
}];

const eventListWithoutImage = [{
    'eventScope': 'public',
    'fee': 200,
    'eventType': 'event',
    'title': 'Community Cleanup Day',
    'isDeleted': 0,
    'isDonatable': false,
    'details': {
        'venue': 'City Park',
        'startDateTime': '2023-11-10T09:00:00.000Z',
        'recurringFrequency': 'month',
        'details': 'Join us for a community cleanup day at City Park. Let\'s make our city cleaner and greener!',
        'isRecurring': true,
        'endDateTime': '2023-11-10T17:00:00.000Z'
    },
    'id': '54404ec7-15af-4ab8-afb3-8db63203e05e',
    'status': 'published',
    'organizationId': '5f5f7e5f7e5f7e5f7e5f7e5f'
}];
Utils.addCommonReqTokenForHMac(request);
describe('Add Event', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });
        TestCase.addEvent.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                    accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                    associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });
                request(process.env.BASE_URL)
                    .post('/event')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should handle error if token is not passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .post('/event')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and correct token is passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .post('/event')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and is verified', (done) => {
            getStub.resolves({ status: 'active', isVerified: 0, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });
            request(process.env.BASE_URL)
                .post('/event')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 423);
                    done();
                });
        });

        it('As a user I should validate the role before creating event', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });
            request(process.env.BASE_URL)
                .post('/event')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 406);
                    done();
                });
        });

        it('As a user I should check if the pto is onboarded on the stripe for the paid event if stripeConnectAccountId is not present',
            (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                    accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP, paymentDetails: { stripeConnectAccountId: '' },
                    associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
                });

                sinon.stub(OrganizationMembers, 'get').resolves({
                    users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
                });

                const organizationInstance = {
                    id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                    paymentDetails: { stripeConnectAccountId: '' },
                    save: sinon.stub().callsFake(async function () {
                        this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                        return Promise.resolve(this);
                    }),
                    toJSON: function () {
                        return { paymentDetails: { stripeConnectAccountId: '' } };
                    }
                };

                const orgGetStub = sinon.stub(organizationModel, 'get');
                orgGetStub.resolves(organizationInstance);
                const createAccountStub = sinon.stub(Stripe, 'createAccount');
                createAccountStub.resolves({ id: 'mocked_account_id' });
                const createOnboardingLinkStub = sinon.stub(Stripe, 'createOnboardingLink');
                createOnboardingLinkStub.resolves({ url: 'mocked_onboarding_url' });
                sinon.stub(Event, 'create').resolves();

                request(process.env.BASE_URL)
                    .post('/event')
                    .set({ Authorization: requestPayloadUser.token })
                    .field('title', data.title)
                    .field('startDate', data.startDate)
                    .field('startTime', data.startTime)
                    .field('endDate', data.endDate)
                    .field('endTime', data.endTime)
                    .field('venue', data.venue)
                    .field('isPaid', data.isPaid)
                    .field('isDonatable', data.isDonatable)
                    .field('fee', data.fee)
                    .field('participantsLimit', data.participantsLimit)
                    .field('volunteerRequired', data.volunteerRequired)
                    .field('volunteerSignupURL', data.volunteerSignupURL)
                    .field('eventDescription', data.eventDescription)
                    .field('isRecurring', data.isRecurring)
                    .field('recurringFrequency', data.recurringFrequency)
                    .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                    .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                    .attach('documents', 'test/mock-data/3kb_file.png')
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.statusCode, 400);
                        orgGetStub.restore();
                        Event.create.restore();
                        done();
                    });
            });

        it('As a user I should check if the pto is onboarded on the stripe connect for the paid event and throw error if user is inactive',
            (done) => {
                getStub.resolves({
                    status: 'active', isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP, paymentDetails: {
                        stripeOnboardingStatus: 'inactive'
                    }, associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
                });

                const orgGetStub = sinon.stub(organizationModel, 'get');
                orgGetStub.resolves({
                    id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                    toJSON: () => { return { paymentDetails: { stripeOnboardingStatus: 'inactive' } }; }
                });

                sinon.stub(Event, 'create').resolves();

                request(process.env.BASE_URL)
                    .post('/event')
                    .set({ Authorization: requestPayloadUser.token })
                    .field('title', data.title)
                    .field('startDate', data.startDate)
                    .field('startTime', data.startTime)
                    .field('endDate', data.endDate)
                    .field('endTime', data.endTime)
                    .field('venue', data.venue)
                    .field('isPaid', data.isPaid)
                    .field('isDonatable', data.isDonatable)
                    .field('fee', data.fee)
                    .field('participantsLimit', data.participantsLimit)
                    .field('volunteerRequired', data.volunteerRequired)
                    .field('volunteerSignupURL', data.volunteerSignupURL)
                    .field('eventDescription', data.eventDescription)
                    .field('isRecurring', data.isRecurring)
                    .field('recurringFrequency', data.recurringFrequency)
                    .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                    .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                    .attach('documents', 'test/mock-data/3kb_file.png')
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.statusCode, 400);
                        orgGetStub.restore();
                        Event.create.restore();
                        done();
                    });
            });

        it('As a user I should validate start date is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Event, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', '14/29/2020')
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('isDonatable', data.isDonatable)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .attach('document', 'test/mock-data/3kb_file.png')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Event.create.restore();
                    done();
                });
        });

        it('As a user I should validate start time is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Event, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', '26:63')
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('isDonatable', data.isDonatable)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .attach('document', 'test/mock-data/3kb_file.png')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Event.create.restore();
                    done();
                });
        });

        it('As a user I should validate end date is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Event, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', '14/12/2020')
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('isDonatable', data.isDonatable)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .attach('document', 'test/mock-data/3kb_file.png')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Event.create.restore();
                    done();
                });
        });

        it('As a user I should validate end time is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Event, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', '26:62')
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('isDonatable', data.isDonatable)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .attach('document', 'test/mock-data/3kb_file.png')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Event.create.restore();
                    done();
                });
        });

        it('As a user I should validate start date time is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Event, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', '12/10/2020')
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('isDonatable', data.isDonatable)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .attach('document', 'test/mock-data/3kb_file.png')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Event.create.restore();
                    done();
                });
        });

        it('As a user I should validate end date time is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Event, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', '12/10/2020')
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('isDonatable', data.isDonatable)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .attach('document', 'test/mock-data/3kb_file.png')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Event.create.restore();
                    done();
                });
        });

        it('As a user I should validate that I am associated with an organization', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(null);
            sinon.stub(Event, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('isDonatable', data.isDonatable)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .attach('document', 'test/mock-data/3kb_file.png')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Event.create.restore();
                    done();
                });
        });

        it('As a user I should validate that image is of correct format', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Event, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('isDonatable', data.isDonatable)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', 'false')
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/TEST.pdf')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Event.create.restore();
                    done();
                });
        });

        it('As a user I should validate that documents are of correct format', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });
            sinon.stub(Event, 'create').resolves();
            const childOrganizationMapping = sinon.stub(childOrganizationMappingModel, 'query');
            childOrganizationMapping.withArgs('organizationId').returns({
                eq: () => ({
                    exec: () => Promise.resolve([{ childId: 123 }])
                })
            });
            request(process.env.BASE_URL)
                .post('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('isDonatable', data.isDonatable)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', 'false')
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('documents', 'test/mock-data/mock-data.js')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Event.create.restore();
                    done();
                });
        });

        it('As a user I should be able to add event with image', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Event, 'create').resolves({ ...eventList[0], status: 'published' });

            request(process.env.BASE_URL)
                .post('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('isDonatable', 'false')
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('quantityType', 'People')
                .field('quantityInstruction', 'Number of people in family')
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .attach('document', 'test/mock-data/3kb_file.png')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgGetStub.restore();
                    Event.create.restore();
                    done();
                });
        });

        it('As a user I should be validate membershipBenefitAmount is number and is greater than 0', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Event, 'create').resolves({ ...eventList[0], status: 'published' });

            request(process.env.BASE_URL)
                .post('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('isDonatable', 'false')
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('quantityType', 'People')
                .field('quantityInstruction', 'Number of people in family')
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .field('isMembershipEnabled', 'true')
                .field('membershipBenefitAmount', '0')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Event.create.restore();
                    done();
                });
        });

        it('As a user I should be able to add event with membership benefits enabled', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Event, 'create').resolves({ ...eventList[0], status: 'published' });

            request(process.env.BASE_URL)
                .post('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('isDonatable', 'false')
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('quantityType', 'People')
                .field('quantityInstruction', 'Number of people in family')
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .field('isMembershipEnabled', 'true')
                .field('membershipBenefitAmount', '5')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgGetStub.restore();
                    Event.create.restore();
                    done();
                });
        });

        it('As a user I should be able to add event with documents', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Event, 'create').resolves(eventList[0]);

            request(process.env.BASE_URL)
                .post('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('isDonatable', 'false')
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .attach('documents', 'test/mock-data/3kb_file.png')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgGetStub.restore();
                    Event.create.restore();
                    done();
                });
        });

        it('As a user I should be able to add calendar event', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Event, 'create').resolves({
                'eventScope': 'public',
                'fee': 200,
                'eventType': 'calendar',
                'title': 'Community Cleanup Day',
                'isDeleted': 0,
                'details': {
                    'venue': 'City Park',
                    'startDateTime': '2023-11-10T09:00:00.000Z',
                    'recurringFrequency': 'month',
                    'details': 'Join us for a community cleanup day at City Park. Let\'s make our city cleaner and greener!',
                    'isRecurring': true,
                    'endDateTime': '2023-11-10T17:00:00.000Z',
                    'documentURLs': ['test document']
                },
                'id': '54404ec7-15af-4ab8-afb3-8db63203e05e',
                'status': 'published',
                'organizationId': '5f5f7e5f7e5f7e5f7e5f7e5f',
                'photoURL': 'test image'
            });

            request(process.env.BASE_URL)
                .post('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('isDonatable', data.isDonatable)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .attach('documents', 'test/mock-data/3kb_file.png')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgGetStub.restore();
                    Event.create.restore();
                    done();
                });
        });

        it('As a user I should delete the uploaded image on S3 if event is not created successfully', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });

            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            request(process.env.BASE_URL)
                .post('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('isDonatable', data.isDonatable)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .attach('documents', 'test/mock-data/3kb_file.png')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
describe('Add Multiple Event', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });
        TestCase.addMultipleEvent.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                    accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                    associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });
                request(process.env.BASE_URL)
                    .post('/event/multiple')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should handle error if token is not passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .post('/event/multiple')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and correct token is passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .post('/event/multiple')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and is verified', (done) => {
            getStub.resolves({ status: 'active', isVerified: 0, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });
            request(process.env.BASE_URL)
                .post('/event/multiple')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 423);
                    done();
                });
        });

        it('As a user I should validate the role before creating event', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });
            request(process.env.BASE_URL)
                .post('/event/multiple')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 406);
                    done();
                });
        });



        it('As a user I should validate start date is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Event, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/event/multiple')
                .set({ Authorization: requestPayloadUser.token })
                .send([{
                    title: data.title,
                    startDate: data.startDate,
                    startTime: '01/27/2023',
                    endDate: data.endDate,
                    endTime: data.endTime,
                    eventDescription: data.eventDescription,
                    organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f'
                }])
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Event.create.restore();
                    done();
                });
        });

        it('As a user I should validate start time is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Event, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/event/multiple')
                .set({ Authorization: requestPayloadUser.token })
                .send([{
                    title: data.title,
                    startDate: data.startDate,
                    startTime: '26:50',
                    endDate: data.endDate,
                    endTime: data.endTime,
                    eventDescription: data.eventDescription,
                    organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f'
                }])
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Event.create.restore();
                    done();
                });
        });

        it('As a user I should validate end date is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Event, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/event/multiple')
                .set({ Authorization: requestPayloadUser.token })
                .send([{
                    startTime: data.startTime,
                    endDate: '01/01/2023',
                    title: data.title,
                    startDate: data.startDate,
                    endTime: data.endTime,
                    eventDescription: data.eventDescription,
                    organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f'
                }])
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Event.create.restore();
                    done();
                });
        });

        it('As a user I should validate end time is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Event, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/event/multiple')
                .set({ Authorization: requestPayloadUser.token })
                .send([{
                    title: data.title,
                    startDate: data.startDate,
                    startTime: data.startTime,
                    endDate: data.endDate,
                    endTime: '26:56',
                    eventDescription: data.eventDescription,
                    organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f'
                }])
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Event.create.restore();
                    done();
                });
        });

        it('As a user I should validate start date time is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Event, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/event/multiple')
                .set({ Authorization: requestPayloadUser.token })
                .send([{
                    title: data.title,
                    startDate: '00/20/2027',
                    startTime: data.startTime,
                    endDate: data.endDate,
                    endTime: data.endTime,
                    eventDescription: data.eventDescription,
                    organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f'
                }])
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Event.create.restore();
                    done();
                });
        });

        it('As a user I should validate start date time is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Event, 'batchPut').resolves();
            const childOrganizationMapping = sinon.stub(childOrganizationMappingModel, 'query');
            childOrganizationMapping.withArgs('organizationId').returns({
                eq: () => ({
                    exec: () => Promise.resolve([{ childId: 123 }])
                })
            });
            request(process.env.BASE_URL)
                .post('/event/multiple')
                .set({ Authorization: requestPayloadUser.token })
                .send([{
                    title: data.title,
                    startDate: data.startDate,
                    startTime: data.startTime,
                    endDate: data.endDate,
                    endTime: data.endTime,
                    eventDescription: data.eventDescription,
                    organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f'
                }])
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgGetStub.restore();
                    Event.batchPut.restore();
                    childOrganizationMapping.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Edit Event', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should validate that event id is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            request(process.env.BASE_URL)
                .put('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', 'abcd')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('Event with provided event id must exist', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Event, 'get').resolves(null);

            request(process.env.BASE_URL)
                .put('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 404);
                    Event.get.restore();
                    done();
                });
        });

        it('As a user I should be able to edit event with no deleted images or documents', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Event, 'get').resolves({ photoURL: '',
                organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', details: { documentURLs: new Set() }, save: () => {
                    return eventList[0];
                } });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);

            request(process.env.BASE_URL)
                .put('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('quantityType', 'People')
                .field('quantityInstruction', 'Number of people in family')
                .field('recurringFrequency', data.recurringFrequency)
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .attach('document', 'test/mock-data/3kb_file.png')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Event.get.restore();
                    done();
                });
        });

        it('As a user I should check if fee is passed', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Event, 'get').resolves({ photoURL: '1.png', isPaid: true, isDonatable: true,
                details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });

            request(process.env.BASE_URL)
                .put('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .field('documentURLs', data.documentURLs)
                .field('deletedImages[0]', 'amazonaws.com/1.png?')
                .field('deletedDocuments[0]', 'amazonaws.com/1.jpg?')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .attach('documents', 'test/mock-data/3kb_file.png')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Event.get.restore();
                    done();
                });
        });

        it('As a user I should check if fee passed is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Event, 'get').resolves({ photoURL: '1.png', isPaid: true, isDonatable: true,
                details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });

            request(process.env.BASE_URL)
                .put('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('fee', 0)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .field('documentURLs', data.documentURLs)
                .field('deletedImages[0]', 'amazonaws.com/1.png?')
                .field('deletedDocuments[0]', 'amazonaws.com/1.jpg?')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .attach('documents', 'test/mock-data/3kb_file.png')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Event.get.restore();
                    done();
                });
        });

        it('As a user I should check if volunteer signup url is passed', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Event, 'get').resolves({
                photoURL: '1.png', isPaid: true, isDonatable: true,
                volunteerRequired: true,
                details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });

            request(process.env.BASE_URL)
                .put('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .field('deletedImages[0]', 'amazonaws.com/1.png?')
                .field('deletedDocuments[0]', 'amazonaws.com/1.jpg?')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .attach('documents', 'test/mock-data/3kb_file.png')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Event.get.restore();
                    done();
                });
        });

        it('As a user I should validate start date is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Event, 'get').resolves({ photoURL: '1.png', isPaid: true, volunteerRequired: true,
                details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });
            request(process.env.BASE_URL)
                .put('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', '14/29/2020')
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Event.get.restore();
                    done();
                });
        });

        it('As a user I should validate start time is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Event, 'get').resolves({
                photoURL: '1.png', isPaid: true, isDonatable: true,
                volunteerRequired: true,
                details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });

            request(process.env.BASE_URL)
                .put('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', '26:63')
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Event.get.restore();
                    done();
                });
        });

        it('As a user I should validate end date is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Event, 'get').resolves({ photoURL: '1.png', isPaid: true, volunteerRequired: true,
                details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });

            request(process.env.BASE_URL)
                .put('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', '14/12/2020')
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Event.get.restore();
                    done();
                });
        });

        it('As a user I should validate end time is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Event, 'get').resolves({
                photoURL: '1.png', isPaid: true,
                volunteerRequired: true,
                details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });

            request(process.env.BASE_URL)
                .put('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', '26:62')
                .field('venue', data.venue)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Event.get.restore();
                    done();
                });
        });

        it('As a user I should validate start date time is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Event, 'get').resolves({ photoURL: '1.png', isPaid: true, volunteerRequired: true,
                details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });

            request(process.env.BASE_URL)
                .put('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', '12/10/2020')
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Event.get.restore();
                    done();
                });
        });

        it('As a user I should validate end date time is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Event, 'get').resolves({
                photoURL: '1.png', isPaid: true, isDonatable: true,
                volunteerRequired: true,
                details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });
            request(process.env.BASE_URL)
                .put('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', '12/10/2020')
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Event.get.restore();
                    done();
                });
        });

        it('As a user I should validate status cannot be change from published to unpublished', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Event, 'get').resolves({
                photoURL: '1.png', isPaid: true,
                volunteerRequired: true,
                status: 'published', details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });
            request(process.env.BASE_URL)
                .put('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .field('eventStatus', 'unpublished')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Event.get.restore();
                    done();
                });
        });

        it('As a user I should be able to update event with updating profile photo and adding more documents', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Event, 'get').resolves({ photoURL: '1.png', isPaid: true, isDonatable: false,
                organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', details: { documentURLs: new Set(['1.jpg']) },
                save: () => {
                    return { ...eventList[0], status: 'published' };
                } });

            request(process.env.BASE_URL)
                .put('/event')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('startTime', data.startTime)
                .field('endDate', data.endDate)
                .field('endTime', data.endTime)
                .field('venue', data.venue)
                .field('isPaid', data.isPaid)
                .field('isDonatable', data.isDonatable)
                .field('fee', data.fee)
                .field('participantsLimit', data.participantsLimit)
                .field('volunteerRequired', data.volunteerRequired)
                .field('volunteerSignupURL', data.volunteerSignupURL)
                .field('eventDescription', data.eventDescription)
                .field('isRecurring', data.isRecurring)
                .field('recurringFrequency', data.recurringFrequency)
                .field('documentURLs', data.documentURLs)
                .field('deletedImages[0]', 'amazonaws.com/1.png?')
                .field('deletedDocuments[0]', 'amazonaws.com/1.jpg?')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .attach('documents', 'test/mock-data/3kb_file.png')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Event.get.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Get Event List', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get event list that are created under my organization', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            const queryStub = sinon.stub(Event, 'query');
            const usingStub = sinon.stub();
            const attributesStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: eqStub.withArgs('organizationId').returns({ using: usingStub }) });
            usingStub.returns({ attributes: attributesStub });
            attributesStub.returns({ exec: execStub });
            execStub.resolves(eventList);

            request(process.env.BASE_URL)
                .get('/event/list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    queryStub.restore();
                    done();
                });
        });

        it('As a user I should get event list that are created under my organization with the passed status', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', role: 'admin' }]
            });

            const queryStub = sinon.stub(Event, 'query');
            const usingStub = sinon.stub();
            const attributesStub = sinon.stub();
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: eqStub.withArgs('organizationId').returns({ using: usingStub }) });
            usingStub.returns({ attributes: attributesStub });
            attributesStub.returns({ where: whereStub.returns({ eq: eqStub.withArgs('draft').returns({ exec: execStub }) }) });
            execStub.resolves(eventList);

            request(process.env.BASE_URL)
                .get('/event/list?status=draft')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    queryStub.restore();
                    done();
                });
        });

        it('As a user I should get empty event list if none exists', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            const queryStub = sinon.stub(Event, 'query');
            const usingStub = sinon.stub();
            const attributesStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: eqStub.withArgs('organizationId').returns({ using: usingStub }) });
            usingStub.returns({ attributes: attributesStub });
            attributesStub.returns({ exec: execStub });
            execStub.resolves([]);

            request(process.env.BASE_URL)
                .get('/event/list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    queryStub.restore();
                    done();
                });
        });

        it('As a user I should event list for an organization', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', role: 'admin' }]
            });

            const queryStub = sinon.stub(Event, 'query');
            const usingStub = sinon.stub();
            const attributesStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: eqStub.withArgs('organizationId').returns({ using: usingStub }) });
            usingStub.returns({ attributes: attributesStub });
            attributesStub.returns({ exec: execStub });
            execStub.resolves([]);

            request(process.env.BASE_URL)
                .get('/event/list?organizationId=5f5f7e5f7e5f7e5f7e5f7e5f')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    queryStub.restore();
                    done();
                });
        });

        it('As a user I should event list for an organization with the passed status', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', role: 'admin' }]
            });

            const queryStub = sinon.stub(Event, 'query');
            const usingStub = sinon.stub();
            const attributesStub = sinon.stub();
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: eqStub.withArgs('organizationId').returns({ using: usingStub }) });
            usingStub.returns({ attributes: attributesStub });
            attributesStub.returns({ where: whereStub.returns({ eq: eqStub.withArgs('draft').returns({ exec: execStub }) }) });
            execStub.resolves(eventList);

            request(process.env.BASE_URL)
                .get('/event/list?status=draft&organizationId=5f5f7e5f7e5f7e5f7e5f7e5f')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    queryStub.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a user I should get error is passed organizationId is not associated with me', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5a', role: 'admin' }]
            });

            sinon.stub(OrganizationMembers, 'get').resolves(null);

            const queryStub = sinon.stub(Event, 'query');
            const usingStub = sinon.stub();
            const attributesStub = sinon.stub();
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: eqStub.withArgs('organizationId').returns({ using: usingStub }) });
            usingStub.returns({ attributes: attributesStub });
            attributesStub.returns({ where: whereStub.returns({ eq: eqStub.withArgs('draft').returns({ exec: execStub }) }) });
            execStub.resolves(eventList);

            request(process.env.BASE_URL)
                .get('/event/list?status=draft&organizationId=5f5f7e5f7e5f7e5f7e5f7e5f')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    queryStub.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Handle Error in getting list', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get error if something went wrong', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            const queryStub = sinon.stub();
            const inStub = sinon.stub();
            const attributesStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: inStub });
            inStub.returns({ attributes: attributesStub });
            attributesStub.returns({ exec: execStub });
            execStub.rejects();
            sinon.replace(Event, 'query', queryStub);

            request(process.env.BASE_URL)
                .get('/event/list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Get Event Details', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should validate the eventId passed', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            request(process.env.BASE_URL)
                .get('/event?eventId=abcd')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error message if event doesnt exists', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(Event, 'get').resolves(null);

            request(process.env.BASE_URL)
                .get('/event?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Event.get.restore();
                    done();
                });
        });

        it('As a user I should get the event details', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(Event, 'get').resolves(eventListWithoutImage[0]);

            request(process.env.BASE_URL)
                .get('/event?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Event.get.restore();
                    done();
                });
        });

        it('As a user I should get the event details with signed urls of image and documents', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(Event, 'get').resolves(eventList[0]);

            request(process.env.BASE_URL)
                .get('/event?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Event.get.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a user I should get error message if event doesn\'t exists, is deleted or if it doesn\'t belong to organization id',
            (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                    accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                    associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5' }] });

                sinon.stub(OrganizationMembers, 'get').resolves(null);

                sinon.stub(Event, 'get').resolves(eventList[0]);

                request(process.env.BASE_URL)
                    .get('/event?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                    .set({ Authorization: requestPayloadUser.token })
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.statusCode, 400);
                        Event.get.restore();
                        done();
                    });
            });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Publish Event', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should be able to publish event', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            sinon.stub(Event, 'get').resolves({ ...eventList[0], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .patch('/event/publish?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Event.get.restore();
                    done();
                });
        });

        it('As a user I should get message if event is already published', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(Event, 'get').resolves({ ...eventListWithoutImage[0], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .patch('/event/publish?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Event.get.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a user I should get error message if I am not associated with organization', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5a' }] });

            sinon.stub(Event, 'get').resolves({ ...eventListWithoutImage[0], save: () => Promise.resolve() });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'deleted' }]
            });

            request(process.env.BASE_URL)
                .patch('/event/publish?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Event.get.restore();
                    done();
                });
        });

        it('As a user I should get error if event doesn\'t exists, is deleted or if it doesn\'t belong to organization id', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5' }] });

            sinon.stub(Event, 'get').resolves(null);

            request(process.env.BASE_URL)
                .patch('/event/publish?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Event.get.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Delete Event', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get be able to delete event', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            sinon.stub(Event, 'get').resolves({ ...eventList[0], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .delete('/event?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Event.get.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As an editor I should delete event', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: 'editor', status: 'active' }]
            });

            sinon.stub(Event, 'get').resolves({ ...eventList[0], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .delete('/event?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Event.get.restore();
                    done();
                });
        });

        it('As a editor I should get error message if I try to delete published event', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(Event, 'get').resolves({ ...eventListWithoutImage[0], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .delete('/event?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 403);
                    Event.get.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a admin I should get error message if event is already published', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            sinon.stub(Event, 'get').resolves({ ...eventListWithoutImage[0], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .delete('/event?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Event.get.restore();
                    done();
                });
        });

        it('As a user I should get error if event doesn\'t exists, is deleted or if it doesn\'t belong to organization id', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(Event, 'get').resolves({ ...eventListWithoutImage[0], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .delete('/event?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Event.get.restore();
                    done();
                });
        });

        it('As a user I should get error I doesnt belong to that organization while deleting event', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5a' }] });

            sinon.stub(Event, 'get').resolves({ ...eventListWithoutImage[0], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .delete('/event?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Event.get.restore();
                    done();
                });
        });

        it('As a user I should get error if event doesnt exists', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5a' }] });

            sinon.stub(Event, 'get').resolves(null);

            request(process.env.BASE_URL)
                .delete('/event?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Event.get.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Get List of Event participants', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get error in get participants list', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const eventGetStub = sinon.stub(Event, 'get');
            eventGetStub.resolves();
            request(process.env.BASE_URL)
                .get('/event/participant?eventId=123')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    eventGetStub.restore();
                    done();
                });
        });

        it('As a user I should get event participant list that are created under my organization', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            const queryStub = sinon.stub(EventSignup, 'query');
            const whereStub = sinon.stub();
            const usingStub = sinon.stub();
            const eqStub = sinon.stub();
            const inStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({ eq: eqStub.withArgs('eventId').returns({ using: usingStub }) });
            usingStub.returns({ where: whereStub });
            whereStub.withArgs('organizationId').returns({ eq: inStub.withArgs(['5f5f7e5f7e5f7e5f7e5f7e5f']).returns({ exec: execStub }) });
            const eventList = [
                { id: 'event1', eventId: '123', organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                    paymentDetails: {}, parentId: 'parent1', childId: 'child1' },
                { id: 'event2', eventId: '123', organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                    paymentDetails: { paymentType: 'cheque' }, parentId: 'parent2', childId: 'child2' }
            ];
            execStub.resolves(eventList);

            const userQueryStub = sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ firstName: 'user', lastName: 'Name' }])
                    })
                }).withArgs('parent1').returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ firstName: 'user1', lastName: 'Name1' }])
                    })
                }).withArgs('parent2').returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ firstName: 'user2', lastName: 'Name2' }])
                    })
                })
            });

            sinon.stub(organizationModel, 'get').resolves({ name: 'Test' });

            const eventGetStub = sinon.stub(Event, 'get');
            eventGetStub.resolves({
                id: '123',
                title: 'Event Title',
                eventType: 'Some Type',
                eventScope: 'Some Scope',
                status: 'Active',
                membershipBenefitDetails: {
                    benefitDiscount: 10,
                    isOnlyForMembers: true
                }
            });

            const childrenGetStub = sinon.stub(Children, 'get').resolves({ id: 'child1', name: 'Child Name 1' });
            childrenGetStub.withArgs({ id: 'child1' }).resolves({ id: 'child1', name: 'Child Name 1', homeRoom: '121456' });
            childrenGetStub.withArgs({ id: 'child2' }).resolves({ id: 'child2', name: 'Child Name 2' });

            request(process.env.BASE_URL)
                .get('/event/participant?eventId=123')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    queryStub.restore();
                    eventGetStub.restore();
                    userQueryStub.restore();
                    organizationModel.get.restore();
                    done();
                });
        });

        it('As a user I should get empty event participant list that are created under my organization', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });

            const queryStub = sinon.stub(EventSignup, 'query');
            const usingStub = sinon.stub();
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const inStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({ eq: eqStub.withArgs('eventId').returns({ using: usingStub }) });
            usingStub.returns({ where: whereStub });
            whereStub.withArgs('organizationId').returns({ eq: inStub.withArgs(['5f5f7e5f7e5f7e5f7e5f7e5f']).returns({ exec: execStub }) });
            const eventList = [];
            execStub.resolves(eventList);
            const eventGetStub = sinon.stub(Event, 'get');
            eventGetStub
                .resolves({ id: '123', title: 'Event Title', eventType: 'Some Type', eventScope: 'Some Scope', status: 'Active' });
            request(process.env.BASE_URL)
                .get('/event/participant?eventId=123')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    queryStub.restore();
                    eventGetStub.restore();
                    done();
                });
        });

        it('As a user I should get error if eventId is not valid', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5ff' }]
            });
            const queryStub = sinon.stub(EventSignup, 'query');
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const inStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({ eq: eqStub.withArgs('eventId').returns({ where: whereStub }) });
            whereStub.withArgs('organizationId').returns({ eq: inStub.withArgs(['5f5f7e5f7e5f7e5f7e5f7e5f']).returns({ exec: execStub }) });
            const eventList = [];
            execStub.rejects(eventList);
            const eventGetStub = sinon.stub(Event, 'get');
            eventGetStub
                .resolves({ id: '123', title: 'Event Title', eventType: 'Some Type', eventScope: 'Some Scope', status: 'Active' });
            request(process.env.BASE_URL)
                .get('/event/participant?eventId=123')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    eventGetStub.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Update payment status of Event participants', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a PTO admin I should update status of participants if that is paid via cash', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });
            const scanStub = sinon.stub(EventSignup, 'get');
            scanStub.returns({
                organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: {
                    paymentStatus: 'pending', paymentType: 'cash'
                }, save: () => Promise.resolve()
            });

            const userQueryStub = sinon.stub(User, 'query');
            userQueryStub.returns({
                eq: () => ({
                    exec: () => ({
                        toJSON: () => [{
                            email: '<EMAIL>'
                        }]
                    })
                })
            });

            const childrenQueryStub = sinon.stub(Children, 'get').resolves({
                firstName: 'John',
                lastName: 'Doe'
            });

            const eventQueryStub = sinon.stub(Event, 'get').resolves({
                title: 'Event Title',
                details: {
                    startDateTime: '2023-11-16T12:00:00Z',
                    endDateTime: '2023-11-16T14:00:00Z',
                    venue: 'Event Venue'
                },
                save: () => Promise.resolve()
            });

            request(process.env.BASE_URL)
                .put('/event/signup-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: '123', status: 'approved' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    scanStub.restore();
                    userQueryStub.restore();
                    childrenQueryStub.restore();
                    eventQueryStub.restore();
                    done();
                });
        });

        it('As a PTO admin I should update status of participants if that is paid via cheque', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const scanStub = sinon.stub(EventSignup, 'get');
            scanStub.returns({
                organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: {
                    paymentStatus: 'pending', paymentType: 'cheque'
                }, save: () => Promise.resolve()
            });

            const userQueryStub = sinon.stub(User, 'query');
            userQueryStub.returns({
                eq: () => ({
                    exec: () => ({
                        toJSON: () => [{
                            email: '<EMAIL>'
                        }]
                    })
                })
            });

            const childrenQueryStub = sinon.stub(Children, 'get').resolves({
                firstName: 'John',
                lastName: 'Doe'
            });

            const eventQueryStub = sinon.stub(Event, 'get').resolves({
                title: 'Event Title',
                details: {
                    startDateTime: '2023-11-16T12:00:00Z',
                    endDateTime: '2023-11-16T14:00:00Z',
                    venue: 'Event Venue'
                },
                save: () => Promise.resolve()
            });

            request(process.env.BASE_URL)
                .put('/event/signup-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: '123', status: 'approved' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    scanStub.restore();
                    userQueryStub.restore();
                    childrenQueryStub.restore();
                    eventQueryStub.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a PTO editor I should get error if I try to update status of participants', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5a', role: 'admin' }]
            });
            const scanStub = sinon.stub(EventSignup, 'get');
            scanStub.returns({
                organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: {
                    paymentStatus: 'pending', paymentType: 'cheque'
                }
            });
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: 'editor', status: 'active' }]
            });
            request(process.env.BASE_URL)
                .put('/event/signup-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: '123', status: 'approved' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 403);
                    scanStub.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a PTO admin I should get error if associated org not found', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5a', role: 'admin' }]
            });
            const scanStub = sinon.stub(EventSignup, 'get');
            scanStub.returns({
                organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: {
                    paymentStatus: 'pending', paymentType: 'cheque'
                }
            });
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: 'super admin', status: 'active' }]
            });
            request(process.env.BASE_URL)
                .put('/event/signup-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: '123', status: 'approved' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    scanStub.restore();
                    done();
                });
        });

        it('As a PTO admin I should get error if participant already paid for the event', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const scanStub = sinon.stub(EventSignup, 'get');
            scanStub.returns({
                organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: {
                    paymentStatus: 'approved', paymentType: 'cheque'
                }
            });
            request(process.env.BASE_URL)
                .put('/event/signup-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: '123', status: 'approved' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    scanStub.restore();
                    done();
                });
        });

        it('As a PTO admin I should get error if event doesnt exists', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const eventGetStub = sinon.stub(EventSignup, 'get');
            eventGetStub.returns(null);
            request(process.env.BASE_URL)
                .put('/event/signup-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: '123', status: 'approved' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    eventGetStub.restore();
                    done();
                });
        });


        it('As a PTO admin I should throw error if payment type was stripe', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const scanStub = sinon.stub(EventSignup, 'get');
            scanStub.returns({
                organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: {
                    paymentStatus: 'pending', paymentType: 'stripe'
                }, save: () => Promise.resolve()
            });

            request(process.env.BASE_URL)
                .put('/event/signup-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: '123', status: 'pending' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    scanStub.restore();
                    done();
                });
        });

        it('As a PTO admin I should get error if status passed is not from allowed list', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });

            request(process.env.BASE_URL)
                .put('/event/signup-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: '123', status: 'register' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Event Search', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get error if search query is not passed', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3 });
            request(process.env.BASE_URL)
                .get('/event/search')
                .set({ Authorization:
                    requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get the search results for the query passed', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1, children: ['child-id1'] });

            sinon.stub(AwsOpenSearchService, 'getAllChildEvents').resolves([{
                _source: { id: 'child-id1', childEvents: ['event-id1'], childCalendarEvents: ['event-id2'], school: '123', homeRoom: '222' }
            }]);

            sinon.stub(AwsOpenSearchService, 'getAllEvents').resolves([{
                _source: { id: 'event-id1', details: { startDateTime: '2023-11-10T09:00:00.000Z' } }
            }]);

            sinon.stub(organizationModel, 'get').resolves({ name: 'org-name' });

            request(process.env.BASE_URL)
                .get('/event/search?query=Event')
                .set({ Authorization:
                    requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    AwsOpenSearchService.getAllChildEvents.restore();
                    AwsOpenSearchService.getAllEvents.restore();
                    organizationModel.get.restore();
                    done();
                });
        });

        it('As a user I should get the search results for the query passed with childId passed', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1, children: ['child-id1'] });

            sinon.stub(AwsOpenSearchService, 'getAllChildEvents').resolves([{
                _source: {
                    id: 'child-id1', childEvents: ['event-id1'], photoURL: 'test-url.com',
                    childCalendarEvents: ['event-id2'], school: '123'
                }
            }]);

            sinon.stub(AwsOpenSearchService, 'getAllEvents').resolves([{
                _source: { id: 'event-id1', details: { startDateTime: '2023-11-10T09:00:00.000Z' } }
            }]);

            sinon.stub(organizationModel, 'get').resolves({ name: 'org-name' });

            request(process.env.BASE_URL)
                .get('/event/search?query=Event&childId=child-id1')
                .set({ Authorization:
                    requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    AwsOpenSearchService.getAllChildEvents.restore();
                    AwsOpenSearchService.getAllEvents.restore();
                    organizationModel.get.restore();
                    done();
                });
        });

        it('As a user I should get empty array if there is no childEvents and childCalendarEvents', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1, children: ['child-id1'] });

            sinon.stub(AwsOpenSearchService, 'getAllChildEvents').resolves([{
                _source: {
                    id: 'child-id1', photoURL: 'test-url.com', school: '123'
                }
            }]);

            sinon.stub(AwsOpenSearchService, 'getAllEvents').resolves([{
                _source: { id: 'event-id1', details: { startDateTime: '2023-11-10T09:00:00.000Z' } }
            }]);

            sinon.stub(organizationModel, 'get').resolves({ name: 'org-name' });

            request(process.env.BASE_URL)
                .get('/event/search?query=Event&childId=child-id1')
                .set({ Authorization:
                    requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    AwsOpenSearchService.getAllChildEvents.restore();
                    AwsOpenSearchService.getAllEvents.restore();
                    organizationModel.get.restore();
                    done();
                });
        });

        it('As a user I should get the search results for the query passed returning empty array', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1, children: ['child-id1'] });

            request(process.env.BASE_URL)
                .get('/event/search?query=Event&childId=child-id1')
                .set({ Authorization:
                    requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Start Upload Chunk', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.startChunkUpload.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                    accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                    associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });
                request(process.env.BASE_URL)
                    .post('/event/start-chunk-upload')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should get error if I am not associated with the organization', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: []
            });

            sinon.stub(OrganizationMembers, 'get').resolves(null);

            request(process.env.BASE_URL)
                .post('/event/start-chunk-upload')
                .set({ Authorization: requestPayloadUser.token })
                .send({ organizationId: '23efd596-1779-4b73-b0c2-9f74ab30915d', fileSize: 100, originalname: 'test.csv' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a user I should get error if file is not passed and organization is associated with the user', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '23efd596-1779-4b73-b0c2-9f74ab30915d' }]
            });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            request(process.env.BASE_URL)
                .post('/event/start-chunk-upload')
                .set({ Authorization: requestPayloadUser.token })
                .send({ organizationId: '23efd596-1779-4b73-b0c2-9f74ab30915d', fileSize: 100, originalname: 'test.csv' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a user I should be able to start upload chunk if file is passed', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '23efd596-1779-4b73-b0c2-9f74ab30915d' }]
            });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            request(process.env.BASE_URL)
                .post('/event/start-chunk-upload')
                .set({ Authorization: requestPayloadUser.token })
                .field('organizationId', '23efd596-1779-4b73-b0c2-9f74ab30915d')
                .field('fileSize', 100)
                .field('originalname', 'test.csv')
                .attach('file', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a user I should be able to start upload chunk if file size is greater than 5MB', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '23efd596-1779-4b73-b0c2-9f74ab30915d' }]
            });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            request(process.env.BASE_URL)
                .post('/event/start-chunk-upload')
                .set({ Authorization: requestPayloadUser.token })
                .field('organizationId', '23efd596-1779-4b73-b0c2-9f74ab30915d')
                .field('fileSize', 8 * 1024 * 1024)
                .field('originalname', 'test.csv')
                .attach('file', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    OrganizationMembers.get.restore();
                    done();
                });
        });


    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Upload file in chunks', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.uploadChunk.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                    accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP });
                request(process.env.BASE_URL)
                    .post('/event/upload-chunk')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should get error if file is not passed', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0,
                role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
            });

            request(process.env.BASE_URL)
                .post('/event/upload-chunk')
                .set({ Authorization: requestPayloadUser.token })
                .send({ fileName: 'test', uploadId: 'test', partNumber: 1 })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should be able to upload file in chunks', (done) => {
            request(process.env.BASE_URL)
                .post('/event/upload-chunk')
                .set({ Authorization: requestPayloadUser.token })
                .field('fileName', 'test')
                .field('uploadId', 'test')
                .field('partNumber', 1)
                .attach('file', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Complete Upload Chunk', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.completeChunkUpload.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
                });
                request(process.env.BASE_URL)
                    .post('/event/complete-chunk-upload')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should be able to complete upload chunk', (done) => {
            request(process.env.BASE_URL)
                .post('/event/complete-chunk-upload')
                .set({ Authorization: requestPayloadUser.token })
                .send({ fileName: 'test', uploadId: 'test', parts: [{ PartNumber: 1, ETag: 'etag1' }, { PartNumber: 2, ETag: 'etag2' }] })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Abort Upload Chunk', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.abortChunkUpload.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
                });
                request(process.env.BASE_URL)
                    .post('/event/abort-chunk-upload')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should be able to abort upload chunk', (done) => {
            request(process.env.BASE_URL)
                .post('/event/abort-chunk-upload')
                .set({ Authorization: requestPayloadUser.token })
                .send({ fileName: 'test', uploadId: 'test' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Generate Presigned Url', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.generatePresignedUrl.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                    accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                    associatedOrganizations:
                        [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '23efd596-1779-4b73-b0c2-9f74ab30915d' }]
                });

                sinon.stub(OrganizationMembers, 'get').resolves({
                    users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
                });

                request(process.env.BASE_URL)
                    .get('/event/generate-presigned-url')
                    .set({ Authorization: requestPayloadUser.token })
                    .query(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        OrganizationMembers.get.restore();
                        done();
                    });
            });
        });

        it('As a user I should be able to generate presigned url', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '23efd596-1779-4b73-b0c2-9f74ab30915d' }]
            });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            request(process.env.BASE_URL)
                .get('/event/generate-presigned-url')
                .set({ Authorization: requestPayloadUser.token })
                .query({ fileName: 'test', organizationId: 'test' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a user I should be able to generate presigned url with eventId', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '23efd596-1779-4b73-b0c2-9f74ab30915d' }]
            });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            request(process.env.BASE_URL)
                .get('/event/generate-presigned-url')
                .set({ Authorization: requestPayloadUser.token })
                .query({ organizationId: 'test', fileName: 'test', eventId: 'test' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    OrganizationMembers.get.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
