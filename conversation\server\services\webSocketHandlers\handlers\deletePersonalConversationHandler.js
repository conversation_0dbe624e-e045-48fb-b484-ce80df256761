const PersonalConversationModel = require('../../../models/personalConversation.model');
const constants = require('../../../util/constants');
const SendMessageService = require('../../sendSocketMessageService');

/**
 * @desc This function is being used to handle delete personal conversation
 * <AUTHOR>
 * @since 27/01/2025
 * @param {Object} eventBody Event Body
 */
module.exports.handleDeletePersonalConversation = async (eventBody) => {
    try {
        const { senderId, receiverId, conversationId, lastMessageId } = eventBody;
        const personalConversation = await getPersonalConversation(senderId, receiverId);

        personalConversation.lastMessageIdBeforeDeleted = lastMessageId;
        await personalConversation.save();

        const data = {
            userAId: senderId,
            userBId: receiverId,
            lastMessageIdBeforeDeleted: lastMessageId,
            conversationId
        };

        await SendMessageService.sendMessagesToUsers(
            [senderId],
            {
                data,
                actionType: constants.ACTION_TYPES.SEND_PERSONAL_MESSAGE.DELETE_PERSONAL_CONVERSATION
            }
        );

        return {
            statusCode: 200,
            message: 'Personal conversation deleted successfully',
            action: 'sendPersonalMessage',
            actionType: constants.ACTION_TYPES.SEND_PERSONAL_MESSAGE.DELETE_PERSONAL_CONVERSATION,
            data: JSON.stringify(data)
        };
    } catch (error) {
        if (error.statusCode) {
            return error;
        }

        return {
            statusCode: 500,
            message: 'Failed to delete personal conversation',
            action: 'sendPersonalMessage',
            actionType: constants.ACTION_TYPES.SEND_PERSONAL_MESSAGE.DELETE_PERSONAL_CONVERSATION
        };
    }
};

/**
 * @desc This function is being used to get personal conversation
 * <AUTHOR>
 * @since 27/01/2025
 * @param {String} senderId Sender Id
 * @param {String} receiverId Receiver Id
 */
const getPersonalConversation = async (senderId, receiverId) => {
    const personalConversation = await PersonalConversationModel.get({ userAId: senderId, userBId: receiverId });

    if (!personalConversation) {
        throw {
            statusCode: 404,
            message: 'Personal conversation not found',
            action: 'sendPersonalMessage',
            actionType: constants.ACTION_TYPES.SEND_PERSONAL_MESSAGE.DELETE_PERSONAL_CONVERSATION
        };
    }

    return personalConversation;
};
