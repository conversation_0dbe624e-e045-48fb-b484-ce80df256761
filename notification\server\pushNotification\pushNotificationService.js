/* eslint-disable max-len */
const PendingPushNotification = require('../models/pendingPushNotification.model');
const MOMENT = require('moment');
const Notification = require('../util/notification');
const NotificationModel = require('../models/notification.model');
const ChildModel = require('../models/child.model');
const stringify = require('json-stringify-safe');
class PushNotificationService {
    static async pushNotfication () {
        try {
            const pendingNotification = await PendingPushNotification.scan().exec();
            if (pendingNotification.length > 0) {
                for (const notification of pendingNotification) {
                    const { pushNotificationTime, payload } = notification;
                    const currentTime = MOMENT().utc();
                    CONSOLE_LOGGER.info('Current time', currentTime, 'Push notification time', pushNotificationTime, MOMENT(pushNotificationTime).isBefore(currentTime));
                    if (MOMENT(pushNotificationTime).isBefore(currentTime)) {
                        await this.sendEventNotification(notification, payload.triggerAt);
                        await PendingPushNotification.delete({ id: notification.id });
                        CONSOLE_LOGGER.info('Notification sent and deleted from pending notification table', notification.id);
                    }
                }
            }
        } catch (error) {
            CONSOLE_LOGGER.error(error.message, 'Error sending notification');
        }
    }

    static async sendEventNotification (notification, hoursUntilNotification) {
        const { notificationAction, associatedChildId, payload } = notification;
        const notificationMsg = await this.getNotificationMessage(payload, hoursUntilNotification);
        const child = await ChildModel.get({ id: associatedChildId });
        const notificationData = {
            details: JSON.stringify({
                associatedChild: { id: associatedChildId },
                score: payload.score,
                id: payload.id,
                startDateTime: payload.startDateTime,
                endDateTime: payload.endDateTime
            }),
            child: JSON.stringify({
                id: child.id,
                firstName: child.firstName,
                lastName: child.lastName,
                photoURL: child.photoURL,
                associatedColor: child.associatedColor
            }),
            route: 'eventDetails'
        };
        const notificationObject = Notification.createNotificationObject({
            title: 'Reminder',
            topic: `childId-${associatedChildId}`,
            body: notificationMsg,
            data: notificationData,
            clickAction: notificationAction
        });
        CONSOLE_LOGGER.info('Sending notification for event signup with object: ', stringify(notificationObject));
        await Notification.sendNotification(notificationObject);
        await this.pushInNotificationTable({ ...notification, title: 'Reminder' }, notificationData, notificationMsg);
    }

    static async pushInNotificationTable (notification, payload, description, trigger) {
        CONSOLE_LOGGER.info('Pushing notification in notification table', notification, payload, description);
        const { associatedChildId, notificationAction, title } = notification;
        const child = await ChildModel.get({ id: associatedChildId });
        for (const guardian of child.guardians) {
            const notificationObject = {
                description,
                associatedChildId,
                notificationAction,
                payload,
                title,
                userId: guardian,
                readStatus: false
            };
            await NotificationModel.create(notificationObject);
            if (trigger === CONSTANTS.TRIGGER.POST_CREATED) {
                const notificationObject = Notification.createNotificationObject({
                    title,
                    data: payload,
                    body: description,
                    topic: `userId-${guardian}`,
                    collapseKey: `${guardian}-${trigger}`,
                    clickAction: 'vaalee://routeName=postDetails'
                });
                await Notification.sendNotification(notificationObject);
            }
        }
    }

    static async getNotificationMessage (payload, hoursUntilNotification) {
        const { startDateTime, endDateTime, title } = payload;
        const start = MOMENT(startDateTime);
        const end = MOMENT(endDateTime);
        const isMultiDayEvent = !start.isSame(end, 'day');
        let message = '';
        if (hoursUntilNotification === '24') {
            if (isMultiDayEvent) {
                message = `${title} begins in 24 hours! Get ready for an immersive multi-day experience!`;
            } else {
                message = `${title} is starting in the next 24 hours! Don't miss out!`;
            }
        } else if (hoursUntilNotification === '72') {
            message = `${title} starts in the next 3 days. Don't miss out!`;
        }
        return message;
    }

}

module.exports = PushNotificationService;
