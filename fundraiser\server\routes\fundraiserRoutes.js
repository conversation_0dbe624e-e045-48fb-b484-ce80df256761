/**
 * This file contains routes used for parent user.
 * Created by Growexx on 19/10/2023.
 * @name parentRoutes
 */
const router = require('express').Router();

const FundraiserController = require('../services/fundraiser/fundraiserController');

const AuthMiddleware = require('../middleware/auth');
const AclMiddleware = require('../middleware/acl');
const UploadMiddleWare = require('../middleware/upload');

router.post('/', AuthMiddleware, AclMiddleware, UploadMiddleWare.any(), FundraiserController.addFundraiser);
router.delete('/', AuthMiddleware, AclMiddleware, FundraiserController.deleteFundraiser);
router.patch('/publish', AuthMiddleware, AclMiddleware, FundraiserController.publishFundraiser);

router.get('/list', AuthMiddleware, AclMiddleware, FundraiserController.getFundraiserList);
router.get('/', AuthMiddleware, AclMiddleware, FundraiserController.getFundraiserDetails);
router.put('/', AuthMiddleware, AclMiddleware, UploadMiddleWare.any(), FundraiserController.updateFundraiser);
router.get('/search', AuthMiddleware, FundraiserController.getSearchFundraiserList);

router.get('/participant', AuthMiddleware, AclMiddleware, FundraiserController.getParticipantFundraiserList);
router.put('/signup-status', AuthMiddleware, AclMiddleware, FundraiserController.updatePaymentStatus);
router.patch('/signup-fulfilled-status', AuthMiddleware, AclMiddleware, FundraiserController.updateFulfilledStatus);
router.get('/generate-presigned-url', AuthMiddleware, AclMiddleware, FundraiserController.generatePresignedUrl);
module.exports = router;
