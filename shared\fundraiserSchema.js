const fundraiserSchema = {
    'index': 'fundraisers',
    'body': {
        'mappings': {
            'properties': {
                'id': {
                    'type': 'keyword',
                    'index': true
                },
                'title': {
                    'type': 'text',
                    'fields': {
                        'keyword': {
                            'type': 'keyword',
                            'ignore_above': 256
                        }
                    }
                },
                'description': {
                    'type': 'text'
                },
                'startDate': {
                    'type': 'date',
                    'format': 'strict_date_optional_time||epoch_millis'
                },
                'endDate': {
                    'type': 'date',
                    'format': 'strict_date_optional_time||epoch_millis'
                },
                'imageURL': {
                    'type': 'keyword'
                },
                'fundraiserType': {
                    'type': 'keyword'
                },
                'products': {
                    'type': 'keyword'
                },
                'membershipBenefitDetails': {
                    'properties': {
                        'benefitDiscount': {
                            'type': 'integer'
                        },
                        'isOnlyForMembers': {
                            'type': 'boolean'
                        }
                    }
                },
                'organizationId': {
                    'type': 'keyword'
                },
                'status': {
                    'type': 'keyword'
                }
            }
        }
    }
};

module.exports = fundraiserSchema;
