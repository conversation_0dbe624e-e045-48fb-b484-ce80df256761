/**
 *  routes and schema for Parents routes
 */

/**
 * @openapi
 * components:
 *  schemas:
 *      addChild:
 *          type: object
 *          required:
 *              - firstName
 *              - lastName
 *              - dob
 *              - schoolId
 *              - zipCode
 *              - associatedColor
 *          properties:
 *              firstName:
 *                  type: string
 *                  description: first name of the child
 *              lastName:
 *                  type: string
 *                  description: last name of the child
 *              dob:
 *                  type: string
 *                  description: dob of child in MM/DD/YYYY format
 *              schooolId:
 *                  type: string
 *                  description: schoolId of the selected school from dropdown
 *              homeroomId:
 *                  type: string
 *                  description: homeroomId of the selected school from dropdown
 *              zipCode:
 *                  type: string
 *                  description: zipCode of child
 *              associatedColor:
 *                  type: string
 *                  description: associated color of child; one of ['#2772ED', '#FACD01', '#90C33A', '#FF82A9', '#CB66D9', '#FF8F7C']
 *
 *          example:
 *                   firstName: Test
 *                   lastName: User
 *                   dob: 12/10/2005
 *                   schoolId: 0fd6a871-a6f5-4690-b06a-d786f1361eef
 *                   homeroomId: 9fd6a871-a6f5-4690-b06a-d786f1361eef
 *                   zipCode: '12345'
 *                   associatedColor: '#90C33A'
 *
 *      successSchoolList:
 *          type: object
 *          properties:
 *              status:
 *                  type: number
 *                  description: status if data exists
 *              data:
 *                  type: string
 *                  description: school list data
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data:
 *                [
 *                  {
                        "applicableAgeRange": "18 months to 6 years",
                        "organizationType": "Montessori/Private",
                        "zipCode": "98765",
                        "createdAt": "2023-10-19T16:22:08.334Z",
                        "name": "Little Explorers Montessori",
                        "id": "1996eabf-1a94-4689-a3c5-8b913744e314",
                        "updatedAt": "2023-10-19T16:22:08.334Z"
                    },
 *                  {
                        "applicableAgeRange": "2 years to 5 years",
                        "organizationType": "Private",
                        "zipCode": "67890",
                        "createdAt": "2023-10-19T16:22:08.334Z",
                        "name": "Sunny Day Preschool",
                        "id": "eeaab921-ff0b-4b91-9cb6-473ef8a11908",
                        "updatedAt": "2023-10-19T16:22:08.334Z"
                    },
 *                ]
 *              message: Success
 *
 *      successAddChild:
 *          type: object
 *          properties:
 *              status:
 *                  type: number
 *                  description: status if data exists
 *              data:
 *                  type: string
 *                  description: school list data
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Child added successfully
 *
 *      successInvitePartner:
 *          type: object
 *          properties:
 *              status:
 *                  type: number
 *                  description: status if data exists
 *              data:
 *                  type: string
 *                  description: List of the pending partner invites
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Partner invited successfully
 *
 *      successAcceptDenyInvitePartner:
 *          type: object
 *          properties:
 *              status:
 *                  type: number
 *                  description: status if data exists
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data:
 *                [
 *                  {
                        "childFirstName": "FirstName 1",
                        "childLastName": "LastName 1",
                        "associatedColor": "#2772ED",
                        "photoURL": "http://test.png",
                        "inviterPartnerFirstName": "PartnerFirstName 1",
                        "inviterPartnerLastName": "PartnerLastName 1",
                        "childId": "123"
                    },
 *                  {
                        "childFirstName": "FirstName 2",
                        "childLastName": "LastName 2",
                        "associatedColor": "#2772ED",
                        "photoURL": "http://test.png",
                        "inviterPartnerFirstName": "PartnerFirstName 2",
                        "inviterPartnerLastName": "PartnerLastName 2",
                        "childId": "223"
                    },
 *                ]
 *              message: Partner invited successfully
 *
 *      successGetInvitePartner:
 *          type: object
 *          properties:
 *              status:
 *                  type: number
 *                  description: status if data exists
 *              data:
 *                  type: string
 *                  description: school list data
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data:
 *                [
 *                  {
                        "childFirstName": "FirstName 1",
                        "childLastName": "LastName 1",
                        "associatedColor": "#2772ED",
                        "photoURL": "http://test.png",
                        "inviterPartnerFirstName": "PartnerFirstName 1",
                        "inviterPartnerLastName": "PartnerLastName 1",
                        "childId": "123"
                    },
 *                  {
                        "childFirstName": "FirstName 2",
                        "childLastName": "LastName 2",
                        "associatedColor": "#2772ED",
                        "photoURL": "http://test.png",
                        "inviterPartnerFirstName": "PartnerFirstName 2",
                        "inviterPartnerLastName": "PartnerLastName 2",
                        "childId": "223"
                    },
 *                ]
 *              message: Success
 */

/**
 * @openapi
 * /parent/get-school-list:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Parent]
 *      summary: get schools list
 *      parameters:
 *          - in: query
 *            name: category
 *            schema:
 *                type: string
 *            description: category of the organisation, can be one of ['PTO', 'School', 'Homeroom']
 *          - in: query
 *            name: schoolId
 *            schema:
 *                type: string
 *            description: uuid of the selected school
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successSchoolList'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /parent/add-child:
 *  post:
 *      security:
 *        - bearerAuth: []
 *      tags: [Parent]
 *      summary: add child
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/addChild'
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successAddChild'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /parent/get-school-list:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Parent]
 *      summary: get schools list
 *      parameters:
 *          - in: query
 *            name: category
 *            schema:
 *                type: string
 *            description: category of the organisation, can be one of ['PTO', 'School', 'Homeroom']
 *          - in: query
 *            name: schoolId
 *            schema:
 *                type: string
 *            description: uuid of the selected school
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successSchoolList'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */


/**
 * @openapi
 * /parent/invite-partner:
 *  post:
 *      security:
 *        - bearerAuth: []
 *      tags: [Parent]
 *      summary: invite partner
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      type: object
 *                      properties:
 *                          email:
 *                              type: string
 *                              description: email to invite partner
 *                          childrenIds:
 *                              type: array
 *                              items:
 *                                  type: string
 *                                  description: array of uuids of the children
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successInvitePartner'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /parent/invite-partner:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Parent]
 *      summary: get pending list of partner invites
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successGetInvitePartner'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /parent/invite-partner:
 *  put:
 *      security:
 *        - bearerAuth: []
 *      tags: [Parent]
 *      summary: accept/deny invite partner
 *      parameters:
 *          - in: query
 *            name: inviterPartnerEmail
 *            required: true
 *            schema:
 *                type: string
 *            description: email of the inviter partner
 *          - in: query
 *            name: childId
 *            required: true
 *            schema:
 *                type: string
 *            description: uuid of the child
 *          - in: query
 *            name: status
 *            required: true
 *            schema:
 *                type: string
 *            description: status of the invite, can be one of ['accepted', 'rejected']
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successAcceptDenyInvitePartner'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * components:
 *  schemas:
 *      Child:
 *          type: object
 *          properties:
 *              id:
 *                  type: string
 *              firstName:
 *                  type: string
 *              lastName:
 *                  type: string
 *              associatedColor:
 *                  type: string
 *      FamilyMember:
 *          type: object
 *          properties:
 *              invitedPartner:
 *                  type: object
 *                  properties:
 *                      firstName:
 *                          type: string
 *                      lastName:
 *                          type: string
 *                      id:
 *                          type: string
 *                      email:
 *                          type: string
 *              invitedChildren:
 *                  type: array
 *                  items:
 *                      type: string
 *      SentRequest:
 *          type: object
 *          properties:
 *              invitedPartner:
 *                  type: object
 *                  properties:
 *                      email:
 *                          type: string
 *              invitedChildren:
 *                  type: array
 *                  items:
 *                      type: string
 */

/**
 * @openapi
 * components:
 *  schemas:
 *      Child:
 *          type: object
 *          properties:
 *              id:
 *                  type: string
 *              firstName:
 *                  type: string
 *              lastName:
 *                  type: string
 *              associatedColor:
 *                  type: string
 *      FamilyMember:
 *          type: object
 *          properties:
 *              invitedPartner:
 *                  type: object
 *                  properties:
 *                      firstName:
 *                          type: string
 *                      lastName:
 *                          type: string
 *                      id:
 *                          type: string
 *                      email:
 *                          type: string
 *              invitedChildren:
 *                  type: array
 *                  items:
 *                      type: string
 *      SentRequest:
 *          type: object
 *          properties:
 *              invitedPartner:
 *                  type: object
 *                  properties:
 *                      email:
 *                          type: string
 *              invitedChildren:
 *                  type: array
 *                  items:
 *                      type: string
 */

/**
 * @openapi
 * components:
 *  schemas:
 *      SuccessSendInvites:
 *          type: object
 *          properties:
 *              status:
 *                  type: integer
 *                  example: 1
 *              data:
 *                  type: object
 *                  properties:
 *                      children:
 *                          type: array
 *                          items:
 *                              $ref: '#/components/schemas/Child'
 *                      familyMembers:
 *                          type: array
 *                          items:
 *                              $ref: '#/components/schemas/FamilyMember'
 *                      sentRequests:
 *                          type: array
 *                          items:
 *                              $ref: '#/components/schemas/SentRequest'
 *              message:
 *                  type: string
 *                  example: "Success"
 */

/**
 * @openapi
 * /parent/send-invites:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Parent]
 *      summary: Get send invites
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/SuccessSendInvites'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * components:
 *  schemas:
 *    SuccessRemoveMember:
 *      type: object
 *      properties:
 *        message:
 *          type: string
 *          example: "Member removed successfully"
 */

/**
 * @openapi
 * /parent/remove-member:
 *  delete:
 *      security:
 *        - bearerAuth: []
 *      tags: [Parent]
 *      summary: remove member
 *      parameters:
 *        - in: query
 *          name: invitedEmail
 *          schema:
 *            type: string
 *          description: The email of the invited partner
 *        - in: query
 *          name: invitedId
 *          schema:
 *            type: string
 *          description: The id of the invited partner
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/SuccessRemoveMember'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /parent/send-invites:
 *   put:
 *     security:
 *       - bearerAuth: []
 *     tags:
 *       - Parent
 *     summary: Save send invites
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               invitedEmail:
 *                 type: string
 *                 example: "<EMAIL>"
 *               invitedId:
 *                 type: string
 *                 example: "647dfe79-2c20-4486-aee8-643037dd2b07"
 *               childrenIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: []
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 1
 *                 message:
 *                   type: string
 *                   example: "Member's invite updated successfully"
 *       400:
 *         description: Validation Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: "Validation error message"
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: "Unauthorized"
 */
