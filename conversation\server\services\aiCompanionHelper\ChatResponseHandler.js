class ChatResponseHandler {
    constructor () {
        this.allChildContext = [];
        this.childRelatedMessages = [];
    }

    processResponse (chatResponse) {
        if (chatResponse?.context) this.allChildContext.push(chatResponse.context);
        if (chatResponse?.answer) this.childRelatedMessages.push(chatResponse.answer);
    }

    getResults () {
        return {
            allChildContext: this.allChildContext,
            childRelatedMessages: this.childRelatedMessages
        };
    }
}

module.exports = ChatResponseHandler;
