class ChatResponseHandler {
    constructor () {
        this.allChildContext = [];
        this.childRelatedMessages = [];
        this.childQuestions = [];
    }

    processResponse (chatResponse) {
        if (chatResponse?.context) this.allChildContext.push(chatResponse.context);
        if (chatResponse?.answer) this.childRelatedMessages.push(chatResponse.answer);
        if (chatResponse?.question) this.childQuestions.push(chatResponse.question);
    }

    getResults () {
        return {
            allChildContext: this.allChildContext,
            childRelatedMessages: this.childRelatedMessages,
            childQuestions: this.childQuestions
        };
    }
}

module.exports = ChatResponseHandler;
