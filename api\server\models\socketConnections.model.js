const dynamoose = require('dynamoose');

const socketConnectionsSchema = new dynamoose.Schema({
    id: {
        hashKey: true,
        type: String,
        required: true
    },
    userId: {
        type: String,
        required: true,
        index: {
            name: 'userId-index',
            global: true,
            project: true
        }
    }
}, {
    timestamps: {
        createdAt: 'createdAt'
    }
});

module.exports = dynamoose.model('SocketConnections', socketConnectionsSchema);
