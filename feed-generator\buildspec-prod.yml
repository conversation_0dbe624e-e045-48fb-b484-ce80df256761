version: 0.2
environment_variables:
  plaintext:
     S3_BUCKET: "vaalee-production-be-artifacts"
     FUNCTION_NAME: "prod-vaalee-feed-generator-function"

phases:
  install:
      runtime-versions:
       nodejs: 18

  pre_build:
    commands:
      - echo install node packages and pre-build commands
      - cd feed-generator  
      - npm install --prod 

  build:
    commands:
      - zip -r prod-vaalee-feed-generator-function.zip index.js node_modules emailTemplates server
      - ls -la
      - pwd

  post_build:
    commands:
      - echo Entering Post_Build Phase
      - aws s3 cp prod-vaalee-feed-generator-function.zip s3://$S3_BUCKET/feed-generator/
      - aws lambda update-function-code --function-name "$FUNCTION_NAME" --s3-bucket $S3_BUCKET --s3-key feed-generator/prod-vaalee-feed-generator-function.zip  

artifacts:
   files:
    - '**/*'
