module.exports = {
    registerAccount: [{
        it: 'As a user I should validate if email is not pass',
        options: {
            email: '',
            password: 'Reset@123'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if email key existing',
        options: {
            password: 'Test@12'
        },
        status: 0
    },
    {
        it: 'As a user I should check small email',
        options: {
            email: 'john',
            password: ''
        },
        status: 0
    },
    {
        it: 'As a user I should check large email',
        options: {
            email: 'john@123456789012345678900',
            password: ''
        },
        status: 0
    },
    {
        it: 'As a user I should check valid email',
        options: {
            email: 'john1',
            password: ''
        },
        status: 0
    },
    {
        it: 'As a user I should validate if password key existing',
        options: {
            email: '<EMAIL>'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if password matches regex',
        options: {
            email: '<EMAIL>',
            password: '12345678910123456789101'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if password is not pass',
        options: {
            email: '<EMAIL>',
            password: ''
        },
        status: 0
    },
    {
        it: 'As a user I should validate if password matches the regex',
        options: {
            email: '<EMAIL>',
            password: 'Test@1234'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if name doesnt match the regex',
        options: {
            email: '<EMAIL>',
            password: 'Test@1234',
            firstName: 'Test$123'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if name has length more than 100',
        options: {
            email: '<EMAIL>',
            password: 'Test@1234',
            firstName: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam at purus quis odio tristique rhoncus.'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if mobile number is passed',
        options: {
            email: '<EMAIL>',
            password: 'Test@1234',
            firstName: 'Test',
            lastName: 'User'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if country code is passed',
        options: {
            email: '<EMAIL>',
            password: 'Test@1234',
            firstName: 'Test',
            lastName: 'User',
            phoneNumber: '123'
        },
        status: 0
    }],
    verifyAccount: [{
        it: 'As a user, I should check blank email.',
        options: {
            email: '',
            otp: 123456
        },
        status: 0
    },
    {
        it: 'As a user, I should check email in request.',
        options: {
            otp: 0
        },
        status: 0
    },
    {
        it: 'As a user, I should check blank otp in request.',
        options: {
            email: '<EMAIL>',
            otp: 0
        },
        status: 0
    },
    {
        it: 'As a user, I should check blank otp in request as string.',
        options: {
            email: '<EMAIL>',
            otp: ''
        },
        status: 0
    },
    {
        it: 'As a user, I should check otp lenght.',
        options: {
            email: '<EMAIL>',
            otp: 12345
        },
        status: 0
    }]
};
