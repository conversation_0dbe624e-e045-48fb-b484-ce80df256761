module.exports = {
    addUpdateAiCompanionGroupFeedback: [
        {
            it: 'As a user I should validate if aiResponseMessageId is not passed',
            options: {},
            status: 0
        },
        {
            it: 'As a user I should validate if userQueryMessageId is not passed',
            options: {
                aiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if groupId is not passed',
            options: {
                aiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                userQueryMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if feedback is not passed',
            options: {
                aiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                userQueryMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if feedback is not valid',
            options: {
                aiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                userQueryMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                feedback: 'invalid'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if aiResponseMessageId is not valid',
            options: {
                aiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                userQueryMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                feedback: 'positive'
            },
            status: 0
        }
    ],
    getAiCompanionGroupFeedbackList: [
        {
            it: 'As a user I should validate if groupId is not passed',
            options: {},
            status: 0
        },
        {
            it: 'As a user I should validate if groupId is not valid',
            options: {
                groupId: 'invalid'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if startAiResponseMessageId is passed but startUserId is not passed',
            options: {
                groupId: '1ec98047-5829-459a-ad2b-853c228038cb',
                startAiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
            },
            status: 0
        }
    ]
};
