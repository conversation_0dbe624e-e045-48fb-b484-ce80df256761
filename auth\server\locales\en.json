{"SUCCESS": "Success", "REGISTER_SUCCESS": "User registration successful", "ALREADY_REGISTER": "This user is already registered with us.", "INACTIVE_USER": "Please activate your account by verifying email via verification code that has been sent earlier", "INVALID_OTP": "Invalid Registration code. Please check and try again.", "INVALID_VERIFY_OTP": "Verification code doesn't match. Please enter correct verification code.", "INVALID_FORGOT_PASSWORD_OTP": "Verification code doesn't match. Please enter correct verification code.", "INVALID_EMAIL_PHONE": "Email address/phone number not found. Please enter a registered email address/phone number.", "USER_VERIFY_SUCCESS": "User is verified successfully", "USER_NOT_FOUND": "Invalid user request", "INVALID_REQUEST": "Request is invalid", "RESEND_OTP_SUCCESS": "Verification code resent successfully", "NEW_USER": "User not found. Please enter a registered email address.", "LOGIN_SUCCESS": "User successfully logged in", "LOGIN_FAILED": "Email & Password do not match", "FIELD_REQUIRED": "%s can't be blank", "FIELD_NOT_VALID": "%s is not valid.", "NAME_NOT_VALID": "%s can only contain alphabets (A-Z), (a-z) and special character hyphen (-), apostrope (') and periods (.).", "PORTAL_EXISTS": "Jira portal is already exists", "ERROR_MSG": "Something went wrong. please try again.", "ACCESS_DENIED": "You are not authorized to access this resource.", "DEACTIVATE_ACCOUNT_BY_ADMIN": "You account has been deactivate.", "PHOTO_DELETE_SUCCESS": "Your profile picture has been deleted successfully.", "INVALID_JIRA_CREDENTIALS": "The entered email and token are not correct. Please verify it.", "SELECT_EMPLOYEE": "Select the employee first", "INVALID_PORTAL_ID": "Jira portal id is invalid.", "PASSWORD_NOT_MATCH": "The passwords do not match", "CHANGE_PASSWORD_SUCCESS": "Password changed successfully", "FILE_NOT_FOUND": "File not found", "TEMPLATE_NAME_REQUIRED": "Template name is required", "TEMPLATE_SUBJECT_REQUIRED": "Template subject is required", "FORGOT_PASSWORD_LINK_SENT_SUCCESS": "Verification code sent successfully", "LINK_IS_VALID": "<PERSON> validated successfully.", "RESET_PASSWORD_SUCCESS": "Your password has been reset successfully.", "SIGNIN_SUCCESS": "User successfully logged in.", "LOGOUT_SUCCESS": "User successfully logged out", "ADD_CHILD_SUCCESS": "Child added successfully"}