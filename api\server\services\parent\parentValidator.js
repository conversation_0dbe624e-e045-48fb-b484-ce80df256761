const GeneralError = require('../../util/GeneralError');
const validation = require('../../util/validation');

/**
 * Class represents validations for parent.
 */
class ParentValidator extends validation {
    constructor (body, locale, file) {
        super(locale);
        this.body = body;
        this.locale = locale;
        this.file = file;
    }

    /**
     * @desc This function is being used to validate request for add child
     * <AUTHOR>
     * @since 19/10/2023
     */
    validate () {
        super.name(this.body.firstName, 'First Name');
        super.name(this.body.lastName, 'Last Name');
        if (this.body.dob && !MOMENT(this.body.dob, 'MM/DD/YYYY', true).isValid()) {
            throw new GeneralError(this.locale(MESSAGES.DATE_FORMAT_ERROR, 'Date of Birth'), 400);
        }
        super.uuid(this.body.schoolId, 'School Name');
        super.field(this.body.zipCode, 'Zip Code');
        super.field(this.body.associatedColor, 'Associated Color');
        if (this.file) {
            this.fileType(this.file);
        }
    }

    /**
     * @desc This function is being used to validate event image fileType
     * <AUTHOR>
     * @since 19/12/2023
     * @param {String} mimeType mimeType
     */
    fileType (file) {
        if (!file.mimetype || CONSTANTS.PROFILE_PICTURE.ALLOWED_TYPE.indexOf(file.mimetype) === -1) {
            throw {
                message: MESSAGES.INVALID_FILE_FORMAT,
                statusCode: 400
            };
        }
    }
}


module.exports = ParentValidator;
