const Organization = require('../../models/organization.model');

class OrganizationRepository {
    /**
     * @desc This function fetch array of organizations with given name and zip code.
     * <AUTHOR>
     * @since 27/08/2024
     * @param {string} orgName orgName
     * @param {string} zipCode zipCode
     */
    static async orgExists (orgName, zipCode) {
        return await Organization.scan()
            .where('name').eq(orgName)
            .where('zipCode').eq(zipCode)
            .exec();
    }

    /**
     * @desc This function returns Organization with given organizationId
     * <AUTHOR>
     * @since 27/08/2024
     * @param {string} organizationId organizationId
     */
    static async getOrganizationById (organizationId) {
        return await Organization.get({ id: organizationId });
    }

    /**
     * @desc This function save or update Organizations in batch
     * <AUTHOR>
     * @since 27/08/2024
     * @param {object} organizations organizations
     */
    static async saveOrUpdateInBatch (organizations) {
        return await Organization.batchPut(organizations);
    }

    /**
     * @desc This function fetch Organizations in batch with given organizationIds
     * <AUTHOR>
     * @since 27/08/2024
     * @param {array} organizationIds organizationIds
     */
    static async getInBatch (organizationIds) {
        return await Organization.batchGet(organizationIds);
    }
}

module.exports = OrganizationRepository;
