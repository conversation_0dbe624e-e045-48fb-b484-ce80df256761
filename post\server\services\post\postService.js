/* eslint-disable max-len */
const PostValidator = require('./postValidator');
const { v4: uuidv4 } = require('uuid');
const UploadService = require('../../util/uploadService');
const Post = require('../../models/post.model');
const ChildOrganizationMapping = require('../../models/childOrganizationMapping.model');
const OrganizationMember = require('../../models/organizationMember.model');
const GeneralError = require('../../util/GeneralError');
const AwsOpenSearchService = require('../../util/opensearch');

/**
 * Class represents services for signin.
 */
class PostService {
    /**
     * @desc This function is being used to add post
     * <AUTHOR>
     * @since 08/11/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {String} req.body.title postTitle
     * @param {String} req.body.subTitle post subTitle
     * @param {String} req.body.publishedDate published Date
     * @param {String} req.body.postStatus status
     * @param {String} req.body.content content
     * @param {Object} user User
     * @param {Object} res Response
     */
    static async addPost (req, user, locale) {
        const Validator = new PostValidator(req.body, locale, req.files);
        Validator.validateAddPost();

        const {
            title,
            subTitle,
            postStatus,
            publishedDate,
            content,
            organizationId
        } = req.body;

        let formattedPublishedDate = publishedDate;

        const orgAssociation = await this.validateOrganizationAssociation(
            organizationId,
            user
        );

        if (publishedDate) {
            formattedPublishedDate = MOMENT.utc(
                `${publishedDate}`,
                'MM/DD/YYYY',
                true
            );
        }
        const uuid = uuidv4();
        const post = await Post.create({
            id: uuid,
            createdBy: user.id,
            status:
                    orgAssociation.associatedOrgRole === CONSTANTS.ORG_ROLE.SUPER_ADMIN
                        ? postStatus
                        : 'draft',
            organizationId: organizationId.trim(),
            title: title.trim(),
            subTitle: subTitle.trim(),
            content: content.trim(),
            publishedDate: formattedPublishedDate.toDate()
        });

        const formattedPost = await this.postSearchFormattedData(post);
        await AwsOpenSearchService.create(CONSTANTS.OPEN_SEARCH.COLLECTION.POST, post.id, formattedPost);
        await this.insertPostInChildPosts(post);

        return post.id;
    }

    static async insertPostInChildPosts (post) {
        const children = await ChildOrganizationMapping.query('organizationId').eq(post.organizationId).exec();
        for (const child of children) {
            await AwsOpenSearchService.registerPostInChildPosts(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, child.childId, post.id);
        }
    }

    static async postSearchFormattedData (post) {
        return {
            id: post.id,
            title: post.title,
            subTitle: post.subTitle,
            status: post.status,
            content: post.content,
            organizationId: post.organizationId
        };
    }


    /**
     * @desc This function is being used to validate organization association
     * <AUTHOR>
     * @since 14/12/2023
     * @param {String} organizationId organization id
     */
    static async validateOrganizationAssociation (organizationId, user) {
        const orgMembers = await OrganizationMember.get(organizationId);
        if (!orgMembers) {
            throw {
                message: MESSAGES.UNAUTHORIZED_ACTION_FOR_ORGANIZATION,
                statusCode: 400
            };
        }

        const orgAssociation = orgMembers.users.find(
            (member) => member.id === user.id
        );
        if (!orgAssociation) {
            throw {
                message: MESSAGES.UNAUTHORIZED_ACTION_FOR_ORGANIZATION,
                statusCode: 400
            };
        }
        if (orgAssociation.status !== CONSTANTS.STATUS.ACTIVE) {
            throw {
                message: MESSAGES.UNAUTHORIZED_ACTION_FOR_ORGANIZATION,
                statusCode: 400
            };
        }
        return orgAssociation;
    }

    static async handleFileDeletion (fileNames) {
        if (!fileNames) {
            return;
        }

        for (const fileName of fileNames) {
            await UploadService.deleteObject(fileName);
        }
    }

    static parseNumber (value) {
        return value ? Number(value.trim()) : undefined;
    }

    static parseBoolean (value) {
        return value.trim() === 'true';
    }

    static async validateDateTime (
        startDate,
        startTime,
        endDate,
        endTime,
        startDateTime,
        endDateTime,
        locale
    ) {
        const isValidDate = (date) => MOMENT(date, 'MM/DD/YYYY', true).isValid();
        const isValidTime = (time) => MOMENT(time, 'HH:mm', true).isValid();

        if (!isValidDate(startDate) || !isValidDate(endDate)) {
            throw new GeneralError(
                locale(MESSAGES.DATE_FORMAT_ERROR, 'Start or End Date'),
                400
            );
        }

        if (!isValidTime(startTime) || !isValidTime(endTime)) {
            throw new GeneralError(
                locale(MESSAGES.TIME_FORMAT_ERROR, 'Start or End Time'),
                400
            );
        }

        const currentDateTime = MOMENT().utc();

        if (startDateTime.isBefore(currentDateTime)) {
            throw new GeneralError(MESSAGES.POST_START_DATE_ERROR, 400);
        }

    }


    static async validatePost (post, postStatus) {
        if (!post) {
            throw {
                message: MESSAGES.POST_NOT_FOUND,
                statusCode: 404
            };
        }

        if (post.status === 'published' && postStatus === 'unpublished') {
            throw new GeneralError(MESSAGES.POST_STATUS_CANT_CHANGE, 400);
        }
    }


    /**
     * @desc This function is being used to update post
     * <AUTHOR>
     * @since 16/11/2023
     * @param {Object} req Request
     * @param {Object} user User
     * @param {Object} locale locale
     */
    static async updatePost (req, user, locale) {
        const validator = new PostValidator(req.body, locale);
        validator.validateUpdatePost();
        const {
            postId,
            title,
            subTitle,
            content,
            publishedDate,
            postStatus
        } = req.body;

        const post = await Post.get({ id: postId });
        await this.validatePost(post, postStatus);

        let formattedPublishedDate = publishedDate;

        const orgAssociation = await this.validateOrganizationAssociation(
            post.organizationId,
            user
        );

        formattedPublishedDate = MOMENT.utc(
            `${publishedDate}`,
            'MM/DD/YYYY',
            true
        );

        post.updatedBy = user.id;
        post.title = title;
        post.subTitle = subTitle;
        post.status =
            orgAssociation.associatedOrgRole === CONSTANTS.ORG_ROLE.SUPER_ADMIN
                ? postStatus
                : 'draft';
        post.content = content;
        post.publishedDate = formattedPublishedDate.toDate();

        const updatedPost = await post.save();

        const formattedPost = await this.postSearchFormattedData(updatedPost);
        await AwsOpenSearchService.updateField(CONSTANTS.OPEN_SEARCH.COLLECTION.POST, updatedPost.id, formattedPost);
    }

    /**
     * @desc This function is being used to get list of Posts
     * <AUTHOR>
     * @since 15/11/2023
     */
    static async getPostList (req, user) {
        const { status, organizationId } = req.query;

        const postAttributes = [
            'id',
            'title',
            'subTitle',
            'content',
            'publishedDate',
            'status',
            'organizationId',
            'updatedAt',
            'createdAt',
            'isDeleted'
        ];

        let posts;

        if (organizationId) {
            await this.validateOrganizationAssociation(organizationId, user);

            const query = Post.query('organizationId')
                .eq(organizationId.trim())
                .using('organizationId-index')
                .attributes(postAttributes);

            if (
                status &&
                CONSTANTS.ALLOWED_POST_STATUS.includes(status.toLowerCase().trim())
            ) {
                posts = await query
                    .where('status')
                    .eq(status.toLowerCase().trim())
                    .exec();
            } else {
                posts = await query.exec();
            }
        } else {
            const queryPromises = user.associatedOrganizations.map(async (org) => {
                const orgMembers = await OrganizationMember.get(org.organizationId);

                const orgAssociation = orgMembers.users.find(
                    (member) => member.id === user.id
                );

                if (orgAssociation.status === CONSTANTS.STATUS.ACTIVE) {
                    const query = Post.query('organizationId')
                        .eq(org.organizationId)
                        .using('organizationId-index')
                        .attributes(postAttributes);

                    if (
                        status &&
                        CONSTANTS.ALLOWED_POST_STATUS.includes(status.toLowerCase().trim())
                    ) {
                        return query.where('status').eq(status.toLowerCase().trim()).exec();
                    } else {
                        return query.exec();
                    }
                } else {
                    return null;
                }
            });

            const postsArrays = await Promise.all(queryPromises);
            posts = postsArrays.filter(Boolean).flatMap((postArray) => postArray);
        }
        posts.sort((a, b) => b.createdAt - a.createdAt);
        return posts.length === 0 ? [] : this.formatPosts(posts);
    }




    /**
     * @desc This function is being used to get post details
     * <AUTHOR>
     * @since 15/11/2023
     * @param {Object} req Request
     * @param {Object} locale Locale passed from request
     */
    static async getPostDetails (req, user, locale) {
        const Validator = new PostValidator(req.body, locale, req.query);
        Validator.validatePostId();

        const { postId } = req.query;
        const postAttributes = [
            'id',
            'title',
            'subTitle',
            'content',
            'publishedDate',
            'status',
            'organizationId',
            'updatedAt',
            'createdAt'
        ];

        const postDetails = await Post.get(
            { id: postId },
            {
                attributes: postAttributes
            }
        );

        if (!postDetails || postDetails.isDeleted) {
            throw {
                message: MESSAGES.POST_NOT_FOUND,
                statusCode: 400
            };
        }

        await this.validateOrganizationAssociation(
            postDetails.organizationId,
            user
        );

        const formattedPost = this.formatPosts([postDetails])[0];

        return {
            ...formattedPost,
            canBeDeleted: formattedPost.status === 'unpublished'
        };
    }

    /**
     * @desc This function is being used to change the status of post to publish
     * <AUTHOR>
     * @since 15/11/2023
     */
    static async publishPost (req, user, locale) {
        const Validator = new PostValidator(req.body, locale, req.query);
        Validator.validatePostId();
        const post = await Post.get(req.query.postId);

        if (!post || post.isDeleted) {
            throw {
                message: MESSAGES.POST_NOT_FOUND,
                statusCode: 400
            };
        }

        const orgAssociation = await this.validateOrganizationAssociation(
            post.organizationId,
            user
        );

        if (orgAssociation.associatedOrgRole !== CONSTANTS.ORG_ROLE.SUPER_ADMIN) {
            throw new GeneralError(MESSAGES.ACCESS_DENIED, 403);
        } else if (post.status === 'published') {
            throw {
                message: MESSAGES.POST_ALREADY_PUBLISHED,
                statusCode: 200
            };
        } else {
            post.status = 'published';
            const updatedPost = await post.save();

            const formattedPost = await this.postSearchFormattedData(updatedPost);
            await AwsOpenSearchService.updateField(CONSTANTS.OPEN_SEARCH.COLLECTION.POST, updatedPost.id, formattedPost);
        }
    }



    /**
     * @desc This function is being used to delete post
     * <AUTHOR>
     * @since 15/11/2023
     */
    static async deletePost (req, user, locale) {
        const Validator = new PostValidator(req.body, locale, req.query);
        Validator.validatePostId();
        const post = await Post.get(req.query.postId);
        if (!post || post.isDeleted) {
            throw {
                message: MESSAGES.POST_NOT_FOUND,
                statusCode: 400
            };
        } else if (post.status === 'published') {
            const orgAssociation = await this.validateOrganizationAssociation(
                post.organizationId,
                user
            );
            if (orgAssociation.associatedOrgRole !== CONSTANTS.ORG_ROLE.SUPER_ADMIN) {
                throw new GeneralError(MESSAGES.ACCESS_DENIED, 403);
            }
            throw {
                message: MESSAGES.POST_CANT_BE_DELETED,
                statusCode: 400
            };
        } else {

            post.isDeleted = 1;
            await post.save();
            await AwsOpenSearchService.delete(CONSTANTS.OPEN_SEARCH.COLLECTION.POST, post.id);
        }
    }

    /**
     * @desc This function is being used to return formatted posts
     * <AUTHOR>
     * @since 15/11/2023
     * @param {Array} posts array of posts
     */
    static formatPosts (posts) {
        return posts
            .filter(({ isDeleted }) => !isDeleted)
            .map(
                ({
                    id,
                    title,
                    subTitle,
                    status,
                    content,
                    organizationId,
                    publishedDate
                }) => ({
                    id,
                    title,
                    subTitle,
                    status,
                    organizationId,
                    content,
                    publishedDate: MOMENT(publishedDate).format('L')
                })
            );
    }


}

module.exports = PostService;
