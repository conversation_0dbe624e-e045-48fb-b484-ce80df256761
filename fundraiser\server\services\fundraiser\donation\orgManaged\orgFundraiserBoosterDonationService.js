const OrgFundraiserBoosterDonationValidator = require('../orgManaged/orgFundraiserBoosterDonationValidator');
const { v4: uuidv4 } = require('uuid');
const OrgManagedFundraiserBoosterDonation = require('../../../../models/orgManagedFundraiserBoosterDonation.model');
const Fundraiser = require('../../../../models/fundraiser.model');
const Organization = require('../../../../models/organization.model');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const UploadService = require('../../../../util/uploadService');
const FundraiserSignup = require('../../../../models/fundraiserSignup.model');
const Child = require('../../../../models/child.model');

/**
 * Class represents controller for org managed fundraiser booster routes.
 */
class OrgFundraiserBoosterDonationService {
    /**
   * @desc This function is being used to add donations for org managed fundraiser booster
   * <AUTHOR>
   * @since 13/09/2024
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async addDonation (req) {
        const signature = req.headers['stripe-signature'];
        let event;
        try {
            event = await stripe.webhooks.constructEvent(req.rawBody, signature, process.env.STRIPE_BOOSTER_WEBHOOK_SECRET);
        } catch (err) {
            CONSOLE_LOGGER.error('Webhook Error:', err);
            throw {
                message: `Webhook Error: ${err.message}`,
                statusCode: 400
            };
        }
        const uuid = uuidv4();
        const session = event.data.object;
        const isOnline = CONSTANTS.PAYMENT_TYPES.ONLINE;
        let donation = {
            id: uuid,
            fundraiserBoosterId: session.metadata.fundraiserBoosterId,
            childName: session.metadata.childName,
            childId: session.metadata.childId,
            donorName: session.metadata.donorName,
            donorMessage: session.metadata.donorMessage,
            amount: parseFloat(session.metadata.donationAmount) / 100,
            platformFee: parseFloat(session.metadata.platformFee) / 100,
            expectDonorMatch: session.metadata.expectDonorMatch === 'true',
            paymentType: isOnline,
            platformFeeCoveredBy: session.metadata.platformFeeCoveredBy,
            status: 'unknown'
        };
        const paymentIntentId = session.payment_intent;
        donation.stripePaymentIntentId = paymentIntentId;
        const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId, {
            expand: ['payment_method']
        });
        if (paymentIntent.status === 'succeeded') {
            CONSOLE_LOGGER.info('Payment: Success');
            donation.status = 'success';
        } else {
            donation.status = 'failed';
            const failureMessage = paymentIntent.charges.data[0]?.failure_message || 'Unknown error';
            CONSOLE_LOGGER.error('Payment: Failed - ', failureMessage);
        }
        donation = await OrgManagedFundraiserBoosterDonation.create(donation);
        CONSOLE_LOGGER.info(JSON.stringify(event), 'all events');
        return donation;
    }

    /**
   * @desc This function is being used to get org managed fundraiser booster details
   * <AUTHOR>
   * @since 13/09/2024
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async getBoosterDetails (req, locale) {
        const validator = new OrgFundraiserBoosterDonationValidator(req.body, req.query, locale);
        validator.validateGetBoosterDetails();

        const { fundraiserId, childId } = req.query;
        const fundraiser = await Fundraiser.get(
            { id: fundraiserId },
            { attributes: [
                'organizationId',
                'title',
                'description',
                'boosterGoal',
                'startDate',
                'endDate',
                'imageURL',
                'status',
                'homeroomStats',
                'raisedDonationsAmountForOrg',
                'childLevelStats'
            ]
            }
        );
        const fundraiserStatus = this.getFundraiserStatus(fundraiser.endDate, fundraiser.status);
        if (fundraiserStatus !== CONSTANTS.STATUS.ACTIVE) {
            return {
                fundraiserStatus,
                fundraiser: {},
                organization: {}
            };
        }
        const signedFundraiserUrl = fundraiser.imageURL ? await UploadService.getSignedUrl(fundraiser.imageURL) : '';
        fundraiser.imageURL = signedFundraiserUrl;
        const organization = await Organization.get(
            { id: fundraiser.organizationId },
            {
                attributes: [
                    'id',
                    'name',
                    'logo',
                    'address',
                    'city',
                    'state',
                    'country',
                    'platformFeeCoveredBy',
                    'minPlatformFeeAmount',
                    'platformFee'
                ]
            }
        );
        const signedOrgLogoUrl = organization.logo ? await UploadService.getSignedUrl(organization.logo) : '';
        organization.logo = signedOrgLogoUrl;

        let donations = [];
        const DESIRED_COUNT = 10;
        const BATCH_SIZE = 15;

        let matchingDonations = [];
        let lastKey = null;

        // Get recent 10 donations for the fundraiser
        do {
            const batchDonations = await OrgManagedFundraiserBoosterDonation.query('fundraiserBoosterId')
                .eq(fundraiserId)
                .using('fundraiserBoosterId-createdAt-index')
                .sort('descending')
                .limit(BATCH_SIZE)
                .startAt(lastKey)
                .attributes(['amount', 'childId', 'childName', 'donorName', 'donorMessage', 'createdAt', 'paymentType', 'status'])
                .exec();

            // filter out offline donations and failed donations
            const filteredBatch = batchDonations.filter(
                (donation) =>
                    donation.paymentType !== CONSTANTS.PAYMENT_TYPES.OFFLINE &&
                donation.status === CONSTANTS.BOOSTER_DONATION_STATUS.SUCCESS
            );

            matchingDonations = matchingDonations.concat(filteredBatch);
            lastKey = batchDonations.lastKey;

        } while (matchingDonations.length < DESIRED_COUNT && lastKey);

        const lastTenSortedBoosterDonations = matchingDonations.slice(0, DESIRED_COUNT);

        // Update the total raised amount for the organization to last 10 sorted donations if raisedDonationsAmountForOrg is not set
        if (!fundraiser.raisedDonationsAmountForOrg) {
            fundraiser.raisedDonationsAmountForOrg = lastTenSortedBoosterDonations.reduce((acc, donation) => acc + donation.amount, 0);
        }
        // Map over the last 10 sorted donations and remove the amount field
        donations = lastTenSortedBoosterDonations.map((donation) => {
            const donationCopy = { ...donation };
            donationCopy.amount = undefined;
            return donationCopy;
        });

        let leaderboard = {
            topDonations: [],
            topRegistrations: []
        };
        leaderboard = await this.generateLeaderboard(fundraiser.homeroomStats, fundraiser.childLevelStats);
        delete fundraiser.homeroomStats;
        delete fundraiser.childLevelStats;
        let child;
        if ( childId ) {
            const childSignedUps = await FundraiserSignup.query('eventId')
                .eq(fundraiserId)
                .where('childId')
                .eq(childId)
                .exec();
            if ( childSignedUps.count > 0 ) {
                const childDetails = await Child.get({ id: childId }, { attributes: ['firstName', 'lastName', 'photoURL'] });
                const signedChildPhotoUrl = childDetails?.photoURL ? await UploadService.getSignedUrl(childDetails.photoURL) : '';
                const boosterDonationsForChild = await OrgManagedFundraiserBoosterDonation.query('fundraiserBoosterId')
                    .eq(fundraiserId)
                    .using('fundraiserBoosterId-createdAt-index')
                    .where('childId')
                    .eq(childId)
                    .sort('descending')
                    .exec();
                const raisedDonationsAmountForChild =
                boosterDonationsForChild ? boosterDonationsForChild.reduce((acc, donation) => acc + donation.amount, 0) : 0;
                // Filtering out Offline donations as DB has both online and just empty payment type
                const lastTenOnlineDonations = boosterDonationsForChild
                    .filter((donation) => donation.paymentType !== CONSTANTS.PAYMENT_TYPES.OFFLINE)
                    .slice(0, 10)
                    .map((donation) => {
                        const donationCopy = { ...donation };
                        donationCopy.amount = undefined;
                        return donationCopy;
                    });
                child = {
                    name: childDetails?.firstName ?? '' + childDetails?.lastName ?? '',
                    photoURL: signedChildPhotoUrl,
                    boosterGoal: childSignedUps[0].boosterGoalForChild,
                    boosterMessage: childSignedUps[0].boosterMessageForChild,
                    raisedDonationsAmount: raisedDonationsAmountForChild
                };
                donations = lastTenOnlineDonations;
            }
        }
        fundraiser.donations = donations;

        return { fundraiserStatus, fundraiser, organization, child, leaderboard };
    }

    static getFundraiserStatus (endDate, status) {
        let fundraiserStatus = 'active';
        if (endDate < new Date()) {
            fundraiserStatus = 'expired';
        }
        if (status !== 'published') {
            fundraiserStatus = 'unpublished';
        }
        return fundraiserStatus;
    }

    static async createStripeSession (req) {
        const {
            childName,
            donorName,
            donorMessage,
            fundraiserBoosterId,
            fundraiserName,
            fundraiserDescription,
            fundraiserImageURL,
            donationAmount,
            childId,
            expectDonorMatch,
            organizationId
        } = req.body;
        const returnURL =
          `${process.env.BOOSTER_URL}?id=${fundraiserBoosterId}` +
          (childId ? `&childId=${childId}` : '') +
          '&sessionId={CHECKOUT_SESSION_ID}';
        const organization = await Organization.get({ id: organizationId },
            { attributes: ['paymentDetails', 'platformFee', 'minPlatformFeeAmount', 'platformFeeCoveredBy'] });
        organization.platformFee = organization.platformFee ?? 0;
        organization.minPlatformFeeAmount = organization.minPlatformFeeAmount ?? 0;

        if (
            !organization ||
            !organization.paymentDetails ||
            organization.paymentDetails.stripeOnboardingStatus?.trim() !== 'active' ||
            !organization.paymentDetails.stripeConnectAccountId?.trim()
        ) {
            throw {
                message: 'Organization does not have active stripe account',
                statusCode: 400
            };
        }
        const calculatedPlatformFee = Number(
            ((donationAmount * organization.platformFee) / 100).toFixed(2)
        );
        const platformFee = Math.max(
            calculatedPlatformFee,
            organization.minPlatformFeeAmount * 100 ?? CONSTANTS.DEFAULT_MIN_PLATFORM_FEE_AMOUNT * 100
        );
        let amount = donationAmount;
        const applicationFeeAmount = platformFee;
        if (
            organization.platformFeeCoveredBy === 'parent'
        ) {
            amount += applicationFeeAmount;
        }

        const session = await stripe.checkout.sessions.create({
            submit_type: 'donate',
            line_items: [{
                price_data: {
                    currency: 'usd',
                    product_data: {
                        name: fundraiserName,
                        images: [fundraiserImageURL],
                        description: fundraiserDescription
                    },
                    unit_amount: amount
                },
                quantity: 1
            }],
            mode: 'payment',
            ui_mode: 'embedded',
            return_url: returnURL,
            metadata: {
                childId,
                childName,
                donorName,
                donorMessage,
                fundraiserBoosterId,
                expectDonorMatch,
                donationAmount,
                platformFee: applicationFeeAmount,
                platformFeeCoveredBy: organization.platformFeeCoveredBy
            },
            payment_intent_data: {
                application_fee_amount: applicationFeeAmount,
                transfer_data: {
                    destination: organization.paymentDetails.stripeConnectAccountId
                }
            }
        }
        );
        return session.client_secret;
    }

    static async getDonations (req) {
        const fundraiserBoosterId = req.query.fundraiserBoosterId;
        const donations = await OrgManagedFundraiserBoosterDonation.query(
            'fundraiserBoosterId'
        )
            .eq(fundraiserBoosterId)
            .using('fundraiserBoosterId-createdAt-index')
            .sort('descending')
            .exec();
        return this.formatDonations(donations);
    }

    static async addManualDonations (req, locale) {
        const { donations, fundraiserBoosterId } = req.body;
        if (!donations || !Array.isArray(donations) || donations.length === 0) {
            throw {
                message: 'Donations are required',
                statusCode: 400
            };
        }
        const isOffline = CONSTANTS.PAYMENT_TYPES.OFFLINE;
        const donationsToSave = donations.map((donation) => {
            const amount = donation.amount;
            donation.id = uuidv4();
            donation.fundraiserBoosterId = fundraiserBoosterId;
            donation.platformFee = 0;
            donation.status = 'success';
            donation.amount = amount ? parseFloat(amount) : undefined;
            donation.paymentType = isOffline;
            donation.platformFeeCoveredBy = 'organization';
            new OrgFundraiserBoosterDonationValidator(donation, {}, locale).validate();
            return donation;
        });
        return await OrgManagedFundraiserBoosterDonation.batchPut(donationsToSave);
    }

    static async getSessionDetails (req) {
        const session = await stripe.checkout.sessions.retrieve(req.query.sessionId);

        return {
            status: session.status,
            payment_status: session.payment_status
        };
    }

    /**
     * Generates a leaderboard for a fundraiser based on homeroom stats
     * @param {Object} homeroomStats - The homeroom stats object
     * @returns {Object} The leaderboard object
     */
    static async generateLeaderboard (homeroomStats, childLevelStats) {
        // Check if homeroom stats are provided
        const { donations = [], registrations = [] } = homeroomStats || {};
        const { donations: childDonations = [] } = childLevelStats || {};

        if (!homeroomStats && !childLevelStats) {
            return {
                topDonations: [],
                topRegistrations: [],
                topChildLevelDonations: []
            };
        }

        // Reassign with defaults
        homeroomStats = { donations, registrations };
        childLevelStats = { donations: childDonations };

        const combinedStats = new Map();

        // Iterate over donations and update combined stats
        homeroomStats.donations.forEach(({ homeRoomId, donationsAmount }) => {
            if (!combinedStats.has(homeRoomId)) {
                combinedStats.set(homeRoomId, {
                    homeRoomId,
                    amount: 0,
                    count: 0
                });
            }
            combinedStats.get(homeRoomId).amount = donationsAmount || 0;
        });

        // Iterate over registrations and update combined stats
        homeroomStats.registrations.forEach(({ homeRoomId, registrationsCount }) => {
            if (!combinedStats.has(homeRoomId)) {
                combinedStats.set(homeRoomId, {
                    homeRoomId,
                    amount: 0,
                    count: 0
                });
            }
            combinedStats.get(homeRoomId).count = registrationsCount || 0;
        });

        const statsArray = Array.from(combinedStats.values());

        // Sort the stats array based on multiple parameters
        const multiParamSort = (a, b, primaryParam, secondaryParam) => {
            const primaryDiff = b[primaryParam] - a[primaryParam];
            if (primaryDiff !== 0) return primaryDiff;
            return b[secondaryParam] - a[secondaryParam];
        };

        // Get top 5 donations and registrations
        const topDonations = statsArray
            .sort((a, b) => multiParamSort(a, b, 'amount', 'count'))
            .slice(0, 5)
            .map(({ homeRoomId, amount, count }, index) => ({
                name: homeRoomId,
                donationsAmount: amount,
                registrationsCount: count,
                rank: index + 1
            }));

        // Get top 5 registrations
        const topRegistrations = statsArray
            .sort((a, b) => multiParamSort(a, b, 'count', 'amount'))
            .slice(0, 5)
            .map(({ homeRoomId, count, amount }, index) => ({
                name: homeRoomId,
                registrationsCount: count,
                donationsAmount: amount,
                rank: index + 1
            }));

        // Get unique homeroom IDs
        const homeroomIds = [
            ...new Set(
                topDonations
                    .map((donation) => donation.name)
                    .concat(topRegistrations.map((donation) => donation.name))
            )
        ];
        if (homeroomIds.length > 0) {
            const homeroomNames = await this.getHomeRoomNames(homeroomIds);
            // Map homeroom IDs to names for top donations and registrations
            topDonations.forEach((donation) => {
                donation.name = homeroomNames[donation.name];
            });
            topRegistrations.forEach((donation) => {
                donation.name = homeroomNames[donation.name];
            });
        }
        let topChildLevelDonations = [];
        if (childLevelStats?.donations?.length > 0) {
            topChildLevelDonations = childLevelStats?.donations
                ?.sort((a, b) => b.donationsAmount - a.donationsAmount)
                ?.slice(0, 10)
                .map((donation, index) => ({
                    ...donation,
                    rank: index + 1
                }));
            const childIds = [...new Set(topChildLevelDonations.map((donation) => donation.childId))];
            const childrenDetails = await Child.batchGet(childIds, { attributes: ['id', 'firstName', 'lastName'] });
            const childNamesMap = new Map();
            childrenDetails.forEach((child) => {
                childNamesMap.set(child.id, `${child.firstName} ${child.lastName}`);
            });
            topChildLevelDonations.forEach((donation) => {
                const child = childNamesMap.get(donation.childId);
                donation.name = child;
            });
        }

        return {
            topDonations,
            topRegistrations,
            topChildLevelDonations
        };
    }

    /**
     * Retrieves homeroom names for a given list of homeroom IDs
     * @param {Array} homeRoomIds - The list of homeroom IDs
     * @returns {Object} An object mapping homeroom IDs to their names
     */
    static async getHomeRoomNames (homeRoomIds) {
        if (homeRoomIds.length === 0) {
            return {};
        }
        const homeRooms = await Organization.batchGet(homeRoomIds, { attributes: ['id', 'name'] });
        return homeRooms.reduce((acc, homeRoom) => {
            acc[homeRoom.id] = homeRoom.name;
            return acc;
        }, {});
    }

    /**
     * Format donations for response
     * @param {Array} donations - The array of donations
     * @returns {Array} The array of formatted donations
     */
    static async formatDonations (donations) {
        const CHUNK_SIZE = 100;
        if (!donations || !Array.isArray(donations) || donations.length === 0) {
            return [];
        }
        const childIds = [...new Set(donations.map((donation) => donation.childId).filter(Boolean))];
        let childLookup = {};
        let homeroomLookup = {};
        if (childIds.length > 0) {
            let homeroomIds = [];
            for (let i = 0; i < childIds.length; i += CHUNK_SIZE) {
                const childrenDetails = await Child.batchGet(childIds.slice(i, i + CHUNK_SIZE), { attributes: ['id', 'homeRoom'] });
                childLookup = { ...childLookup, ...childrenDetails.reduce((acc, child) => {
                    acc[child.id] = child.homeRoom;
                    return acc;
                }, {}) };
                homeroomIds = [...new Set(Object.values(childLookup).filter(Boolean))];
            }
            homeroomLookup = await this.getHomeRoomNames(homeroomIds);
        }
        return donations.map((donation) => {
            const donationCopy = { ...donation };
            delete donationCopy.fundraiserBoosterId;
            delete donationCopy.stripePaymentIntentId;
            donationCopy.homeRoom = homeroomLookup[childLookup[donation.childId]] || '';
            return donationCopy;
        });
    }
}

module.exports = OrgFundraiserBoosterDonationService;
