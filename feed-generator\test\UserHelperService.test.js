const sinon = require('sinon');
const AWS = require('aws-sdk');
const AWSMock = require('jest-aws-sdk-mock');
const Redis = require('ioredis');
const CONSOLE_LOGGER = require('../server/logger');
const handler = require('../index');
const RedisUtil = require('../server/redisUtil');
const ConstantModel = require('../server/models/constant.model');
const CONSTANTS = require('../server/constants');
const { beforeEach, afterEach } = require('mocha');

const assert = sinon.assert;

describe('UserHelperService', () => {
    let sandbox;
    let stubRedisConnect;
    let pipelineExecStub;
    let pipelineStub;

    before(() => {
        AWSMock.setSDKInstance(AWS);
        sandbox = sinon.createSandbox();
    });

    after(() => {
        AWSMock.restore();
        sinon.restore();
        sandbox.restore();
        pipelineStub.restore();
    });

    beforeEach(() => {
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        pipelineExecStub = sinon.stub();
        pipelineStub = sinon.stub(Redis.prototype, 'pipeline').returns({ exec: pipelineExecStub });
        sinon.stub(ConstantModel, 'get').withArgs(CONSTANTS.FEED_VERSION_PREFIX).resolves({ value: 'v1' });
    });

    afterEach(() => {
        sinon.restore();
    });

    try {
        it('should insert user details in redis when USER is inserted in dynamodb', async () => {
            const setHashValueStub = sinon.stub(RedisUtil, 'setHashValue');
            setHashValueStub.resolves(true);

            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/User/stream/2025-03-03T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'associatedOrganizations': { 'L': [{ 'S': '65824922-7a2d-49c0-8f39-ff4d7a729ecd' }] },
                        'lastName': { 'S': 'Doe' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'isDeleted': { 'N': '0' },
                        'id': { 'S': 'userId1' },
                        'firstName': { 'S': 'John' },
                        'email': { 'S': '<EMAIL>' },
                        'children': { 'L': [] }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            assert.calledOnce(setHashValueStub);
        });

        it('should update user details in redis when USER is updated in dynamodb', async () => {
            const setHashValueStub = sinon.stub(RedisUtil, 'setHashValue');
            setHashValueStub.resolves(true);

            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/User/stream/2025-03-03T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'id': { 'S': 'userId1' },
                        'firstName': { 'S': 'John' },
                        'lastName': { 'S': 'Doe' },
                        'email': { 'S': '<EMAIL>' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'isDeleted': { 'N': '0' },
                        'children': { 'L': [] }
                    },
                    OldImage: {
                        'id': { 'S': 'userId1' },
                        'firstName': { 'S': 'John' },
                        'lastName': { 'S': 'Doe' },
                        'email': { 'S': '<EMAIL>' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'isDeleted': { 'N': '0' },
                        'children': { 'L': [] }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            assert.calledOnce(setHashValueStub);
        });

        it('should remove user details from redis when USER is deleted in dynamodb', async () => {
            const deleteKeyStub = sinon.stub(RedisUtil, 'deleteKey');
            deleteKeyStub.resolves(true);

            pipelineExecStub.resolves();

            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/User/stream/2025-03-03T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'id': { 'S': 'userId1' },
                        'firstName': { 'S': 'John' },
                        'lastName': { 'S': 'Doe' },
                        'email': { 'S': '<EMAIL>' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'isDeleted': { 'N': '1' }
                    },
                    OldImage: {
                        'id': { 'S': 'userId1' },
                        'firstName': { 'S': 'John' },
                        'lastName': { 'S': 'Doe' },
                        'email': { 'S': '<EMAIL>' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'isDeleted': { 'N': '0' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            assert.calledOnce(deleteKeyStub);
            assert.calledOnce(pipelineExecStub);
        });

        it('should add user details, if user deleted status is changed back to 0', async () => {
            const setHashValueStub = sinon.stub(RedisUtil, 'setHashValue');
            setHashValueStub.resolves(true);

            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/User/stream/2025-03-03T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'id': { 'S': 'userId1' },
                        'firstName': { 'S': 'John' },
                        'lastName': { 'S': 'Doe' },
                        'email': { 'S': '<EMAIL>' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'isDeleted': { 'N': '0' },
                        'children': { 'L': [] }
                    },
                    OldImage: {
                        'id': { 'S': 'userId1' },
                        'firstName': { 'S': 'John' },
                        'lastName': { 'S': 'Doe' },
                        'email': { 'S': '<EMAIL>' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'isDeleted': { 'N': '1' },
                        'children': { 'L': [] }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            assert.calledOnce(setHashValueStub);
        });

        it('should delete user details from redis when USER is deleted in dynamodb', async () => {
            const deleteKeyStub = sinon.stub(RedisUtil, 'deleteKey');
            deleteKeyStub.resolves(true);

            pipelineExecStub.resolves();

            const record = {
                eventName: 'REMOVE',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/User/stream/2025-03-03T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        'id': { 'S': 'userId1' },
                        'firstName': { 'S': 'John' },
                        'lastName': { 'S': 'Doe' },
                        'email': { 'S': '<EMAIL>' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'isDeleted': { 'N': '1' },
                        'children': { 'L': [] }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            assert.calledOnce(deleteKeyStub);
            assert.calledOnce(pipelineExecStub);
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
