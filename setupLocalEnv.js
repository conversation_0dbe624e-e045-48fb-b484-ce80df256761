const fs = require('fs');
const path = require('path');

// Load configuration from config.json
const config = require('./config.json');

// Define directories
const directories = ['api', 'auth', 'event', 'feed', 'notification', 'post', 'fundraiser'];

// Iterate over directories
directories.forEach(dir => {
  const dirPath = path.join(__dirname, dir);
  const envFilePath = path.join(dirPath, 'local.env');
  const envSettings = config[dir];

  // Check if directory exists
  if (fs.existsSync(dirPath)) {
    // Write settings to local.env file in each directory
    fs.writeFileSync(envFilePath, formatSettings(envSettings));
    console.log(`Generated local.env for ${dir}`);
  } else {
    console.log(`Directory ${dir} does not exist`);
  }
});

// Helper function to format settings as environment variables
function formatSettings(settings) {
  return Object.entries(settings)
    .map(([key, value]) => `${key}='${value}'`)
    .join('\n');
}