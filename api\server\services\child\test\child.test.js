const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const sinon = require('sinon');
const User = require('../../../models/user.model');
const Child = require('../../../models/child.model');
const ChildOrganizationMapping = require('../../../models/childOrganizationMapping.model');
const Organization = require('../../../models/organization.model');
const dynamoose = require('dynamoose');
const TestCase = require('./testcaseChild');
const axios = require('axios');
const jwt = require('jsonwebtoken');
const NotificationModel = require('../../../models/notification.model');
const AwsOpenSearchService = require('../../../util/opensearch');
const Utils = require('../../../util/utilFunctions');
const UploadService = require('../../../util/uploadService');
const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
/* User Token */
const user = {
    id: '95285616-3769-4b2e-b36b-898597b8146e',
    sub: '95285616-3769-4b2e-b36b-898597b8146e',
    email: '<EMAIL>',
    isVerified: 1,
    role: 1,
    status: 'active',
    isDeleted: 0
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'testToken', tokenOptionalInfo)
};

Utils.addCommonReqTokenForHMac(request);
describe('Child Details', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get error if child is not associated with me', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });
            request(process.env.BASE_URL)
                .get('/child?childId=15285616-3769-4b2e-b36b-898597b8146e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 404);
                    done();
                });
        });

        it('As a user I should get child details', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });
            sinon.stub(Child, 'get').resolves({
                id: 123, firstName: 'John', lastName: 'Doe', followers: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e', status: 'accepted'
                }],
                followings: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e', status: 'accepted'
                }],
                school: '123', homeRoom: '123'
            });
            sinon.stub(Organization, 'get').resolves({ name: 'org name' });
            request(process.env.BASE_URL)
                .get('/child?childId=95285616-3769-4b2e-b36b-898597b8146e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    Organization.get.restore();
                    done();
                });
        });

        it('As a user I should get child details with 0 followers and followings count', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });
            sinon.stub(Child, 'get').resolves({
                id: 123, firstName: 'John', lastName: 'Doe',
                school: '123', homeRoom: '123'
            });
            sinon.stub(Organization, 'get').resolves({ name: 'org name' });
            request(process.env.BASE_URL)
                .get('/child?childId=95285616-3769-4b2e-b36b-898597b8146e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    Organization.get.restore();
                    done();
                });
        });
    }
    catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Send follow request', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });
        TestCase.followChild.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({ status: 'active', isVerified: 1, role: 1 });
                request(process.env.BASE_URL)
                    .post('/child/follow')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should get error if child is not associated with me', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });
            request(process.env.BASE_URL)
                .post('/child/follow')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    followerChildId: '75285616-3769-4b2e-b36b-898597b8146e',
                    followingChildId: '87285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error if child to follow is associated with me', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });
            request(process.env.BASE_URL)
                .post('/child/follow')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    followerChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    followingChildId: '95285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error if child to follow is associated with me', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves(null);

            request(process.env.BASE_URL)
                .post('/child/follow')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    followerChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    followingChildId: '88285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Child.get.restore();
                    done();
                });
        });

        it('As a user I should get error if request is in pending', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e'
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves(null);

            request(process.env.BASE_URL)
                .post('/child/follow')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    followerChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    followingChildId: '88285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should get error if I am already following that child', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e', followings: [{
                    childId: '88285616-3769-4b2e-b36b-898597b8146e',
                    status: 'accepted'
                }]
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e', followers: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'accepted'
                }]
            });

            request(process.env.BASE_URL)
                .post('/child/follow')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    followerChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    followingChildId: '88285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should get error if user has already rejected my request', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e', followings: [{
                    childId: '88285616-3769-4b2e-b36b-898597b8146e',
                    status: 'rejected'
                }]
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e', followers: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'rejected'
                }]
            });

            request(process.env.BASE_URL)
                .post('/child/follow')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    followerChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    followingChildId: '88285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should get error if I am already following that child', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e', followings: [{
                    childId: '88285616-3769-4b2e-b36b-898597b8146e',
                    status: 'requested'
                }]
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e', followers: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'requested'
                }]
            });

            request(process.env.BASE_URL)
                .post('/child/follow')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    followerChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    followingChildId: '88285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should get error if I am not in same school as following child', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e', followings: [{
                    childId: '88285616-3769-4b2e-b36b-898597b8146e',
                    status: 'requested'
                }],
                school: '123e4567-e89b-12d3-a456-426614174000'
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e', followers: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'requested'
                }],
                school: '456e4567-e89b-12d3-a456-426614174000'
            });

            request(process.env.BASE_URL)
                .post('/child/follow')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    followerChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    followingChildId: '88285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should send follow request to child successfully', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e'
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e'
            });

            sinon.stub(Child.transaction, 'update').resolves();

            sinon.stub(dynamoose, 'transaction').resolves();

            request(process.env.BASE_URL)
                .post('/child/follow')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    followerChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    followingChildId: '88285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childGetStub.restore();
                    Child.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });
    }
    catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('send connection request', () => {

    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.connectChild.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({ status: 'active', isVerified: 1, role: 1 });
                request(process.env.BASE_URL)
                    .post('/child/connect')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should get error if child is not associated with me', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });
            request(process.env.BASE_URL)
                .post('/child/connect')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    requesterChildId: '75285616-3769-4b2e-b36b-898597b8146e',
                    requestedChildId: '87285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error if child to connect is associated with me', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });
            request(process.env.BASE_URL)
                .post('/child/connect')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    requesterChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    requestedChildId: '95285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error if child doesn\'t exists', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves(null);

            request(process.env.BASE_URL)
                .post('/child/connect')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    requesterChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    requestedChildId: '88285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Child.get.restore();
                    done();
                });
        });

        it('As a user I should get error if child to connect doesn\'t exists', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e'
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves(null);

            request(process.env.BASE_URL)
                .post('/child/connect')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    requesterChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    requestedChildId: '88285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should get error if I am already connected with that child', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e', connections: [{
                    childId: '88285616-3769-4b2e-b36b-898597b8146e',
                    status: 'connected'
                }]
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e', connections: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'connected'
                }]
            });

            request(process.env.BASE_URL)
                .post('/child/connect')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    requesterChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    requestedChildId: '88285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should get error if user has already rejected my request', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e', connections: [{
                    childId: '88285616-3769-4b2e-b36b-898597b8146e',
                    status: 'rejected'
                }]
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e', connections: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'rejected'
                }]
            });

            request(process.env.BASE_URL)
                .post('/child/connect')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    requesterChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    requestedChildId: '88285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should get error if I have already sent connection request', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e', connections: [{
                    childId: '88285616-3769-4b2e-b36b-898597b8146e',
                    status: 'requestedTo'
                }]
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e', connections: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'requestedBy'
                }]
            });

            request(process.env.BASE_URL)
                .post('/child/connect')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    requesterChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    requestedChildId: '88285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should get error if I have already received connection request', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e', connections: [{
                    childId: '88285616-3769-4b2e-b36b-898597b8146e',
                    status: 'requestedBy'
                }]
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e', connections: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'requestedTo'
                }]
            });

            request(process.env.BASE_URL)
                .post('/child/connect')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    requesterChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    requestedChildId: '88285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should get error if I am not in same school as child to connect', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e',
                school: '123e4567-e89b-12d3-a456-426614174000'
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e',
                school: '456e4567-e89b-12d3-a456-426614174000'
            });

            request(process.env.BASE_URL)
                .post('/child/connect')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    requesterChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    requestedChildId: '88285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should send follow request to child successfully', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get').resolves({
                guardians: ['123'],
                connections: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'requestedBy'
                }],
                save: () => { }
            });

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e'
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e',
                photoURL: 'https://test.com/test.jpg'
            });

            sinon.stub(Child.transaction, 'update').resolves();

            sinon.stub(dynamoose, 'transaction').resolves();

            const axiosPostStub = sinon.stub(axios, 'post').resolves({ data: {} });
            const pushInNotificationStub = sinon.stub(NotificationModel, 'batchPut').resolves();
            request(process.env.BASE_URL)
                .post('/child/connect')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    requesterChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    requestedChildId: '88285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childGetStub.restore();
                    Child.transaction.update.restore();
                    dynamoose.transaction.restore();
                    axiosPostStub.restore();
                    pushInNotificationStub.restore();
                    done();
                });
        });

        it('As a user I should send follow request to child successfully and send notification', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get').resolves({
                guardians: ['123'],
                connections: [{
                    childId: '88285616-3769-4b2e-b36b-898597b8146e',
                    status: 'requestedBy'
                }],
                save: () => { }
            });

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e'
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e'
            });

            sinon.stub(Child.transaction, 'update').resolves();

            sinon.stub(dynamoose, 'transaction').resolves();

            const axiosPostStub = sinon.stub(axios, 'post').resolves({ data: {} });
            const pushInNotificationStub = sinon.stub(NotificationModel, 'batchPut').resolves();
            request(process.env.BASE_URL)
                .post('/child/connect')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    requesterChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    requestedChildId: '88285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childGetStub.restore();
                    Child.transaction.update.restore();
                    dynamoose.transaction.restore();
                    axiosPostStub.restore();
                    pushInNotificationStub.restore();
                    done();
                });
        });

        it('As a user I should send follow request to child successfully and should not send notification if there is error', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get').resolves({
                guardians: ['123'],
                connections: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'requestedBy'
                }],
                save: () => { }
            });

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e'
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e'
            });

            sinon.stub(Child.transaction, 'update').resolves();

            sinon.stub(dynamoose, 'transaction').resolves();

            const axiosPostStub = sinon.stub(axios, 'post').rejects({ data: {} });
            const pushInNotificationStub = sinon.stub(NotificationModel, 'batchPut').resolves();
            request(process.env.BASE_URL)
                .post('/child/connect')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    requesterChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    requestedChildId: '88285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childGetStub.restore();
                    Child.transaction.update.restore();
                    dynamoose.transaction.restore();
                    axiosPostStub.restore();
                    pushInNotificationStub.restore();
                    done();
                });
        });

        it('should handle error when jwtClient authorize fails', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get').resolves({
                guardians: ['123'],
                connections: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'requestedBy'
                }],
                save: () => { }
            });

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e'
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e'
            });

            sinon.stub(Child.transaction, 'update').resolves();

            sinon.stub(dynamoose, 'transaction').resolves();

            const axiosPostStub = sinon.stub(axios, 'post').resolves({ data: {} });
            const pushInNotificationStub = sinon.stub(NotificationModel, 'batchPut').resolves();
            request(process.env.BASE_URL)
                .post('/child/connect')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    requesterChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    requestedChildId: '88285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childGetStub.restore();
                    Child.transaction.update.restore();
                    dynamoose.transaction.restore();
                    axiosPostStub.restore();
                    pushInNotificationStub.restore();
                    done();
                });
        });
    }
    catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Change follow request status', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.changeFollowRequestStatus.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({ status: 'active', isVerified: 1, role: 1 });
                request(process.env.BASE_URL)
                    .put('/child/follow-request')
                    .set({
                        Authorization: requestPayloadUser.token
                    })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should get error if child is not associated with me', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });
            request(process.env.BASE_URL)
                .put('/child/follow-request')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    followerChildId: '75285616-3769-4b2e-b36b-898597b8146e',
                    followingChildId: '87285616-3769-4b2e-b36b-898597b8146e',
                    status: 'accepted'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error if I try to follow my child', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });
            request(process.env.BASE_URL)
                .put('/child/follow-request')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    followerChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    followingChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'accepted'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error message if status is already accepted', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e', followers: [{
                    childId: '88285616-3769-4b2e-b36b-898597b8146e', status: 'accepted'
                }]
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e', followings: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e', status: 'accepted'
                }]
            });

            request(process.env.BASE_URL)
                .put('/child/follow-request')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    followerChildId: '88285616-3769-4b2e-b36b-898597b8146e',
                    followingChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'accepted'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should get error message if request is not found', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e', followers: []
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e', followings: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e', status: 'accepted'
                }]
            });

            request(process.env.BASE_URL)
                .put('/child/follow-request')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    followerChildId: '88285616-3769-4b2e-b36b-898597b8146e',
                    followingChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'accepted'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should be able to accept follow request', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e', followers: [{
                    childId: '88285616-3769-4b2e-b36b-898597b8146e', status: 'requested'
                }]
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e', followings: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e', status: 'requested'
                }]
            });

            sinon.stub(Child.transaction, 'update').resolves();

            sinon.stub(dynamoose, 'transaction').resolves();

            request(process.env.BASE_URL)
                .put('/child/follow-request')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    followerChildId: '88285616-3769-4b2e-b36b-898597b8146e',
                    followingChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'accepted'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childGetStub.restore();
                    Child.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });

        it('As a user I should be able to deny follow request', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e', followers: [{
                    childId: '88285616-3769-4b2e-b36b-898597b8146e', status: 'requested'
                }]
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e', followings: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e', status: 'requested'
                }]
            });

            sinon.stub(Child.transaction, 'update').resolves();

            sinon.stub(dynamoose, 'transaction').resolves();

            request(process.env.BASE_URL)
                .put('/child/follow-request')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    followerChildId: '88285616-3769-4b2e-b36b-898597b8146e',
                    followingChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'rejected'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childGetStub.restore();
                    Child.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });
    }
    catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Change connection request status', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.changeConnectionRequestStatus.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({ status: 'active', isVerified: 1, role: 1 });
                request(process.env.BASE_URL)
                    .put('/child/connection-request')
                    .set({
                        Authorization: requestPayloadUser.token
                    })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should get error if child is not associated with me', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });
            request(process.env.BASE_URL)
                .put('/child/connection-request')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    requesterChildId: '75285616-3769-4b2e-b36b-898597b8146e',
                    requestedChildId: '87285616-3769-4b2e-b36b-898597b8146e',
                    status: 'connected'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error if I try to connect with my child', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });
            request(process.env.BASE_URL)
                .put('/child/connection-request')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    requesterChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    requestedChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'connected'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error message if status is already connected', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e', connections: [{
                    childId: '88285616-3769-4b2e-b36b-898597b8146e', status: 'connected'
                }]
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e', connections: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e', status: 'connected'
                }]
            });

            request(process.env.BASE_URL)
                .put('/child/connection-request')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    requesterChildId: '88285616-3769-4b2e-b36b-898597b8146e',
                    requestedChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'connected'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should get error message if request is not found', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e', connections: []
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e', connections: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e', status: 'connected'
                }]
            });

            request(process.env.BASE_URL)
                .put('/child/connection-request')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    requesterChildId: '88285616-3769-4b2e-b36b-898597b8146e',
                    requestedChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'connected'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should be able to accept connection request', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e', connections: [{
                    childId: '88285616-3769-4b2e-b36b-898597b8146e', status: 'requestedTo'
                }]
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e', connections: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e', status: 'requestedBy'
                }]
            });

            sinon.stub(Child.transaction, 'update').resolves();

            sinon.stub(dynamoose, 'transaction').resolves();

            request(process.env.BASE_URL)
                .put('/child/connection-request')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    requesterChildId: '88285616-3769-4b2e-b36b-898597b8146e',
                    requestedChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'connected'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childGetStub.restore();
                    Child.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });

        it('As a user I should be able to deny connection request', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs('95285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '95285616-3769-4b2e-b36b-898597b8146e', connections: [{
                    childId: '88285616-3769-4b2e-b36b-898597b8146e', status: 'requestedTo', notificationIds: ['123', '234']
                }]
            });
            childGetStub.withArgs('88285616-3769-4b2e-b36b-898597b8146e').resolves({
                id: '88285616-3769-4b2e-b36b-898597b8146e', connections: [{
                    childId: '95285616-3769-4b2e-b36b-898597b8146e', status: 'requestedBy', notificationIds: ['123']
                }]
            });

            sinon.stub(Child.transaction, 'update').resolves();

            sinon.stub(dynamoose, 'transaction').resolves();

            sinon.stub(NotificationModel, 'batchDelete').resolves();

            request(process.env.BASE_URL)
                .put('/child/connection-request')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    requesterChildId: '88285616-3769-4b2e-b36b-898597b8146e',
                    requestedChildId: '95285616-3769-4b2e-b36b-898597b8146e',
                    status: 'rejected'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childGetStub.restore();
                    Child.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Search child', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.searchChild.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({ status: 'active', isVerified: 1, role: 1 });
                request(process.env.BASE_URL)
                    .get('/child/search')
                    .set({
                        Authorization: requestPayloadUser.token
                    })
                    .query(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should get error if child is not associated with me', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });
            request(process.env.BASE_URL)
                .get('/child/search')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    searchValue: 'test'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should be able to search child', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                school: '123e4567-e89b-12d3-a456-426614174000',
                followings: [{
                    childId: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    status: 'accepted'
                }]
            });

            const childOrgMappingStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();

            childOrgMappingStub.returns({
                eq: eqStub.returns({
                    using: usingStub.returns({
                        exec: execStub.returns([{
                            childId: 'xyz85616-3769-4b2e-b36b-898597b8146e'
                        }])
                    })
                })
            });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test'
            }]);

            sinon.stub(Organization, 'get').resolves({ name: 'Test School' });

            request(process.env.BASE_URL)
                .get('/child/search')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    searchValue: 'test'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    ChildOrganizationMapping.query.restore();
                    Child.batchGet.restore();
                    Organization.get.restore();
                    done();
                });
        });

        it('As a user I should be able to search child and get status if I already have sent follow request', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                school: '123e4567-e89b-12d3-a456-426614174000',
                followings: [{
                    childId: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    status: 'rejected'
                }]
            });

            const childOrgMappingStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();

            childOrgMappingStub.returns({
                eq: eqStub.returns({
                    using: usingStub.returns({
                        exec: execStub.returns([{
                            childId: 'xyz85616-3769-4b2e-b36b-898597b8146e'
                        }])
                    })
                })
            });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test'
            }]);

            sinon.stub(Organization, 'get').resolves({ name: 'Test School' });

            request(process.env.BASE_URL)
                .get('/child/search')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    searchValue: 'test'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    ChildOrganizationMapping.query.restore();
                    Child.batchGet.restore();
                    Organization.get.restore();
                    done();
                });
        });

        it('As a user I should be able to search child and get status if I have not already have sent follow request', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                school: '123e4567-e89b-12d3-a456-426614174000'
            });

            const childOrgMappingStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();

            childOrgMappingStub.returns({
                eq: eqStub.returns({
                    using: usingStub.returns({
                        exec: execStub.returns([{
                            childId: 'xyz85616-3769-4b2e-b36b-898597b8146e'
                        }])
                    })
                })
            });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test'
            }]);

            sinon.stub(Organization, 'get').resolves({ name: 'Test School' });

            request(process.env.BASE_URL)
                .get('/child/search')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    searchValue: 'test'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    ChildOrganizationMapping.query.restore();
                    Child.batchGet.restore();
                    Organization.get.restore();
                    done();
                });
        });

        it('As a user I should be able to search child and get empty list if no child found', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                school: '123e4567-e89b-12d3-a456-426614174000'
            });

            const childOrgMappingStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();

            childOrgMappingStub.returns({
                eq: eqStub.returns({
                    using: usingStub.returns({
                        exec: execStub.returns([])
                    })
                })
            });

            request(process.env.BASE_URL)
                .get('/child/search')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    searchValue: 'test'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    ChildOrganizationMapping.query.restore();
                    done();
                });
        });

        it('As a user I should be able to search child with his image in response', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                school: '123e4567-e89b-12d3-a456-426614174000',
                followings: [{
                    childId: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    status: 'requested'
                }]
            });

            const childOrgMappingStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();

            childOrgMappingStub.returns({
                eq: eqStub.returns({
                    using: usingStub.returns({
                        exec: execStub.returns([{
                            childId: 'xyz85616-3769-4b2e-b36b-898597b8146e'
                        }])
                    })
                })
            });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test',
                photoURL: 'https://test.com/test.jpg',
                homeRoom: 'xyz85616-3769-4b2e-b36b-898597b8146f'
            }]);

            sinon.stub(Organization, 'get').resolves({ name: 'Test School' });

            request(process.env.BASE_URL)
                .get('/child/search')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    searchValue: 'test'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    ChildOrganizationMapping.query.restore();
                    Child.batchGet.restore();
                    Organization.get.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Search org child', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.orgChildSearch.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({ status: 'active', isVerified: 1, role: 1 });
                request(process.env.BASE_URL)
                    .get('/child/org-child-search')
                    .set({
                        Authorization: requestPayloadUser.token
                    })
                    .query(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should get error if child is not associated with me', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });
            request(process.env.BASE_URL)
                .get('/child/org-child-search')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    searchValue: 'test'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should be able to search org child', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                school: '123e4567-e89b-12d3-a456-426614174000',
                connections: [{
                    childId: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    status: 'connected'
                }]
            });

            const searchChildStub = sinon.stub(AwsOpenSearchService, 'searchChild');
            searchChildStub.resolves([
                {
                    _index: 'children',
                    _id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    _source: {
                        id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                        firstName: 'Test',
                        lastName: 'Child',
                        school: '123e4567-e89b-12d3-a456-426614174000',
                        associatedOrganizations: [],
                        associatedColor: '#90C33A',
                        photoURL: null,
                        homeRoom: 'Room 101'
                    }
                }
            ]);

            const childOrgMappingStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();

            childOrgMappingStub.returns({
                eq: eqStub.returns({
                    using: usingStub.returns({
                        exec: execStub.returns([{
                            childId: 'xyz85616-3769-4b2e-b36b-898597b8146e'
                        }])
                    })
                })
            });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test'
            }]);

            sinon.stub(Organization, 'get').resolves({ name: 'Test School' });

            request(process.env.BASE_URL)
                .get('/child/org-child-search')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    searchValue: 'test'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    ChildOrganizationMapping.query.restore();
                    Child.batchGet.restore();
                    Organization.get.restore();
                    searchChildStub.restore();
                    done();
                });
        });

        it('As a user I should be able to search org child and get status if I already have sent follow request', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                school: '123e4567-e89b-12d3-a456-426614174000',
                connections: [{
                    childId: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    status: 'requestedTo'
                }]
            });

            const searchChildStub = sinon.stub(AwsOpenSearchService, 'searchChild');
            searchChildStub.resolves([
                {
                    _index: 'children',
                    _id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    _source: {
                        id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                        firstName: 'Test',
                        lastName: 'Child',
                        school: '123e4567-e89b-12d3-a456-426614174000',
                        associatedOrganizations: [],
                        associatedColor: '#90C33A',
                        photoURL: 'https://test.com/test.jpg',
                        homeRoom: 'Room 101'
                    }
                }
            ]);

            const childOrgMappingStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();

            childOrgMappingStub.returns({
                eq: eqStub.returns({
                    using: usingStub.returns({
                        exec: execStub.returns([{
                            childId: 'xyz85616-3769-4b2e-b36b-898597b8146e'
                        }])
                    })
                })
            });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test'
            }]);

            sinon.stub(Organization, 'get').resolves({ name: 'Test School' });

            request(process.env.BASE_URL)
                .get('/child/org-child-search')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    searchValue: 'test'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    ChildOrganizationMapping.query.restore();
                    Child.batchGet.restore();
                    Organization.get.restore();
                    searchChildStub.restore();
                    done();
                });
        });

        it('As a user I should be able to search org child and get rejected status if I already have sent follow request', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                school: '123e4567-e89b-12d3-a456-426614174000',
                connections: [{
                    childId: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    status: 'rejected'
                }]
            });

            const searchChildStub = sinon.stub(AwsOpenSearchService, 'searchChild');
            searchChildStub.resolves([
                {
                    _index: 'children',
                    _id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    _source: {
                        id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                        firstName: 'Test',
                        lastName: 'Child',
                        school: '123e4567-e89b-12d3-a456-426614174000',
                        associatedOrganizations: [],
                        associatedColor: '#90C33A',
                        photoURL: null,
                        homeRoom: 'Room 101'
                    }
                }
            ]);

            const childOrgMappingStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();

            childOrgMappingStub.returns({
                eq: eqStub.returns({
                    using: usingStub.returns({
                        exec: execStub.returns([{
                            childId: 'xyz85616-3769-4b2e-b36b-898597b8146e'
                        }])
                    })
                })
            });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test'
            }]);

            sinon.stub(Organization, 'get').resolves({ name: 'Test School' });

            request(process.env.BASE_URL)
                .get('/child/org-child-search')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    searchValue: 'test'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    ChildOrganizationMapping.query.restore();
                    Child.batchGet.restore();
                    Organization.get.restore();
                    searchChildStub.restore();
                    done();
                });
        });

        it('As a user I should be able to search orgchild and get status if I have not already have sent follow request', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                school: '123e4567-e89b-12d3-a456-426614174000'
            });
            const searchChildStub = sinon.stub(AwsOpenSearchService, 'searchChild');
            searchChildStub.resolves([
                {
                    _index: 'children',
                    _id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    _source: {
                        id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                        firstName: 'Test',
                        lastName: 'Child',
                        school: '123e4567-e89b-12d3-a456-426614174000',
                        associatedOrganizations: [],
                        associatedColor: '#90C33A',
                        photoURL: null,
                        homeRoom: 'Room 101'
                    }
                }
            ]);
            const childOrgMappingStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();

            childOrgMappingStub.returns({
                eq: eqStub.returns({
                    using: usingStub.returns({
                        exec: execStub.returns([{
                            childId: 'xyz85616-3769-4b2e-b36b-898597b8146e'
                        }])
                    })
                })
            });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test'
            }]);

            sinon.stub(Organization, 'get').resolves({ name: 'Test School' });

            request(process.env.BASE_URL)
                .get('/child/org-child-search')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    searchValue: 'test'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    ChildOrganizationMapping.query.restore();
                    Child.batchGet.restore();
                    Organization.get.restore();
                    searchChildStub.restore();
                    done();
                });
        });

        it('As a user I should be able to search org child and get empty list if no child found', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                school: '123e4567-e89b-12d3-a456-426614174000'
            });
            const searchChildStub = sinon.stub(AwsOpenSearchService, 'searchChild');
            searchChildStub.resolves([]);

            const childOrgMappingStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();

            childOrgMappingStub.returns({
                eq: eqStub.returns({
                    using: usingStub.returns({
                        exec: execStub.returns([])
                    })
                })
            });

            request(process.env.BASE_URL)
                .get('/child/org-child-search')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    searchValue: 'test'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    ChildOrganizationMapping.query.restore();
                    searchChildStub.restore();
                    done();
                });
        });

        it('As a user I should be able to search org child with his image in response', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                school: '123e4567-e89b-12d3-a456-426614174000',
                connections: [{
                    childId: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    status: 'requestedBy'
                }]
            });

            const searchChildStub = sinon.stub(AwsOpenSearchService, 'searchChild');
            searchChildStub.resolves([
                {
                    _index: 'children',
                    _id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    _source: {
                        id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                        firstName: 'Test',
                        lastName: 'Child',
                        school: '123e4567-e89b-12d3-a456-426614174000',
                        associatedOrganizations: [],
                        associatedColor: '#90C33A',
                        photoURL: 'http://example.com/image.jpg',
                        homeRoom: 'Room 101'
                    }
                }
            ]);

            const childOrgMappingStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();

            childOrgMappingStub.returns({
                eq: eqStub.returns({
                    using: usingStub.returns({
                        exec: execStub.returns([{
                            childId: 'xyz85616-3769-4b2e-b36b-898597b8146e'
                        }])
                    })
                })
            });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test',
                photoURL: 'https://test.com/test.jpg',
                homeRoom: 'xyz85616-3769-4b2e-b36b-898597b8146f'
            }]);

            sinon.stub(Organization, 'get').resolves({ name: 'Test School' });

            request(process.env.BASE_URL)
                .get('/child/org-child-search')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    searchValue: 'test'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    ChildOrganizationMapping.query.restore();
                    Child.batchGet.restore();
                    Organization.get.restore();
                    searchChildStub.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Unfollow Child', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.unfollowChild.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({ status: 'active', isVerified: 1, role: 1 });
                request(process.env.BASE_URL)
                    .post('/child/unfollow')
                    .set({
                        Authorization: requestPayloadUser.token
                    })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should get error if follow request is in requested state and I try to unfollow', (done) => {
            const reqBody = {
                followerChildId: '75285616-3769-4b2e-b36b-898597b8146e',
                followingChildId: '88285616-3769-4b2e-b36b-898597b8146e'
            };

            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: [reqBody.followerChildId] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs(reqBody.followerChildId).resolves({
                id: reqBody.followerChildId, followings: [{
                    childId: reqBody.followingChildId, status: 'requested'
                }]
            });
            childGetStub.withArgs(reqBody.followingChildId).resolves({
                id: reqBody.followingChildId, followers: [{
                    childId: reqBody.followerChildId, status: 'requested'
                }]
            });

            request(process.env.BASE_URL)
                .post('/child/unfollow')
                .set({ Authorization: requestPayloadUser.token })
                .send(reqBody)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childGetStub.restore();
                    done();
                });
        });


        it('As a user I should get error if follow request doesnt exists and I try to unfollow', (done) => {
            const reqBody = {
                followerChildId: '75285616-3769-4b2e-b36b-898597b8146e',
                followingChildId: '88285616-3769-4b2e-b36b-898597b8146e'
            };

            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: [reqBody.followerChildId] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs(reqBody.followerChildId).resolves({
                id: reqBody.followerChildId, followings: []
            });
            childGetStub.withArgs(reqBody.followingChildId).resolves({
                id: reqBody.followingChildId, followers: []
            });

            request(process.env.BASE_URL)
                .post('/child/unfollow')
                .set({ Authorization: requestPayloadUser.token })
                .send(reqBody)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should be able to unfollow child', (done) => {
            const reqBody = {
                followerChildId: '75285616-3769-4b2e-b36b-898597b8146e',
                followingChildId: '88285616-3769-4b2e-b36b-898597b8146e'
            };

            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: [reqBody.followerChildId] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs(reqBody.followerChildId).resolves({
                id: reqBody.followerChildId, followings: [{
                    childId: reqBody.followingChildId, status: 'accepted'
                }]
            });
            childGetStub.withArgs(reqBody.followingChildId).resolves({
                id: reqBody.followingChildId, followers: [{
                    childId: reqBody.followerChildId, status: 'accepted'
                }]
            });

            sinon.stub(Child.transaction, 'update').resolves();

            sinon.stub(dynamoose, 'transaction').resolves();

            request(process.env.BASE_URL)
                .post('/child/unfollow')
                .set({ Authorization: requestPayloadUser.token })
                .send(reqBody)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childGetStub.restore();
                    Child.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Get relationships lists', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.getRelationships.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({ status: 'active', isVerified: 1, role: 1 });
                request(process.env.BASE_URL)
                    .get('/child/relationships')
                    .set({
                        Authorization: requestPayloadUser.token
                    })
                    .query(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should get error if child is not associated with me', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: [] });
            request(process.env.BASE_URL)
                .get('/child/relationships')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    relationshipType: 'followers'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get followers list', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                followers: [{
                    childId: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    status: 'accepted'
                }]
            });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test'
            }]);

            sinon.stub(Organization, 'get').resolves({ name: 'Test School' });

            request(process.env.BASE_URL)
                .get('/child/relationships')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    relationshipType: 'followers'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    Child.batchGet.restore();
                    Organization.get.restore();
                    done();
                });
        });

        it('As a user I should get followings list', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                followings: [{
                    childId: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    status: 'accepted'
                }]
            });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test'
            }]);

            sinon.stub(Organization, 'get').resolves({ name: 'Test School' });

            request(process.env.BASE_URL)
                .get('/child/relationships')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    relationshipType: 'followings'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    Child.batchGet.restore();
                    Organization.get.restore();
                    done();
                });
        });

        it('As a user I should get requestedTo list', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                followings: [{
                    childId: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    status: 'requested'
                }]
            });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test'
            }]);

            sinon.stub(Organization, 'get').resolves({ name: 'Test School' });

            request(process.env.BASE_URL)
                .get('/child/relationships')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    relationshipType: 'requestedTo'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    Child.batchGet.restore();
                    Organization.get.restore();
                    done();
                });
        });

        it('As a user I should get requestedBy list', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                followers: [{
                    childId: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    status: 'requested'
                }]
            });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test'
            }]);

            sinon.stub(Organization, 'get').resolves({ name: 'Test School' });

            request(process.env.BASE_URL)
                .get('/child/relationships')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    relationshipType: 'requestedBy'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    Child.batchGet.restore();
                    Organization.get.restore();
                    done();
                });
        });

        it('As a user I should get empty requestedBy list', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                followers: [{
                    childId: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    status: 'accepted'
                }]
            });

            request(process.env.BASE_URL)
                .get('/child/relationships')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    relationshipType: 'requestedBy'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Get connections list', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.getConnections.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({ status: 'active', isVerified: 1, role: 1 });
                request(process.env.BASE_URL)
                    .get('/child/child-connections')
                    .set({
                        Authorization: requestPayloadUser.token
                    })
                    .query(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should get error if child is not associated with me', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: [] });
            request(process.env.BASE_URL)
                .get('/child/child-connections')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    connectionType: 'connected'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get connections list', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                connections: [{
                    childId: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    status: 'connected'
                }]
            });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test'
            }]);

            sinon.stub(Organization, 'get').resolves({ name: 'Test School' });

            request(process.env.BASE_URL)
                .get('/child/child-connections')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    connectionType: 'connected'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    Child.batchGet.restore();
                    Organization.get.restore();
                    done();
                });
        });

        it('As a user I should get requestedTo list', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                connections: [{
                    childId: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    status: 'requestedTo'
                }]
            });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test'
            }]);

            sinon.stub(Organization, 'get').resolves({ name: 'Test School' });

            request(process.env.BASE_URL)
                .get('/child/child-connections')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    connectionType: 'requestedTo'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    Child.batchGet.restore();
                    Organization.get.restore();
                    done();
                });
        });

        it('As a user I should get requestedBy list', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                connections: [{
                    childId: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    status: 'requestedBy'
                }]
            });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test'
            }]);

            sinon.stub(Organization, 'get').resolves({ name: 'Test School' });

            request(process.env.BASE_URL)
                .get('/child/child-connections')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    connectionType: 'requestedBy'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    Child.batchGet.restore();
                    Organization.get.restore();
                    done();
                });
        });

        it('As a user I should get empty requestedBy list', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                connections: [{
                    childId: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                    status: 'connected'
                }]
            });

            request(process.env.BASE_URL)
                .get('/child/child-connections')
                .set({ Authorization: requestPayloadUser.token })
                .query({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    connectionType: 'requestedBy'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Validate child existence', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a parent I should get error if child alreay exists', (done) => {
            const addChild = {
                firstName: 'Test',
                lastName: 'User',
                gender: 'male',
                schoolId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                zipCode: '12345',
                associatedColor: '#FFFFFF',
                homeroomId: '9fd6a871-a6f5-4690-b06a-d786f1361eef'
            };

            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });
            const childStub = sinon.stub(Child, 'query');
            childStub.returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        exec: sinon.stub().resolves([{ id: '123' }])
                                    })
                                })
                            })
                        })
                    })
                })
            });
            request(process.env.BASE_URL)
                .post('/child/exists')
                .set({ Authorization: requestPayloadUser.token })
                .send(addChild)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childStub.restore();
                    done();
                });
        });

        it('As a parent I should add child if child does not exists', (done) => {
            const addChild = {
                firstName: 'Test',
                lastName: 'User',
                gender: 'male',
                schoolId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                zipCode: '12345',
                associatedColor: '#FFFFFF',
                homeroomId: '9fd6a871-a6f5-4690-b06a-d786f1361eef'
            };

            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });
            const childStub = sinon.stub(Child, 'query');
            childStub.returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        exec: sinon.stub().resolves([])
                                    })
                                })
                            })
                        })
                    })
                })
            });
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.withArgs({ id: addChild.schoolId }).returns({ category: CONSTANTS.CATEGORIES.SCHOOL });
            orgScanStub.withArgs({ id: addChild.homeroomId }).returns({
                category: CONSTANTS.CATEGORIES.HOME_ROOM, parentOrganization: addChild.schoolId
            });

            sinon.stub(Organization, 'query').returns({
                eq: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            attributes: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ id: '123' }])
                            })
                        })
                    }),
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ id: '123' }])
                    })
                })
            });

            sinon.stub(Child, 'create').resolves({ id: '1' });
            sinon.stub(ChildOrganizationMapping, 'create').resolves();
            sinon.stub(User, 'update').resolves();

            request(process.env.BASE_URL)
                .post('/child/exists')
                .set({ Authorization: requestPayloadUser.token })
                .send(addChild)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childStub.restore();
                    orgScanStub.restore();
                    Organization.query.restore();
                    Child.create.restore();
                    ChildOrganizationMapping.create.restore();
                    User.update.restore();
                    done();
                });
        });
        it('As a parent I should add child if child does not exists with child profile picture', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });
            const childStub = sinon.stub(Child, 'query');
            childStub.returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        exec: sinon.stub().resolves([])
                                    })
                                })
                            })
                        })
                    })
                })
            });
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.withArgs({ id: '0fd6a871-a6f5-4690-b06a-d786f1361eef' }).returns({ category: CONSTANTS.CATEGORIES.SCHOOL });
            orgScanStub.withArgs({ id: '9fd6a871-a6f5-4690-b06a-d786f1361eef' }).returns({
                category: CONSTANTS.CATEGORIES.HOME_ROOM, parentOrganization: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
            });

            sinon.stub(Organization, 'query').returns({
                eq: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            attributes: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ id: '123' }])
                            })
                        })
                    }),
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ id: '123' }])
                    })
                })
            });

            sinon.stub(Child, 'create').resolves({ id: '1' });
            sinon.stub(ChildOrganizationMapping, 'create').resolves();
            sinon.stub(User, 'update').resolves();

            request(process.env.BASE_URL)
                .post('/child/exists')
                .set({ Authorization: requestPayloadUser.token })
                .field('firstName', 'Test')
                .field('lastName', 'User')
                .field('gender', 'male')
                .field('schoolId', '0fd6a871-a6f5-4690-b06a-d786f1361eef')
                .field('zipCode', '12345')
                .field('associatedColor', '#FFFFFF')
                .field('homeroomId', '9fd6a871-a6f5-4690-b06a-d786f1361eef')
                .attach('photo', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childStub.restore();
                    Organization.query.restore();
                    done();
                });
        });

        it('As a parent I should get error if child profile picture is not valid', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['95285616-3769-4b2e-b36b-898597b8146e'] });
            const childStub = sinon.stub(Child, 'query');
            childStub.returns({
                eq: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            where: sinon.stub().returns({
                                eq: sinon.stub().returns({
                                    where: sinon.stub().returns({
                                        eq: sinon.stub().returns({
                                            exec: sinon.stub().resolves({ count: 0 })
                                        })
                                    })
                                })
                            })
                        })
                    })
                })
            });

            request(process.env.BASE_URL)
                .post('/child/exists')
                .set({ Authorization: requestPayloadUser.token })
                .field('firstName', 'Test')
                .field('lastName', 'User')
                .field('gender', 'male')
                .field('schoolId', '0fd6a871-a6f5-4690-b06a-d786f1361eef')
                .field('zipCode', '12345')
                .field('associatedColor', '#FFFFFF')
                .field('homeroomId', '9fd6a871-a6f5-4690-b06a-d786f1361eef')
                .attach('photo', 'test/mock-data/TEST.pdf')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childStub.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Update Child', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.updateChild.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({ status: 'active', isVerified: 1, role: 1 });
                request(process.env.BASE_URL)
                    .put('/child/')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('should give error is child is not associated with me', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves(null);

            request(process.env.BASE_URL)
                .put('/child/')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    firstName: 'test',
                    lastName: 'test',
                    dob: '12/12/2015',
                    schoolId: '123e4567-e89b-12d3-a456-426614174000',
                    homeroomId: '456e4567-e89b-12d3-a456-426614174000',
                    zipCode: '12345',
                    associatedColor: '#2772ED'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Child.get.restore();
                    done();
                });
        });

        it('should get error message if new school doesnt exist', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                school: 'xyze4567-e89b-12d3-a456-426614174000'
            });

            sinon.stub(Organization, 'get').resolves(null);

            request(process.env.BASE_URL)
                .put('/child/')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    firstName: 'test',
                    lastName: 'test',
                    dob: '12/12/2015',
                    schoolId: '123e4567-e89b-12d3-a456-426614174000',
                    homeroomId: '456e4567-e89b-12d3-a456-426614174000',
                    zipCode: '12345',
                    associatedColor: '#2772ED'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Child.get.restore();
                    Organization.get.restore();
                    done();
                });
        });

        it('should get error message if new homeroom doesnt exist', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                school: 'xzye4567-e89b-12d3-a456-426614174000',
                homeroom: 'xyze4567-e89b-12d3-a456-426614174000',
                associatedOrganizations: ['123e4567-e89b-12d3-a456-426614174000', 'xzye4567-e89b-12d3-a456-426614174000']
            });

            sinon.stub(Organization, 'get').resolves({
                id: '123e4567-e89b-12d3-a456-426614174000',
                category: 'School',
                parentOrganization: 'xyze4567-e89b-12d3-a456-426614174000'
            });

            const childOrgMappingStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const whereStub = sinon.stub();
            const execStub = sinon.stub();
            childOrgMappingStub.returns({ eq: eqStub });
            whereStub.returns({ eq: eqStub });
            eqStub.withArgs('xzye4567-e89b-12d3-a456-426614174000').returns({ where: whereStub });
            eqStub.withArgs('123e4567-e89b-12d3-a456-426614174000').returns({ where: whereStub });
            eqStub.withArgs('75285616-3769-4b2e-b36b-898597b8146e').returns({ exec: execStub });
            execStub.resolves([{ id: '123e4567-e89b-12d3-a456-426614174000', save: () => {} }]);

            const OrgStub = sinon.stub(Organization, 'query');
            const orgEqStub = sinon.stub();
            const orgWhereStub = sinon.stub();
            const orgExecStub = sinon.stub();
            OrgStub.returns({ eq: orgEqStub });
            orgWhereStub.returns({ eq: orgEqStub });
            orgEqStub.withArgs('xzye4567-e89b-12d3-a456-426614174000').returns({ where: orgWhereStub });
            orgEqStub.withArgs('123e4567-e89b-12d3-a456-426614174000').returns({ where: orgWhereStub });
            orgEqStub.withArgs('PTO').returns({ exec: orgExecStub });
            orgExecStub.resolves([{ id: '123e4567-e89b-12d3-a456-426614174000', save: () => {} }]);

            request(process.env.BASE_URL)
                .put('/child/')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    firstName: 'test',
                    lastName: 'test',
                    dob: '12/12/2015',
                    schoolId: '123e4567-e89b-12d3-a456-426614174000',
                    homeroomId: '456e4567-e89b-12d3-a456-426614174000',
                    zipCode: '12345',
                    associatedColor: '#2772ED'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Child.get.restore();
                    Organization.get.restore();
                    Organization.query.restore();
                    childOrgMappingStub.restore();
                    done();
                });
        });

        it('As a user I should be able to edit details of my child', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                school: '123e4567-e89b-12d3-a456-426614174000',
                homeRoom: '456e4567-e89b-12d3-a456-426614174000',
                associatedOrganizations: ['123e4567-e89b-12d3-a456-426614174000', 'xyze4567-e89b-12d3-a456-426614174000']
            });

            const orgModelStub = sinon.stub(Organization, 'get').resolves({
                name: 'test'
            });
            orgModelStub.withArgs({ id: '456e4567-e89b-12d3-a456-426614174000' }).resolves({
                id: '456e4567-e89b-12d3-a456-426614174000',
                category: 'Homeroom',
                parentOrganization: '123e4567-e89b-12d3-a456-426614174000',
                name: 'test'
            });

            const childOrgMappingStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const whereStub = sinon.stub();
            const execStub = sinon.stub();
            childOrgMappingStub.returns({ eq: eqStub });
            whereStub.returns({ eq: eqStub });
            eqStub.withArgs('123e4567-e89b-12d3-a456-426614174000').returns({ where: whereStub });
            eqStub.withArgs('xyze4567-e89b-12d3-a456-426614174000').returns({ where: whereStub });
            eqStub.withArgs('75285616-3769-4b2e-b36b-898597b8146e').returns({ exec: execStub });
            execStub.resolves([{ id: '123e4567-e89b-12d3-a456-426614174000' }]);

            sinon.stub(Child.transaction, 'update').resolves();
            sinon.stub(ChildOrganizationMapping.transaction, 'update').resolves();
            sinon.stub(dynamoose, 'transaction').resolves();

            request(process.env.BASE_URL)
                .put('/child/')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    firstName: 'test',
                    lastName: 'test',
                    dob: '12/12/2015',
                    schoolId: '123e4567-e89b-12d3-a456-426614174000',
                    homeroomId: '456e4567-e89b-12d3-a456-426614174000',
                    zipCode: '12345',
                    associatedColor: '#2772ED'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    Organization.get.restore();
                    childOrgMappingStub.restore();
                    Child.transaction.update.restore();
                    ChildOrganizationMapping.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });

        it('As a user I should be able to edit details of my child without dob', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                school: '123e4567-e89b-12d3-a456-426614174000',
                homeRoom: '456e4567-e89b-12d3-a456-426614174000',
                associatedOrganizations: ['123e4567-e89b-12d3-a456-426614174000', 'xyze4567-e89b-12d3-a456-426614174000']
            });

            const orgModelStub = sinon.stub(Organization, 'get').resolves({
                name: 'test'
            });
            orgModelStub.withArgs({ id: '456e4567-e89b-12d3-a456-426614174000' }).resolves({
                id: '456e4567-e89b-12d3-a456-426614174000',
                category: 'Homeroom',
                parentOrganization: '123e4567-e89b-12d3-a456-426614174000',
                name: 'test'
            });

            const childOrgMappingStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const whereStub = sinon.stub();
            const execStub = sinon.stub();
            childOrgMappingStub.returns({ eq: eqStub });
            whereStub.returns({ eq: eqStub });
            eqStub.withArgs('123e4567-e89b-12d3-a456-426614174000').returns({ where: whereStub });
            eqStub.withArgs('xyze4567-e89b-12d3-a456-426614174000').returns({ where: whereStub });
            eqStub.withArgs('75285616-3769-4b2e-b36b-898597b8146e').returns({ exec: execStub });
            execStub.resolves([{ id: '123e4567-e89b-12d3-a456-426614174000' }]);

            sinon.stub(Child.transaction, 'update').resolves();
            sinon.stub(ChildOrganizationMapping.transaction, 'update').resolves();
            sinon.stub(dynamoose, 'transaction').resolves();

            request(process.env.BASE_URL)
                .put('/child/')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    firstName: 'test',
                    lastName: 'test',
                    schoolId: '123e4567-e89b-12d3-a456-426614174000',
                    homeroomId: '456e4567-e89b-12d3-a456-426614174000',
                    zipCode: '12345',
                    associatedColor: '#2772ED'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    Organization.get.restore();
                    childOrgMappingStub.restore();
                    Child.transaction.update.restore();
                    ChildOrganizationMapping.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });

        it('As a user I should be able to edit details of my child and child should get my existing family membership', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'],
                membershipsPurchased: [{
                    endDate: MOMENT().add(5, 'minutes'),
                    organizationId: '123e4567-e89b-12d3-a456-426614174000', fundraiserSignupId: 'fundraiserSignupId'
                }]
            });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                school: '123e4567-e89b-12d3-a456-426614174000',
                homeRoom: '456e4567-e89b-12d3-a456-426614174000',
                associatedOrganizations: ['123e4567-e89b-12d3-a456-426614174000', 'xyze4567-e89b-12d3-a456-426614174000'],
                membershipsPurchased: [{
                    endDate: MOMENT().add(5, 'minutes'),
                    organizationId: '123e4567-e89b-12d3-a456-426614174000', fundraiserSignupId: 'fundraiserSignupId'
                }]
            });

            const orgModelStub = sinon.stub(Organization, 'get').resolves({
                name: 'test'
            });
            orgModelStub.withArgs({ id: '456e4567-e89b-12d3-a456-426614174000' }).resolves({
                id: '456e4567-e89b-12d3-a456-426614174000',
                category: 'Homeroom',
                parentOrganization: '123e4567-e89b-12d3-a456-426614174000',
                name: 'test'
            });

            const childOrgMappingStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const whereStub = sinon.stub();
            const execStub = sinon.stub();
            childOrgMappingStub.returns({ eq: eqStub });
            whereStub.returns({ eq: eqStub });
            eqStub.withArgs('123e4567-e89b-12d3-a456-426614174000').returns({ where: whereStub });
            eqStub.withArgs('xyze4567-e89b-12d3-a456-426614174000').returns({ where: whereStub });
            eqStub.withArgs('75285616-3769-4b2e-b36b-898597b8146e').returns({ exec: execStub });
            execStub.resolves([{ id: '123e4567-e89b-12d3-a456-426614174000' }]);

            sinon.stub(Child.transaction, 'update').resolves();
            sinon.stub(ChildOrganizationMapping.transaction, 'update').resolves();
            sinon.stub(dynamoose, 'transaction').resolves();


            request(process.env.BASE_URL)
                .put('/child/')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    firstName: 'test',
                    lastName: 'test',
                    dob: '12/12/2015',
                    schoolId: '123e4567-e89b-12d3-a456-426614174000',
                    homeroomId: '456e4567-e89b-12d3-a456-426614174000',
                    zipCode: '12345',
                    associatedColor: '#2772ED'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    Organization.get.restore();
                    childOrgMappingStub.restore();
                    Child.transaction.update.restore();
                    ChildOrganizationMapping.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });

        it('As a user I should be able to edit details and homeroom of my child if homeroomId is not passed', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                school: '456e4567-e89b-12d3-a456-426614174000',
                homeRoom: 'xyze4567-e89b-12d3-a456-426614174000',
                associatedOrganizations: ['123e4567-e89b-12d3-a456-426614174000', 'xyze4567-e89b-12d3-a456-426614174000'],
                followers: [{ name: 'test' }], followings: [{ name: 'test' }], connections: [{ name: 'test' }]
            });

            const orgModelStub = sinon.stub(Organization, 'get').resolves({ 'name': 'test' });
            orgModelStub.withArgs({ id: '456e4567-e89b-12d3-a456-426614174000' }).resolves({
                id: '456e4567-e89b-12d3-a456-426614174000',
                category: 'Homeroom',
                parentOrganization: '123e4567-e89b-12d3-a456-426614174000'
            });
            orgModelStub.withArgs({ id: '123e4567-e89b-12d3-a456-426614174000' }).resolves({
                id: '123e4567-e89b-12d3-a456-426614174000',
                category: 'School'
            });

            const childOrgMappingStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const whereStub = sinon.stub();
            const execStub = sinon.stub();
            childOrgMappingStub.returns({ eq: eqStub });
            whereStub.returns({ eq: eqStub });
            eqStub.withArgs('456e4567-e89b-12d3-a456-426614174000').returns({ where: whereStub });
            eqStub.withArgs('xyze4567-e89b-12d3-a456-426614174000').returns({ where: whereStub });
            eqStub.withArgs('123e4567-e89b-12d3-a456-426614174000').returns({ where: whereStub });
            eqStub.withArgs('75285616-3769-4b2e-b36b-898597b8146e').returns({ exec: execStub });
            execStub.resolves([{
                id: '123e4567-e89b-12d3-a456-426614174000',
                childOrganizationMappingId: '123e4567-e89b-12d3-a456-426614174000'
            }]);

            sinon.stub(Child.transaction, 'update').resolves();
            sinon.stub(ChildOrganizationMapping.transaction, 'update').resolves();
            sinon.stub(ChildOrganizationMapping.transaction, 'delete').resolves();
            sinon.stub(dynamoose, 'transaction').resolves();

            const OrgStub = sinon.stub(Organization, 'query');
            const orgEqStub = sinon.stub();
            const orgWhereStub = sinon.stub();
            const orgExecStub = sinon.stub();
            OrgStub.returns({ eq: orgEqStub });
            orgWhereStub.returns({ eq: orgEqStub });
            orgEqStub.withArgs('456e4567-e89b-12d3-a456-426614174000').returns({ where: orgWhereStub });
            orgEqStub.withArgs('123e4567-e89b-12d3-a456-426614174000').returns({ where: orgWhereStub });
            orgEqStub.withArgs('PTO').returns({ exec: orgExecStub });
            orgExecStub.resolves([{ id: '123e4567-e89b-12d3-a456-426614174000', save: () => {} }]);

            request(process.env.BASE_URL)
                .put('/child/')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    firstName: 'test',
                    lastName: 'test',
                    dob: '12/12/2015',
                    schoolId: '123e4567-e89b-12d3-a456-426614174000',
                    zipCode: '12345',
                    associatedColor: '#2772ED'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    Organization.get.restore();
                    Organization.query.restore();
                    childOrgMappingStub.restore();
                    Child.transaction.update.restore();
                    ChildOrganizationMapping.transaction.update.restore();
                    ChildOrganizationMapping.transaction.delete.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });

        it('As a user I should be able to edit details and homeroom of my child', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                school: '456e4567-e89b-12d3-a456-426614174000',
                homeRoom: 'xyze4567-e89b-12d3-a456-426614174000',
                associatedOrganizations: ['123e4567-e89b-12d3-a456-426614174000', 'xyze4567-e89b-12d3-a456-426614174000'],
                followers: [], followings: [], photoURL: 'https://test.com/test.jpg'
            });

            const orgModelStub = sinon.stub(Organization, 'get').resolves({ 'name': 'test' });
            orgModelStub.withArgs({ id: '456e4567-e89b-12d3-a456-426614174000' }).resolves({
                id: '456e4567-e89b-12d3-a456-426614174000',
                category: 'Homeroom',
                parentOrganization: '123e4567-e89b-12d3-a456-426614174000'
            });
            orgModelStub.withArgs({ id: '123e4567-e89b-12d3-a456-426614174000' }).resolves({
                id: '123e4567-e89b-12d3-a456-426614174000',
                category: 'School'
            });

            const childOrgMappingStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const whereStub = sinon.stub();
            const execStub = sinon.stub();
            childOrgMappingStub.returns({ eq: eqStub });
            whereStub.returns({ eq: eqStub });
            eqStub.withArgs('456e4567-e89b-12d3-a456-426614174000').returns({ where: whereStub });
            eqStub.withArgs('xyze4567-e89b-12d3-a456-426614174000').returns({ where: whereStub });
            eqStub.withArgs('123e4567-e89b-12d3-a456-426614174000').returns({ where: whereStub });
            eqStub.withArgs('75285616-3769-4b2e-b36b-898597b8146e').returns({ exec: execStub });
            execStub.resolves([{ id: '123e4567-e89b-12d3-a456-426614174000' }]);

            const OrgStub = sinon.stub(Organization, 'query');
            const orgEqStub = sinon.stub();
            const orgWhereStub = sinon.stub();
            const orgExecStub = sinon.stub();
            OrgStub.returns({ eq: orgEqStub });
            orgWhereStub.returns({ eq: orgEqStub });
            orgEqStub.withArgs('456e4567-e89b-12d3-a456-426614174000').returns({ where: orgWhereStub });
            orgEqStub.withArgs('123e4567-e89b-12d3-a456-426614174000').returns({ where: orgWhereStub });
            orgEqStub.withArgs('PTO').returns({ exec: orgExecStub });
            orgExecStub.resolves([{ id: '123e4567-e89b-12d3-a456-426614174000' }]);

            sinon.stub(Child.transaction, 'update').resolves();
            sinon.stub(ChildOrganizationMapping.transaction, 'update').resolves();
            sinon.stub(dynamoose, 'transaction').resolves();


            request(process.env.BASE_URL)
                .put('/child/')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    childId: '75285616-3769-4b2e-b36b-898597b8146e',
                    firstName: 'test',
                    lastName: 'test',
                    dob: '12/12/2015',
                    schoolId: '123e4567-e89b-12d3-a456-426614174000',
                    homeroomId: '456e4567-e89b-12d3-a456-426614174000',
                    zipCode: '12345',
                    associatedColor: '#2772ED'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    Organization.get.restore();
                    Organization.query.restore();
                    childOrgMappingStub.restore();
                    Child.transaction.update.restore();
                    ChildOrganizationMapping.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });

        it('As a user I should be able to edit details of my child and upload image', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            sinon.stub(Child, 'get').resolves({
                id: '75285616-3769-4b2e-b36b-898597b8146e',
                school: '123e4567-e89b-12d3-a456-426614174000',
                homeRoom: 'abce4567-e89b-12d3-a456-426614174000',
                associatedOrganizations: ['123e4567-e89b-12d3-a456-426614174000', 'xyze4567-e89b-12d3-a456-426614174000']
            });

            const orgModelStub = sinon.stub(Organization, 'get').resolves({ 'name': 'test' });
            orgModelStub.withArgs({ id: '456e4567-e89b-12d3-a456-426614174000' }).resolves({
                id: '456e4567-e89b-12d3-a456-426614174000',
                category: 'Homeroom',
                parentOrganization: '123e4567-e89b-12d3-a456-426614174000'
            });

            const childOrgMappingStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const whereStub = sinon.stub();
            const execStub = sinon.stub();
            childOrgMappingStub.returns({ eq: eqStub });
            whereStub.returns({ eq: eqStub });
            eqStub.withArgs('abce4567-e89b-12d3-a456-426614174000').returns({ where: whereStub });
            eqStub.withArgs('75285616-3769-4b2e-b36b-898597b8146e').returns({ exec: execStub });
            execStub.resolves([{ id: '123e4567-e89b-12d3-a456-426614174000' }]);

            sinon.stub(Child.transaction, 'update').resolves();
            sinon.stub(ChildOrganizationMapping.transaction, 'update').resolves();
            sinon.stub(dynamoose, 'transaction').resolves();

            request(process.env.BASE_URL)
                .put('/child/')
                .set({ Authorization: requestPayloadUser.token })
                .field('childId', '75285616-3769-4b2e-b36b-898597b8146e')
                .field('firstName', 'test')
                .field('lastName', 'test')
                .field('dob', '12/12/2015')
                .field('schoolId', '123e4567-e89b-12d3-a456-426614174000')
                .field('homeroomId', '456e4567-e89b-12d3-a456-426614174000')
                .field('zipCode', '12345')
                .field('associatedColor', '#2772ED')
                .attach('photo', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    Organization.get.restore();
                    childOrgMappingStub.restore();
                    Child.transaction.update.restore();
                    ChildOrganizationMapping.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });

        it('As a user I should get error message if I upload image of wrong mimetype', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['75285616-3769-4b2e-b36b-898597b8146e'] });

            request(process.env.BASE_URL)
                .put('/child/')
                .set({ Authorization: requestPayloadUser.token })
                .field('childId', '75285616-3769-4b2e-b36b-898597b8146e')
                .field('firstName', 'test')
                .field('lastName', 'test')
                .field('dob', '12/12/2015')
                .field('schoolId', '123e4567-e89b-12d3-a456-426614174000')
                .field('homeroomId', '456e4567-e89b-12d3-a456-426614174000')
                .field('zipCode', '12345')
                .field('associatedColor', '#2772ED')
                .attach('photo', 'test/mock-data/TEST.pdf')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Remove follower', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.removeFollower.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({ status: 'active', isVerified: 1, role: 1 });
                request(process.env.BASE_URL)
                    .put('/child/remove-follower')
                    .set({
                        Authorization: requestPayloadUser.token
                    })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should get error if child is not associated with me', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: [] });
            request(process.env.BASE_URL)
                .put('/child/remove-follower')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    followingChildId: '75285616-3769-4b2e-b36b-898597b8146e',
                    followerChildId: '88285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error if follow request is in requested state and I try to remove follower', (done) => {
            const reqBody = {
                followerChildId: '75285616-3769-4b2e-b36b-898597b8146e',
                followingChildId: '88285616-3769-4b2e-b36b-898597b8146e'
            };

            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: [reqBody.followingChildId] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs(reqBody.followingChildId).resolves({
                id: reqBody.followingChildId, followings: [{
                    childId: reqBody.followerChildId, status: 'requested'
                }]
            });
            childGetStub.withArgs(reqBody.followerChildId).resolves({
                id: reqBody.followerChildId, followers: [{
                    childId: reqBody.followingChildId, status: 'requested'
                }]
            });

            request(process.env.BASE_URL)
                .put('/child/remove-follower')
                .set({ Authorization: requestPayloadUser.token })
                .send(reqBody)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should be able to remove follower', (done) => {
            const reqBody = {
                followerChildId: '75285616-3769-4b2e-b36b-898597b8146e',
                followingChildId: '88285616-3769-4b2e-b36b-898597b8146e'
            };

            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: [reqBody.followingChildId] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs(reqBody.followingChildId).resolves({
                id: reqBody.followingChildId, followers: [{
                    childId: reqBody.followerChildId, status: 'accepted'
                }]
            });
            childGetStub.withArgs(reqBody.followerChildId).resolves({
                id: reqBody.followerChildId, followings: [{
                    childId: reqBody.followingChildId, status: 'accepted'
                }]
            });

            sinon.stub(Child.transaction, 'update').resolves();

            sinon.stub(dynamoose, 'transaction').resolves();

            request(process.env.BASE_URL)
                .put('/child/remove-follower')
                .set({ Authorization: requestPayloadUser.token })
                .send(reqBody)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childGetStub.restore();
                    Child.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Remove Connection', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.removeConnection.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({ status: 'active', isVerified: 1, role: 1 });
                request(process.env.BASE_URL)
                    .put('/child/remove-connection')
                    .set({
                        Authorization: requestPayloadUser.token
                    })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should get error if child is not associated with me', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: [] });
            request(process.env.BASE_URL)
                .put('/child/remove-connection')
                .set({ Authorization: requestPayloadUser.token })
                .send({
                    requesterChildId: '75285616-3769-4b2e-b36b-898597b8146e',
                    requestedChildId: '88285616-3769-4b2e-b36b-898597b8146e'
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error if connection request is in requested state and I try to remove connection', (done) => {
            const reqBody = {
                requesterChildId: '75285616-3769-4b2e-b36b-898597b8146e',
                requestedChildId: '88285616-3769-4b2e-b36b-898597b8146e'
            };

            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: [reqBody.requestedChildId] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs(reqBody.requesterChildId).resolves({
                id: reqBody.requesterChildId, connections: [{
                    childId: reqBody.requestedChildId, status: 'requestedTo'
                }]
            });
            childGetStub.withArgs(reqBody.requestedChildId).resolves({
                id: reqBody.requestedChildId, connections: [{
                    childId: reqBody.requesterChildId, status: 'requestedBy'
                }]
            });

            request(process.env.BASE_URL)
                .put('/child/remove-connection')
                .set({ Authorization: requestPayloadUser.token })
                .send(reqBody)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should get error if connection request not found', (done) => {
            const reqBody = {
                requesterChildId: '75285616-3769-4b2e-b36b-898597b8146e',
                requestedChildId: '88285616-3769-4b2e-b36b-898597b8146e'
            };

            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: [reqBody.requestedChildId] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs(reqBody.requesterChildId).resolves({
                id: reqBody.requesterChildId, connections: []
            });
            childGetStub.withArgs(reqBody.requestedChildId).resolves({
                id: reqBody.requestedChildId, connections: [{
                    childId: reqBody.requesterChildId, status: 'requestedBy'
                }]
            });

            request(process.env.BASE_URL)
                .put('/child/remove-connection')
                .set({ Authorization: requestPayloadUser.token })
                .send(reqBody)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should be able to remove follower', (done) => {
            const reqBody = {
                requesterChildId: '75285616-3769-4b2e-b36b-898597b8146e',
                requestedChildId: '88285616-3769-4b2e-b36b-898597b8146e'
            };

            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: [reqBody.requestedChildId] });

            const childGetStub = sinon.stub(Child, 'get');

            childGetStub.withArgs(reqBody.requestedChildId).resolves({
                id: reqBody.requestedChildId, connections: [{
                    childId: reqBody.requesterChildId, status: 'connected'
                }]
            });
            childGetStub.withArgs(reqBody.requesterChildId).resolves({
                id: reqBody.requesterChildId, connections: [{
                    childId: reqBody.requestedChildId, status: 'connected'
                }]
            });

            sinon.stub(Child.transaction, 'update').resolves();

            sinon.stub(dynamoose, 'transaction').resolves();

            request(process.env.BASE_URL)
                .put('/child/remove-connection')
                .set({ Authorization: requestPayloadUser.token })
                .send(reqBody)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childGetStub.restore();
                    Child.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Get associated organizations', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get error if child is not associated with me', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['child-id1'] });
            request(process.env.BASE_URL)
                .get('/child/organizations?childId=child-id2')
                .set({ Authorization:
                    requestPayloadUser.token
                })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get all associated organizations details of the passed child', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['child-id1'] });

            sinon.stub(Child, 'get').resolves({ id: 'child-id1', associatedOrganizations: ['org-id1', 'org-id2'] });
            sinon.stub(Organization, 'batchGet').resolves([{ id: 'org-id1', name: 'test' }]);

            request(process.env.BASE_URL)
                .get('/child/organizations?childId=child-id1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    Organization.batchGet.restore();
                    done();
                });
        });

        it('As a user I should get all associated organizations details with homeroom of the passed child', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['child-id1'] });

            sinon.stub(Child, 'get').resolves({ id: 'child-id1',
                associatedOrganizations: ['org-id1', 'org-id2'], school: 'org-id1', homeRoom: 'org-id2' });
            sinon.stub(Organization, 'batchGet').resolves([{ id: 'org-id1', name: 'test' },
                { id: 'org-id2', name: 'test', category: 'Homeroom', parentOrganization: 'org-id1' }]);

            request(process.env.BASE_URL)
                .get('/child/organizations?childId=child-id1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    Organization.batchGet.restore();
                    done();
                });
        });

        it('As a user I should get all associated organizations details of the passed child with photoURL', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['child-id1'] });

            sinon.stub(Child, 'get').resolves({
                id: 'child-id1', associatedOrganizations: ['org-id1', 'org-id2'], photoURL: 'https://test.com/test.jpg'
            });
            sinon.stub(Organization, 'batchGet').resolves([{ id: 'org-id1', name: 'test' }]);

            request(process.env.BASE_URL)
                .get('/child/organizations?childId=child-id1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    Organization.batchGet.restore();
                    done();
                });
        });

        it('As a user I should get empty list if I don\'t have any child', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: [] });
            request(process.env.BASE_URL)
                .get('/child/organizations')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

        it('As a user I should get all associated organizations details of all my children', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['child-id1', 'child-id2'] });

            sinon.stub(Child, 'batchGet').resolves([
                { id: 'child-id1', associatedOrganizations: ['org-id1', 'org-id2'], photoURL: 'https://test.com/test.jpg' },
                { id: 'child-id2', associatedOrganizations: ['org-id1', 'org-id2'] }
            ]);
            sinon.stub(Organization, 'batchGet').resolves([{ id: 'org-id1', name: 'test' }, { id: 'org-id2', name: 'test' }]);

            request(process.env.BASE_URL)
                .get('/child/organizations')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.batchGet.restore();
                    Organization.batchGet.restore();
                    done();
                });
        });

        it('As a user I should get all associated organizations details with homeroom of all my children', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['child-id1', 'child-id2'] });

            sinon.stub(Child, 'batchGet').resolves([
                { id: 'child-id1', associatedOrganizations: ['org-id1', 'org-id2'],
                    photoURL: 'https://test.com/test.jpg', school: 'org-id1', homeRoom: 'org-id2' },
                { id: 'child-id2', associatedOrganizations: ['org-id1', 'org-id2'] }
            ]);
            sinon.stub(Organization, 'batchGet').resolves([{ id: 'org-id1', name: 'test' },
                { id: 'org-id2', name: 'test', category: 'Homeroom', parentOrganization: 'org-id1' }]);

            request(process.env.BASE_URL)
                .get('/child/organizations')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.batchGet.restore();
                    Organization.batchGet.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Generate presigned url', () => {
    let getStub;
    before(async () => {
        getStub = sinon.stub(User, 'get');
    });

    after(async () => {
        sinon.restore();
    });

    it('should handle error if signed url is not generated', (done) => {
        getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['child-id1'] });

        sinon.stub(UploadService, 'getPreSignedUrlForUpload').rejects(new Error('Failed to generate signed url'));

        request(process.env.BASE_URL)
            .get('/child/generate-presigned-url')
            .set({ Authorization: requestPayloadUser.token })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                UploadService.getPreSignedUrlForUpload.restore();
                done();
            });
    });

    it('As a user I should get presigned url for child', (done) => {
        getStub.resolves({ status: 'active', isVerified: 1, role: 1, children: ['child-id1'] });

        request(process.env.BASE_URL)
            .get('/child/generate-presigned-url?childId=child-id1')
            .set({ Authorization: requestPayloadUser.token })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
                done();
            });
    });
});
