const Stripe = require('../../util/Stripe');
const validation = require('../../util/validation');
const Organization = require('../../models/organization.model');
const lodash = require('lodash');
const Utils = require('../../util/utilFunctions');
const OrganizationMembers = require('../../models/organizationMember.model');

const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

/**
 * Class represents services for stripe
 */
class PaymentService {
    /**
     * @desc This function is being used to create Organization account in stripe connect
     * <AUTHOR>
     * @since 09/11/2023
     * @param {Object} reqObj org object
     */
    static async createAccount (reqObj, reqbody) {
        const account = await Stripe.createAccount(stripe);
        const accountLink = await Stripe.createOnboardingLink(stripe, account);
        const org = await Organization.get(reqObj.id);
        org.paymentDetails.stripeConnectAccountId = account.id;
        await org.save();
        reqbody.url = accountLink.url;
        await Utils.sendOrgToMail(reqbody,
            'stripeLoginUrl.html', `Stripe Onboarding URL for ${CONSTANTS.APP_NAME}`);
        return { url: accountLink.url };
    }

    /**
     * @desc This function is being used to resend mail to Organization for stripe connect onboarding
     * @desc if stripeConnectAccountId is present and stripeOnboardingStatus is not active then resend mail
     * <AUTHOR>
     * @since 09/11/2023
     * @param {Object} req req
     * @param {String} req.id organization id
     */
    static async resendAccountLink (req, locale) {
        const { orgId } = req.query;
        const Validator = new validation(locale);
        Validator.field(orgId, 'Organization Id');
        let stripeAccountId;
        const organization = await Organization.get({ id: orgId });
        if (!lodash.get(organization, 'paymentDetails.stripeConnectAccountId')) {
            const account = await Stripe.createAccount(stripe);
            stripeAccountId = account.id;
            organization.paymentDetails.stripeConnectAccountId = account.id;
            await organization.save();
        } else {
            const { paymentDetails: { stripeConnectAccountId } } = organization;
            stripeAccountId = stripeConnectAccountId;
        }
        const accountLink = await Stripe.createOnboardingLink(stripe, { id: stripeAccountId });

        const orgManager = await OrganizationMembers.get({ organizationId: orgId });
        const associatedOrgAdmin = orgManager.users;
        await Promise.all(associatedOrgAdmin.map(async (orgAdmin) => {
            if (orgAdmin.associatedOrgRole === CONSTANTS.ROLE.ORG_SUPER_ADMIN) {
                await Utils.sendOrgToMail({ url: accountLink.url, email: orgAdmin.email },
                    'stripeLoginUrl.html', `Stripe Onboarding URL for ${CONSTANTS.APP_NAME}`);
            }
        }));
        return { url: accountLink.url };
    }

    /**
     * @desc This function is being used handleWebhook
     * <AUTHOR>
     * @since 09/11/2023
     * @param {Object} user user
     */
    static async stripeWebhookConnect (req, res) {
        const signature = req.headers['stripe-signature'];
        let event;
        try {
            event = await Stripe.constructWebhookEvent(req.rawBody, signature, stripe);
        } catch (err) {
            CONSOLE_LOGGER.error('Webhook Error:', err);
            throw {
                message: `Webhook Error: ${err.message}`,
                statusCode: 400
            };
        }

        if (event.type === 'account.updated') {
            // account updated
            if (lodash.get(event.data.object, 'requirements.disabled_reason') === 'platform_paused') {
                const organization = await Organization.scan('paymentDetails.stripeConnectAccountId').eq(event.data.object.id).exec();
                const org = await Organization.get(organization.toJSON()[0].id);
                org.paymentDetails.stripeOnboardingStatus = 'payoutPaused';
                org.allowedPaymentType.stripe = false;
                await org.save();
            } else if (lodash.get(event.data.object, 'requirements.disabled_reason') === 'rejected.fraud') {
                const organization = await Organization.scan('paymentDetails.stripeConnectAccountId').eq(event.data.object.id).exec();
                const org = await Organization.get(organization.toJSON()[0].id);
                org.paymentDetails.stripeOnboardingStatus = 'rejectedFraud';
                org.allowedPaymentType.stripe = false;
                await org.save();
            } else if (lodash.get(event.data.object, 'requirements.disabled_reason') === 'rejected.other') {
                const organization = await Organization.scan('paymentDetails.stripeConnectAccountId').eq(event.data.object.id).exec();
                const org = await Organization.get(organization.toJSON()[0].id);
                org.paymentDetails.stripeOnboardingStatus = 'rejectedOther';
                org.allowedPaymentType.stripe = false;
                await org.save();
            } else if (lodash.get(event.data.object, 'capabilities.transfers') === 'active') {
                const organization = await Organization.scan('paymentDetails.stripeConnectAccountId').eq(event.data.object.id).exec();
                const org = await Organization.get(organization.toJSON()[0].id);
                org.paymentDetails.stripeOnboardingStatus = 'active';
                await org.save();
            } else {
                CONSOLE_LOGGER.info('Account updated: ', event.data.object, 'event type: account updated');
            }
        }
        CONSOLE_LOGGER.info(`Unhandled event type: ${event.type}`);
        res.send();
    }
}

module.exports = PaymentService;
