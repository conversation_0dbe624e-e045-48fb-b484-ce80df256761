const dynamoose = require('dynamoose');
const Child = require('./child.model');
const { v4: uuidv4 } = require('uuid');

const pendingPartnerInviteSchema = new dynamoose.Schema({
    id: {
        hashKey: true,
        type: String,
        default: () => uuidv4()
    },
    invitedPartner: {
        type: String,
        required: true,
        index: {
            global: true,
            name: 'invitedPartner-index',
            project: true
        }
    },
    inviterPartnerId: {
        type: String,
        required: true,
        index: {
            global: true,
            name: 'inviterPartnerId-index',
            project: true
        }
    },
    inviterPartnerEmail: {
        type: String,
        required: true
    },
    children: {
        type: Array,
        schema: [{
            type: Object,
            schema: {
                childId: { type: Child, required: true },
                invitedAt: { type: Date, default: Date.now }
            }
        }],
        default: []
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('PendingPartnerInvite', pendingPartnerInviteSchema);
