
const ParentService = require('./parentService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for parent user routes.
 */
class ParentController {
    /**
     * @desc This function is being used to get list of schools
     * <AUTHOR>
     * @since 19/10/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async getSchoolList (req, res) {
        try {
            const data = await ParentService.getSchoolList(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in getting school list: ' + error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to add child by parent
     * <AUTHOR>
     * @since 19/10/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {String} req.body.firstName firstName
     * @param {String} req.body.lastName lastName
     * @param {String} req.body.dob Date of Birth
     * @param {String} req.body.gender Gender
     * @param {String} req.body.schoolId schoolId
     * @param {String} req.body.zipCode zipCode
     * @param {String} req.body.associatedColor associatedColor
     * @param {function} res Response
     */
    static async addChild (req, res) {
        try {
            const data = await ParentService.addChild(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.ADD_CHILD_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in adding child: ' + error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to invite partner
     * <AUTHOR>
     * @since 28/12/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async invitePartner (req, res) {
        try {
            const data = await ParentService.invitePartner(req, res.locals.user, res.__);
            const message = data?.isReinvite ? MESSAGES.PARENT_REINVITE_SUCCESS : MESSAGES.PARTNER_INVITE_SUCCESS;
            Utils.sendResponse(null, undefined, res, message);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in inviting partner: ' + error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to get invite partner list
     * <AUTHOR>
     * @since 18/01/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async getInvitePartnerList (req, res) {
        try {
            const data = await ParentService.getInvitePartnerList(res.locals.user);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get invite partner list: ' + error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to update status of invite partner
     * <AUTHOR>
     * @since 18/01/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async updateInvitePartner (req, res) {
        try {
            const { responseList, child, message } = await ParentService.updateInvitePartner(req, res.locals.user, res.__);
            Utils.sendResponse(null, { invites: responseList, status: req.query.status, child }, res, message);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in updating status of invite partner: ' + error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    static async getSendInvites (req, res) {
        try {
            const data = await ParentService.getSendInvites(res.locals.user);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in sending invites: ' + error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    static async saveSendInvites (req, res) {
        try {
            const data = await ParentService.saveSendInvites(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.MEMBERS_UPDATED_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in sending invites: ' + error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    static async removeMember (req, res) {
        try {
            const data = await ParentService.removeMember(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.REMOVE_MEMBER_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in removing member: ' + error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = ParentController;
