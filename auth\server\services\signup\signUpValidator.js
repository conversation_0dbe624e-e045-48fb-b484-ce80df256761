const GeneralError = require('../../util/GeneralError');
const validation = require('../../util/validation');
/**
 * Class represents validations for signup.
 */
class SignUpValidator extends validation {
    constructor (body, locale) {
        super(locale);
        this.body = body;
    }

    /**
     * @desc This function is being used to validate request for user signUp
     * <AUTHOR>
     * @since 27/03/2021
     */
    validate () {
        super.email(this.body.email);
        super.password(this.body.password, 'Password');
        super.name(this.body.firstName, 'First Name');
        super.name(this.body.lastName, 'Last Name');
    }

    /**
     * @desc This function is being used to validate OTP request
     * <AUTHOR>
     * @since 27/03/2021
     */
    otpValidate () {
        super.email(this.body.email);
        super.otp(this.body.otp);
    }

    /**
     * @desc This function is being used to validate OTP type request
     * <AUTHOR>
     * @since 19/10/2023
     */
    otpTypeValidate () {
        const { otpType } = this.body;
        super.field(this.body.otpType, 'OTP type');
        if (!CONSTANTS.OTP_TYPE.includes(otpType)) {
            throw new GeneralError(this.__('FIELD_NOT_VALID', 'OTP type'), 400);
        }
    }
}

module.exports = SignUpValidator;
