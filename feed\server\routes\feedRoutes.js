/**
 * This file contains routes used for comment.
 * Created by Growexx on 29/11/2023.
 * @name feedRoutes
 */
const router = require('express').Router();

const FeedController = require('../services/feed/feedController');
const CommentController = require('../services/comment/commentController');

const AuthMiddleware = require('../middleware/auth');
const AclMiddleware = require('../middleware/acl');
const HmacMiddleware = require('../middleware/hmac');

router.get('/list', HmacMiddleware, AuthMiddleware, AclMiddleware, FeedController.getFeedList);
router.get('/registered', HmacMiddleware, AuthMiddleware, AclMiddleware, FeedController.getRegisteredFeeds);
// Redis routes
router.get('/events', HmacMiddleware, AuthMiddleware, FeedController.getFeedListRedis);
router.get('/registered-events', HmacMiddleware, AuthMiddleware, FeedController.getRegisteredFeedsRedis);

router.get('/calendar-events', HmacMiddleware, AuthMiddleware, FeedController.getCalendarFeedsFromRedis);

router.get('/details', HmacMiddleware, AuthMiddleware, FeedController.getFeedDetails);
router.post('/comment', HmacMiddleware, AuthMiddleware, AclMiddleware, CommentController.addComment);
router.get('/comment/list', HmacMiddleware, AuthMiddleware, AclMiddleware, CommentController.getCommentList);
router.delete('/comment', HmacMiddleware, AuthMiddleware, AclMiddleware, CommentController.deleteComment);

router.get('/search', HmacMiddleware, AuthMiddleware, FeedController.searchFeed);

module.exports = router;
