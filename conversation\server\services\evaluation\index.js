require('dotenv').config({ path: './local.env' });
const { LangsmithEvaluatorService } = require('./rag-eval.service');

global.CONSOLE_LOGGER = require('../../util/logger');
global.CONSTANTS = require('../../util/constants');
global.MESSAGES = require('../../locales/en.json');
global.MOMENT = require('moment');
global._ = require('lodash');

async function bootstrap () {
    const evaluator = new LangsmithEvaluatorService();
    await evaluator.runEval();
}

bootstrap().catch((err) => {
    console.error('❌ Evaluation failed:', err);
});
