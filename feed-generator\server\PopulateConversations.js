const GroupsModel = require('./models/groups.model');
const GroupMembersModel = require('./models/groupMembers.model');
const ConversationService = require('./ConversationService');
const UserModel = require('./models/user.model');
const UserHelperService = require('./UserHelperService');
const MessageModel = require('./models/message.model');
const PersonalConversationModel = require('./models/personalConversation.model');
const PersonalMessageModel = require('./models/personalMessage.model');
const RedisUtil = require('./redisUtil');
const Utils = require('./Utils');
const CONSOLE_LOGGER = require('./logger');

class PopulateConversations {
    /**
     * @description Populates the conversations
     * <AUTHOR>
     * @param {Redis} redis - The redis client
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async populateConversations (redis, versionPrefix) {
        try {
            const groups = await GroupsModel.scan().exec();
            const groupMembers = await GroupMembersModel.scan().exec();
            const users = await UserModel.scan().exec();
            const personalConversations = await PersonalConversationModel.scan().exec();

            await this.populateUsers(redis, users, versionPrefix);
            await this.populateGroups(redis, groups, versionPrefix);
            await this.populateGroupMembers(redis, groupMembers, versionPrefix);
            await this.populatePersonalConversations(redis, personalConversations, versionPrefix);
        } catch (error) {
            CONSOLE_LOGGER.error('Error populating conversations', error);
        }
    }

    /**
     * @description Populates the users
     * <AUTHOR>
     * @param {Redis} redis - The redis client
     * @param {Array} users - The users
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async populateUsers (redis, users, versionPrefix) {
        for (const user of users) {
            CONSOLE_LOGGER.info('Adding user', user.id);
            await UserHelperService.addOrUpdateUserDetails(redis, user, versionPrefix);
        }
    }

    /**
     * @description Populates the groups
     * <AUTHOR>
     * @param {Redis} redis - The redis client
     * @param {Array} groups - The groups
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async populateGroups (redis, groups, versionPrefix) {
        for (const group of groups) {
            const { groupId } = group;
            CONSOLE_LOGGER.info('Adding group', groupId);
            await ConversationService.addOrUpdateGroupDetailsToHashSet(redis, group, versionPrefix);

            await this.populateGroupMessages(redis, groupId, versionPrefix);
        }
    }

    /**
     * @description Populates the group messages
     * <AUTHOR>
     * @param {Redis} redis - The redis client
     * @param {String} groupId - The group id
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async populateGroupMessages (redis, groupId, versionPrefix) {
        const conversationMessagesKey = Utils.getConversationMessagesKey({ versionPrefix, conversationId: groupId });
        await RedisUtil.deleteKeyIfExists(redis, conversationMessagesKey);

        const messages = await MessageModel.query('groupId').eq(groupId)
            .using('groupId-createdAt-index')
            .sort('descending')
            .limit(11).exec();

        for (const message of messages) {
            CONSOLE_LOGGER.info('Adding message', message.id);
            await ConversationService.addGroupMessageToCache({ redis, versionPrefix, messageDetails: message, isPopulatingCache: true });
        }
    }

    /**
     * @description Populates the group members
     * <AUTHOR>
     * @param {Redis} redis - The redis client
     * @param {Array} groupMembers - The group members
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async populateGroupMembers (redis, groupMembers, versionPrefix) {
        for (const member of groupMembers) {
            CONSOLE_LOGGER.info('Adding group member', member.id);
            await ConversationService.addOrUpdateGroupMemberToHashSet(redis, member, versionPrefix);
        }
    }

    /**
     * @description Populates the personal conversations
     * <AUTHOR>
     * @param {Redis} redis - The redis client
     * @param {Array} personalConversations - The personal conversations
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async populatePersonalConversations (redis, personalConversations, versionPrefix) {
        for (const personalConversation of personalConversations) {
            const { conversationId } = personalConversation;
            CONSOLE_LOGGER.info('Adding personal conversation', conversationId);
            await ConversationService.addOrUpdatePersonalConversationDetailsToHashSet(
                redis,
                personalConversation,
                versionPrefix
            );

            await this.populatePersonalMessages(redis, conversationId, versionPrefix);
        }
    }

    /**
     * @description Populates the personal messages
     * <AUTHOR>
     * @param {Redis} redis - The redis client
     * @param {String} conversationId - The conversation id
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async populatePersonalMessages (redis, conversationId, versionPrefix) {
        const conversationMessagesKey =
            Utils.getConversationMessagesKey({ versionPrefix, conversationId });
        await RedisUtil.deleteKeyIfExists(redis, conversationMessagesKey);

        const personalMessages = await PersonalMessageModel.query('conversationId').eq(conversationId)
            .using('conversationId-createdAt-index')
            .sort('descending')
            .limit(11).exec();

        for (const message of personalMessages) {
            CONSOLE_LOGGER.info('Adding personal message', message.messageId);
            await ConversationService.addPersonalMessageToCache({
                redis,
                versionPrefix,
                personalMessageDetails: message,
                isPopulatingCache: true
            });
        }
    }
}
module.exports = PopulateConversations;
