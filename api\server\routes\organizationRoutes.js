/**
 * This file contains routes used for organization.
 * Created by Growexx on 07/11/2023.
 * @name OrganizationRoutes
 */
const router = require('express').Router();

const OrganizationController = require('../services/organization/organizationController');
const PaymentController = require('../services/payment/paymentController');

const AuthMiddleware = require('../middleware/auth');
const AclMiddleWare = require('../middleware/acl');
const UploadMiddleware = require('../middleware/upload');

router.post(
    '/',
    AuthMiddleware,
    UploadMiddleware.single('logo'),
    AclMiddleWare,
    OrganizationController.addOrganization
);
router.post('/resend-onboarding', AuthMiddleware, AclMiddleWare, PaymentController.resendAccountLink);
router.put(
    '/',
    AuthMiddleware,
    UploadMiddleware.single('logo'),
    AclMiddleWare,
    OrganizationController.updateOrganizationDetails
);
router.put('/status', AuthMiddleware, AclMiddleWare, OrganizationController.updateOrganizationStatus);
router.delete('/', AuthMiddleware, AclMiddleWare, OrganizationController.deleteOrganization);
router.get('/list', AuthMiddleware, AclMiddleWare, OrganizationController.getOrganizationList);
router.get('/', AuthMiddleware, AclMiddleWare, OrganizationController.getOrganizationDetails);
router.get('/users', AuthMiddleware, AclMiddleWare, OrganizationController.getOrganizationUsers);
router.get('/search', AuthMiddleware, OrganizationController.getOrganizationSearch);
router.post('/user', AuthMiddleware, AclMiddleWare, OrganizationController.addOrganizationUser);
router.put('/user', AuthMiddleware, AclMiddleWare, OrganizationController.updateOrganizationUser);
router.delete('/user', AuthMiddleware, AclMiddleWare, OrganizationController.deleteOrganizationUser);
router.post('/add-children', AuthMiddleware, OrganizationController.addChildrenToOrganization);
router.get('/associatedOrganizations', AuthMiddleware, AclMiddleWare, OrganizationController.getAssociatedOrganizations);
router.post('/associatedOrganizations', AuthMiddleware, AclMiddleWare, OrganizationController.addAssociatedOrganizations);
router.get('/generate-presigned-url', AuthMiddleware, AclMiddleWare, OrganizationController.generatePresignedUrl);

module.exports = router;
