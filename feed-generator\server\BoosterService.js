const Fundraiser = require('./models/fundraiser.model');
const Child = require('./models/child.model');
const CONSOLE_LOGGER = require('./logger');
const CONSTANTS = require('./constants');

/**
 * Service class for managing Booster fundraiser-related operations
 */
class BoosterService {
    /**
     * Updates donation amounts and leaderboard statistics for a homeroom
     * @param {Object} donation - The donation object containing amount and related details
     * @returns {Promise<Object>} Updated booster object
     */
    static async updateDonationsAmountRaisedAndLeaderboard (donation) {
        try {
            // Get current homeroom stats and booster details
            const { homeroomStats, booster, childLevelDonations } = await this.getHomeroomAndChildLevelStats(donation.fundraiserBoosterId);
            const { donations: homeroomDonations = [] } = homeroomStats;

            booster.raisedDonationsAmountForOrg = (booster.raisedDonationsAmountForOrg || 0) + donation.amount;

            // Return early if donation is not associated with a specific child
            if (!await this.isChildSpecificDonation(donation)) {
                CONSOLE_LOGGER.info('is not child specific donation');
                return await booster.save();
            }

            const { homeRoom: homeroomId } = await Child.get({ id: donation.childId }, {
                attributes: ['homeRoom']
            });

            if (!homeroomId) {
                CONSOLE_LOGGER.info('homeroomId not found for child', donation.childId);
                return await booster.save();
            }

            // Update homeroom stats
            const existingDonation = homeroomDonations?.find(
                donation => donation.homeRoomId === homeroomId
            );

            if (existingDonation) {
                existingDonation.donationsAmount += donation.amount;
            } else {
                CONSOLE_LOGGER.info('new donation for homeroom', homeroomId);
                homeroomDonations.push({
                    homeRoomId: homeroomId,
                    donationsAmount: donation.amount
                });
            }

            // Update child level stats
            const childLevelDonation = childLevelDonations?.find(
                existingDonation => existingDonation.childId === donation.childId
            );

            if (childLevelDonation) {
                childLevelDonation.donationsAmount += donation.amount;
            } else {
                CONSOLE_LOGGER.info('new donation for child', donation.childId);
                childLevelDonations.push({
                    childId: donation.childId,
                    donationsAmount: donation.amount
                });
            }

            // Update booster with new homeroom stats
            booster.homeroomStats = {
                ...booster.homeroomStats,
                donations: homeroomDonations
            };

            // Update booster with new child level stats
            booster.childLevelStats = {
                ...booster.childLevelStats,
                donations: childLevelDonations
            };

            CONSOLE_LOGGER.info('Booster updated successfully!', booster.id);
            return await booster.save();
        } catch (error) {
            CONSOLE_LOGGER.error('Error updating donation amounts:', error);
            throw error;
        }
    }

    /**
     * Updates the signups leaderboard for a homeroom
     * @param {Object} fundraiserSignup - The signup object containing registration details
     * @returns {Promise<Object>} Updated booster object
     */
    static async updateSignupsLeaderboard (fundraiserSignup) {
        try {
            const { homeroomStats, booster } = await this.getHomeroomAndChildLevelStats(fundraiserSignup.eventId);

            const { homeRoom: homeroomId } = await Child.get(
                { id: fundraiserSignup.childId },
                { attributes: ['homeRoom'] }
            );

            if (!homeroomId) {
                CONSOLE_LOGGER.info('homeroomId not found for child', fundraiserSignup.childId);
                return booster;
            }

            const { registrations = [] } = homeroomStats;
            const existingRegistration = registrations?.find(
                reg => reg.homeRoomId === homeroomId
            );

            if (existingRegistration) {
                existingRegistration.registrationsCount += 1;
            } else {
                CONSOLE_LOGGER.info('new registration for homeroom', homeroomId);
                registrations.push({
                    homeRoomId: homeroomId,
                    registrationsCount: 1
                });
            }

            // Update booster with new registration stats
            booster.homeroomStats = {
                ...booster.homeroomStats,
                registrations: registrations
            };
            return await booster.save();
        } catch (error) {
            CONSOLE_LOGGER.error('Error updating signup leaderboard:', error);
            throw error;
        }
    }

    /**
     * Retrieves homeroom statistics for a fundraiser
     * @param {string} fundraiserId - The ID of the fundraiser
     * @returns {Promise<Object>} Object containing homeroom stats and booster details
     */
    static async getHomeroomAndChildLevelStats (fundraiserId) {
        const booster = await Fundraiser.get({ id: fundraiserId });
        if (!booster) {
            throw new Error('Booster not found');
        }
        // Initialize empty stats if none exist
        const homeroomStats = booster.homeroomStats ?? {
            donations: [],
            registrations: []
        };
        const childLevelDonations = booster.childLevelStats?.donations ?? [];
        return { homeroomStats, booster, childLevelDonations };
    }

    /**
     * Checks if a fundraiser is of type Booster
     * @param {string} fundraiserId - The ID of the fundraiser to check
     * @returns {Promise<boolean>} True if fundraiser is a Booster type
     */
    static async isBoosterFundraiser (fundraiserId) {
        const fundraiser = await Fundraiser.get({ id: fundraiserId }, {
            attributes: ['fundraiserType']
        });
        if (!fundraiser) {
            throw new Error('Fundraiser not found');
        }
        return fundraiser.fundraiserType === CONSTANTS.FUNDRAISER_TYPES.BOOSTER;
    }

    /**
     * Checks if a donation is associated with a specific child
     * @param {Object} donation - The donation object to check
     * @returns {Promise<boolean>} True if donation has a childId
     */
    static async isChildSpecificDonation (donation) {
        return Boolean(donation.childId);
    }

    /**
     * Checks if a donation was successful
     * @param {Object} donation - The donation object to check
     * @returns {Promise<boolean>} True if donation status is 'success'
     */
    static async isSuccessfulDonation (donation) {
        return donation.status === 'success';
    }
}

module.exports = BoosterService;
