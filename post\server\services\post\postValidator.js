const validation = require('../../util/validation');
const GeneralError = require('../../util/GeneralError');

/**
 * Class represents validations for parent.
 */
class PostValidator extends validation {
    constructor (body, locale, query) {
        super(locale);
        this.body = body;
        this.query = query;
    }

    /**
     * @desc This function is being used to validate request for add post
     * <AUTHOR>
     * @since 08/11/2023
     */
    validate () {
        super.field(this.body.title, 'Post Title');
        super.field(this.body.subTitle, 'Post Sub-Title');
        super.field(this.body.content, 'Post Content');
        super.field(this.body.postStatus, 'Post Status');
       
        if (this.body.postStatus) {
            if (!CONSTANTS.ALLOWED_POST_STATUS.includes(this.body.postStatus.trim())) {
                throw new GeneralError(MESSAGES.INVALID_POST_STATUS, 400);
            }
           
        }
       
    }




    /**
     * @desc This function is being used to validate request for add post
     * <AUTHOR>
     * @since 27/11/2023
     */
    validateAddPost () {
        this.validate();
        super.field(this.body.organizationId, 'Organization Id');
    }


    /**
     * @desc This function is being used to validate request for update post
     * <AUTHOR>
     * @since 17/11/2023
     */
    validateUpdatePost () {
        super.param(this.body.postId, 'Post Id');
        super.uuid(this.body.postId, 'Post Id');
        this.validate();
    }

    

    /**
     * @desc This function is being used to validate if post id is sent in params
     * <AUTHOR>
     * @since 10/11/2023
     */
    validatePostId () {
        super.uuid(this.query.postId, 'Post Id');
    }

   
}


module.exports = PostValidator;
