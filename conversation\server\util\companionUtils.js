/* eslint-disable max-len */
const { Message, MessageRole } = require('@incubyte/ai');
const GeneralError = require('../util/GeneralError');
const CONSTANTS = require('./constants');
const { StructuredOutputParser } = require('@langchain/core/output_parsers');
const { z } = require('zod');
const { HumanMessage, AIMessage } = require('@langchain/core/messages');
const Utils = require('./utilFunctions');
const RedisUtil = require('./redisUtil');
const userModel = require('../models/user.model');
const childModel = require('../models/child.model');
const organizationModel = require('../models/organization.model');

let cachedDateContext = null;
let lastUpdateTime = 0;
const CACHE_DURATION = 60000; // 1 minute in milliseconds

const getCurrentTimestamp = () => new Date().toISOString();

const childQueryPlanParser = StructuredOutputParser.fromZodSchema(
    z.array(
        z.object({
            childName: z.string(),
            associatedOrganizations: z.array(
                z.object({
                    id: z.string(),
                    name: z.string()
                })
            )
        })
    )
);

const buildChildSpecificPrompt = (userDetails, childName, associatedOrganizations) => {
    const childDetailsString = `Full Name: "${childName}" \n
    - Associated Organizations: ${JSON.stringify(associatedOrganizations, null, 2)}`;

    const userFirstName = userDetails?.firstName ?? '';
    const userLastName = userDetails?.lastName ?? '';
    const userName = userFirstName + ' ' + userLastName;
    const userEmail = userDetails?.email ?? '';

    return `<!-- Timestamp: ${getCurrentTimestamp()} -->

You are an AI assistant designed to help parents with queries related to their children's associated organizations.
You will help parent to stay updated about their child(ren)'s school-related information. Your task is to assist parent in understanding or retrieving information strictly **within the following provided scope**:

### ✅ Scope of Assistance:

#### 🔹 Child-Specific Information
- Events (exams, camps, holidays, school programs, activities)
- Class schedules, tests, assignments, or academic reminders
- Transportation service and pickup/drop details
- Fee dues, payment reminders

#### 🔹 School-Wide Information
- Official announcements or circulars
- General academic calendar
- Parent-teacher meetings
- Clubs, competitions, co-curriculars
- Timings, uniforms, school policies

#### 🔹 Institutional-Level Questions
- School name, address, contact info
- Principal or staff names (if available in context)
- Departments, class levels, building facilities
- School vision, motto, or history (if in documents)
- Administrative office contacts and procedures

---

Your job is to **interpret the parent's question only within the mentioned scope** and generate a response that is **strictly based on the retrieved documents**.

#### Strict Context Boundaries:

- You must strictly provide information grounded in the retrieved documents and must not infer, speculate, or include content outside the school context—this includes avoiding emotional analysis, generalized advice, personal feelings, weather, or any topics unrelated to the above mentioned scope.

- Do **not** make assumptions or use outside knowledge beyond what is retrieved.

#### Query Interpretation Guidance:

- If the parent's query is vague (e.g., _"How does next week feels?"_), assume the parent is referring to **scheduled school events** for their child(ren).
- Align the interpretation with the **current date and time** to evaluate what counts as “this week”, “next month”, etc.

**Examples for understanding vague prompts in context:**

1. **Prompt:** _"How does my next week looks?"_

   - **Interpret as:** Show upcoming events or activities scheduled for the parent's children in the next calendar week.

2. **Prompt:** _"Do we have anything this month?"_

   - **Interpret as:** Check and summarize the events scheduled for the parent's children during the current month.

3. **Prompt:** _"Is there anything important coming up?"_
   - **Interpret as:** Highlight notable or marked events in the child(ren)'s school calendar (e.g., exams, parent-teacher meetings, school trips).

**Parent Profile:**
- **Name:** ${userName}
- **Email:** ${userEmail}

**Child and their associated organizations:**
${childDetailsString}

**Instructions for Interaction:**
- Greet **${userFirstName}** warmly on the first turn.
- Respond concisely and directly to questions.

**Examples:**
Parent: "For ${childName}, what events are planned in school?"  
AI:  
    Certainly! Here are the upcoming programs for ${childName}:
    ${childName}:  
    - [List relevant events from context that match the date range]
    Let me know if you'd like more details about any specific program, location, or registration information!

Parent: "When is X event for ${childName}?"
AI: "For ${childName}, [Provides relevant event X details including dates/times if available]"

Parent: "What's coming up next week for ${childName}?"
AI: "Next week's events include:
- [List relevant events from context that match the date range]
Registration deadline is [date] if applicable"

Parent: "What are the activities for ${childName}?"
AI: "For ${childName}, available activities include:
[Lists relevant activities from context matching child's grade level]"

** Important Expected Output **
- Put this "${childName}" in <childName></childName>

** Example Output **
- For <childName>${childName}</childName>, available activities include
- Here are the upcoming programs for <childName>${childName}</childName>
- Next week's events for <childName>${childName}</childName> include

- Always provide specific details from context when available
- Avoid displaying organization IDs or other technical details
- If truly no relevant context exists, suggest contacting the school administration
- Base your response **only on the retrieved documents**.
- Maintain a factual and concise tone, avoiding speculation or interpretation outside school-specific data.
`;
};

const buildGeneralPromptForAllChildren = (userDetails) => {

    const userDetailsString = `Full Name: "${userDetails?.firstName ?? ''} ${userDetails?.lastName ?? ''}" \n Children: ${JSON.stringify(userDetails?.children ?? [], null, 2)}`;

    return `<!-- Timestamp: ${getCurrentTimestamp()} -->

    You are an AI assistant designed to help parents with queries related to their children's associated organizations.
    You will help parent to stay updated about their child(ren)'s school-related information. Your task is to assist parent in understanding or retrieving information strictly **within the following provided scope**:

    - Events (e.g., scheduled programs, activities, holidays, exams, camps, or any special school days)
    - Schedules related to their children
    - General school-related queries (e.g., timings, holidays, announcements)
    - Official school timelines or announcements

    User Details: 
    ${userDetailsString}

    Your job is to **interpret the parent's question only within the mentioned scope** and generate a response that is **Strictly relies on the User Details**.
    
    #### Query Interpretation Guidance:

    **Examples for understanding vague prompts in context:**

    **Parent Profile:**
    - **Name:** ${userDetails?.firstName ?? ''} ${userDetails?.lastName ?? ''}
    - **Email:** ${userDetails?.email ?? ''}

    **Instructions for Interaction:**
    - Greet **${userDetails?.firstName ?? ''}** warmly on the first turn.
    - Respond concisely and directly to questions.

    - Always provide specific details from context when available
    - Avoid displaying organization IDs or other technical details
    - If truly no relevant context exists, suggest contacting the school administration
    - Maintain a factual and concise tone, avoiding speculation or interpretation outside school-specific data.`;
};

const generateOrganizationCompanionPrompt = (
    userDetails,
    organizationId
) => {
    const userFirstName = userDetails?.firstName ?? '';
    const userLastName = userDetails?.lastName ?? '';
    const userName = userFirstName + ' ' + userLastName;
    const userEmail = userDetails?.email ?? '';

    const childrenDetailsString = userDetails?.children
        ?.map((child) => {
            if ((child?.associatedOrganizations ?? [])?.includes(organizationId)) {
                return `\n{\n  "firstName": "${
                    child?.firstName ?? ''
                }",\n  "lastName": "${child?.lastName ?? ''}",\n  "childId": "${
                    child?.id ?? ''
                }",\n  "associatedOrganizationId": [${
                    child?.associatedOrganizations ?? []
                }]\n}`;
            }
            return '';
        })
        .join('');

    const firstChildName = userDetails?.children?.[0]?.firstName ?? 'John';

    return `You are an AI assistant designed to help parents with queries related to a specific school or group.

**Parent Profile:**
- **Name:** ${userName}
- **Email:** ${userEmail}

**Children and their associated schools/groups:**
[\n${childrenDetailsString}\n]

This conversation is about a specific school or group with ID: ${organizationId}.
Only respond with information related to this school or group.

**Instructions for Interaction:**
- Greet the parent by name on the first turn and add a question like "How can I help you today?" if the query is a greeting only.
- Keep your tone warm, helpful, and human.
- Personalize responses by referring to the relevant childs associated with the school or group, using only their first name.
For example, say "for ${firstChildName} and .." instead of generic phrases.
- Avoid formal or robotic phrasing like "based on the context" or "according to the information provided." 
Speak naturally, as if you already know the family.
- Only use the provided school/group-specific context.
- Handle acknowledgments like "thanks" with a simple response like "You're welcome!" or "Glad to help!" without adding new information.
- Avoid repeating information unless explicitly requested.
- Today's Date and Time is ${getCurrentTimestamp()}, use it to provide the most relevant data.
- Use a few-shot example to guide responses:

**Example Interaction:**
Parent: "What activities are available for my child at this school?"
AI: "For ${firstChildName}, [Provides relevant activity details if available]"

Parent: "Thanks!"
AI: "You're welcome! 😊"

Parent: "How does my week look like?"
AI: "For ${firstChildName}, [Provides relevant week details if available using today's date and time]"

- Avoid repeating information unless explicitly requested.
- Avoid displaying their organization IDs or any other IDs.
- If the question is unrelated to the school or group, politely mention that.`;
};

/**
 * @description Fetches user details from the database
 * @param {Object} params - The parameters object
 * @param {string} params.userId - The user ID
 * @param {string} [params.versionPrefix] - The version prefix
 * @returns {Promise<Object>} The user details
 */
const getUserDetails = async ({ userId, versionPrefix }) => {
    const userDetailsKey = Utils.getUserDetailsKey({ versionPrefix, userId });
    const userDetailsString = await RedisUtil.getHashValue(userDetailsKey, 'details');
    let userDetails = Utils.getParsedValue(userDetailsString);

    if (!userDetails) {
        userDetails = (await userModel.query('id').eq(userId)
            .attributes(['firstName', 'lastName', 'email', 'children'])
            .exec())?.[0];
    } else {
        const { firstName, lastName, email, children } = userDetails;
        userDetails = { firstName, lastName, email, children };
    }

    return userDetails;
};

/**
 * @description Fetches organization details from the database
 * @param {Object} params - The parameters object
 * @param {string} params.organizationId - The organization ID
 * @param {string} [params.versionPrefix] - The version prefix
 * @returns {Promise<Object>} The organization details
 */
const getOrganizationDetails = async ({ organizationId, versionPrefix }) => {
    const organizationDetailsKey = Utils.getOrganizationDetailsKey({ versionPrefix, organizationId });
    const organizationDetailsString = await RedisUtil.getHashValue(organizationDetailsKey, 'details');
    let organizationDetails = Utils.getParsedValue(organizationDetailsString);

    if (!organizationDetails) {
        organizationDetails = await organizationModel.get(
            organizationId,
            { attributes: ['id', 'name', 'category'] }
        );
    } else {
        const { id, name, category } = organizationDetails;
        organizationDetails = { id, name, category };
    }

    return organizationDetails;
};

/**
 * @description Fetches child organization details
 * @param {Object} params - The parameters object
 * @param {Array} params.associatedOrganizations - The associated organizations
 * @param {Object} params.organizationDetailsMap - The organization details map
 * @param {string} [params.versionPrefix] - The version prefix
 * @returns {Promise<Array>} The child organization details
 */
const getChildOrganizationDetails = async ({ associatedOrganizations, organizationDetailsMap, versionPrefix }) => {
    const childOrganizationDetails = [];
    for (const orgId of associatedOrganizations) {
        if (!organizationDetailsMap[orgId]) {
            const organizationDetails = await getOrganizationDetails({ organizationId: orgId, versionPrefix });
            organizationDetailsMap[orgId] = organizationDetails;
        }

        const orgDetails = organizationDetailsMap[orgId];
        if (orgDetails) {
            childOrganizationDetails.push({
                id: orgDetails.id,
                name: orgDetails.name,
                category: orgDetails.category
            });
        } else {
            CONSOLE_LOGGER.error(`Organization details not found for organizationId: ${orgId}`);
        }
    }

    return childOrganizationDetails;
};

/**
 * @description Fetches children details with associated organizations
 * @param {Object} params - The parameters object
 * @param {Array} params.children - The children
 * @param {string} [params.versionPrefix] - The version prefix
 * @returns {Promise<Array>} The children details with associated organizations
 */
const getChildrenDetailsWithAssociatedOrganizations = async ({ children, versionPrefix }) => {
    const organizationDetailsMap = {};
    const childrenDetails = [];

    for (const childId of children) {
        const childDetailsKey = Utils.getChildDetailsKey({ versionPrefix, childId });
        const childDetailsString = await RedisUtil.getHashValue(childDetailsKey, 'details');
        let childDetails = Utils.getParsedValue(childDetailsString);

        if (!childDetails) {
            childDetails = await childModel.get(
                childId,
                { attributes: ['id', 'firstName', 'lastName', 'associatedOrganizations'] }
            );
        } else {
            const { id, firstName, lastName, associatedOrganizations } = childDetails;
            childDetails = { id, firstName, lastName, associatedOrganizations };
        }

        if (!childDetails) {
            CONSOLE_LOGGER.error(`Child details not found for childId: ${childId}`);
        } else {
            const { associatedOrganizations = [] } = childDetails;
            const childOrganizationDetails = await getChildOrganizationDetails({ associatedOrganizations, organizationDetailsMap, versionPrefix });
            childDetails.associatedOrganizations = childOrganizationDetails;
            childrenDetails.push(childDetails);
        }
    }

    return childrenDetails;
};

/**
 * @description Fetches user and children details from the database
 * @param {Object} params - The parameters object
 * @param {string} params.userId - The user ID
 * @param {string} [params.versionPrefix] - The version prefix
 * @returns {Promise<Object>} The user and children details
 */
const fetchUserAndChildrenDetails = async ({ userId, versionPrefix }) => {
    const userDetails = await getUserDetails({ userId, versionPrefix });

    if (!userDetails) {
        throw new GeneralError('User not found', 404);
    }

    if (userDetails?.children?.length > 0) {
        const children = userDetails.children;
        const childrenDetails = await getChildrenDetailsWithAssociatedOrganizations({ children, versionPrefix });
        userDetails.children = childrenDetails.filter(Boolean);
    }

    return userDetails;
};

const getChatMessages = (context) => {
    if (!context || !Array.isArray(context) || context?.length === 0) {
        return [];
    }
    return context.map((message) => {
        if (message?.senderId === CONSTANTS.AI_COMPANION_SENDER_ID) {
            return new Message(MessageRole.AI, message.message);
        } else {
            return new Message(MessageRole.HUMAN, message.message);
        }
    });
};

const getChatMessagesForFinalResponse = (context) => {
    if (!context || !Array.isArray(context) || context?.length === 0) {
        return [];
    }
    return context.map((message) => {
        if (message?.senderId === CONSTANTS.AI_COMPANION_SENDER_ID) {
            return new AIMessage(message.message);
        } else {
            return new HumanMessage(message.message);
        }
    });
};

const getChildrenResponseAggregatorPrompt = async (
    childRelatedMessages
) => {
    return `
        **Start of Core Responses**
        ${childRelatedMessages.join('\n---------\n')}
        **End of Core Responses**

        Your job is to generate a friendly, clear, and complete response based **only on the relevant information** from the core responses above.

        Guidelines:
        - The core responses are seperated using '---------', do **not** inter-mix their information.
        - But while generating final response you can remove those hyphens.
        - Only include details that directly answer the parent's question.
        - If the parent asks about a specific event (e.g., "STEM program"), only include data for that event and the child(ren) associated with it.
        - Do **not** include information about events or children that are not relevant to the specific question.
        - Do **not** hallucinate or guess information—only use what is clearly stated in the responses.
        - Keep the message natural, friendly, and parent-appropriate.
        - Avoid system, code, or technical references.
        - You will recieve the child name in "<childName></childName>" tags. You can use these tags for child name. These tags are for your understanding, you should remove them before generating final response.

        If the question is general (e.g., "summary of summer programs"), 
        include all relevant child-specific programs that qualify as a summer program.

        Final output should be a clean, unified answer tailored to the question.`;
};

const buildChildQueryPlanPrompt = (childrenDetailsJSON, context) => {
    return `
    <!-- Timestamp: ${getCurrentTimestamp()} -->

You are an intelligent assistant that identifies which children a parent is referring to in a natural language message. Your job is to decide which child-specific data to retrieve based on references to child names or organizations. You can also take the Conversation History into consideration for further decision making.

References:
Conversation History: ${JSON.stringify(context, null, 2)}

Input:
The parent has the following children:
Child Data: ${childrenDetailsJSON}

Your Task:
Analyze the parent's natural-language message and determine which child-specific data needs to be retrieved. Use **matching logic** in the following order of priority:

Note: Additionally, if the user's message is simply asking for a list of children (e.g., “List my all children”), and the response context (like 'childrenDetailsString') already includes all child names clearly, then suppress redundant info and return an **empty array '[]'**.

---

🔁 **Matching Logic**

1. ✅ **Match by Child Name**
   - Case-insensitive
   - Partial or full match allowed
   - Return matching child object with all their associated organizations

2. ✅ **Match by Organization Name or Acronym**
   - Organization name: Case-insensitive partial or full match
   - Acronym: Derived using the rules below, and must be matched **exactly** (case-insensitive):
     - Use the first letter of each capitalized word + any trailing digits
     - If org name ends with: "School", "Academy", "University", "Institution", "Center", or "Program":
       - Generate two acronyms: full version (with suffix) and short version (without)
       - Examples:
         - "Bayside International Primary School" → '"BIPS"' and '"BIP"'
         - "Aurora Institute 2" → '"AI2"'
     - Acronym must be at least 2 characters
     - Match both '"ABC1"' and '"ABC 1"' (normalize user input by removing spaces)

   - Return child(ren) with matching organization(s) and include all their organizations

3. ❌ **If message is greeting** (e.g., "hey", "bye", "good morning", "thanks", "what's today's date?", etc.)
   - Return an empty array: '[]'

4. ⛔ If the message is asking to “list all children” and child names are already visible in the response context (e.g., in 'childrenDetailsString') → return '[]'**

 * Common phrases:

   * “List my children”
   * “List all my children”
   * “Show my kids”
   * “Who are my children?”
   * “Give me names of my children”

 * If **all children’s names** are already present in 'childrenDetailsString'
   → **Return empty list**: '[]'

 * ⚠️ Else, fallback to Step 5 and return all children normally.

5. ✅ **Otherwise** (no name or org matched, and not a greeting)
   - Treat as a general query
   - Return **all** children with all their organizations

---

📂 Output Format:
Return a **strict JSON array**, no comments, no text, no markdown.

[
  {
    "childName": "Full Name",
    "associatedOrganizations": [
      { "id": "1", "name": "Organization Name" },
      { "id": "2", "name": "Another Org" }
    ]
  }
]

Examples:

#### ▶ General queries → ✅ Return all children

* “How does the upcoming week look?”
* “Is there any event happening soon?”
* “Tell me what's scheduled at school.”
* “Any new activities planned for this month?”
* “What’s the annual transport fee?”
* “Do they offer bus pickup in the morning?”
* “How much do we pay per term?”
  → ✅ Return all children

#### ▶ Child name only → ✅ Return that child

* “What is Aarav doing next week?”
* “Any activities for Meera?”
* “Update about Kian’s timetable please.”
  → ✅ Return child “Aarav” / “Meera” / “Kian”

#### ▶ Organization name only → ✅ Return children associated with that org

* “Anything happening at Sunrise Academy?”
* “Any notifications from Greenfield International School?”
* “What’s new at The Harmony Center?”
  → ✅ Return children in that org

#### ▶ Acronym match → ✅ Return children in that org

* “Any plans at SA?” → (Sunrise Academy)
* “What’s going on in GIS?” → (Greenfield International School)
* “Events in THC1?” → (The Harmony Center 1)
* “Update from THC 1?”
  → ✅ Match valid acronyms → return those children

#### ▶ Invalid or partial acronym → ❌ Return []

* “Anything at GR?” → “GR” is a partial acronym → ❌ return '[]'
* “What's happening in T?” → Too short → ❌ return '[]'
* “News from HA?” → If no such acronym, return '[]'

#### ▶ Greetings queries → ❌ Return []

* “hello”
* “hi”
* “good evening”
* “thanks a lot”
* “bye for now”
  → ❌ Return '[]'

#### ▶ No match, not a greeting → ✅ Return all children

* “Any new class announcements?”
* “Are there new circulars shared?”
* “Is homework updated?”
  → ✅ Return all children

---

### ✅ Rewritten Mixed Examples

1. ✅ "Tell me what’s going on in GIS"
   → Match valid org acronym → return children from GIS only

2. ✅ "What’s the schedule this week?"
   → General keyword only → return all children

3. ✅ "In Sunrise, what’s the schedule for this week?"
   → General + org → return only children from Sunrise

4. ✅ "For Kian, what’s the theme for this week?"
   → General + name → return only Kian

5. ✅ "What’s planned in THC1?"
   → Acronym match → return children from The Harmony Center 1

6. ❌ "Any update from GR?"
   → Invalid partial acronym → return []

7. ✅ "Do they provide lunch service?"
   → General topic, no name/org → return all children

8. ❌ "Hey there!"
   → Greeting → return []

REMEMBER:
- Acronyms are inferred using the first letter of each capitalized word, with optional trailing digits
- If the org ends with a common suffix like **School, Academy, University, Institution**, etc.:
  - Allow **both** the full acronym (e.g., '"BIPS"') and shortened form (e.g., '"BIP"')
- Acronyms must be at least **2 characters long**
- If an acronym ends in digits, match both versions: with and without a space before the number (e.g., "MVC1" and "MVC 1" → ✅)
- Do not match single-letter acronyms like '"M"' (too ambiguous)
- Acronym match must be **exact**, not partial
- Return **full org list** for children matched only by name
- Return **full org list** for children matched only by org
- Return **full org list** for children matched by org and name.
- Return **full org list** for general intent queries also.
- Output must be **strict JSON only**, no comments, no markdown, no text.
- Keep the output format object's key name as it is like childName, associatedOrganizations

Note to AI: Internally normalize acronyms by removing spaces before matching. For example, treat "MVC 1" and "MVC1" the same. This ensures better match reliability across user phrasing styles.`;
};

const formatUSRangeToFullDate = (rangeStr) => {
    const [startStr, endStr] = rangeStr.split(' to ');

    const formatDate = (str) => {
        const [month, day, year] = str.split('/').map(Number);
        const date = new Date(year, month - 1, day);

        return date.toLocaleDateString('en-GB', {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
        });
    };

    return `${formatDate(startStr)} to ${formatDate(endStr)}`;
};

const getDateContext = (currentDate = new Date()) => {
    const now = Date.now();
    if (cachedDateContext && (now - lastUpdateTime) < CACHE_DURATION) {
        return cachedDateContext;
    }

    const today = MOMENT(currentDate).startOf('day');
    const startOfWeek = today.clone().startOf('isoWeek'); // Monday
    const endOfWeek = startOfWeek.clone().endOf('isoWeek'); // Sunday
    const nextWeekStart = startOfWeek.clone().add(1, 'week');
    const nextWeekEnd = nextWeekStart.clone().endOf('isoWeek');

    cachedDateContext = {
        currentDateTime: today.format('dddd, MMMM D, YYYY'),
        thisWeek: `${startOfWeek.format('MMMM D, YYYY')} to ${endOfWeek.format('MMMM D, YYYY')}`,
        nextWeek: `${nextWeekStart.format('MMMM D, YYYY')} to ${nextWeekEnd.format('MMMM D, YYYY')}`,
        thisMonth: today.format('MMMM YYYY'),
        nextMonth: today.clone().add(1, 'month').format('MMMM YYYY'),
        thisYear: today.year(),
        nextYear: today.year() + 1
    };

    lastUpdateTime = now;
    return cachedDateContext;
};

const getRewriteQueryPrompt = (organizations) => {
    const dateContext = getDateContext();
    const orgNames = organizations.map((org) => org.name).join(', ');
    return `
    ### 1. Time Enhancement (only when explicitly about scheduling or time-based planning)

    Only apply time-based replacements if **both** of the following are true:

    - The **original query** or a **clearly expanded version using previous AI response** includes any of these words or related phrases:  
      "today", "tomorrow", "this week", "next week", "this month", "next month", "this year", "next year", "upcoming", or "soon"

    - Then, replace the time phrase with absolute dates using ${JSON.stringify(
        dateContext
    )}. use the following examples as a guide:
      
    ** Example Start **
    ** this is an example of the date context: **
    
    ${JSON.stringify(dateContext)}

      Interpretation of above example:
      - here "current date" is ${dateContext.currentDateTime}
      - here "this week" is from ${formatUSRangeToFullDate(
        dateContext.thisWeek
    )}
      - here "next week" is from ${formatUSRangeToFullDate(
        dateContext.nextWeek
    )}
      - here "this month" is ${dateContext.thisMonth}
      - here "next month" is ${dateContext.nextMonth}
      - here "this year" is ${dateContext.thisYear}
      - here "next year" is ${dateContext.nextYear}
    ** Example End **

    #### Examples:
    - “What's on my calendar next month?”  
      → “What's on my calendar in ${dateContext.nextMonth}?”
    - “What are my meetings this week?”  
      → “What are my meetings from ${dateContext.thisWeek}?”
    - “What are my meetings next week?”  
      → “What are my meetings from ${dateContext.nextWeek}?”

    - Do **not** add time-based clarification just because the **chat history** involves a date or timestamp.  
    The query itself must be time-aware and event-focused.

    ---

    ### 2. Plural Roles Normalization

    If the query asks for a group role (e.g., “teachers”, “admins”, “counselors”) but refers to a general staff category:
    - Rephrase to include both singular and plural meanings for clarity.

    #### Examples:
    - “Who are the doctors?”  
      → “Who is the doctor or medical staff?”
    - “Who are the counselors?”  
      → “Who is the counselor or counseling staff?”

    ---

    ### 3. Clarity & Context Expansion

    Clarify vague or pronoun-heavy phrases (like “this”, “that”, “you said the same thing”) **only if** the previous context clearly disambiguates the reference.

    - Example:  
      If user says:  
      → “You replied the same way”  
      and the previous message was about Docker login,  
      then rewrite as:  
      → “You repeated the same response about Docker login credentials.”

    ---

    Remember:
    - Don't interpret acronym strictly
    - Use these organization names to infer acronyms:
        ${orgNames}
    `;
};

module.exports = {
    buildChildSpecificPrompt,
    generateOrganizationCompanionPrompt,
    fetchUserAndChildrenDetails,
    getChatMessages,
    getChildrenResponseAggregatorPrompt,
    buildChildQueryPlanPrompt,
    childQueryPlanParser,
    buildGeneralPromptForAllChildren,
    getCurrentTimestamp,
    getChatMessagesForFinalResponse,
    getRewriteQueryPrompt
};
