const handler = require('../index');
const AWS = require('aws-sdk');
const AWSMock = require('jest-aws-sdk-mock');
const sinon = require('sinon');
const Organization = require('../server/models/organization.model');
const ChildOrganizationMapping = require('../server/models/childOrganizationMapping.model');
const Child = require('../server/models/child.model');
const ConstantModel = require('../server/models/constant.model');
const Redis = require('ioredis');
const RedisUtil = require('../server/redisUtil');
const Groups = require('../server/models/groups.model');
const organizationMemberModel = require('../server/models/organizationMember.model');
const PendingPushNotification = require('../server/models/pendingPushNotification.model');
const EventSignups = require('../server/models/eventSignup.model');
const User = require('../server/models/user.model');
const { afterEach, beforeEach } = require('mocha');
const CONSOLE_LOGGER = require('../server/logger');
const { SQSClient } = require('@aws-sdk/client-sqs');
const CONSTANTS = require('../server/constants');

describe('Insert event', () => {
    let stubRedisConnect;
    let pipelineExecStub;
    let pipelineStub;

    before(() => {
        AWSMock.setSDKInstance(AWS);
    });

    after(() => {
        AWSMock.restore();
        sinon.restore();
        pipelineStub.restore();
    });

    beforeEach(() => {
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
        pipelineExecStub = sinon.stub();
        pipelineStub = sinon.stub(Redis.prototype, 'pipeline').returns({ exec: pipelineExecStub });
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        sinon.stub(Child, 'batchGet').resolves([
            {
                id: 'child1',
                guardians: ['guardianId1']
            },
            { id: 'child2', guardians: ['guardianId2'] }
        ]);
        sinon.stub(ConstantModel, 'get').withArgs(CONSTANTS.FEED_VERSION_PREFIX).resolves({ value: 'v1' });
    });

    afterEach(() => {
        sinon.restore();
    });

    try {
        it('should process INSERT events', async () => {
            sinon.stub(Organization, 'get').resolves({ name: 'Test' });
            pipelineExecStub.resolves();
            const mockedResponse = [
                { id: 'child1', associatedOrganizations: ['123'] }
            ];

            const queryStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({ eq: eqStub.returns({ using: usingStub.returns({ exec: execStub.resolves(mockedResponse) }) }) });

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([]);

            sinon.stub(Groups, 'create').resolves();
            sinon.stub(organizationMemberModel, 'get').resolves({
                users: [
                    { id: 'userId1', status: 'active' },
                    { id: 'userId2', status: 'deleted' }
                ]
            });

            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Events/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test event' },
                        details: { M: { startDateTime: { S: '2020-01-01T00:00:00.000Z' } } },
                        comments: { S: 'Test comment' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        eventType: { S: 'event' },
                        eventScope: { S: 'Test event scope' },
                        sheetUrl: { S: 'Test sheet url' },
                        fee: { S: 'Test fee' },
                        status: { S: 'published' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(pipelineExecStub);
        });

        it('should process INSERT calendar events', async () => {
            pipelineExecStub.resolves();
            const mockedResponse = [
                { id: 'child1', associatedOrganizations: ['123'] }
            ];
            sinon.stub(Organization, 'get').resolves({ name: 'Test' });

            const queryStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({ eq: eqStub.returns({ using: usingStub.returns({ exec: execStub.resolves(mockedResponse) }) }) });

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([]);

            const startDate = new Date();
            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Events/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test event' },
                        details: { M: { startDateTime: { S: startDate } } },
                        comments: { S: 'Test comment' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        eventType: { S: 'calendar' },
                        eventScope: { S: 'Test event scope' },
                        sheetUrl: { S: 'Test sheet url' },
                        fee: { S: 'Test fee' },
                        status: { S: 'published' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(pipelineExecStub);
        });

        it('should process INSERT calendar events if child has no guardians array', async () => {
            pipelineExecStub.resolves();
            const mockedResponse = [
                { id: 'child1', associatedOrganizations: ['123'] }
            ];
            sinon.stub(Organization, 'get').resolves({ name: 'Test' });

            const queryStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({ eq: eqStub.returns({ using: usingStub.returns({ exec: execStub.resolves(mockedResponse) }) }) });

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([]);

            const startDate = new Date();
            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Events/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test event' },
                        details: { M: { startDateTime: { S: startDate } } },
                        comments: { S: 'Test comment' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        eventType: { S: 'calendar' },
                        eventScope: { S: 'Test event scope' },
                        sheetUrl: { S: 'Test sheet url' },
                        fee: { S: 'Test fee' },
                        status: { S: 'published' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(pipelineExecStub);
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Update event', () => {
    let sandbox;
    let stubRedisConnect;
    let pipelineExecStub;
    let pipelineStub;

    before(() => {
        sandbox = sinon.createSandbox();
        AWSMock.setSDKInstance(AWS);
    });

    after(() => {
        AWSMock.restore();
        sinon.restore();
        pipelineStub.restore();
        sandbox.restore();
    });

    afterEach(() => {
        sinon.restore();
    });

    beforeEach(() => {
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
        pipelineExecStub = sinon.stub();
        pipelineStub = sinon.stub(Redis.prototype, 'pipeline').returns({ exec: pipelineExecStub });
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        sinon.stub(Organization, 'get').resolves({ name: 'Test' });

        sinon.stub(PendingPushNotification, 'delete').resolves();
        sinon.stub(PendingPushNotification, 'create').resolves();
        const pendingPushNotificationStub = sinon.stub(PendingPushNotification, 'query');
        const eqPendingPushNotificationStub = sinon.stub();
        const usingPendingPushNotificationStub = sinon.stub();
        const execPendingPushNotificationStub = sinon.stub();

        pendingPushNotificationStub.returns({
            eq: eqPendingPushNotificationStub.returns({
                using: usingPendingPushNotificationStub.returns({
                    exec: execPendingPushNotificationStub.resolves([
                        {
                            associatedChildId: '0fd6a871-a6f5-4690-b06a-d786f1361eef', triggerAt: '72',
                            save: () => { }, delete: () => { }
                        }])
                })
            })
        });

        sinon.stub(EventSignups, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ parentId: '0fd6a871-a6f5-4690-b06a-d786f1361eef' }])
                    })
                })
            })
        });

        sinon.stub(ConstantModel, 'get').withArgs(CONSTANTS.FEED_VERSION_PREFIX).resolves({ value: 'v1' });
    });

    try {
        it('should process MODIFY events', async () => {
            const sendStub = sandbox.stub(SQSClient.prototype, 'send');
            sendStub.resolves();

            sinon.stub(Groups, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([])
                    })
                })
            });

            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Events/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test event' },
                        details: { M: { startDateTime: { S: '2020-01-01T00:00:00.000Z' } } },
                        comments: { S: 'Test comment' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        eventType: { S: 'Test event type' },
                        eventScope: { S: 'Test event scope' },
                        sheetUrl: { S: 'Test sheet url' },
                        fee: { S: '100' },
                        status: { S: 'draft' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    },
                    NewImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test event' },
                        details: { M: { startDateTime: { S: '2028-01-01T00:00:00.000Z' } } },
                        comments: { S: 'Test comment' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        eventType: { S: 'Test event type' },
                        eventScope: { S: 'Test event scope' },
                        sheetUrl: { S: 'Test sheet url' },
                        fee: { S: 'Test fee' },
                        status: { S: 'draft' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                participants: ['0fd6a871-a6f5-4690-b06a-d786f1361eef']
            }));

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([
                JSON.stringify({
                    eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                    childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    isSignedUp: false,
                    status: ''
                })
            ]);

            await handler.handler(event);

            sinon.assert.called(pipelineExecStub);
        });

        it('should process MODIFY events for comments', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Events/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test event' },
                        details: { M: { startDateTime: { S: '2020-01-01T00:00:00.000Z' } } },
                        comments: {
                            L: [{
                                M: {
                                    childId: { S: '95285616-3769-4b2e-b36b-898597b81461' },
                                    message: { S: 'my first comment' },
                                    parentId: { S: '95285616-3769-4b2e-b36b-898597b8146e' },
                                    createdAt: { S: '2023-11-28T09:37:15.207Z' }
                                }
                            }]
                        },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        eventType: { S: 'Test event type' },
                        eventScope: { S: 'Test event scope' },
                        sheetUrl: { S: 'Test sheet url' },
                        fee: { S: '100' },
                        status: { S: 'published' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    },
                    NewImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test event' },
                        details: { M: { startDateTime: { S: '2020-01-01T00:00:00.000Z' } } },
                        comments: {
                            L: [{
                                M: {
                                    childId: { S: '95285616-3769-4b2e-b36b-898597b81462' },
                                    message: { S: 'my second comment' },
                                    parentId: { S: '95285616-3769-4b2e-b36b-898597b8146f' },
                                    createdAt: { S: '2023-11-28T09:38:15.207Z' }
                                }
                            }]
                        },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        eventType: { S: 'event' },
                        eventScope: { S: 'Test event scope' },
                        sheetUrl: { S: 'Test sheet url' },
                        fee: { S: 'Test fee' },
                        status: { S: 'published' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    }
                }
            };

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                title: 'Test event',
                details: { startDateTime: '2020-01-01T00:00:00.000Z' },
                comments: 'Test comment',
                organizationId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                eventType: 'Test event type',
                eventScope: 'Test event scope',
                sheetUrl: 'Test sheet url',
                fee: '100',
                status: 'unpublished',
                photoURL: 'Test photo url',
                createdAt: '2020-01-01T00:00:00.000Z',
                updatedAt: '2020-01-01T00:00:00.000Z'
            }));

            sinon.stub(Child, 'batchGet').resolves([
                {
                    firstName: 'test', lastName: 'user', associatedColor: '#000000'
                }
            ]);
            sinon.stub(Child, 'get').resolves({
                firstName: 'test', lastName: 'user', associatedColor: '#000000'
            });

            sinon.stub(ChildOrganizationMapping, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([])
                    })
                })
            });

            sinon.stub(Groups, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([
                            {
                                groupId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                                groupType: 'event',
                                status: 'active'
                            }
                        ])
                    })
                })
            });

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([
                JSON.stringify({
                    eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                    childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    isSignedUp: false,
                    status: ''
                })
            ]);
            const event = {
                Records: [record]
            };
            await handler.handler(event);

            sinon.assert.called(pipelineExecStub);
        });

        it('should process MODIFY events and soft delete', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Events/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test event' },
                        details: { M: { startDateTime: { S: '2020-01-01T00:00:00.000Z' } } },
                        comments: { S: 'Test comment' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        eventType: { S: 'Test event type' },
                        eventScope: { S: 'Test event scope' },
                        sheetUrl: { S: 'Test sheet url' },
                        fee: { S: '100' },
                        status: { S: 'unpublished' },
                        isDeleted: { N: 0 },
                        photoURL: { S: 'Test photo url' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    },
                    NewImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test event' },
                        details: { M: { startDateTime: { S: '2020-01-01T00:00:00.000Z' } } },
                        comments: { S: 'Test comment' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        eventType: { S: 'Test event type' },
                        eventScope: { S: 'Test event scope' },
                        sheetUrl: { S: 'Test sheet url' },
                        fee: { S: 'Test fee' },
                        status: { S: 'in-review' },
                        isDeleted: { N: 1 },
                        photoURL: { S: 'Test photo url' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            sinon.stub(User, 'query').resolves({ fcmToken: '0fd6a871-a6f5-4690-b06a-d786f1361eef' });
            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([
                JSON.stringify({
                    eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                    childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    isSignedUp: false,
                    status: ''
                }),
                JSON.stringify({
                    eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eeg',
                    child: { id: 'ace0149e-447d-4f7f-9402-95a96963de9b', associatedImageOrColor: '#FACD01' },
                    isSignedUp: false,
                    status: ''
                })
            ]);

            const mockedResponse = [
                { childId: 'ace0149e-447d-4f7f-9402-95a96963de9b', associatedOrganizations: ['123'] }
            ];

            const queryStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({ eq: eqStub.returns({ using: usingStub.returns({ exec: execStub.resolves(mockedResponse) }) }) });

            sinon.stub(Child, 'batchGet').resolves([
                { id: 'ace0149e-447d-4f7f-9402-95a96963de9b', guardians: ['userId1', 'userId2'] }
            ]);

            sinon.stub(Groups, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([])
                    })
                })
            });

            await handler.handler(event);

            sinon.assert.called(queryStub);
        });

        it('should process MODIFY events if status is changed to draft from published', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Events/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test event' },
                        details: { M: { startDateTime: { S: '2020-01-01T00:00:00.000Z' } } },
                        comments: { S: 'Test comment' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        eventType: { S: 'Test event type' },
                        eventScope: { S: 'Test event scope' },
                        sheetUrl: { S: 'Test sheet url' },
                        fee: { S: 'Test fee' },
                        status: { S: 'published' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    },
                    NewImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test event' },
                        details: { M: { startDateTime: { S: '2020-01-01T00:00:00.000Z' } } },
                        comments: { S: 'Test comment' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        eventType: { S: 'calendar' },
                        eventScope: { S: 'Test event scope' },
                        sheetUrl: { S: 'Test sheet url' },
                        fee: { S: 'Test fee' },
                        status: { S: 'draft' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            const mockedResponse = [
                { childId: 'child1', associatedOrganizations: ['123'] }
            ];

            const queryStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({ eq: eqStub.returns({ using: usingStub.returns({ exec: execStub.resolves(mockedResponse) }) }) });

            sinon.stub(User, 'query').resolves({ fcmToken: '0fd6a871-a6f5-4690-b06a-d786f1361eef' });

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                participants: ['0fd6a871-a6f5-4690-b06a-d786f1361eef']
            }));

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([
                JSON.stringify(
                    {
                        eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                        childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                        isSignedUp: false,
                        status: ''
                    })
            ]);

            sinon.stub(Groups, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([])
                    })
                })
            });

            sinon.stub(Child, 'batchGet').resolves([
                { id: 'child1', guardians: ['userId1', 'userId2'] }
            ]);

            await handler.handler(event);

            sinon.assert.called(queryStub);
        });

        it('should process MODIFY events if status is changed to published from draft', async () => {
            const mockedResponse = [
                { childId: 'child1', associatedOrganizations: ['123'] }
            ];
            sinon.stub(ChildOrganizationMapping, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves(mockedResponse)
                    })
                })
            });

            sinon.stub(Groups, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([])
                    })
                })
            });

            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Events/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test event' },
                        details: { M: { startDateTime: { S: '2020-01-01T00:00:00.000Z' } } },
                        comments: { S: 'Test comment' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        eventType: { S: 'Test event type' },
                        eventScope: { S: 'Test event scope' },
                        sheetUrl: { S: 'Test sheet url' },
                        fee: { S: '100' },
                        status: { S: 'unpublished' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    },
                    NewImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test event' },
                        details: { M: { startDateTime: { S: '2028-01-01T00:00:00.000Z' } } },
                        comments: { S: 'Test comment' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        eventType: { S: 'Test event type' },
                        eventScope: { S: 'Test event scope' },
                        sheetUrl: { S: 'Test sheet url' },
                        fee: { S: 'Test fee' },
                        status: { S: 'published' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            sinon.stub(Child, 'batchGet').resolves([
                { id: 'child1', guardians: ['userId1', 'userId2'] }
            ]);

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                participants: ['0fd6a871-a6f5-4690-b06a-d786f1361eef']
            }));

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([
                JSON.stringify(
                    {
                        eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                        childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                        isSignedUp: false,
                        status: ''
                    })
            ]);

            await handler.handler(event);
        });

        it('should process MODIFY events and update registered existing feeds', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Events/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test event' },
                        details: { M: { startDateTime: { S: '2020-01-01T00:00:00.000Z' } } },
                        comments: { S: 'Test comment' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        eventType: { S: 'Test event type' },
                        eventScope: { S: 'Test event scope' },
                        sheetUrl: { S: 'Test sheet url' },
                        fee: { S: 'Test fee' },
                        status: { S: 'published' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    },
                    NewImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test event' },
                        details: { M: { startDateTime: { S: '202-01-01T00:00:00.000Z' } } },
                        comments: { S: 'Test comment' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        eventType: { S: 'Test event type' },
                        eventScope: { S: 'Test event scope' },
                        sheetUrl: { S: 'Test sheet url' },
                        fee: { S: 'Test fee' },
                        status: { S: 'published' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            const mockedResponse = [
                { id: 'child1', associatedOrganizations: ['123'] }
            ];

            sinon.stub(Child, 'batchGet').resolves([
                { childId: 'child1', guardians: ['userId1', 'userId2'] }
            ]);

            const queryStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({ eq: eqStub.returns({ using: usingStub.returns({ exec: execStub.resolves(mockedResponse) }) }) });

            const getElementsOfSortedSetByScoreStub = sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore');
            getElementsOfSortedSetByScoreStub.onCall(0).resolves([]);
            getElementsOfSortedSetByScoreStub.onCall(1).resolves([JSON.stringify({
                eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                isSignedUp: false,
                status: ''
            })]);

            sinon.stub(Groups, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([])
                    })
                })
            });

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                participants: ['0fd6a871-a6f5-4690-b06a-d786f1361eef']
            }));

            await handler.handler(event);
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
