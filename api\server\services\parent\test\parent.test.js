/* eslint-disable max-len */
const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const sinon = require('sinon');
const User = require('../../../models/user.model');
const Child = require('../../../models/child.model');
const Event = require('../../../models/event.model');
const Organization = require('../../../models/organization.model');
const dynamoose = require('dynamoose');
const ChildOrganizationMapping = require('../../../models/childOrganizationMapping.model');
const TestCase = require('./testcaseParent');
const jwt = require('jsonwebtoken');
const PendingParentInvite = require('../../../models/pendingPartnerInvite.model');
const Utils = require('../../../util/utilFunctions');
const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
/* User Token */
const user = {
    id: '95285616-3769-4b2e-b36b-898597b8146e',
    email: '<EMAIL>'
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'testingjwt', tokenOptionalInfo)
};
const fakeOrgList = [
    {
        'country': 'USA',
        'zipCode': 'A3457F',
        'address': 'Address Line 1',
        'isDeleted': 0,
        'city': 'Illnois',
        'name': 'Jainil PTO',
        'id': '4cbba60d-0a42-4a66-a9ab-99cf9e46edad',
        'state': 'New York',
        'category': 'PTO',
        'createdAt': '1699610199019'
    }
];
Utils.addCommonReqTokenForHMac(request);
describe('Add Child', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });
        TestCase.addChild.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({ status: 'active', isVerified: 1, role: 1 });
                request(process.env.BASE_URL)
                    .post('/parent/add-child')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should get error if school does not exists', (done) => {
            const addChild = {
                firstName: 'Test',
                lastName: 'User',
                gender: 'male',
                schoolId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                zipCode: '12345',
                associatedColor: '#FFFFFF',
                homeroomId: '9fd6a871-a6f5-4690-b06a-d786f1361eef'
            };

            getStub.resolves({ status: 'active', isVerified: 1, role: 1 });
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.withArgs({ id: '0fd6a871-a6f5-4690-b06a-d786f1361eef' }).returns({ category: CONSTANTS.CATEGORIES.HOME_ROOM });


            request(process.env.BASE_URL)
                .post('/parent/add-child')
                .set({ Authorization: requestPayloadUser.token })
                .send(addChild)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgScanStub.restore();
                    done();
                });
        });

        it('As a user I should get error if home rome does not exists', (done) => {
            const addChild = {
                firstName: 'Test',
                lastName: 'User',
                gender: 'male',
                schoolId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                zipCode: '12345',
                associatedColor: '#FFFFFF',
                homeroomId: '9fd6a871-a6f5-4690-b06a-d786f1361eef'
            };

            getStub.resolves({ status: 'active', isVerified: 1, role: 1 });
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.withArgs({ id: addChild.schoolId }).returns({ category: CONSTANTS.CATEGORIES.SCHOOL });
            orgScanStub.withArgs({ id: addChild.homeroomId }).returns({
                category: CONSTANTS.CATEGORIES.PTO, parentOrganization: addChild.schoolId
            });


            request(process.env.BASE_URL)
                .post('/parent/add-child')
                .set({ Authorization: requestPayloadUser.token })
                .send(addChild)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgScanStub.restore();
                    done();
                });
        });

        it('As a user I should add child', (done) => {
            const addChild = {
                firstName: 'Test',
                lastName: 'User',
                gender: 'male',
                schoolId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                zipCode: '12345',
                associatedColor: '#FFFFFF',
                homeroomId: '9fd6a871-a6f5-4690-b06a-d786f1361eef'
            };

            getStub.resolves({ status: 'active', isVerified: 1, role: 1 });
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.withArgs({ id: addChild.schoolId }).returns({ category: CONSTANTS.CATEGORIES.SCHOOL });
            orgScanStub.withArgs({ id: addChild.homeroomId }).returns({
                category: CONSTANTS.CATEGORIES.HOME_ROOM, parentOrganization: addChild.schoolId
            });

            sinon.stub(Organization, 'query').returns({
                eq: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            attributes: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ id: '123' }])
                            })
                        })
                    }),
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ id: '123' }])
                    })
                })
            });

            sinon.stub(Child, 'create').resolves({ id: '1' });
            sinon.stub(ChildOrganizationMapping, 'create').resolves();
            sinon.stub(User, 'update').resolves();

            request(process.env.BASE_URL)
                .post('/parent/add-child')
                .set({ Authorization: requestPayloadUser.token })
                .send(addChild)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgScanStub.restore();
                    Child.create.restore();
                    Organization.query.restore();
                    ChildOrganizationMapping.create.restore();
                    User.update.restore();
                    done();
                });
        });

        it('As a user I should add child without homeroomId', (done) => {
            const addChild = {
                firstName: 'Test',
                lastName: 'User',
                gender: 'male',
                schoolId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                zipCode: '12345',
                associatedColor: '#FFFFFF'
            };

            getStub.resolves({ status: 'active', isVerified: 1, role: 1 });
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.withArgs({ id: addChild.schoolId }).returns({ category: CONSTANTS.CATEGORIES.SCHOOL });

            sinon.stub(Child, 'create').resolves({ id: '1', associatedOrganizations: ['123', '222'] });
            sinon.stub(ChildOrganizationMapping, 'create').resolves();
            sinon.stub(User, 'update').resolves();
            const eventQueryStub = sinon.stub(Event, 'query');
            eventQueryStub.withArgs('organizationId').returns({
                eq: () => ({
                    where: () => ({
                        eq: () => ({
                            exec: () => Promise.resolve([{ id: '123' }])
                        })
                    })
                })
            });

            sinon.stub(Organization, 'query').returns({
                eq: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            attributes: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ id: '123' }])
                            })
                        })
                    }),
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ id: '123' }])
                    })
                })
            });

            request(process.env.BASE_URL)
                .post('/parent/add-child')
                .set({ Authorization: requestPayloadUser.token })
                .send(addChild)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgScanStub.restore();
                    Child.create.restore();
                    Organization.query.restore();
                    ChildOrganizationMapping.create.restore();
                    User.update.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
describe('Get School List', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get school list with empty array', (done) => {
            const scanStub = sinon.stub();
            const execStub = sinon.stub();

            scanStub.returns({ exec: execStub });
            execStub.resolves({ count: 1 });

            getStub.resolves({ status: 'active', isVerified: 1, role: 1 });

            const orgScanStub = sinon.stub(Organization, 'query');
            orgScanStub.withArgs('category')
                .returns({
                    eq: sinon.stub().returns({
                        using: sinon.stub().returns({
                            where: sinon.stub().returns({
                                eq: sinon.stub().returns({
                                    attributes: sinon.stub().returns({
                                        exec: sinon.stub().resolves([])
                                    })
                                })
                            })
                        })
                    })
                });
            request(process.env.BASE_URL)
                .get('/parent/get-school-list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgScanStub.restore();
                    done();
                });
        });

        it('As a user I should get school list', (done) => {
            const scanStub = sinon.stub();
            const execStub = sinon.stub();

            scanStub.returns({ exec: execStub });
            execStub.resolves({ count: 1 });

            getStub.resolves({ status: 'active', isVerified: 1, role: 1 });

            const orgScanStub = sinon.stub(Organization, 'query');
            orgScanStub.withArgs('category')
                .returns({
                    eq: sinon.stub().returns({
                        using: sinon.stub().returns({
                            where: sinon.stub().returns({
                                eq: sinon.stub().returns({
                                    attributes: sinon.stub().returns({
                                        exec: sinon.stub().resolves({
                                            populate: () => fakeOrgList
                                        })
                                    })
                                })
                            })
                        })
                    })
                });
            request(process.env.BASE_URL)
                .get('/parent/get-school-list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgScanStub.restore();
                    done();
                });
        });

        it('As a user I should get school list with category and schoolId', (done) => {
            const scanStub = sinon.stub();
            const execStub = sinon.stub();

            scanStub.returns({ exec: execStub });
            execStub.resolves({ count: 1 });

            getStub.resolves({ status: 'active', isVerified: 1, role: 1 });

            const orgScanStub = sinon.stub(Organization, 'query');
            orgScanStub.withArgs('category')
                .returns({
                    eq: sinon.stub().returns({
                        using: sinon.stub().returns({
                            where: sinon.stub().returns({
                                eq: sinon.stub().returns({
                                    where: sinon.stub().returns({
                                        eq: sinon.stub().returns({
                                            attributes: sinon.stub().returns({
                                                exec: sinon.stub().resolves({
                                                    populate: () => fakeOrgList
                                                })
                                            })
                                        })
                                    })
                                })
                            })
                        })
                    })
                });
            request(process.env.BASE_URL)
                .get(`/parent/get-school-list?category=${CONSTANTS.CATEGORIES.HOME_ROOM}&schoolId=4cbba60d-0a42-4a66-a9ab-99cf9e46edad`)
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgScanStub.restore();
                    done();
                });
        });

        it('As a user I should get school list with category and schoolId with empty list', (done) => {
            const scanStub = sinon.stub();
            const execStub = sinon.stub();

            scanStub.returns({ exec: execStub });
            execStub.resolves({ count: 1 });

            getStub.resolves({ status: 'active', isVerified: 1, role: 1 });

            const orgScanStub = sinon.stub(Organization, 'query');
            orgScanStub.withArgs('category')
                .returns({
                    eq: sinon.stub().returns({
                        using: sinon.stub().returns({
                            where: sinon.stub().returns({
                                eq: sinon.stub().returns({
                                    where: sinon.stub().returns({
                                        eq: sinon.stub().returns({
                                            attributes: sinon.stub().returns({
                                                exec: sinon.stub().resolves([])
                                            })
                                        })
                                    })
                                })
                            })
                        })
                    })
                });
            request(process.env.BASE_URL)
                .get(`/parent/get-school-list?category=${CONSTANTS.CATEGORIES.HOME_ROOM}&schoolId=4cbba60d-0a42-4a66-a9ab-99cf9e46edad`)
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgScanStub.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
describe('Get School List handle Error', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get error if something goes wrong', (done) => {
            const scanStub = sinon.stub();
            const execStub = sinon.stub();

            scanStub.returns({ exec: execStub });
            execStub.rejects({ count: 1 });

            getStub.resolves({ status: 'active', isVerified: 1, role: 1 });

            request(process.env.BASE_URL)
                .get('/parent/get-school-list?category=123')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
describe('Invite Partner', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get error if childId is not valid', (done) => {
            getStub.resolves({ id: '123', status: 'active', isVerified: 1, role: 1, children: ['59985bdc-77ba-4911-ba98-d49230566445'] });

            request(process.env.BASE_URL)
                .post('/parent/invite-partner?email=<EMAIL>&childId=59985bdc-77ba-4911-ba98-d49230566444')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error if partner already added the child', (done) => {
            getStub.resolves({
                id: '123', status: 'active', isVerified: 1, role: 1,
                children: ['59985bdc-77ba-4911-ba98-d49230566444'],
                sendInvites: [{
                    invitedPartnerEmail: '<EMAIL>',
                    children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566444', status: 'pending' }]
                }]
            });
            const pendingParentInviteStub = sinon.stub(PendingParentInvite, 'query');
            const childGetStub = sinon.stub(Child, 'get').resolves({});
            const userQuery = sinon.stub(User, 'query');
            userQuery.returns({
                eq: sinon.stub().returns({
                    exec: sinon.stub().resolves([{
                        children: ['59985bdc-77ba-4911-ba98-d49230566444']
                    }])
                })
            });
            pendingParentInviteStub.withArgs('invitedPartner')
                .returns({
                    eq: sinon.stub().returns({
                        using: sinon.stub().returns({
                            exec: sinon.stub().resolves([{
                                inviterPartnerId: '124',
                                children: ['59985bdc-77ba-4911-ba98-d49230566444']
                            }])
                        })
                    })
                });

            request(process.env.BASE_URL)
                .post('/parent/invite-partner?email=<EMAIL>&childId=59985bdc-77ba-4911-ba98-d49230566444')
                .set({ Authorization: requestPayloadUser.token })
                .send({ email: '<EMAIL>', childrenIds: ['59985bdc-77ba-4911-ba98-d49230566444'], sendInvites: [] })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    pendingParentInviteStub.restore();
                    userQuery.restore();
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should get error if it is not my child', (done) => {
            getStub.resolves({
                id: '123', status: 'active', isVerified: 1, role: 1, children: ['59985bdc-77ba-4911-ba98-d49230566444'],
                partnerInvites: [], sendInvites: []
            });
            const pendingParentInviteStub = sinon.stub(PendingParentInvite, 'query');
            const childGetStub = sinon.stub(Child, 'get').resolves({});
            const userQuery = sinon.stub(User, 'query');
            userQuery.returns({
                eq: sinon.stub().returns({
                    exec: sinon.stub().resolves([{
                        children: ['59985bdc-77ba-4911-ba98-d49230566445'],
                        partnerInvites: [{
                            invitedPartnerEmail: '<EMAIL>',
                            children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566444', invitedAt: new Date() }],
                            inviterPartnerId: '123'
                        }],
                        save: () => Promise.resolve()
                    }])
                })
            });
            pendingParentInviteStub.withArgs('invitedPartner')
                .returns({
                    eq: sinon.stub().returns({
                        using: sinon.stub().returns({
                            exec: sinon.stub().resolves([{
                                inviterPartnerId: '124',
                                children: ['59985bdc-77ba-4911-ba98-d49230566454']
                            }])
                        })
                    })
                });

            request(process.env.BASE_URL)
                .post('/parent/invite-partner?email=<EMAIL>&childId=59985bdc-77ba-4911-ba98-d49230566444')
                .set({ Authorization: requestPayloadUser.token })
                .send({ email: '<EMAIL>', childrenIds: ['59985bdc-77ba-4911-ba98-d49230566445'] })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    pendingParentInviteStub.restore();
                    userQuery.restore();
                    childGetStub.restore();
                    done();
                });
        });

        it('As a user I should invite user', (done) => {
            getStub.resolves({
                id: '123', status: 'active', isVerified: 1, role: 1, children: ['59985bdc-77ba-4911-ba98-d49230566444'], sendInvites: []
            });
            const pendingParentInviteStub = sinon.stub(PendingParentInvite, 'query');
            const childGetStub = sinon.stub(Child, 'get').resolves({});
            const userQuery = sinon.stub(User, 'query');
            userQuery.returns({
                eq: sinon.stub().returns({
                    exec: sinon.stub().resolves([{
                        children: ['59985bdc-77ba-4911-ba98-d49230566445'],
                        partnerInvites: [{
                            inviterPartnerEmail: '<EMAIL>',
                            children: ['59985bdc-77ba-4911-ba98-d49230566444'],
                            inviterPartnerId: '123'
                        }]
                    }])
                })
            });
            pendingParentInviteStub.withArgs('invitedPartner')
                .returns({
                    eq: sinon.stub().returns({
                        using: sinon.stub().returns({
                            exec: sinon.stub().resolves([{
                                inviterPartnerId: '124',
                                children: ['59985bdc-77ba-4911-ba98-d49230566444']
                            }])
                        })
                    })
                });
            sinon.stub(User.transaction, 'update').resolves();
            sinon.stub(dynamoose, 'transaction').resolves();

            request(process.env.BASE_URL)
                .post('/parent/invite-partner?email=<EMAIL>&childId=59985bdc-77ba-4911-ba98-d49230566444')
                .set({ Authorization: requestPayloadUser.token })
                .send({ email: '<EMAIL>', childrenIds: ['59985bdc-77ba-4911-ba98-d49230566444'] })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    pendingParentInviteStub.restore();
                    userQuery.restore();
                    childGetStub.restore();
                    User.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });

        it('As a user I should invite partner if user not exists', (done) => {
            getStub.resolves({
                id: '123', status: 'active', isVerified: 1, role: 1,
                children: ['59985bdc-77ba-4911-ba98-d49230566444']
            });
            const pendingParentInviteStub = sinon.stub(PendingParentInvite, 'query');
            const childGetStub = sinon.stub(Child, 'get').resolves({});
            const userQuery = sinon.stub(User, 'query');
            userQuery.returns({
                eq: sinon.stub().returns({
                    exec: sinon.stub().resolves([])
                })
            });
            sinon.stub(PendingParentInvite.transaction, 'create').resolves();
            sinon.stub(User.transaction, 'update').resolves();
            sinon.stub(dynamoose, 'transaction').resolves;

            pendingParentInviteStub.withArgs('invitedPartner')
                .returns({
                    eq: sinon.stub().returns({
                        using: sinon.stub().returns({
                            exec: sinon.stub().resolves([{
                                inviterPartnerId: '123',
                                children: ['59985bdc-77ba-4911-ba98-d49230566445'],
                                invitedPartnerEmail: '<EMAIL>',
                                save: () => Promise.resolve()
                            }])
                        })
                    })
                });

            request(process.env.BASE_URL)
                .post('/parent/invite-partner?email=<EMAIL>&childId=59985bdc-77ba-4911-ba98-d49230566444')
                .set({ Authorization: requestPayloadUser.token })
                .send({ email: '<EMAIL>', childrenIds: ['59985bdc-77ba-4911-ba98-d49230566444'] })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    pendingParentInviteStub.restore();
                    userQuery.restore();
                    childGetStub.restore();
                    PendingParentInvite.transaction.create.restore();
                    User.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
describe('Get Invite Partner List', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get empty list if partner is not valid', (done) => {
            getStub.resolves({ id: '123', status: 'active', isVerified: 1, role: 1, children: ['59985bdc-77ba-4911-ba98-d49230566445'] });

            request(process.env.BASE_URL)
                .get('/parent/invite-partner')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

        it('As a user I should get empty list if partner is empty', (done) => {
            getStub.resolves({
                id: '123', status: 'active', isVerified: 1, role: 1,
                children: ['59985bdc-77ba-4911-ba98-d49230566445'], partnerInvites: []
            });

            request(process.env.BASE_URL)
                .get('/parent/invite-partner')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

        it('As a user I should get error for inconsistant data', (done) => {
            getStub.resolves({
                id: '123', status: 'active', isVerified: 1, role: 1,
                children: ['59985bdc-77ba-4911-ba98-d49230566445'], partnerInvites: ['123']
            });

            request(process.env.BASE_URL)
                .get('/parent/invite-partner')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get pending partner invite list', (done) => {
            getStub.resolves({
                id: '123', status: 'active', isVerified: 1, role: 1, children: ['59985bdc-77ba-4911-ba98-d49230566444'],
                partnerInvites: [
                    {
                        invitedPartnerEmail: '<EMAIL>',
                        children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445', invitedAt: new Date() }],
                        inviterPartnerId: 'abc'
                    },
                    {
                        invitedPartnerEmail: '<EMAIL>',
                        children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566446', invitedAt: new Date() }],
                        inviterPartnerId: 'xyz'
                    }
                ],
                email: '<EMAIL>'
            });

            const childGetStub = sinon.stub(Child, 'batchGet').resolves([
                {
                    firstName: 'Test',
                    lastName: 'User',
                    associatedColor: '#FFFFFF',
                    id: '59985bdc-77ba-4911-ba98-d49230566445',
                    homeRoom: '9fd6a871-a6f5-4690-b06a-d786f1361eef',
                    school: '9fd6a871-a6f5-4690-b06a-d786f1361eef'
                },
                {
                    firstName: 'Test',
                    lastName: 'child',
                    associatedColor: '#FFFFFF',
                    id: '59985bdc-77ba-4911-ba98-d49230566446',
                    photoURL: 'http://test.com',
                    school: '9fd6a871-a6f5-4690-b06a-d786f1361eef'
                }
            ]);
            const userGetStub = sinon.stub(User, 'batchGet').resolves([
                {
                    id: 'abc',
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User'
                },
                {
                    id: 'xyz',
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User'
                }
            ]);
            const orgSub = sinon.stub(Organization, 'get').resolves({
                id: 'abc',
                name: 'Test',
                category: CONSTANTS.CATEGORIES.SCHOOL
            });
            request(process.env.BASE_URL)
                .get('/parent/invite-partner')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    userGetStub.restore();
                    childGetStub.restore();
                    orgSub.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
describe('Accept/Deny Invite Partner', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get error if status for accept and deny is not valid', (done) => {
            getStub.resolves({
                id: '123', status: 'active', isVerified: 1, role: 1,
                children: ['59985bdc-77ba-4911-ba98-d49230566444', '59985bdc-77ba-4911-ba98-d49230566445'],
                partnerInvites: [
                    {
                        invitedPartnerEmail: '<EMAIL>',
                        children: ['59985bdc-77ba-4911-ba98-d49230566445'],
                        inviterPartnerId: 'abc'
                    },
                    {
                        invitedPartnerEmail: '<EMAIL>',
                        children: ['59985bdc-77ba-4911-ba98-d49230566446'],
                        inviterPartnerId: 'xyz'
                    }
                ],
                email: '<EMAIL>'
            });

            request(process.env.BASE_URL)
                .put('/parent/invite-partner?status=test&childId=59985bdc-77ba-4911-ba98-d49230566445&inviterPartnerEmail=<EMAIL>')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get response with message invite not found if id not exists', (done) => {
            getStub.resolves({
                id: '123', status: 'active', isVerified: 1, role: 1, children: ['59985bdc-77ba-4911-ba98-d49230566444'],
                partnerInvites: [
                    {
                        inviterPartnerEmail: '<EMAIL>',
                        children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445', invitedAt: new Date() }],
                        inviterPartnerId: 'abc'
                    },
                    {
                        inviterPartnerEmail: '<EMAIL>',
                        children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566446', invitedAt: new Date() }],
                        inviterPartnerId: 'xyz'
                    }
                ],
                email: '<EMAIL>',
                save: () => Promise.resolve({
                    partnerInvites: [
                        {
                            inviterPartnerEmail: '<EMAIL>',
                            children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566446', invitedAt: new Date() }],
                            inviterPartnerId: 'xyz'
                        }
                    ]
                })
            });
            const childNewStub = sinon.stub(Child, 'get').resolves({});
            const childGetStub = sinon.stub(Child, 'batchGet').resolves([
                {
                    firstName: 'Test',
                    lastName: 'User',
                    associatedColor: '#FFFFFF',
                    id: '59985bdc-77ba-4911-ba98-d49230566445',
                    homeRoom: '9fd6a871-a6f5-4690-b06a-d786f1361eef',
                    school: '9fd6a871-a6f5-4690-b06a-d786f1361eef'
                },
                {
                    firstName: 'Test',
                    lastName: 'child',
                    associatedColor: '#FFFFFF',
                    id: '59985bdc-77ba-4911-ba98-d49230566446',
                    photoURL: 'http://test.com',
                    school: '9fd6a871-a6f5-4690-b06a-d786f1361eef'
                }
            ]);
            const orgSub = sinon.stub(Organization, 'get').resolves({
                id: 'abc',
                name: 'Test',
                category: CONSTANTS.CATEGORIES.SCHOOL
            });
            const userGetStub = sinon.stub(User, 'batchGet').resolves([
                {
                    id: 'abc',
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User'
                },
                {
                    id: 'xyz',
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User'
                }
            ]);
            request(process.env.BASE_URL)
                .put('/parent/invite-partner?status=accepted&childId=59985bdc-77ba-4911-ba98-d49230566448&inviterPartnerEmail=<EMAIL>')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childNewStub.restore();
                    childGetStub.restore();
                    userGetStub.restore();
                    orgSub.restore();
                    done();
                });
        });

        it('As a user I should add child and get pending partner list if I accept the request', (done) => {
            getStub.resolves({
                id: '123', status: 'active', isVerified: 1, role: 1, children: ['59985bdc-77ba-4911-ba98-d49230566444'],
                partnerInvites: [
                    {
                        inviterPartnerEmail: '<EMAIL>',
                        children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445', invitedAt: new Date() }],
                        inviterPartnerId: 'abc'
                    },
                    {
                        inviterPartnerEmail: '<EMAIL>',
                        children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566446', invitedAt: new Date() }],
                        inviterPartnerId: 'xyz'
                    }
                ],
                email: '<EMAIL>',
                sendInvites: [{
                    invitedPartnerEmail: '<EMAIL>',
                    children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445', status: 'pending' }]
                }],
                save: () => Promise.resolve({
                    partnerInvites: [
                        {
                            inviterPartnerEmail: '<EMAIL>',
                            children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566446', invitedAt: new Date() }],
                            inviterPartnerId: 'xyz'
                        }
                    ]
                })
            });
            const childNewStub = sinon.stub(Child, 'get').resolves({
                guardians: [],
                save: () => { }
            });
            const childGetStub = sinon.stub(Child, 'batchGet').resolves([
                {
                    firstName: 'Test',
                    lastName: 'User',
                    associatedColor: '#FFFFFF',
                    id: '59985bdc-77ba-4911-ba98-d49230566445',
                    homeRoom: '9fd6a871-a6f5-4690-b06a-d786f1361eef',
                    school: '9fd6a871-a6f5-4690-b06a-d786f1361eef'
                },
                {
                    firstName: 'Test',
                    lastName: 'child',
                    associatedColor: '#FFFFFF',
                    id: '59985bdc-77ba-4911-ba98-d49230566446',
                    photoURL: 'http://test.com',
                    school: '9fd6a871-a6f5-4690-b06a-d786f1361eef'
                }
            ]);
            const orgSub = sinon.stub(Organization, 'get').resolves({
                id: 'abc',
                name: 'Test',
                category: CONSTANTS.CATEGORIES.SCHOOL
            });
            const userGetStub = sinon.stub(User, 'batchGet').resolves([
                {
                    id: 'abc',
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User'
                },
                {
                    id: 'xyz',
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User'
                }
            ]);

            sinon.stub(User.transaction, 'update').resolves();
            sinon.stub(Child.transaction, 'update').resolves();
            sinon.stub(dynamoose, 'transaction').resolves();

            request(process.env.BASE_URL)
                .put('/parent/invite-partner?status=accepted&childId=59985bdc-77ba-4911-ba98-d49230566445&inviterPartnerEmail=<EMAIL>')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childNewStub.restore();
                    childGetStub.restore();
                    userGetStub.restore();
                    orgSub.restore();
                    User.transaction.update.restore();
                    Child.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });

        it('As a user I should reject the request and get the pending partner list', (done) => {
            getStub.resolves({
                id: '123', status: 'active', isVerified: 1, role: 1, children: ['59985bdc-77ba-4911-ba98-d49230566444'],
                partnerInvites: [
                    {
                        inviterPartnerEmail: '<EMAIL>',
                        children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445', invitedAt: new Date() }],
                        inviterPartnerId: 'abc'
                    },
                    {
                        inviterPartnerEmail: '<EMAIL>',
                        children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566446', invitedAt: new Date() }],
                        inviterPartnerId: 'xyz'
                    }
                ],
                email: '<EMAIL>',
                sendInvites: [{
                    invitedPartnerEmail: '<EMAIL>',
                    children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445', status: 'accepted' }]
                }],
                save: () => Promise.resolve({
                    partnerInvites: [
                        {
                            inviterPartnerEmail: '<EMAIL>',
                            children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566446', invitedAt: new Date() }],
                            inviterPartnerId: 'xyz'
                        }
                    ]
                })
            });

            const childGetStub = sinon.stub(Child, 'batchGet').resolves([
                {
                    firstName: 'Test',
                    lastName: 'User',
                    associatedColor: '#FFFFFF',
                    id: '59985bdc-77ba-4911-ba98-d49230566445',
                    homeRoom: '9fd6a871-a6f5-4690-b06a-d786f1361eef',
                    school: '9fd6a871-a6f5-4690-b06a-d786f1361eef'
                },
                {
                    firstName: 'Test',
                    lastName: 'child',
                    associatedColor: '#FFFFFF',
                    id: '59985bdc-77ba-4911-ba98-d49230566446',
                    photoURL: 'http://test.com',
                    school: '9fd6a871-a6f5-4690-b06a-d786f1361eef'
                }
            ]);
            const orgSub = sinon.stub(Organization, 'get').resolves({
                id: 'abc',
                name: 'Test',
                category: CONSTANTS.CATEGORIES.SCHOOL
            });
            const userGetStub = sinon.stub(User, 'batchGet').resolves([
                {
                    id: 'abc',
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User'
                },
                {
                    id: 'xyz',
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User'
                }
            ]);

            sinon.stub(User.transaction, 'update').resolves();
            sinon.stub(dynamoose, 'transaction').resolves();

            request(process.env.BASE_URL)
                .put('/parent/invite-partner?status=rejected&childId=59985bdc-77ba-4911-ba98-d49230566445&inviterPartnerEmail=<EMAIL>')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childGetStub.restore();
                    userGetStub.restore();
                    orgSub.restore();
                    User.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });

        it('As a user I should get response list if child already exists', (done) => {
            getStub.resolves({
                id: '123', status: 'active', isVerified: 1, role: 1, children: ['59985bdc-77ba-4911-ba98-d49230566445'],
                partnerInvites: [
                    {
                        inviterPartnerEmail: '<EMAIL>',
                        children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445', invitedAt: new Date() }],
                        inviterPartnerId: 'abc'
                    },
                    {
                        inviterPartnerEmail: '<EMAIL>',
                        children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566446', invitedAt: new Date() }],
                        inviterPartnerId: 'xyz'
                    }
                ],
                email: '<EMAIL>',
                save: () => Promise.resolve({
                    partnerInvites: [
                        {
                            inviterPartnerEmail: '<EMAIL>',
                            children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566446', invitedAt: new Date() }],
                            inviterPartnerId: 'xyz'
                        }
                    ]
                })
            });

            const childGetStub = sinon.stub(Child, 'batchGet').resolves([
                {
                    firstName: 'Test',
                    lastName: 'User',
                    associatedColor: '#FFFFFF',
                    id: '59985bdc-77ba-4911-ba98-d49230566445',
                    homeRoom: '9fd6a871-a6f5-4690-b06a-d786f1361eef',
                    school: '9fd6a871-a6f5-4690-b06a-d786f1361eef'
                },
                {
                    firstName: 'Test',
                    lastName: 'child',
                    associatedColor: '#FFFFFF',
                    id: '59985bdc-77ba-4911-ba98-d49230566446',
                    photoURL: 'http://test.com',
                    school: '9fd6a871-a6f5-4690-b06a-d786f1361eef'
                }
            ]);
            const orgSub = sinon.stub(Organization, 'get').resolves({
                id: 'abc',
                name: 'Test',
                category: CONSTANTS.CATEGORIES.SCHOOL
            });
            const userGetStub = sinon.stub(User, 'batchGet').resolves([
                {
                    id: 'abc',
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User'
                },
                {
                    id: 'xyz',
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User'
                }
            ]);
            request(process.env.BASE_URL)
                .put('/parent/invite-partner?status=rejected&childId=59985bdc-77ba-4911-ba98-d49230566445&inviterPartnerEmail=<EMAIL>')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childGetStub.restore();
                    userGetStub.restore();
                    orgSub.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
describe('Send Invites Error', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get error if something goes wrong', (done) => {
            const scanStub = sinon.stub();
            const execStub = sinon.stub();

            scanStub.returns({ exec: execStub });
            execStub.rejects({ count: 1 });

            getStub.resolves({ status: 'active', isVerified: 1, role: 1 });

            request(process.env.BASE_URL)
                .get('/parent/send-invites')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
describe('Send Invites', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get send invite list with empty array', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, children: ['59985bdc-77ba-4911-ba98-d49230566445'],
                sendInvites: []
            });
            sinon.stub(Child, 'batchGet').resolves([{
                id: '59985bdc-77ba-4911-ba98-d49230566445',
                firstName: 'test',
                lastName: 'test'
            }]);
            request(process.env.BASE_URL)
                .get('/parent/send-invites')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.batchGet.restore();
                    done();
                });
        });
        it('As a user I should get send invite list with empty array', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, children: ['59985bdc-77ba-4911-ba98-d49230566445'],
                sendInvites: [
                    {
                        invitedPartnerEmail: '<EMAIL>',
                        children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445', status: 'pending' }]
                    },
                    {
                        invitedPartnerEmail: '<EMAIL>',
                        children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445', status: 'accepted' }]
                    }
                ]
            });
            sinon.stub(Child, 'batchGet').resolves([{
                id: '59985bdc-77ba-4911-ba98-d49230566445',
                firstName: 'test',
                lastName: 'test',
                photoURL: 'http://test.com'
            }]);
            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([])
                    })
                })
            });
            sinon.stub(PendingParentInvite, 'query').returns({
                eq: sinon.stub().returns({
                    exec: sinon.stub().resolves([])
                })
            });
            request(process.env.BASE_URL)
                .get('/parent/send-invites')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Child.batchGet.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Save send invites', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });
        it('As a user I should be able to send invite for another child', (done) => {
            getStub.resolves({
                id: 'userId1', status: 'active', isVerified: 1, role: 1, children: ['59985bdc-77ba-4911-ba98-d49230566445'],
                sendInvites: [
                    {
                        invitedPartnerEmail: '<EMAIL>',
                        children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445', status: 'pending' }]
                    },
                    {
                        invitedPartnerEmail: '<EMAIL>',
                        children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445', status: 'accepted' }, { childId: '59985bdc-77ba-4911-ba98-d49230566444', status: 'pending' }]
                    }
                ],
                partnerInvites: [{
                    inviterPartnerEmail: '<EMAIL>',
                    children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445' }]
                }]
            });

            sinon.stub(Child, 'get').resolves({ guardians: ['userId1'] });
            sinon.stub(Child.transaction, 'update').resolves();
            sinon.stub(dynamoose, 'transaction').resolves();

            request(process.env.BASE_URL)
                .put('/parent/send-invites')
                .set({ Authorization: requestPayloadUser.token })
                .send({ invitedEmail: '<EMAIL>', invitedId: '1234', childrenIds: ['childId2'] })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    Child.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });

        it('As a user I should be able to send invite for another child if already in partner invite', (done) => {
            getStub.resolves({
                id: 'userId1', status: 'active', isVerified: 1, role: 1, children: ['59985bdc-77ba-4911-ba98-d49230566445'],
                email: '<EMAIL>',
                sendInvites: [
                    {
                        invitedPartnerEmail: '<EMAIL>',
                        children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445', status: 'pending' }]
                    },
                    {
                        invitedPartnerEmail: '<EMAIL>',
                        children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445', status: 'accepted' }, { childId: '59985bdc-77ba-4911-ba98-d49230566444', status: 'pending' }]
                    }
                ],
                partnerInvites: [{
                    inviterPartnerEmail: '<EMAIL>',
                    children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445' }]
                }]
            });

            sinon.stub(Child, 'get').resolves({ guardians: ['userId1'] });
            sinon.stub(Child.transaction, 'update').resolves();
            sinon.stub(dynamoose, 'transaction').resolves();

            request(process.env.BASE_URL)
                .put('/parent/send-invites')
                .set({ Authorization: requestPayloadUser.token })
                .send({ invitedEmail: '<EMAIL>', invitedId: '1234', childrenIds: ['childId2'] })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    Child.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });

        it('As a user I should be able to revoke every child access', (done) => {
            getStub.resolves({
                id: 'userId1', status: 'active', isVerified: 1, role: 1, children: ['59985bdc-77ba-4911-ba98-d49230566445'],
                sendInvites: [
                    {
                        invitedPartnerEmail: '<EMAIL>',
                        children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445', status: 'pending' }]
                    },
                    {
                        invitedPartnerEmail: '<EMAIL>',
                        children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445', status: 'accepted' }, { childId: '59985bdc-77ba-4911-ba98-d49230566444', status: 'pending' }]
                    }
                ],
                partnerInvites: [{
                    inviterPartnerEmail: '<EMAIL>',
                    children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445' }]
                }]
            });

            sinon.stub(Child, 'get').resolves({ guardians: ['userId1'] });
            sinon.stub(User.transaction, 'update').resolves();
            sinon.stub(Child.transaction, 'update').resolves();
            sinon.stub(dynamoose, 'transaction').resolves();

            request(process.env.BASE_URL)
                .put('/parent/send-invites')
                .set({ Authorization: requestPayloadUser.token })
                .send({ invitedEmail: '<EMAIL>', invitedId: '1234', childrenIds: [] })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.get.restore();
                    User.transaction.update.restore();
                    Child.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Remove member', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get error if send invites accepted exists', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, children: ['59985bdc-77ba-4911-ba98-d49230566445'],
                sendInvites: [{
                    invitedPartnerEmail: '<EMAIL>',
                    children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445', status: 'accepted' }]
                }]
            });
            request(process.env.BASE_URL)
                .delete('/parent/remove-member?invitedEmail=<EMAIL>&invitedId=59985bdc-77ba-4911-ba98-d49230566445')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should remove member', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, children: ['59985bdc-77ba-4911-ba98-d49230566445'],
                email: '<EMAIL>',
                sendInvites: [{
                    invitedPartnerEmail: '<EMAIL>',
                    children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445', status: 'pending' }]
                }],
                partnerInvites: []
            });
            sinon.stub(User.transaction, 'update').resolves();
            sinon.stub(dynamoose, 'transaction').resolves();
            request(process.env.BASE_URL)
                .delete('/parent/remove-member?invitedEmail=<EMAIL>&invitedId=59985bdc-77ba-4911-ba98-d49230566445')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    User.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });

        it('As a user I should remove member', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, children: ['59985bdc-77ba-4911-ba98-d49230566445'],
                email: '<EMAIL>',
                sendInvites: [{
                    invitedPartnerEmail: '<EMAIL>',
                    children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445', status: 'pending' }]
                }],
                partnerInvites: [{
                    inviterPartnerEmail: '<EMAIL>',
                    children: [{ childId: '59985bdc-77ba-4911-ba98-d49230566445' }]
                }]
            });
            sinon.stub(User.transaction, 'update').resolves();
            sinon.stub(dynamoose, 'transaction').resolves();

            request(process.env.BASE_URL)
                .delete('/parent/remove-member?invitedEmail=<EMAIL>&invitedId=59985bdc-77ba-4911-ba98-d49230566445')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    User.transaction.update.restore();
                    dynamoose.transaction.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
