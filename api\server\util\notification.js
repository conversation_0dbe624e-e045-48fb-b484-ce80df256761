/**
 * This class represents all the notification services functions
 */
class Notification {
    /**
     * @desc This function is being used to create a notification object
     * <AUTHOR>
     * @since 01/03/2024
     * @param {String} topic
     * @param {String} title
     * @param {String} body
     * @param {Object} data
     * @param {String} clickAction
     */
    static createNotificationObject ({ topic, title, body, data, clickAction }) {
        return {
            topic,
            data,
            notification: {
                title,
                body
            },
            android: {
                notification: {
                }
            },
            apns: {
                payload: {
                    aps: {
                        category: clickAction
                    }
                }
            }
        };
    }
}

module.exports = Notification;
