const User = require('../models/user.model');
const Group = require('../models/groups.model');
const { SQSClient, SendMessageCommand } = require('@aws-sdk/client-sqs');
const config = { region: process.env.AWS_REGION };
const sqsClient = new SQSClient(config);

class SQSPushNotificationService {
    static async sendGroupMessage (senderId, groupId, message, groupMemberIds) {
        const senderName = await this.getSenderName(senderId);
        const groupName = await this.getGroupName(groupId);
        const trimmedMessage = message.length > 100 ? message.substring(0, 100) + '...' : message;
        const pushMessageNotificationToSQSPromise = groupMemberIds
            .filter((userId) => userId !== senderId)
            .map(async (userId) => {
                const notificationObject = {
                    topicKey: `userId-${userId}`,
                    trigger: 'conversation',
                    messageText: trimmedMessage,
                    senderName,
                    groupId,
                    groupName
                };
                const input = {
                    QueueUrl: process.env.QUEUE_URL,
                    MessageBody: JSON.stringify(notificationObject)
                };
                const command = new SendMessageCommand(input);
                await sqsClient.send(command);
            });
        Promise.all(pushMessageNotificationToSQSPromise);
    }

    static async getSenderName (senderId) {
        const senderDetails = await User.query('id').eq(senderId).attributes(['firstName', 'lastName']).exec();
        if (!senderDetails || senderDetails.length === 0) {
            return '';
        }
        return this.getName(senderDetails[0]);
    }

    static getName (userDetails) {
        const firstName = userDetails?.firstName ? userDetails.firstName : '';
        const lastName = userDetails?.lastName ? ' ' + userDetails.lastName : '';
        return firstName + lastName;
    }

    static async getGroupName (groupId) {
        const groupDetails = await Group.query('groupId').eq(groupId)
            .attributes(['groupType', 'organizationMetaData', 'eventMetaData']).exec();
        if ( !groupDetails || groupDetails.length === 0) {
            return 'Deleted Group';
        }
        const isOrgGroup = [CONSTANTS.GROUP_TYPES.ORGANIZATION, CONSTANTS.GROUP_TYPES.ORGANIZATION_ADMIN]
            .includes(groupDetails[0].groupType);
        return isOrgGroup ? groupDetails[0].organizationMetaData?.name : groupDetails[0].eventMetaData?.title;
    }

    static async sendPersonalMessage (receiverId, senderId, senderDetails, conversationId, message) {
        const senderName = senderDetails ? this.getName(senderDetails) : await this.getSenderName(senderId);
        const trimmedMessage = message.length > 100 ? message.substring(0, 100) + '...' : message;
        const notificationObject = {
            topicKey: `userId-${receiverId}`,
            trigger: 'personalConversation',
            messageText: trimmedMessage,
            senderName,
            conversationId,
            receiverId,
            senderId
        };
        const input = {
            QueueUrl: process.env.QUEUE_URL,
            MessageBody: JSON.stringify(notificationObject)
        };
        const command = new SendMessageCommand(input);
        await sqsClient.send(command);
    }
}

module.exports = SQSPushNotificationService;
