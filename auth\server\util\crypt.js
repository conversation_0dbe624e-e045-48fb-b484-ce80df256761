const JWT = require('./jwt');

class Crypt {
    /**
     * This function is being used to save user detail before login
     * <AUTHOR>
     * @param {Object} user user
     * @param {function} callback callback
     * @since 07/12/2023
     */
    static async getUserToken (user) {
        const token = JWT.generate({
            id: user.id,
            email: user.email,
            role: user.role,
            accessLevel: user.accessLevel,
            firstName: user.firstName,
            lastName: user.lastName,
            isVerified: user.isVerified,
            status: user.status,
            isDeleted: user.isDeleted,
            photoURL: user.photoURL,
            fcmToken: user.fcmToken,
            sub: user.id
        });

        return {
            token
        };
    }

    /**
     * This function is being used to get conversation token
     * <AUTHOR>
     * @param {String} userId user id
     * @since 05/11/2024
     */
    static async getConversationToken (userId) {
        const token = JWT.generate({
            id: userId
        }, 60);

        return token;
    }
}

module.exports = Crypt;
