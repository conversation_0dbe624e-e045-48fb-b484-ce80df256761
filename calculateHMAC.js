const dotenv = require('dotenv');
const env = process.env.NODE_ENV || 'local';
dotenv.config({ path: env + '.env' });

const crypto = require('crypto');

const secretKey = process.env.HMAC_SECRET_KEY;

// parsing command line arguments
const args = process.argv.slice(2);

if (args.length === 0) {
    console.error('Please provide a URL.');
    process.exit(1);
}

const inputUrl = args[0];
console.log('URL:', inputUrl);


const currentDate = new Date().toISOString().slice(0, 10).replace(/-/g, '');
const MMYYYYDD = `${currentDate.slice(4, 6)}${currentDate.slice(0, 4)}${currentDate.slice(6)}`;
const message = MMYYYYDD + inputUrl;
const calculatedHmac = crypto.createHmac('sha256', secretKey).update(message).digest('hex');

// output to console
console.log('Calculated HMAC:', calculatedHmac);
