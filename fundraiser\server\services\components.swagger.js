/**
 * Contains security and schemas for error or success message definitions
 */

/**
 * @openapi
 * components:
 *   securitySchemes:
 *      bearerAuth:
 *          name: 'Authorization'
 *          type: apiKey
 *          in: header
 *          bearerFormat: JWT
 *
 *   security:
 *      - bearerAuth: []
 *
 *   messageDefinition:
 *          type: object
 *          properties:
 *              status:
 *                  type: number
 *                  description: error/success code (0-error, 1-success)
 *              message:
 *                  type: string
 *                  description: error/success message
 *              data:
 *                  type: string
 *                  description: testing email
 *              email:
 *                  type: string
 *                  description: unique email address
 *              phoneNumber:
 *                  type: string
 *                  description: unique mobile phone number
 *              otp:
 *                  type: number
 *                  description: one time password
 *              token:
 *                  type: string
 *                  description: token for signup and login
 *              password:
 *                 type: string
 *                 description: password for signup and login
 *              responseData:
 *                  type: object
 *
 *   schemas:
 *
 *      errorBadRequest:
 *          properties:
 *              status:
 *                 $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Request is invalid
 *
 *      errorUserRegister:
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: This user is already registered with us.
 *
 *      unexpectedError:
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Something went wrong. please try again.
 *
 *      agencySignUp:
 *          properties:
 *              email:
 *                  $ref: '#/components/messageDefinition/properties/email'
 *              password:
 *                  $ref: '#/components/messageDefinition/properties/password'
 *              token:
 *                  $ref: '#/components/messageDefinition/properties/token'
 *      successUserRegister:
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: User registration successful
 *
 *      userVerify:
 *          type: object
 *          properties:
 *              email:
 *                  $ref: '#/components/messageDefinition/properties/email'
 *              otp:
 *                  $ref: '#/components/messageDefinition/properties/otp'
 *              type:
 *                  type: string
 *                  description: type of otp mobile/email
 *          example:
 *              email:
 *                  $ref: '#/components/schemas/userSignUp/example/email'
 *              otp: 123456
 *              fcmToken: "fcmToken"
 *
 *      userOtpVerify:
 *          type: object
 *          properties:
 *              email:
 *                  $ref: '#/components/messageDefinition/properties/email'
 *              otp:
 *                  $ref: '#/components/messageDefinition/properties/otp'
 *              type:
 *                  type: string
 *                  description: type of otp email/mobile
 *          example:
 *              email:
 *                  $ref: '#/components/schemas/userSignUp/example/email'
 *              otp: 123456
 *
 *      successVerifyUser:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *              data:
 *                  type: object
 *                  description: success data
 *          example:
 *              status: 1
 *              data:
 *                 {
 *                      lastName: "Doe",
 *                      role: 1,
 *                      isVerified: 1,
 *                      accessToken: "accessToken",
 *                      firstName: "John",
 *                      phoneNumber: "8974568971",
 *                      countryCode: "+91",
 *                      id: "id",
 *                      fcmToken: "fcmToken",
 *                      email: "<EMAIL>",
 *                      refreshToken: "refreshToken",
 *                      status: "active",
 *                      updatedAt: "2023-10-17T07:27:27.065Z"
 *                  }
 *              message: Success
 *
 *
 *      resendOTPEmail:
 *          type: object
 *          properties:
 *              email:
 *                  $ref: '#/components/messageDefinition/properties/email'
 *              otpType:
 *                  type: string
 *                  description: type of otp
 *          example:
 *              email:
 *                  $ref: '#/components/schemas/userSignUp/example/email'
 *              otpType: type of otp
 *
 *      successResendOTP:
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Email resend successful
 *      successLogin:
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *              data:
 *                  $ref: '#/components/messageDefinition/properties/responseData'
 *          example:
 *              status: 1
 *              message: User successfully logged in
 *              data:
 *                  id: eda833cf-f0bf-4af9-b211-9eb5cf20b46b
 *                  firstName: Same
 *                  lastName: Jones
 *                  email: <EMAIL>
 *                  countryCode: "+91"
 *                  phoneNumber: "8974568971"
 *                  role: 1
 *                  token: TOKEN
 *                  refreshToken: REFRESH_TOKEN
 *
 *      unauthorisedAccess:
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Email & Password do not match.
 *
 *      unauthorisedAccessLogin:
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Email & Password do not match.
 *
 *      userForgotPassword:
 *          type: object
 *          properties:
 *              email:
 *                  $ref: '#/components/messageDefinition/properties/email'
 *          example:
 *              email: <EMAIL>
 *
 *      errorUserNotFound:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Email address/phone number not found. Please enter a registered email address/phone number.
 *
 *      userNotFound:
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: User not found. Please enter a registered email address.
 *
 *      userVerifyToken:
 *          type: object
 *          properties:
 *              token:
 *                  $ref: '#/components/messageDefinition/properties/token'
 *          example:
 *              token: 4hoR8EAXYEbT
 *
 *      userResetPassword:
 *          type: object
 *          properties:
 *              email:
 *                  $ref: '#/components/messageDefinition/properties/email'
 *              password:
 *                  $ref: '#/components/messageDefinition/properties/password'
 *          example:
 *              email: <EMAIL>
 *              password: password
 *
 *      passwordInvalid:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Please enter password.
 *
 *      errorForgotPassword:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Please enter email address
 *
 *      successForgotPassword:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: OTP sent successfully
 *
 *      successVerifyToken:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Link validated successfully.
 *
 *      errorVerifyToken:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Link has expired, kindly reset password again.
 *
 *      successResetPassword:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Your password has been reset successfully.
 *
 *      successUserDetails:
 *          type: object
 *          properties:
 *              status:
 *                  type: boolean
 *                  description: status if user is present
 *              data:
 *                  type: string
 *                  description: details of the user
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data:
 *                  id : id
 *                  email : email
 *                  firstName : Sam
 *                  lastName: Jones
 *                  phoneNumber : phone number
 *                  photoURL : url
 *                  role: 1
 *                  status: status
 *              message: Success
 *
 *      successUserUpdateDetails:
 *          type: object
 *          properties:
 *              status:
 *                  type: boolean
 *                  description: status if user is present
 *              data:
 *                  type: string
 *                  description: details of the user
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: true
 *              data:
 *                  email : email
 *                  phoneNumber: +122345677890
 *                  isEmailVerified: false
 *                  isMobileVerified: false
 *              message: Success
 *
 *
 *      successUserList:
 *          type: object
 *          properties:
 *              status:
 *                  type: number
 *                  description: status if data exists
 *              data:
 *                  type: string
 *                  description: user data
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data:
 *                  docs : [{
 *                          id:
 *                              id,
 *                          email:
 *                              <EMAIL>,
 *                          first_name:
 *                              John,
 *                          last_name:
 *                              Doe,
 *                          phone_number:
 *                              1234567890,
 *                          date_of_birth:
 *                              22-2-2000,
 *                          is_enabled:
 *                              1
 *                          }]
 *                  page: 1
 *                  totalDocs: 5
 *                  totalPages: 3
 *                  limit: 2
 *              message: Success
 *
 *
 *      successUploadProfilePicture:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: object
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *
 *          example:
 *              status: 1
 *              data:
 *                  profilePicture: s3url
 *              message: Success
 *
 *      successDeleteProfilePicture:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Success
 *
 *      changePassword:
 *          type: object
 *          properties:
 *              oldPassword:
 *                  type: string
 *              newPassword:
 *                  type: string
 *          example:
 *              oldPassword: OLD PASSWORD SHA256
 *              newPassword: NEW PASSWORD SHA256
 *
 *      successChangePassword:
 *          type: object
 *          properties:
 *              status:
 *                  type: boolean
 *                  description: status if user is present
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: true
 *              message: Password changed successfully
 *
 *      inValidFiledError:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: <Field> is not valid.
 *
 *      validationError:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: <Field> can't be blank.
 *
 *      unauthorisedAccessUser:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: You are not authorized to access this resource.
 *
 */
