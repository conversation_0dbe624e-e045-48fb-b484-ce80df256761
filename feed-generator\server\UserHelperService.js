const RedisUtil = require('./redisUtil');
const Utils = require('./Utils');

/**
 * <AUTHOR>
 * @since 04/03/2025
 * @description A helper service for user details
 */
class UserHelperService {
    /**
     * <AUTHOR>
     * @since 04/03/2025
     * @description Adds or updates the user details in the cache
     * @param {Object} redis - The Redis client
     * @param {Object} userDetails - The user details to add or update
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<Object>} The result of the Redis operation
     */
    static async addOrUpdateUserDetails (redis, userDetails, versionPrefix) {
        const { id: userId } = userDetails;
        const userDetailsKey = Utils.getUserDetailsKey({ versionPrefix, userId });
        const formattedUserDetails = this.getFormattedUserDetails({ userDetails });

        return await RedisUtil.setHashValue(redis, userDetailsKey, 'details', formattedUserDetails);
    }

    /**
     * <AUTHOR>
     * @since 04/03/2025
     * @description Handles the user details update
     * @param {Object} redis - The Redis client
     * @param {Object} oldUserDetails - The old user details
     * @param {Object} newUserDetails - The new user details
     * @returns {Promise<Object>} The result of the Redis operation
     */
    static async handleUserDetailsUpdate (redis, oldUserDetails, newUserDetails, versionPrefix) {
        if (oldUserDetails.isDeleted === 0 && newUserDetails.isDeleted === 1) {
            return await this.removeUserDetails(redis, oldUserDetails, versionPrefix);
        } else if (oldUserDetails.isDeleted === 1 && newUserDetails.isDeleted === 0) {
            return await this.addOrUpdateUserDetails(redis, newUserDetails, versionPrefix);
        } else {
            return await this.addOrUpdateUserDetails(redis, newUserDetails, versionPrefix);
        }
    }

    /**
     * <AUTHOR>
     * @since 04/03/2025
     * @description Removes the user details from the cache
     * @param {Object} redis - The Redis client
     * @param {Object} userDetails - The user details to remove
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<Object>} The result of the Redis operation
     */
    static async removeUserDetails (redis, userDetails, versionPrefix) {
        const { id: userId } = userDetails;
        const userDetailsKey = Utils.getUserDetailsKey({ versionPrefix, userId });
        const pipeline = redis.pipeline();

        RedisUtil.deleteKey(pipeline, userDetailsKey);

        return await pipeline.exec();
    }

    /**
     * <AUTHOR>
     * @since 04/03/2025
     * @description Formats the user details
     * @param {Object} userDetails - The user details to format
     * @returns {Object} The formatted user details
     */
    static getFormattedUserDetails ({ userDetails }) {
        const { id, email, firstName, lastName, status, isDeleted, children, associatedOrganizations, accessLevel } = userDetails;
        return {
            id,
            email,
            firstName,
            lastName,
            status,
            isDeleted,
            children,
            associatedOrganizations,
            accessLevel
        };
    }
}

module.exports = UserHelperService;
