/**
 * This file contains routes used for parent user.
 * Created by Growexx on 19/10/2023.
 * @name parentRoutes
 */
const router = require('express').Router();

const ParentController = require('../services/parent/parentController');

const AuthMiddleware = require('../middleware/auth');
const UploadMiddleware = require('../middleware/upload');

router.get('/get-school-list', AuthMiddleware, ParentController.getSchoolList);
router.post('/add-child', AuthMiddleware, UploadMiddleware.single('photo'), ParentController.addChild);
router.post('/invite-partner', AuthMiddleware, ParentController.invitePartner);
router.get('/invite-partner', AuthMiddleware, ParentController.getInvitePartnerList);
router.put('/invite-partner', AuthMiddleware, ParentController.updateInvitePartner);

router.get('/send-invites', AuthMiddleware, ParentController.getSendInvites);
router.put('/send-invites', AuthMiddleware, ParentController.saveSendInvites);

router.delete('/remove-member', AuthMiddleware, ParentController.removeMember);

module.exports = router;
