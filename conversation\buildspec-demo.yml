version: 0.2
environment_variables:
  plaintext:
     S3_BUCKET: "vaalee-demo-be-artifacts"
     FUNCTION_NAME: "demo-vaalee-conversation-function"
env:
  parameter-store:
     GITHUB_PACKAGE_TOKEN: 'GITHUB_PACKAGE_TOKEN'
phases:
  install:
      runtime-versions:
       nodejs: 18

  pre_build:
    commands:
      - echo install node packages and pre-build commands
      - cd conversation  
      - echo "//npm.pkg.github.com/:_authToken=$GITHUB_PACKAGE_TOKEN" >> ~/.npmrc
      - echo "@incubyte:registry=https://npm.pkg.github.com" >> ~/.npmrc
      - npm install --prod

  build:
    commands:
      - zip -r demo-vaalee-conversation-function.zip index.js node_modules emailTemplates server
      - ls -la
      - pwd

  post_build:
    commands:
      - echo Entering Post_Build Phase
      - aws s3 cp demo-vaalee-conversation-function.zip s3://$S3_BUCKET/conversation/
      - aws lambda update-function-code --function-name "$FUNCTION_NAME" --s3-bucket $S3_BUCKET --s3-key conversation/demo-vaalee-conversation-function.zip  

artifacts:
   files:
    - '**/*'
