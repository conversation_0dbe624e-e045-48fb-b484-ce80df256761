const ChildService = require('./childService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for child routes.
 */
class ChildController {
    /**
     * @desc This function is being used to get child details
     * <AUTHOR>
     * @since 22/10/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async getChildDetails (req, res) {
        try {
            const data = await ChildService.getChildDetails(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get child details', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to check child existence
     * <AUTHOR>
     * @since 27/10/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async validateChildExists (req, res) {
        try {
            const data = await ChildService.validateChildExists(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.ADD_CHILD_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in validate child exists', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to send follow request to a child
     * <AUTHOR>
     * @since 19/12/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async sendFollowRequestToChild (req, res) {
        try {
            const data = await ChildService.sendFollowRequestToChild(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.FOLLOW_REQUEST_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in send follow request', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to change follow request status of a child by parent
     * <AUTHOR>
     * @since 20/12/2023
     * @name changeFollowRequestStatus
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async changeFollowRequestStatus (req, res) {
        try {
            const data = await ChildService.changeFollowRequestStatus(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.FOLLOW_REQUEST_STATUS_CHANGED);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in change follow request status', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to search child by parent within same school of parent's child
     * @name searchChild
     * <AUTHOR>
     * @since 22/12/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async searchChild (req, res) {
        try {
            const data = await ChildService.searchChild(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in search child', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to get relationships of a child by parent
     * @name getRelationships
     * <AUTHOR>
     * @since 22/12/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async getRelationships (req, res) {
        try {
            const data = await ChildService.getRelationships(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get child connection relationships', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to unfollow a child by parent
     * @name unfollowChild
     * <AUTHOR>
     * @since 26/12/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async unfollowChild (req, res) {
        try {
            const data = await ChildService.unfollowChild(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.CHILD_UNFOLLOW_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in unfollow child', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to update child details by parent
     * <AUTHOR>
     * @since 19/12/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async updateChildDetails (req, res) {
        try {
            const data = await ChildService.updateChildDetails(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in update child details: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used remove follower of a child by parent
     * @name removeFollower
     * <AUTHOR>
     * @since 28/12/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async removeFollower (req, res) {
        try {
            const data = await ChildService.removeFollower(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.FOLLOWER_REMOVED_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in remove follower', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is used to search child in organization by parent
     * @name searchChildInOrganization
     * <AUTHOR>
     * @since 16/01/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async searchChildInOrganization (req, res) {
        try {
            const data = await ChildService.searchChildInOrganization(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in search child in org:', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is used to send connection request to a child by parent
     * @name sendConnectionRequest
     * <AUTHOR>
     * @since 16/01/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async sendConnectionRequest (req, res) {
        try {
            const data = await ChildService.sendConnectionRequest(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.CONNECTION_REQUEST_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in send connection request', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function used to get child connections
     * @name getChildConnections
     * <AUTHOR>
     * @since 17/01/2024
     * @param {Object} req Request
     * @param {function} res Response
    */
    static async getChildConnections (req, res) {
        try {
            const data = await ChildService.getChildConnections(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get child connections', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function used to change connection request status
     * @name changeConnectionRequestStatus
     * <AUTHOR>
     * @since 17/01/2024
     * @param {Object} req Request
     * @param {function} res Response
    */
    static async changeConnectionRequestStatus (req, res) {
        try {
            const data = await ChildService.changeConnectionRequestStatus(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.CONNECTION_REQUEST_STATUS_CHANGED);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in change connection request status:', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function used to remove connection
     * @name removeConnection
     * <AUTHOR>
     * @since 22/01/2024
     * @param {Object} req Request
     * @param {function} res Response
    */
    static async removeConnection (req, res) {
        try {
            const data = await ChildService.removeConnection(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.CONNECTION_REMOVED_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in remove connection', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function used to get associated organizations of a child
     * @name getAssociatedOrganizations
     * <AUTHOR>
     * @since 22/01/2024
     * @param {Object} req Request
     * @param {function} res Response
    */
    static async getAssociatedOrganizations (req, res) {
        try {
            const data = await ChildService.getAssociatedOrganizations(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get associated organizations', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function used to generate presigned url for child
     * @name generatePresignedUrl
     * <AUTHOR>
     * @since 30/12/2024
     * @param {Object} req Request
     * @param {function} res Response
    */
    static async generatePresignedUrl (req, res) {
        try {
            const data = await ChildService.generatePresignedUrl(req, res.locals.user);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in generate presigned url', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = ChildController;
