module.exports = {
    addUpdateAiCompanionScreenFeedback: [
        {
            it: 'As a user I should validate if aiResponseMessageId is not passed',
            options: {},
            status: 0
        },
        {
            it: 'As a user I should validate if userQueryMessageId is not passed',
            options: {
                aiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if userQueryMessage is not passed',
            options: {
                aiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                userQueryMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if aiResponseMessage is not passed',
            options: {
                aiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                userQueryMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                userQueryMessage: 'Hello'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if aiResponseMessageContexts is not passed',
            options: {
                aiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                userQueryMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                userQueryMessage: 'Hello',
                aiResponseMessage: 'Hello, how can I assist you today?'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if feedback is not passed',
            options: {
                aiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                userQueryMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                userQueryMessage: 'Hello',
                aiResponseMessage: 'Hello, how can I assist you today?',
                aiResponseMessageContexts: '[{"source": {"url": "https://test.source.com"}}]'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if feedback is not valid',
            options: {
                aiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                userQueryMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                userQueryMessage: 'Hello',
                aiResponseMessage: 'Hello, how can I assist you today?',
                aiResponseMessageContexts: '[{"source": {"url": "https://test.source.com"}}]',
                feedback: 'invalid'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if aiResponseMessageId is not valid',
            options: {
                aiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                userQueryMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                userQueryMessage: 'Hello',
                aiResponseMessage: 'Hello, how can I assist you today?',
                aiResponseMessageContexts: '[{"source": {"url": "https://test.source.com"}}]',
                feedback: 'positive'
            },
            status: 0
        }
    ],
    getAiCompanionScreenFeedbackList: [
        {
            it: 'As a user I should validate if startAiResponseMessageId is not valid',
            options: {
                startAiResponseMessageId: 'invalid'
            },
            status: 0
        }
    ]
};
