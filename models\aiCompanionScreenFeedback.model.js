const dynamoose = require('dynamoose');
const CONSTANTS = require('../util/constants');

const aiCompanionScreenFeedbackSchema = new dynamoose.Schema({
    aiResponseMessageId: {
        type: String,
        hashKey: true
    },
    userId: {
        type: String,
        rangeKey: true,
        index: {
            name: 'userId-index',
            global: true,
            project: true
        }
    },
    userQueryMessageId: {
        type: String,
        required: true
    },
    userQueryMessage: {
        type: String,
        required: true
    },
    aiResponseMessage: {
        type: String,
        required: true
    },
    aiResponseMessageContexts: {
        type: String
    },
    feedback: {
        type: String,
        required: true,
        enum: CONSTANTS.AI_COMPANION_FEEDBACK_VALUES
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('AiCompanionScreenFeedbacks', aiCompanionScreenFeedbackSchema);
