const validation = require('../../util/validation');
const GeneralError = require('../../util/GeneralError');

/**
 * Class represents validations for child.
 */
class ChildValidator extends validation {
    constructor (body, locale, query, file) {
        super(locale);
        this.body = body;
        this.file = file;
        this.query = query;
        this.locale = locale;
    }

    /**
     * @desc This function is being used to validate follow request to a child
     * <AUTHOR>
     * @since 20/12/2023
     */
    validateFollowRequest () {
        const { followerChildId, followingChildId } = this.body;
        super.uuid(followerChildId, 'Follower Child Id');
        super.uuid(followingChildId, 'Following Child Id');
    }

    /**
     * @desc This function is being used to validate send connection request to a child by parent
     * <AUTHOR>
     * @since 16/01/2024
    */
    validateConnectionRequest () {
        const { requesterChildId, requestedChildId } = this.body;
        super.uuid(requesterChildId, 'Requester Child Id');
        super.uuid(requestedChildId, 'Requested Child Id');
    }

    /**
     * @desc This function is being used to validate change follow request status of a child by parent
     * @name validateChangeFollowRequestStatus
     * <AUTHOR>
     * @since 21/12/2023
     */
    validateChangeFollowRequestStatus () {
        const { followerChildId, followingChildId, status } = this.body;
        super.uuid(followerChildId, 'Follower Child Id');
        super.uuid(followingChildId, 'Following Child Id');
        super.field(status, 'Status');
        super.enum(status.trim(), ['accepted', 'rejected'], 'Status');
    }

    /**
     * @desc This function is being used to validate change connection request status of a child by parent
     * @name validateChangeConnectionRequestStatus
     * <AUTHOR>
     * @since 17/01/2024
     */
    validateChangeConnectionRequestStatus () {
        const { requesterChildId, requestedChildId, status } = this.body;
        super.uuid(requesterChildId, 'Requester Child Id');
        super.uuid(requestedChildId, 'Requested Child Id');
        super.field(status, 'Status');
        super.enum(status.trim(), ['connected', 'rejected'], 'Status');
    }

    /**
     * @desc This function is being used to validate search child by parent within same school of parent's child
     * @name searchChild
     * <AUTHOR>
     * @since 22/12/2023
     */
    validateSearchChild () {
        const { childId, searchValue } = this.query;
        super.uuid(childId, 'Child Id');
        super.field(searchValue, 'Search Value');
    }

    /**
     * @desc This function is being used to validate get relationships request
     * @name validateGetRelationships
     * <AUTHOR>
     * @since 26/12/2023
     */
    validateGetRelationships () {
        const { childId, relationshipType } = this.query;
        super.uuid(childId, 'Child Id');
        super.enum(relationshipType, ['followers', 'followings', 'requestedBy', 'requestedTo'], 'Relationship Type');
    }

    /**
     * @desc This function is being used to validate get child connections request
     * @name validateGetChildConnections
     * <AUTHOR>
     * @since 17/01/2024
     */
    validateGetChildConnections () {
        const { childId, connectionType } = this.query;
        super.uuid(childId, 'Child Id');
        super.enum(connectionType, ['requestedBy', 'requestedTo', 'connected'], 'Connection Type');
    }

    /**
     * @desc This function is being used to validate request for add child
     * <AUTHOR>
     * @since 19/12/2023
     */
    validateUpdateChild () {
        const { childId, firstName, lastName, dob, schoolId, zipCode, associatedColor } = this.body;
        super.uuid(childId, 'Child Id');
        super.name(firstName, 'First Name');
        super.name(lastName, 'Last Name');
        if (dob && !MOMENT(dob, 'MM/DD/YYYY', true).isValid()) {
            throw new GeneralError(this.locale(MESSAGES.DATE_FORMAT_ERROR, 'Date of Birth'), 400);
        }
        super.uuid(schoolId, 'School Name');
        super.field(zipCode, 'Zip Code');
        super.enum(associatedColor.trim(), CONSTANTS.ALLOWED_ASSOCIATED_COLORS, 'Associated Color');
        if (this.file) {
            this.fileType(this.file);
        }
    }

    /**
     * @desc This function is being used to validate event image fileType
     * <AUTHOR>
     * @since 19/12/2023
     * @param {String} mimeType mimeType
     */
    fileType (file) {
        if (!file.mimetype || CONSTANTS.PROFILE_PICTURE.ALLOWED_TYPE.indexOf(file.mimetype) === -1) {
            throw {
                message: MESSAGES.INVALID_FILE_FORMAT,
                statusCode: 400
            };
        }
    }
}


module.exports = ChildValidator;
