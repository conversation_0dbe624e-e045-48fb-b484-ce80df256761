const GroupMembers = require('../../models/groupMembers.model');
const SocketConnections = require('../../models/socketConnections.model');
const {
    ApiGatewayManagementApiClient,
    PostToConnectionCommand
} = require('@aws-sdk/client-apigatewaymanagementapi');
const apigateway = new ApiGatewayManagementApiClient({
    endpoint: process.env.SOCKET_API_GATEWAY_ENDPOINT
});
const CONSTANTS = require('../../util/constants');
const { v4: uuidv4 } = require('uuid');

class ConversationService {
    static async getGroupMemberIds (groupId) {
        return (
            await GroupMembers.query('groupId')
                .eq(groupId)
                .using('groupId-index')
                .attributes(['userId'])
                .exec()
        ).map((member) => member.userId);
    }

    static async getActiveSocketConnections (groupMemberIds) {
        const socketConnectionPromises = groupMemberIds.map(async (userId) => {
            return SocketConnections.query('userId')
                .eq(userId)
                .using('userId-index')
                .exec();
        });
        return (await Promise.all(socketConnectionPromises)).flat();
    }

    static async sendMessagesToConnections (activeSocketConnections, messageData, statusCode = 200) {
        const sendMessagePromises = activeSocketConnections.map(
            async (connection) => {
                const connectionId = connection.id;
                await this.sendMessageToConnection({
                    connectionId,
                    messageData,
                    statusCode
                });
            }
        );
        await Promise.all(sendMessagePromises);
    }

    /**
     * @description Sends a message to a connection
     * @param {Object} params
     * @param {string} params.connectionId
     * @param {Object} params.messageData
     * @param {number} [params.statusCode]
     * @param {boolean} [params.isChunk]
     * @param {number} [params.chunkIndex]
     * @param {number} [params.totalChunks]
     * @returns {Promise<void>}
     */
    static async sendMessageToConnection ({
        connectionId,
        messageData,
        statusCode = 200,
        isChunk = false,
        chunkIndex = 0,
        totalChunks = 0,
        chunkKey = null
    }) {
        const stringifiedMessageData = isChunk
            ? JSON.stringify({ chunk: messageData, chunkIndex, totalChunks, isChunk, chunkKey })
            : JSON.stringify({ ...messageData, statusCode, isChunk });

        if (!isChunk && Buffer.byteLength(stringifiedMessageData, 'utf8') > CONSTANTS.ALLOWED_MESSAGE_SIZE) {
            const generatedChunkKey = uuidv4();
            const chunks = this.chunkMessageData(stringifiedMessageData);
            for (const [index, chunk] of chunks.entries()) {
                await this.sendMessageToConnection({
                    connectionId,
                    statusCode,
                    chunkKey: generatedChunkKey,
                    messageData: chunk,
                    isChunk: true,
                    chunkIndex: index,
                    totalChunks: chunks.length
                });
            }
        } else {
            const postParams = {
                ConnectionId: connectionId,
                Data: stringifiedMessageData
            };
            try {
                await apigateway.send(new PostToConnectionCommand(postParams));
            } catch (error) {
                if (error.statusCode === 410) {
                    await SocketConnections.delete({ id: connectionId });
                    CONSOLE_LOGGER.error(`--> Stale connection removed --> ${connectionId}`);
                } else {
                    CONSOLE_LOGGER.error(`--> Error while sending message to connection --> ${connectionId}`, error);
                }
            }
        }
    }

    /**
     * @description Chunks a message data into chunks of ALLOWED_MESSAGE_SIZE, ensuring each chunk (with metadata) is under the limit
     * @param {string} data
     * @returns {Array}
     */
    static chunkMessageData (data) {
        const MAX_CHARS_ESTIMATE = Math.floor(CONSTANTS.ALLOWED_MESSAGE_SIZE / 4);
        const chunks = [];
        let currentIndex = 0;
        while (currentIndex < data.length) {
            const chunk = data.slice(currentIndex, currentIndex + MAX_CHARS_ESTIMATE);
            chunks.push(chunk);
            currentIndex += MAX_CHARS_ESTIMATE;
        }
        return chunks;
    }
}

module.exports = ConversationService;
