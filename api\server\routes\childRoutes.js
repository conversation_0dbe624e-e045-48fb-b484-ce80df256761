/**
 * This file contains routes used for child.
 * Created by Growexx on 19/12/2023.
 * @name childRoutes
 */
const router = require('express').Router();

const ChildController = require('../services/child/childController');

const AuthMiddleware = require('../middleware/auth');
const UploadMiddleware = require('../middleware/upload');

router.get('/', AuthMiddleware, ChildController.getChildDetails);
router.post('/exists', AuthMiddleware, UploadMiddleware.single('photo'), ChildController.validateChildExists);
router.post('/follow', AuthMiddleware, ChildController.sendFollowRequestToChild);
router.post('/connect', AuthMiddleware, ChildController.sendConnectionRequest);
router.put('/follow-request', AuthMiddleware, ChildController.changeFollowRequestStatus);
router.put('/connection-request', AuthMiddleware, ChildController.changeConnectionRequestStatus);
router.put('/remove-follower', AuthMiddleware, ChildController.removeFollower);
router.put('/remove-connection', AuthMiddleware, ChildController.removeConnection);
router.post('/unfollow', AuthMiddleware, ChildController.unfollowChild);
router.get('/search', AuthMiddleware, ChildController.searchChild);
router.get('/org-child-search', AuthMiddleware, ChildController.searchChildInOrganization);
router.get('/relationships', AuthMiddleware, ChildController.getRelationships);
router.get('/child-connections', AuthMiddleware, ChildController.getChildConnections);
router.put('/', AuthMiddleware, UploadMiddleware.single('photo'), ChildController.updateChildDetails);
router.get('/organizations', AuthMiddleware, ChildController.getAssociatedOrganizations);
router.get('/generate-presigned-url', AuthMiddleware, ChildController.generatePresignedUrl);

module.exports = router;
