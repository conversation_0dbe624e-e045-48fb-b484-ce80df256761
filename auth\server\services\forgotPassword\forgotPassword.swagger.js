/**
 *  routes and schema for ForgotPassword
 */

/**
 * @openapi
 * components:
 *  schemas:
 *      successVerifyOtp:
 *          type: object
 *          properties:
 *              status:
 *                  type: number
 *                  description: Verification code verified successfully
 *              data:
 *                  type: string
 *                  description: data
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Success
 *
 *      errorBadRequest:
 *          type: object
 *          properties:
 *              status:
 *                  type: number
 *                  description: invalid otp
 *              data:
 *                  type: string
 *                  description: data
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Verification code doesn't match. Please enter correct verification code.
 */

/**
 * @openapi
 * /auth/forgot-password:
 *  post:
 *      tags: [Authentication]
 *      summary: Forgot password
 *      requestBody:
 *          required: true
 *          description: Body parameter
 *          content:
 *              application/json:
 *                  schema:
 *                      allOf:
 *                          - $ref: '#/components/schemas/userForgotPassword'
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successForgotPassword'
 *          403:
 *              description: User not active
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/errorUserNotFound'
 *          404:
 *              description: User not found
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/userNotFound'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/errorBadRequest'
 *          500:
 *              description: Internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /auth/verify-otp:
 *  post:
 *      tags: [Authentication]
 *      summary: User otp verification
 *      requestBody:
 *          required: true
 *          description: Body parameter
 *          content:
 *              application/json:
 *                  schema:
 *                      allOf:
 *                          - $ref: '#/components/schemas/userOtpVerify'
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successVerifyOtp'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/errorBadRequest'
 *          404:
 *              description: User not found
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/userNotFound'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /auth/reset-password:
 *  post:
 *      tags: [Authentication]
 *      summary: Reset user password
 *      requestBody:
 *          required: true
 *          description: Body parameter
 *          content:
 *              application/json:
 *                  schema:
 *                      allOf:
 *                          - $ref: '#/components/schemas/userResetPassword'
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successResetPassword'
 *          400:
 *              description: Invalid request
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          404:
 *              description: User not found
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/userNotFound'
 *          500:
 *              description: Internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */
