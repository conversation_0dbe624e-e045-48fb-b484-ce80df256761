const RedisUtil = require('./redisUtil');
const EventSignups = require('./models/eventSignup.model');
const User = require('./models/user.model');
const MOMENT = require('moment');
const CONSTANTS = require('./constants');
const CONSOLE_LOGGER = require('./logger');
const Stripe = require('./Stripe');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const FeedHelperService = require('./FeedHelperService');
const EventHelperService = require('./EventHelperService');
const NotificationHelperService = require('./NotificationHelperService');
const PostHelperService = require('./PostHelperService');
const FundraiserHelperService = require('./FundraiserHelperService');
const MembershipHelperService = require('./MembershipHelperService');
const ChildHelperService = require('./ChildHelperService');
const DBModelHelperService = require('./DBModelHelperService');
const EventSignupHelperService = require('./EventSignupHelperService');
const FundraiserSignupHelperService = require('./FundraiserSignupHelperService');
const EmailHelperService = require('./EmailHelperService');
const OrganizationHelperService = require('./OrganizationHelperService');
const Utils = require('./Utils');

class FeedService {
    /**
     * @description Generates a feed and stores it in the cache
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} event - The event object
     * @returns {Promise<Object>} The pipeline result
     */
    static async generateFeedAndStoreToCache (redis, event) {
        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
        const feed = await FeedHelperService.generateFeed(event, []);
        const pipeline = redis.pipeline();
        RedisUtil.addEventDetailsToHash(Utils.getEventDetailsKey({ versionPrefix, eventId: event.id }), pipeline, feed);
        await EventHelperService.addReferenceToUserAndChildren({
            redis,
            event,
            isCreated: true,
            oldEvent: event,
            versionPrefix
        });
        return await pipeline.exec();
    }

    /**
     * @description Generates a post feed and stores it in the cache
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} post - The post object
     * @returns {Promise<Object>} The pipeline result
     */
    static async generatePostFeedAndStoreToCache (redis, post) {
        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
        const feed = await FeedHelperService.generatePostFeed(post);
        const pipeline = redis.pipeline();
        RedisUtil.addPostDetailsToHash(Utils.getPostDetailsKey({ versionPrefix, postId: post.id }), pipeline, feed);
        await PostHelperService.addPostReferenceToUserAndChildren({
            redis,
            post,
            versionPrefix,
            isCreated: true,
            oldPost: post
        });
        return await pipeline.exec();
    }

    /**
     * @description Generates a fundraiser feed and stores it in the cache
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} fundraiser - The fundraiser object
     * @returns {Promise<Object>} The pipeline result
     */
    static async generateFundraiserFeedAndStoreToCache (redis, fundraiser) {
        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
        const feed = await FeedHelperService.generateFundraiserFeed(fundraiser);
        const pipeline = redis.pipeline();
        RedisUtil.addFundraiserDetailsToHash(Utils.getFundraiserDetailsKey({ versionPrefix, fundraiserId: fundraiser.id }), pipeline, feed);
        await FundraiserHelperService.addFundraiserReferenceToUserAndChildren({
            redis,
            fundraiser,
            versionPrefix,
            isCreated: true,
            oldFundraiser: fundraiser
        });
        return await pipeline.exec();
    }

    /**
     * @description Updates a feed and stores it in the cache
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} oldEvent - The old event object
     * @param {Object} newEvent - The new event object
     * @returns {Promise<Object>} The pipeline result
    */
    static async updateFeedAndStoreToCache (redis, oldEvent, newEvent) {
        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
        if (newEvent.isDeleted === 1) {
            return await FeedHelperService.deleteAllEventReferences({
                redis,
                versionPrefix,
                event: newEvent,
                shouldDeleteRegisteredEvents: true,
                isCalendarEvent: newEvent.eventType === CONSTANTS.EVENT_TYPE.CALENDAR
            });
        }

        const redisFeed = JSON.parse(
            await RedisUtil.getHashValue(
                redis,
                Utils.getEventDetailsKey({ versionPrefix, eventId: newEvent.id }),
                'details'
            )
        );

        const feed = await FeedHelperService.generateFeed(newEvent, redisFeed.participants);

        const pipeline = redis.pipeline();

        RedisUtil.addEventDetailsToHash(Utils.getEventDetailsKey({ versionPrefix, eventId: newEvent.id }), pipeline, feed);

        const timestamp = MOMENT(oldEvent.details.startDateTime).toDate().getTime();
        await EventHelperService.addReferenceToUserAndChildren({
            redis,
            timestamp,
            oldEvent,
            versionPrefix,
            event: newEvent,
            isCreated: false
        });

        await pipeline.exec();
        return await NotificationHelperService.updatePendingPushNotifications(feed);
    }

    /**
     * @description Updates a fundraiser feed and stores it in the cache
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} oldFundraiser - The old fundraiser object
     * @param {Object} newFundraiser - The new fundraiser object
     * @returns {Promise<Object>} The pipeline result
    */
    static async updateFundraiserFeedAndStoreToCache (
        redis,
        oldFundraiser,
        newFundraiser
    ) {
        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();

        if (newFundraiser.isDeleted === 1) {
            return await FeedHelperService.deleteAllFundraiserReferences({
                redis,
                versionPrefix,
                fundraiser: newFundraiser,
                shouldDeleteRegisteredEvents: true
            });
        }

        const feed = await FeedHelperService.generateFundraiserFeed(newFundraiser);
        const pipeline = redis.pipeline();
        RedisUtil.addFundraiserDetailsToHash(
            Utils.getFundraiserDetailsKey({ versionPrefix, fundraiserId: newFundraiser.id }),
            pipeline,
            feed
        );
        const timestamp = MOMENT(oldFundraiser.startDate).toDate().getTime();
        await FundraiserHelperService.addFundraiserReferenceToUserAndChildren({
            redis,
            timestamp,
            oldFundraiser,
            versionPrefix,
            fundraiser: newFundraiser,
            isCreated: false
        });
        await MembershipHelperService.updateMembershipValidity(oldFundraiser, newFundraiser);
        return await pipeline.exec();
    }

    /**
     * @description Updates a post feed and stores it in the cache
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} oldEvent - The old event object
     * @param {Object} newEvent - The new event object
     * @returns {Promise<Object>} The pipeline result
    */
    static async updatePostFeedAndStoreToCache (redis, oldEvent, newEvent) {
        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();

        if (newEvent.isDeleted === 1) {
            return await FeedHelperService.deleteAllPostReferences(redis, newEvent, versionPrefix);
        }
        const feed = await FeedHelperService.generatePostFeed(newEvent);
        const pipeline = redis.pipeline();
        RedisUtil.addPostDetailsToHash(Utils.getPostDetailsKey({ versionPrefix, postId: newEvent.id }), pipeline, feed);

        const timestamp = MOMENT(oldEvent.publishedDate).toDate().getTime();
        await PostHelperService.addPostReferenceToUserAndChildren({
            redis,
            timestamp,
            versionPrefix,
            post: newEvent,
            isCreated: false,
            oldPost: oldEvent
        });
        return await pipeline.exec();
    }

    /**
     * @description Removes a registered event
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} eventSignUp - The event signup object
     * @returns {Promise<Object>} The pipeline result
    */
    static async removeRegisteredEvents (redis, eventSignUp) {
        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();

        const childDetails = await DBModelHelperService.getChildDetailsWithAttributes(eventSignUp.childId, CONSTANTS.CHILD_ATTRIBUTES);

        return await EventSignupHelperService.removeRegisteredEventReferences({ redis, eventSignUp, childDetails, versionPrefix });
    }

    /**
     * @description Removes a fundraiser signup feed
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} fundraiserSignup - The fundraiser signup object
     * @returns {Promise<Object>} The pipeline result
    */
    static async removeFundraiserSignupFeed (redis, fundraiserSignup) {
        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();

        const childRegisteredKey = Utils.getRegisteredChildKey({ versionPrefix, childId: fundraiserSignup.childId });
        const feed = JSON.parse(
            await RedisUtil.getHashValue(
                redis,
                Utils.getFundraiserDetailsKey({ versionPrefix, fundraiserId: fundraiserSignup.eventId }),
                'details'
            )
        );
        const childDetails = await DBModelHelperService.getChildDetailsWithAttributes(fundraiserSignup.childId, CONSTANTS.CHILD_ATTRIBUTES);

        await FundraiserSignupHelperService.removeFundraiserSignupFeed({
            redis,
            fundraiserSignup,
            childRegisteredKey,
            feed,
            childDetails,
            versionPrefix
        });
        return fundraiserSignup.childId;
    }

    /**
     * @description Generates a feed for a child
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} child - The child object
     * @returns {Promise<Object>} The pipeline result
    */
    static async generateFeedForChild (redis, child) {
        CONSOLE_LOGGER.info('Generating event feed for child..');
        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();

        const events = await DBModelHelperService.getEventsForOrganizations(child.associatedOrganizations);

        return await ChildHelperService.addEventsToFeed({ redis, events, child, versionPrefix });
    }

    /**
     * @description Generates a fundraiser feed for a child
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} child - The child object
     * @returns {Promise<Object>} The pipeline result
    */
    static async generateFundraiserFeedForChild (redis, child) {
        CONSOLE_LOGGER.info('Generating fundraiser feed for child..');
        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();

        const fundraisers = await DBModelHelperService.getFundraisersForOrganizations(child.associatedOrganizations);

        return await ChildHelperService.addFundraisersToFeed(redis, fundraisers, child, versionPrefix);
    }

    /**
     * @description Generates a post feed for a child
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} child - The child object
     * @returns {Promise<Object>} The pipeline result
    */
    static async generatePostFeedForChild (redis, child) {
        CONSOLE_LOGGER.info('Generating post feed for child..');
        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
        const posts = await DBModelHelperService.getPostsForOrganizations(child.associatedOrganizations);
        CONSOLE_LOGGER.info('Posts fetched for child', posts);
        return await ChildHelperService.addPostsToFeed({ redis, posts, child, versionPrefix });
    }

    /**
     * @description Adds a registered event to the feed
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} eventSignUp - The event signup object
     * @returns {Promise<Object>} The pipeline result
    */
    static async addToRegisteredEvents (redis, eventSignUp) {
        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();

        const childKey = Utils.getChildKey({ versionPrefix, childId: eventSignUp.childId });
        const childRegisteredKey = Utils.getRegisteredChildKey({ versionPrefix, childId: eventSignUp.childId });
        const feed = JSON.parse(
            await RedisUtil.getHashValue(
                redis,
                Utils.getEventDetailsKey({ versionPrefix, eventId: eventSignUp.eventId }),
                'details'
            )
        );

        const childDetails = await DBModelHelperService.getChildDetailsWithAttributes(eventSignUp.childId, CONSTANTS.CHILD_ATTRIBUTES);

        if (
            eventSignUp.paymentDetails.paymentStatus !==
            CONSTANTS.PAYMENT_STATUS.PAYMENT_INITIATED
        ) {
            await EventSignupHelperService.addOrUpdateRegisteredEvents({
                redis,
                eventSignUp,
                feed,
                childKey,
                childRegisteredKey,
                childDetails,
                versionPrefix
            });
        }

        if (
            eventSignUp.paymentDetails.paymentStatus ===
            CONSTANTS.PAYMENT_STATUS.APPROVED
        ) {
            await EventSignupHelperService.updateEventParticipants({ redis, eventSignUp, feed, childDetails, versionPrefix });
        }

        return eventSignUp.childId;
    }

    /**
     * @description Adds a fundraiser signup to the feed
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} fundraiserSignup - The fundraiser signup object
     * @returns {Promise<Object>} The pipeline result
    */
    static async addToFundraiserSignupFeed (redis, fundraiserSignup) {
        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();

        const childKey = Utils.getChildKey({ versionPrefix, childId: fundraiserSignup.childId });
        const childRegisteredKey = Utils.getRegisteredChildKey({ versionPrefix, childId: fundraiserSignup.childId });
        const feed = JSON.parse(
            await RedisUtil.getHashValue(
                redis,
                Utils.getFundraiserDetailsKey({ versionPrefix, fundraiserId: fundraiserSignup.eventId }),
                'details'
            )
        );
        const childDetails = await DBModelHelperService.getChildDetailsWithAttributes(fundraiserSignup.childId, CONSTANTS.CHILD_ATTRIBUTES);

        if (
            fundraiserSignup.paymentDetails.paymentStatus !==
            CONSTANTS.PAYMENT_STATUS.PAYMENT_INITIATED
        ) {
            await FundraiserSignupHelperService.addOrUpdateRegisteredFundraiser({
                redis,
                fundraiserSignup,
                feed,
                childKey,
                childRegisteredKey,
                childDetails,
                versionPrefix
            });
        }

        if (
            fundraiserSignup.paymentDetails.paymentStatus ===
            CONSTANTS.PAYMENT_STATUS.APPROVED
        ) {
            await NotificationHelperService.sendFundraiserNotification(fundraiserSignup, feed);
        }

        return fundraiserSignup.childId;
    }

    /**
     * @description Updates a registered event
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} oldEventSignup - The old event signup object
     * @param {Object} newEventSignup - The new event signup object
     * @returns {Promise<Object>} The pipeline result
    */
    static async updateRegisteredEvents (redis, oldEventSignup, newEventSignup) {
        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();

        const childKey = Utils.getChildKey({ versionPrefix, childId: newEventSignup.childId });
        const childRegisteredKey = Utils.getRegisteredChildKey({ versionPrefix, childId: newEventSignup.childId });
        const feed = JSON.parse(
            await RedisUtil.getHashValue(
                redis,
                Utils.getEventDetailsKey({ versionPrefix, eventId: newEventSignup.eventId }),
                'details'
            )
        );

        const childDetails = await DBModelHelperService.getChildDetailsWithAttributes(newEventSignup.childId, CONSTANTS.CHILD_ATTRIBUTES);

        if (
            oldEventSignup.paymentDetails.paymentStatus ===
            CONSTANTS.PAYMENT_STATUS.PAYMENT_INITIATED &&
            newEventSignup.paymentDetails.paymentStatus !==
            CONSTANTS.PAYMENT_STATUS.PAYMENT_INITIATED
        ) {
            await EventSignupHelperService.addOrUpdateRegisteredEvents({
                eventSignUp: newEventSignup,
                redis,
                feed,
                childKey,
                childRegisteredKey,
                childDetails,
                versionPrefix
            });
        } else {
            await EventSignupHelperService.addOrUpdateRegisteredEvents({
                eventSignUp: newEventSignup,
                childKey: childRegisteredKey,
                redis,
                feed,
                childRegisteredKey,
                childDetails,
                versionPrefix
            });
        }

        if (
            newEventSignup.paymentDetails.paymentStatus ===
            CONSTANTS.PAYMENT_STATUS.APPROVED &&
            oldEventSignup.paymentDetails.paymentStatus !==
            CONSTANTS.PAYMENT_STATUS.APPROVED
        ) {
            await EventSignupHelperService.updateEventParticipants({
                eventSignUp: newEventSignup,
                redis,
                feed,
                childDetails,
                versionPrefix
            });
        }
        return newEventSignup.childId;
    }

    /**
     * @description Updates a fundraiser signup
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} oldFundraiserSignup - The old fundraiser signup object
     * @param {Object} newFundraiserSignup - The new fundraiser signup object
     * @returns {Promise<Object>} The pipeline result
    */
    static async updateFundraiserSignupFeed (
        redis,
        oldFundraiserSignup,
        newFundraiserSignup
    ) {
        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();

        const childKey = Utils.getChildKey({ versionPrefix, childId: newFundraiserSignup.childId });
        const childRegisteredKey = Utils.getRegisteredChildKey({ versionPrefix, childId: newFundraiserSignup.childId });
        const feed = JSON.parse(
            await RedisUtil.getHashValue(
                redis,
                Utils.getFundraiserDetailsKey({ versionPrefix, fundraiserId: newFundraiserSignup.eventId }),
                'details'
            )
        );
        const childDetails = await DBModelHelperService.getChildDetailsWithAttributes(
            newFundraiserSignup.childId,
            CONSTANTS.CHILD_ATTRIBUTES
        );

        if (
            oldFundraiserSignup.paymentDetails.paymentStatus ===
            CONSTANTS.PAYMENT_STATUS.PAYMENT_INITIATED &&
            newFundraiserSignup.paymentDetails.paymentStatus !==
            CONSTANTS.PAYMENT_STATUS.PAYMENT_INITIATED
        ) {
            await FundraiserSignupHelperService.addOrUpdateRegisteredFundraiser({
                redis,
                feed,
                childKey,
                childRegisteredKey,
                childDetails,
                versionPrefix,
                fundraiserSignup: newFundraiserSignup
            });
        } else {
            await FundraiserSignupHelperService.addOrUpdateRegisteredFundraiser({
                redis,
                feed,
                childRegisteredKey,
                childDetails,
                versionPrefix,
                fundraiserSignup: newFundraiserSignup,
                childKey: childRegisteredKey
            });
        }

        if (
            newFundraiserSignup.paymentDetails.paymentStatus ===
            CONSTANTS.PAYMENT_STATUS.APPROVED &&
            oldFundraiserSignup.paymentDetails.paymentStatus !==
            CONSTANTS.PAYMENT_STATUS.APPROVED
        ) {
            await NotificationHelperService.sendFundraiserNotification(newFundraiserSignup, feed);
        }

        return newFundraiserSignup.childId;
    }

    /**
     * @description Gets the signed in users
     * <AUTHOR>
     * @param {string} eventId - The event id
     * @returns {Promise<Object>} The pipeline result
    */
    static async getSignedInUsers (eventId) {
        const signups = await EventSignups.query('eventId')
            .eq(eventId)
            .using('eventId-index')
            .attributes(['parentId'])
            .exec();

        if (signups.length === 0) {
            return [];
        }

        const parents = signups.map((participant) => {
            return participant.parentId;
        });

        const users = [];
        for (const parentId of parents) {
            users.push(
                await User.query(
                    { id: parentId },
                    {
                        attributes: ['id', 'fcmToken']
                    }
                )
            );
        }

        return users;
    }

    /**
     * @description Updates the feed for a child
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} oldChild - The old child object
     * @param {Object} newChild - The new child object
     * @returns {Promise<Object>} The pipeline result
    */
    static async updateFeedForChild (redis, oldChild, newChild) {
        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
        await ChildHelperService.handleOrganizationChanges(redis, oldChild, newChild, versionPrefix);

        await ChildHelperService.handleConnectionChanges(oldChild, newChild);

        await ChildHelperService.handleGuardiansChanges(redis, oldChild, newChild, versionPrefix);

        return await ChildHelperService.updateChildDetailsInFeed(redis, newChild, versionPrefix);
    }

    /**
     * @description Updates the donation amount raised and sends a notification
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} orgManagedFundraiserBoosterDonation - The organization managed fundraiser booster donation object
     * @returns {Promise<Object>} The pipeline result
    */
    static async updateDonationAmountRaisedAndSendNotification (
        redis,
        orgManagedFundraiserBoosterDonation
    ) {
        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();

        if (
            orgManagedFundraiserBoosterDonation.status === CONSTANTS.PAYMENT_STATUS.SUCCESS &&
            orgManagedFundraiserBoosterDonation.childId
        ) {
            const childDetails = await DBModelHelperService.getChildDetailsWithAttributes(
                orgManagedFundraiserBoosterDonation.childId,
                CONSTANTS.CHILD_ATTRIBUTES
            );

            await FundraiserSignupHelperService.handleSuccessfulPaymentForOrgManagedDonation({
                redis,
                orgManagedFundraiserBoosterDonation,
                childDetails,
                versionPrefix
            });
        }

        if (
            orgManagedFundraiserBoosterDonation.status === CONSTANTS.PAYMENT_STATUS.SUCCESS
            && orgManagedFundraiserBoosterDonation.stripePaymentIntentId
        ) {
            const paymentIntent = await Stripe.retrievePaymentIntent(stripe, orgManagedFundraiserBoosterDonation.stripePaymentIntentId);
            await EmailHelperService.sendEmailReceiptToUser(paymentIntent, orgManagedFundraiserBoosterDonation);
        }
        return orgManagedFundraiserBoosterDonation.childId;
    }

    /**
     * @description Adds the organization details to redis cache
     * <AUTHOR>
     * @param {Redis} redis The redis client
     * @param {Object} organizationDetails The organization details
     */
    static async addOrUpdateOrganizationDetailsToCache (redis, organizationDetails) {
        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();

        return await OrganizationHelperService.addOrUpdateOrganizationDetailsToCache(redis, organizationDetails, versionPrefix);
    }
}

module.exports = FeedService;
