/**
 * This file contains controller for conversation.
 * Created by Growexx on 09/10/2024.
 * @name conversationController
 */

const ConversationService = require('./conversationService');
const Utils = require('../../../util/utilFunctions');

/**
 * Class represents controller for conversation routes.
 */
class ConversationController {
    /**
     * @desc This function is being used to get group conversation list
     * <AUTHOR>
     * @since 09/10/2024
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async getGroupConversationList (req, res) {
        try {
            const data = await ConversationService.getGroupConversationList(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get group conversation list', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to get group members
     * <AUTHOR>
     * @since 14/10/2024
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async getGroupMembers (req, res) {
        try {
            const data = await ConversationService.getGroupMembers(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get group members list', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to generate presigned url
     * <AUTHOR>
     * @since 20/12/2024
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async generatePresignedUrl (req, res) {
        try {
            const data = await ConversationService.generatePresignedUrl(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in generate presigned url', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = ConversationController;
