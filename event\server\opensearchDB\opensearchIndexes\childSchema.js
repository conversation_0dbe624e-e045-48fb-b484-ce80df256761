const childSchema = {
    'index': 'children',
    'body': {
        'mappings': {
            'properties': {
                'id': {
                    'type': 'keyword',
                    'index': true
                },
                'firstName': {
                    'type': 'text'
                },
                'lastName': {
                    'type': 'text'
                },
                'associatedColor': {
                    'type': 'keyword'
                },
                'photoURL': {
                    'type': 'keyword'
                },
                'associatedOrganizations': {
                    'type': 'keyword'
                },
                'homeRoom': {
                    'type': 'keyword'
                },
                'school': {
                    'type': 'keyword'
                },
                'childEvents': {
                    'type': 'keyword'
                },
                'childCalendarEvents': {
                    'type': 'keyword'
                },
                'childFeeds': {
                    'type': 'keyword'
                },
                'childPosts': {
                    'type': 'keyword'
                },
                'childPendingEvents': {
                    'type': 'keyword'
                },
                'childFundraiserFeeds': {
                    'type': 'keyword'
                },
                'childFundraiserSignups': {
                    'type': 'nested',
                    'properties': {
                        'fundraiserId': {
                            'type': 'keyword'
                        },
                        'fundraiserSignupId': {
                            'type': 'keyword'
                        },
                        'purchasedProducts': {
                            'type': 'keyword'
                        }
                    }
                },
                'childPendingFundraiserSignups': {
                    'type': 'nested',
                    'properties': {
                        'fundraiserId': {
                            'type': 'keyword'
                        },
                        'fundraiserSignupId': {
                            'type': 'keyword'
                        },
                        'purchasedProducts': {
                            'type': 'keyword'
                        }
                    }
                },
                'guardians': {
                    'type': 'keyword'
                },
                'connections': {
                    'type': 'nested',
                    'properties': {
                        'childId': {
                            'type': 'keyword'
                        },
                        'status': {
                            'type': 'keyword'
                        }
                    }
                }
            }
        }
    }
};

module.exports = childSchema;
