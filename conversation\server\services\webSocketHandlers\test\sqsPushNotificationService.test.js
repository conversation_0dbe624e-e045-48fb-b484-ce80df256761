const { beforeEach, afterEach } = require('mocha');
const SQSPushNotificationService = require('../../sqsPushNotificationService');
const { SQSClient } = require('@aws-sdk/client-sqs');
const sinon = require('sinon');
const UserModel = require('../../../models/user.model');
const GroupsModel = require('../../../models/groups.model');

describe('SQS Push Notification Service', () => {
    let sendStub;
    let userQueryStub;
    let groupQueryStub;

    beforeEach(() => {
        sendStub = sinon.stub(SQSClient.prototype, 'send');
        userQueryStub = sinon.stub(UserModel, 'query');
        groupQueryStub = sinon.stub(GroupsModel, 'query');
    });

    afterEach(() => {
        sinon.restore();
    });

    it('should sendGroupMessage to SQS', async () => {
        const senderId = 'test_sender_id';
        const groupId = 'test_group_id';
        const message = 'a'.repeat(101);
        const groupMemberIds = ['test_group_member_id_1', 'test_group_member_id_2'];

        userQueryStub.returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().resolves([])
                })
            })
        });

        groupQueryStub.returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().resolves([])
                })
            })
        });

        sendStub.resolves();

        await SQSPushNotificationService.sendGroupMessage(senderId, groupId, message, groupMemberIds);

        sinon.assert.calledOnce(userQueryStub);
        sinon.assert.calledOnce(groupQueryStub);
        sinon.assert.calledTwice(sendStub);
    });

    it('should sendGroupMessage to SQS for organization group', async () => {
        const senderId = 'test_sender_id';
        const groupId = 'test_group_id';
        const message = 'a'.repeat(10);
        const groupMemberIds = ['test_group_member_id_1', 'test_group_member_id_2'];

        userQueryStub.returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().resolves([{
                        id: 'test_user_id'
                    }])
                })
            })
        });

        groupQueryStub.returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().resolves([{
                        groupType: 'organization',
                        organizationMetaData: {
                            name: 'test_organization_name'
                        }
                    }])
                })
            })
        });

        sendStub.resolves();

        await SQSPushNotificationService.sendGroupMessage(senderId, groupId, message, groupMemberIds);

        sinon.assert.calledOnce(userQueryStub);
        sinon.assert.calledOnce(groupQueryStub);
        sinon.assert.calledTwice(sendStub);
    });

    it('should sendGroupMessage to SQS for event group', async () => {
        const senderId = 'test_sender_id';
        const groupId = 'test_group_id';
        const message = 'a'.repeat(10);
        const groupMemberIds = ['test_group_member_id_1', 'test_group_member_id_2'];

        userQueryStub.returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().resolves([{
                        id: 'test_user_id',
                        firstName: 'test_first_name',
                        lastName: 'test_last_name'
                    }])
                })
            })
        });

        groupQueryStub.returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().resolves([{
                        groupType: 'event',
                        eventMetaData: {
                            title: 'test_event_title'
                        }
                    }])
                })
            })
        });

        sendStub.resolves();

        await SQSPushNotificationService.sendGroupMessage(senderId, groupId, message, groupMemberIds);

        sinon.assert.calledOnce(userQueryStub);
        sinon.assert.calledOnce(groupQueryStub);
        sinon.assert.calledTwice(sendStub);
    });

    it('should sendPersonalMessage to SQS', async () => {
        const senderId = 'test_sender_id';
        const receiverId = 'test_receiver_id';
        const senderDetails = {
            firstName: 'test_first_name',
            lastName: 'test_last_name'
        };
        const conversationId = 'test_conversation_id';
        const message = 'a'.repeat(101);

        sendStub.resolves();

        await SQSPushNotificationService.sendPersonalMessage(receiverId, senderId, senderDetails, conversationId, message);

        sinon.assert.calledOnce(sendStub);
    });

    it('should sendPersonalMessage to SQS if sender details are not found', async () => {
        const senderId = 'test_sender_id';
        const receiverId = 'test_receiver_id';
        const conversationId = 'test_conversation_id';
        const message = 'a'.repeat(10);

        userQueryStub.returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().resolves([{
                        id: 'test_user_id',
                        firstName: 'test_first_name',
                        lastName: 'test_last_name'
                    }])
                })
            })
        });

        sendStub.resolves();

        await SQSPushNotificationService.sendPersonalMessage(receiverId, senderId, undefined, conversationId, message);

        sinon.assert.calledOnce(sendStub);
    });
});
