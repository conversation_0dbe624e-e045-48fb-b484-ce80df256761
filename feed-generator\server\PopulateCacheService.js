const DBModelHelperService = require('./DBModelHelperService');
const EventHelperService = require('./EventHelperService');
const EventSignupHelperService = require('./EventSignupHelperService');
const FeedHelperService = require('./FeedHelperService');
const FundraiserHelperService = require('./FundraiserHelperService');
const FundraiserSignupHelperService = require('./FundraiserSignupHelperService');
const OrganizationHelperService = require('./OrganizationHelperService');
const PostHelperService = require('./PostHelperService');
const Utils = require('./Utils');
const CONSTANTS = require('./constants');
const CONSOLE_LOGGER = require('./logger');
const RedisUtil = require('./redisUtil');
const MOMENT = require('moment');

class PopulateCacheService {
    /**
     * @description Populates the cache
     * <AUTHOR>
     * @param {Redis} redis - The redis client
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async populateCache (redis, versionPrefix) {
        try {
            const childrenDetailsMap = await this.populateOrganizationEvents(redis, versionPrefix);
            await this.populateEventRegistrations(redis, childrenDetailsMap, versionPrefix);
            await this.populateFundraiserRegistrations(redis, childrenDetailsMap, versionPrefix);
        } catch (error) {
            CONSOLE_LOGGER.error('Error populating cache ', error);
        }
    }

    /**
     * @description Gets the version prefix according to the previous version
     * <AUTHOR>
     * @param {String} prevVersion - The previous version
     * @returns {String} The version prefix
     */
    static getVersionPrefixAccToPrev (prevVersion) {
        CONSOLE_LOGGER.info('Previous version', prevVersion);
        return prevVersion === CONSTANTS.VERSION_PREFIXES.VERSION_PREFIX_V1
            ? CONSTANTS.VERSION_PREFIXES.VERSION_PREFIX_V2
            : CONSTANTS.VERSION_PREFIXES.VERSION_PREFIX_V1;
    }

    /**
     * @description Updates the version prefix to the database
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async updateVersionPrefixToDB (versionPrefix) {
        await DBModelHelperService.updateVersionPrefixForRedisKeys(versionPrefix);
    }

    /**
     * @description Clears the redis cache for the version
     * <AUTHOR>
     * @param {Redis} redis - The redis client
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async clearRedisCacheForVersion (redis, versionPrefix) {
        CONSOLE_LOGGER.info('Clearing redis cache for version', versionPrefix);
        const pipeline = redis.pipeline();

        const keys = await RedisUtil.getKeysForPattern(redis, `${versionPrefix}:*`);

        for (const key of keys) {
            RedisUtil.deleteKey(pipeline, key);
        }

        await pipeline.exec();
        CONSOLE_LOGGER.info('Redis cache cleared for version', versionPrefix);
    }

    static async clearRedisCacheForNonVersionedKeys (redis) {
        const pipeline = redis.pipeline();
        const keys = await RedisUtil.getKeysForPattern(redis, '*');
        const filteredKeys = keys.filter(key => !/^v[12]:/.test(key));
        for (const key of filteredKeys) {
            RedisUtil.deleteKey(pipeline, key);
        }

        await pipeline.exec();
        CONSOLE_LOGGER.info('Redis cache cleared for non versioned keys');
    }

    /**
     * @description Populates the organization events
     * <AUTHOR>
     * @param {Redis} redis - The redis client
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<Object>} The children details map
     */
    static async populateOrganizationEvents (redis, versionPrefix) {
        const events = await DBModelHelperService.getAllEvents();
        CONSOLE_LOGGER.info('All events fetched');

        const fundraisers = await DBModelHelperService.getAllFundraisers();
        CONSOLE_LOGGER.info('All fundraisers fetched');

        const posts = await DBModelHelperService.getAllPosts();
        CONSOLE_LOGGER.info('All posts fetched');

        const expirationDays = await DBModelHelperService.getPostExpirationDays();
        CONSOLE_LOGGER.info(`Post expiration days: ${expirationDays}`);

        const organizationDetailsMap = (
            await DBModelHelperService.getAllOrganizations()
        ).reduce((acc, organization) => {
            acc[organization.id] = organization;
            return acc;
        }, {});
        CONSOLE_LOGGER.info('All organizations fetched');

        const organizationEvents = {};
        const organizationFundraisers = {};
        const organizationPosts = {};
        const childrenDetailsMap = {};

        events.forEach((event) => {
            const { organizationId } = event;
            if (!organizationEvents[organizationId]) {
                organizationEvents[organizationId] = [];
            }
            organizationEvents[organizationId].push(event);
        });

        fundraisers.forEach((fundraiser) => {
            const { organizationId } = fundraiser;
            if (!organizationFundraisers[organizationId]) {
                organizationFundraisers[organizationId] = [];
            }
            organizationFundraisers[organizationId].push(fundraiser);
        });

        posts.forEach((post) => {
            const { organizationId } = post;
            if (!organizationPosts[organizationId]) {
                organizationPosts[organizationId] = [];
            }
            organizationPosts[organizationId].push(post);
        });

        for (const organizationId in organizationDetailsMap) {
            if (Object.hasOwn(organizationDetailsMap, organizationId)) {
                CONSOLE_LOGGER.info(`Processing organization: ${organizationId}`);

                const organizationDetails = organizationDetailsMap[organizationId];
                await this.handleInsertOrgDetailsToCache({ redis, organizationDetails, organizationId, versionPrefix });

                const orgEvents = (organizationEvents[organizationId])?.filter(Boolean);
                const orgFundraisers = (organizationFundraisers[organizationId])?.filter(Boolean);
                const orgPosts = (organizationPosts[organizationId])?.filter(Boolean);

                const childrenAssociatedWithOrganization = await this.getChildrenAssociatedWithOrganization({
                    orgEvents,
                    orgFundraisers,
                    orgPosts,
                    organizationId,
                    childrenDetailsMap
                });

                await this.populateEvents({
                    redis,
                    childrenAssociatedWithOrganization,
                    childrenDetailsMap,
                    orgEvents,
                    organizationId,
                    versionPrefix
                });

                await this.populateFundraisers({
                    redis,
                    orgFundraisers,
                    organizationId,
                    childrenAssociatedWithOrganization,
                    childrenDetailsMap,
                    versionPrefix
                });

                await this.populatePosts({
                    redis,
                    orgPosts,
                    organizationId,
                    childrenAssociatedWithOrganization,
                    childrenDetailsMap,
                    expirationDays,
                    versionPrefix
                });
            } else {
                CONSOLE_LOGGER.info(`No organization details found for organization: ${organizationId}`);
            }
        }

        return childrenDetailsMap;
    }

    /**
     * @description Handles inserting organization details to cache
     * <AUTHOR>
     * @param {Redis} redis - The redis client
     * @param {Object} organizationDetails - The organization details
     * @param {string} organizationId - The organization id
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async handleInsertOrgDetailsToCache ({ redis, organizationDetails, organizationId, versionPrefix }) {
        if (organizationDetails) {
            await OrganizationHelperService.addOrUpdateOrganizationDetailsToCache(redis, organizationDetails, versionPrefix);
            CONSOLE_LOGGER.info(`Organization details added to cache for organization: ${organizationId}`);
        } else {
            CONSOLE_LOGGER.info(`No organization details found for organization: ${organizationId}`);
        }
    }

    /**
     * @description Gets the children associated with an organization
     * <AUTHOR>
     * @param {Array} orgEvents - The organization events
     * @param {Array} orgFundraisers - The organization fundraisers
     * @param {Array} orgPosts - The organization posts
     * @param {string} organizationId - The organization id
     * @param {Object} childrenDetailsMap - The children details map
     * @returns {Promise<Object>} The children associated with the organization
     */
    static async getChildrenAssociatedWithOrganization ({ orgEvents, orgFundraisers, orgPosts, organizationId, childrenDetailsMap }) {
        let childrenAssociatedWithOrganization = [];

        if (orgEvents?.length > 0 || orgFundraisers?.length > 0 || orgPosts?.length > 0) {
            childrenAssociatedWithOrganization = await DBModelHelperService.getChildOrganizationMapping(organizationId);
            CONSOLE_LOGGER.info(`Children associated with organization: ${childrenAssociatedWithOrganization.length}`);

            const fetchedChildIds = Object.keys(childrenDetailsMap);

            const uniqueChildrenIds = new Set(
                (childrenAssociatedWithOrganization
                    .map((child) => child.childId))
                    .filter((childId) => !fetchedChildIds.includes(childId))
            );

            if (uniqueChildrenIds.size > 0) {
                const newFetchedChildren = await DBModelHelperService.batchGetChildDetailsWithAttributes(
                    Array.from(uniqueChildrenIds).filter(Boolean)
                );
                newFetchedChildren.forEach((child) => {
                    childrenDetailsMap[child.id] = child;
                });
            }
            CONSOLE_LOGGER.info('Children details map populated');
        }

        return childrenAssociatedWithOrganization;
    }

    /**
     * @description Populates the events
     * <AUTHOR>
     * @param {Redis} redis - The redis client
     * @param {Array} childrenAssociatedWithOrganization - The children associated with the organization
     * @param {Object} childrenDetailsMap - The children details map
     * @param {Array} orgEvents - The organization events
     * @param {string} organizationId - The organization id
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async populateEvents ({
        redis,
        childrenAssociatedWithOrganization,
        childrenDetailsMap,
        orgEvents,
        organizationId,
        versionPrefix
    }) {
        if (orgEvents?.length > 0) {
            for (const event of orgEvents) {
                const { details, status, id: eventId, eventType } = event;

                CONSOLE_LOGGER.info(`Processing event: ${eventId}`);
                const feed = await FeedHelperService.generateFeed(event, []);
                CONSOLE_LOGGER.info(`Feed generated for event: ${eventId}`);

                const pipeline = redis.pipeline();
                RedisUtil.addEventDetailsToHash(Utils.getEventDetailsKey({ versionPrefix, eventId }), pipeline, feed);

                if (
                    (MOMENT(details.endDateTime).isAfter(MOMENT().utc()) && status === CONSTANTS.EVENT_STATUS.PUBLISHED) ||
                    eventType === CONSTANTS.EVENT_TYPE.CALENDAR
                ) {
                    await EventHelperService.handlePublishedEvent({
                        children: childrenAssociatedWithOrganization,
                        childrenDetailsMap,
                        event,
                        redis,
                        versionPrefix
                    });
                    CONSOLE_LOGGER.info(`Event helper service handled for event: ${eventId}`);
                }

                await pipeline.exec();
                CONSOLE_LOGGER.info(`Pipeline executed for event: ${eventId}`);
            }
        } else {
            CONSOLE_LOGGER.info(`No events found for organization: ${organizationId}`);
        }
    }

    /**
     * @description Populates the fundraisers
     * <AUTHOR>
     * @param {Redis} redis - The redis client
     * @param {Array} orgFundraisers - The organization fundraisers
     * @param {string} organizationId - The organization id
     * @param {Array} childrenAssociatedWithOrganization - The children associated with the organization
     * @param {Object} childrenDetailsMap - The children details map
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async populateFundraisers ({
        redis,
        orgFundraisers,
        organizationId,
        childrenAssociatedWithOrganization,
        childrenDetailsMap,
        versionPrefix
    }) {
        if (orgFundraisers?.length > 0) {
            for (const fundraiser of orgFundraisers) {
                const { endDate, status, id: fundraiserId, startDate, fundraiserType } = fundraiser;

                CONSOLE_LOGGER.info(`Processing fundraiser: ${fundraiserId}`);
                const fundraiserFeed = await FeedHelperService.generateFundraiserFeed(fundraiser);
                CONSOLE_LOGGER.info(`Feed generated for fundraiser: ${fundraiserId}`);

                const pipeline = redis.pipeline();
                RedisUtil.addFundraiserDetailsToHash(
                    Utils.getFundraiserDetailsKey({ versionPrefix, fundraiserId }),
                    pipeline,
                    fundraiserFeed
                );

                if (MOMENT(endDate).isAfter(MOMENT().utc()) && status === CONSTANTS.FUNDRAISER_STATUS.PUBLISHED) {
                    await FundraiserHelperService.handleFundraiser({
                        children: childrenAssociatedWithOrganization,
                        redis,
                        fundraiserId,
                        fundraiserType,
                        startDate,
                        childrenDetailsMap,
                        versionPrefix
                    });
                    CONSOLE_LOGGER.info(`Fundraiser helper service handled for fundraiser: ${fundraiser.id}`);
                }

                await pipeline.exec();
                CONSOLE_LOGGER.info(`Pipeline executed for fundraiser: ${fundraiser.id}`);
            }
        } else {
            CONSOLE_LOGGER.info(`No fundraisers found for organization: ${organizationId}`);
        }
    }

    /**
     * @description Populates the posts
     * <AUTHOR>
     * @param {Redis} redis - The redis client
     * @param {Array} orgPosts - The organization posts
     * @param {string} organizationId - The organization id
     * @param {Array} childrenAssociatedWithOrganization - The children associated with the organization
     * @param {Object} childrenDetailsMap - The children details map
     * @param {number} expirationDays - The expiration days
     * @returns {Promise<void>}
     */
    static async populatePosts ({
        redis,
        orgPosts,
        organizationId,
        childrenAssociatedWithOrganization,
        childrenDetailsMap,
        expirationDays,
        versionPrefix
    }) {
        if (orgPosts?.length > 0) {
            for (const post of orgPosts) {
                const { publishedDate, status, id: postId } = post;

                CONSOLE_LOGGER.info(`Processing post: ${postId}`);
                const postFeed = await FeedHelperService.generatePostFeed(post);
                CONSOLE_LOGGER.info(`Feed generated for post: ${postId}`);

                const pipeline = redis.pipeline();
                RedisUtil.addPostDetailsToHash(Utils.getPostDetailsKey({ versionPrefix, postId }), pipeline, postFeed);

                if (
                    MOMENT(publishedDate)
                        .add(expirationDays, 'days')
                        .isAfter(MOMENT().utc())
                    && status === CONSTANTS.POST_STATUS.PUBLISHED
                ) {
                    await PostHelperService.handlePost({
                        children: childrenAssociatedWithOrganization,
                        isCreated: false,
                        redis,
                        childrenDetailsMap,
                        publishedDate,
                        postId,
                        organizationId,
                        post,
                        versionPrefix
                    });
                }

                await pipeline.exec();
                CONSOLE_LOGGER.info(`Pipeline executed for post: ${post.id}`);
            }
        } else {
            CONSOLE_LOGGER.info(`No posts found for organization: ${organizationId}`);
        }
    }

    /**
     * @description Populates the event registrations
     * <AUTHOR>
     * @param {Redis} redis - The redis client
     * @param {Object} childrenDetailsMap - The children details map
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async populateEventRegistrations (redis, childrenDetailsMap, versionPrefix) {
        const eventRegistrations = await DBModelHelperService.getAllEventRegistrations();
        CONSOLE_LOGGER.info('All event registrations fetched');

        for (const eventSignUp of eventRegistrations) {
            const { childId, eventId } = eventSignUp;
            CONSOLE_LOGGER.info(`Processing event registration: ${eventSignUp.id}`);

            const childKey = Utils.getChildKey({ versionPrefix, childId });
            const childRegisteredKey = Utils.getRegisteredChildKey({ versionPrefix, childId });

            const feed = JSON.parse(
                await RedisUtil.getHashValue(
                    redis,
                    Utils.getEventDetailsKey({ versionPrefix, eventId }),
                    'details'
                )
            );
            CONSOLE_LOGGER.info(`Feed fetched for event: ${eventId}`);

            let childDetails = childrenDetailsMap[childId];

            if (!childDetails) {
                childDetails = await DBModelHelperService.getChildDetailsWithAttributes(childId, CONSTANTS.CHILD_ATTRIBUTES);
                childrenDetailsMap[childId] = childDetails;
            }
            CONSOLE_LOGGER.info(`Child details fetched for child: ${childId}`);

            if (childDetails && feed) {
                if (
                    eventSignUp.paymentDetails.paymentStatus !==
                    CONSTANTS.PAYMENT_STATUS.PAYMENT_INITIATED
                ) {
                    CONSOLE_LOGGER.info(`Adding or updating registered events for registration: ${eventSignUp.id}`);

                    const feedReference = {
                        eventId,
                        childId
                    };

                    await EventSignupHelperService.addOrUpdateRegisteredEvents({
                        redis,
                        eventSignUp,
                        feed,
                        childKey,
                        childRegisteredKey,
                        childDetails,
                        versionPrefix,
                        isPopulatingCache: true,
                        feedReferenceForPopulatingCache: feedReference
                    });
                }

                if (
                    eventSignUp.paymentDetails.paymentStatus ===
                    CONSTANTS.PAYMENT_STATUS.APPROVED
                ) {
                    CONSOLE_LOGGER.info(`Updating event participants for registration: ${eventSignUp.id}`);
                    await EventSignupHelperService.updateEventParticipants({
                        redis,
                        eventSignUp,
                        feed,
                        childDetails,
                        versionPrefix,
                        isPopulatingCache: true
                    });
                }
            } else {
                CONSOLE_LOGGER.info(`Child details not found for child: ${childId}`);
            }
        }
    }

    /**
     * @description Populates the fundraiser registrations
     * <AUTHOR>
     * @param {Redis} redis - The redis client
     * @param {Object} childrenDetailsMap - The children details map
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async populateFundraiserRegistrations (redis, childrenDetailsMap, versionPrefix) {
        const fundraiserRegistrations = await DBModelHelperService.getAllFundraiserRegistrations();
        CONSOLE_LOGGER.info('All fundraiser registrations fetched');

        for (const fundraiserSignup of fundraiserRegistrations) {
            const { childId, eventId: fundraiserId, id: fundraiserSignupId } = fundraiserSignup;
            CONSOLE_LOGGER.info(`Processing fundraiser registration: ${fundraiserSignupId}`);

            const childKey = Utils.getChildKey({ versionPrefix, childId });
            const childRegisteredKey = Utils.getRegisteredChildKey({ versionPrefix, childId });

            const feed = JSON.parse(
                await RedisUtil.getHashValue(
                    redis,
                    Utils.getFundraiserDetailsKey({ versionPrefix, fundraiserId }),
                    'details'
                )
            );
            CONSOLE_LOGGER.info(`Feed fetched for fundraiser: ${fundraiserId}`);

            let childDetails = childrenDetailsMap[childId];

            if (!childDetails) {
                childDetails = await DBModelHelperService.getChildDetailsWithAttributes(childId, CONSTANTS.CHILD_ATTRIBUTES);
                childrenDetailsMap[childId] = childDetails;
            }
            CONSOLE_LOGGER.info(`Child details fetched for child: ${childId}`);

            if (childDetails && feed) {
                if (
                    fundraiserSignup.paymentDetails.paymentStatus !==
                    CONSTANTS.PAYMENT_STATUS.PAYMENT_INITIATED
                ) {
                    CONSOLE_LOGGER.info(`Adding or updating registered fundraiser for registration: ${fundraiserSignupId}`);

                    const feedReference = {
                        isFundraiser: true,
                        eventId: fundraiserId,
                        childId
                    };

                    await FundraiserSignupHelperService.addOrUpdateRegisteredFundraiser({
                        redis,
                        fundraiserSignup,
                        feed,
                        childKey,
                        childRegisteredKey,
                        childDetails,
                        versionPrefix,
                        isPopulatingCache: true,
                        feedReferenceForPopulatingCache: feedReference
                    });
                }
            } else {
                CONSOLE_LOGGER.info(`Child details not found for child: ${childId}`);
            }
        }
    }
}

module.exports = PopulateCacheService;
