const sinon = require('sinon');
const assert = require('assert');
const UpdateFeedsService = require('../updateChildFeedsService');
const Redis = require('ioredis');
const CONSTANTS = require('../../util/constants');
const constantModel = require('../../models/constant.model');

describe('UpdateFeedsService', () => {
    let stubRedisConnect;
    before(() => {
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
    });
    after(() => {
        stubRedisConnect.restore();
    });
    it('should remove past feeds from the child feeds', async () => {
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        let redisStub = sinon.stub(Redis.prototype, 'hget');
        redisStub.withArgs(sinon.match.string, 'details').resolves(JSON.stringify({
            endDateTime: new Date().toISOString(),
            eventType: CONSTANTS.EVENT_TYPES.EVENT,
            endDate: new Date().toISOString(),
            isFundraiser: true
        }));
        redisStub = sinon.stub(Redis.prototype, 'zrevrangebyscore').resolves([JSON.stringify({ eventId: '1', child: { id: '1' } }),
            JSON.stringify({ fundraiserId: '1', child: { id: '1' } })]);
        redisStub = sinon.stub(Redis.prototype, 'keys')
            .resolves(['v1:event-details:1'])
            .withArgs('v1:child-events:*')
            .resolves(['v1:child-events:1'])
            .withArgs('v1:user-events:*')
            .resolves(['v1:user-events:1']);

        redisStub = sinon.stub(Redis.prototype, 'info').resolves('memory');
        sinon.stub(Redis.prototype, 'pipeline').returnsThis();
        sinon.stub(Redis.prototype, 'zrem').returnsThis();
        sinon.stub(Redis.prototype, 'exec').resolves();

        sinon.stub(constantModel, 'get')
            .onFirstCall()
            .resolves({ value: 'v1' })
            .onSecondCall()
            .resolves({ value: '15' });

        await UpdateFeedsService.updateChildFeeds();
        assert(redisStub.called);

        sinon.restore();
    });

    it('should remove past posts from the child feeds', async () => {
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        let redisStub = sinon.stub(Redis.prototype, 'hget');
        redisStub.withArgs(sinon.match.string, 'details').resolves(JSON.stringify({
            publishedDate: MOMENT().subtract(16, 'days'),
            eventType: CONSTANTS.EVENT_TYPES.EVENT,
            isPost: true
        }));
        redisStub = sinon.stub(Redis.prototype, 'zrevrangebyscore').resolves([
            JSON.stringify({ postId: '1', child: { id: '1' } })
        ]);
        redisStub = sinon.stub(Redis.prototype, 'keys')
            .resolves(['v1:event-details:1'])
            .withArgs('v1:child-events:*')
            .resolves(['v1:child-events:1'])
            .withArgs('v1:user-events:*')
            .resolves(['v1:user-events:1']);
        redisStub = sinon.stub(Redis.prototype, 'info').resolves('memory');
        sinon.stub(Redis.prototype, 'pipeline').returnsThis();
        sinon.stub(Redis.prototype, 'zrem').returnsThis();
        sinon.stub(Redis.prototype, 'exec').resolves();
        sinon.stub(constantModel, 'get').
            onFirstCall()
            .resolves({ value: 'v1' })
            .onSecondCall()
            .resolves({ value: '15' });

        await UpdateFeedsService.updateChildFeeds();
        assert(redisStub.called);

        sinon.restore();
    });

    it('should skip if no past events in the feeds', async () => {
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        let redisStub = sinon.stub(Redis.prototype, 'hget');
        redisStub.withArgs(sinon.match.string, 'details').resolves(JSON.stringify({
            endDateTime: new Date().toISOString(),
            eventType: 'calendar'
        }));
        redisStub = sinon.stub(Redis.prototype, 'zrevrangebyscore').resolves([JSON.stringify({ eventId: '1', child: { id: '1' } })]);
        redisStub = sinon.stub(Redis.prototype, 'keys')
            .resolves(['v1:event-details:1'])
            .withArgs('v1:child-events:*')
            .resolves(['v1:child-events:1'])
            .withArgs('v1:user-events:*')
            .resolves(['v1:user-events:1']);
        redisStub = sinon.stub(Redis.prototype, 'info').resolves('memory');
        sinon.stub(Redis.prototype, 'pipeline').returnsThis();
        sinon.stub(Redis.prototype, 'zrem').returnsThis();
        sinon.stub(Redis.prototype, 'exec').resolves();
        sinon.stub(constantModel, 'get')
            .onFirstCall()
            .resolves({ value: 'v1' })
            .onSecondCall()
            .resolves({ value: '15' });

        await UpdateFeedsService.updateChildFeeds();
        assert(redisStub.called);

        sinon.restore();
    });

    it('should handle errors', async () => {
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        const error = new Error('Test error');
        const redisStub = sinon.stub(Redis.prototype, 'info').throws(error);
        sinon.stub(Redis.prototype, 'keys')
            .resolves(['v1:event-details:1'])
            .withArgs('v1:child-events:*')
            .resolves(['v1:child-events:1'])
            .withArgs('v1:user-events:*')
            .resolves(['v1:user-events:1']);
        sinon.stub(constantModel, 'get')
            .onFirstCall()
            .resolves({ value: 'v1' })
            .onSecondCall()
            .resolves({ value: '15' });

        await UpdateFeedsService.updateChildFeeds();
        assert(redisStub.called);
        sinon.restore();
    });
});
