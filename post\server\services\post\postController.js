const PostService = require('./postService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for post routes.
 */
class PostController {
    /**
   * @desc This function is being used to add post
   * <AUTHOR>
   * @since 08/11/2023
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async addPost (req, res) {
        try {
            const data = await PostService.addPost(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.ADD_POST_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in add post:', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
    /**
   * @desc This function is being used to add multiple posts
   * <AUTHOR>
   * @since 18/01/2024
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async addMultiplePost (req, res) {
        try {
            const data = await PostService.addMultiplePost(
                req,
                res.locals.user,
                res.__
            );
            Utils.sendResponse(null, data, res, MESSAGES.ADD_POST_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in add post', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to update post
   * <AUTHOR>
   * @since 16/11/2023
   * @param {Object} req Request
   * @param {Object} res Response
   */
    static async updatePost (req, res) {
        try {
            const data = await PostService.updatePost(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.POST_UPDATE_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in update post', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to get list of posts
   * <AUTHOR>
   * @since 15/11/2023
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async getPostList (req, res) {
        try {
            const data = await PostService.getPostList(req, res.locals.user);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get post list', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }



    /**
   * @desc This function is being used to get post details
   * <AUTHOR>
   * @since 15/11/2023
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async getPostDetails (req, res) {
        try {
            const data = await PostService.getPostDetails(
                req,
                res.locals.user,
                res.__
            );
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get post details', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to update status of post to published
   * <AUTHOR>
   * @since 15/11/2023
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async publishPost (req, res) {
        try {
            const data = await PostService.publishPost(
                req,
                res.locals.user,
                res.__
            );
            Utils.sendResponse(null, data, res, MESSAGES.POST_PUBLISH_SUCCESS);
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to delete post
   * <AUTHOR>
   * @since 15/11/2023
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async deletePost (req, res) {
        try {
            const data = await PostService.deletePost(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.POST_DELETE_SUCCESS);
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }

}

module.exports = PostController;
