/* eslint-disable max-len */
module.exports = {
    // For AES, this is always 16
    IV_LENGTH: 16,
    LOG_LEVEL: 'debug',
    REGEX: {
        EMAIL: /^[A-Za-z0-9](\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\.([a-z]{1,6}\.)?[a-z]{2,6}$/,
        NAME: /^[a-zA-Z0-9,'~._^ -]{3,100}$/,
        ALPHA_ONLY: /^[a-zA-Z']*$/,
        ALPHA_SPECIAL_CHAR: /^[ A-Za-z0-9_@./#&+-]*$/,
        ALPHA_SPECIAL_CHAR_EXCEPT_NUMBER: /^[ A-Za-z_@./#&+-]*$/,
        FULL_ACCESS: /^[^<> ?//\\]+$/,
        ALPHA_NUMARIC: /^[\w@ ]+$/,
        templateName: /^[ A-Za-z0-9_@./#&+-]*$/,
        SUBJECT: /^[ A-Za-z0-9_@./#&+-]*$/,
        URL: /(http(s)?:\/\/www\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\.[a-z]{2,4}\b([-a-zA-Z0-9@:%+.~#?&//=]*)/,
        MOBILE: /^([+]\d{1,2})?\d{10}$/,
        PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
        UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
        DONATION_AMOUNT: /^([0-9]\d{0,5})(\.\d{0,2})?$/
    },
    VERIFIED: {
        PENDING: 0,
        ACTIVE: 1
    },
    STATUS: {
        INACTIVE: 'inactive',
        ACTIVE: 'active',
        SUSPENDED: 'suspended',
        FREEZED: 'freezed',
        PUBLISHED: 'published'
    },
    ENVIRONMENT: {
        TESTING: 'testing',
        LOCAL: 'local',
        DEV: 'dev',
        PRODUCTION: 'production'
    },
    CLIENT_INFO: {
        EMAIL: '<EMAIL>',
        HELP_EMAIL: '<EMAIL>'
    },
    APP_NAME: 'Vaalee',
    ROLE: {
        USER: 1,
        ORG_ADMIN: 3,
        ADMIN: 4
    },
    ACCESS_LEVEL: {
        APP: 'app',
        ORG_APP: 'org_app',
        ROOT: 'root'
    },
    AWS_S3_PUBLIC_BUCKET: 'vaalee-assets',
    GROUP_TYPES: {
        ORGANIZATION: 'organization',
        ORGANIZATION_ADMIN: 'organization_admin',
        EVENT: 'event'
    },
    GROUP_CONVERSATION_STATUS: {
        ACTIVE: 'active',
        DELETED: 'deleted',
        EXPIRED: 'expired'
    },
    GROUP_MEMBER_STATUS: {
        ACTIVE: 'active',
        DISABLED: 'disabled',
        REMOVED: 'removed'
    },
    ORGANIZATION_ROLE: {
        SUPER_ADMIN: 'super admin',
        ADMIN: 'admin',
        EDITOR: 'editor'
    },
    MESSAGE_LAZY_LOADING_LIMIT: 25,
    INITIAL_MESSAGE_FETCH_LIMIT: 20,
    COMPRESSION_QUALITY: 60,
    CONNECTION_STATUS: {
        CONNECTED: 'connected',
        REQUESTED_TO: 'requestedTo',
        REQUESTED_BY: 'requestedBy'
    },
    FLAG_MESSAGE_REASON: {
        INAPPROPRIATE_CONTENT: 'Inappropriate Content',
        SPAM: 'Spam',
        MISINFORMATION: 'Misinformation',
        OFFENSIVE_LANGUAGE: 'Offensive Language',
        IMPERSONATION: 'Impersonation',
        HATE_SPEECH: 'Hate Speech',
        SCAMMING: 'Scamming'
    },
    FLAG_MESSAGE_STATUS: {
        PENDING: 'pending',
        MESSAGE_DELETED: 'message_deleted',
        USER_DISABLED_COMMENTING: 'user_disabled_commenting',
        DISMISSED: 'dismissed'
    },
    ALLOWED_REACTIONS: ['1F44D', '1F44E', '2665'],
    ACTION_TYPES: {
        GET_GROUP_MESSAGES: {
            LAZY_LOAD_GROUP_MESSAGES: 'LAZY_LOAD_GROUP_MESSAGES',
            GET_GROUP_LIST_AND_MEMBERS: 'GET_GROUP_LIST_AND_MEMBERS',
            GET_USER_CHILDREN_LIST: 'GET_USER_CHILDREN_LIST',
            GET_GROUP_MEMBERS_AND_MESSAGES: 'GET_GROUP_MEMBERS_AND_MESSAGES'
        },
        GET_PERSONAL_MESSAGES: {
            LAZY_LOAD_PERSONAL_MESSAGES: 'LAZY_LOAD_PERSONAL_MESSAGES',
            GET_USER_CONNECTED_CHILDREN: 'GET_USER_CONNECTED_CHILDREN',
            GET_USER_CONNECTIONS_LIST: 'GET_USER_CONNECTIONS_LIST'
        },
        SEND_GROUP_MESSAGE: {
            NEW_GROUP_MESSAGE: 'NEW_GROUP_MESSAGE',
            EDIT_GROUP_MESSAGE: 'EDIT_GROUP_MESSAGE',
            DELETE_GROUP_MESSAGE: 'DELETE_GROUP_MESSAGE',
            ADD_REACTION: 'ADD_REACTION',
            UPDATE_REACTION: 'UPDATE_REACTION',
            REMOVE_REACTION: 'REMOVE_REACTION',
            READ_GROUP_MESSAGE: 'READ_GROUP_MESSAGE',
            MUTE_GROUP_CONVERSATION: 'MUTE_GROUP_CONVERSATION'
        },
        SEND_PERSONAL_MESSAGE: {
            NEW_PERSONAL_MESSAGE: 'NEW_PERSONAL_MESSAGE',
            START_PERSONAL_CONVERSATION: 'START_PERSONAL_CONVERSATION',
            READ_PERSONAL_MESSAGE: 'READ_PERSONAL_MESSAGE',
            EDIT_PERSONAL_MESSAGE: 'EDIT_PERSONAL_MESSAGE',
            DELETE_PERSONAL_MESSAGE: 'DELETE_PERSONAL_MESSAGE',
            ADD_PERSONAL_MESSAGE_REACTION: 'ADD_PERSONAL_MESSAGE_REACTION',
            UPDATE_PERSONAL_MESSAGE_REACTION: 'UPDATE_PERSONAL_MESSAGE_REACTION',
            REMOVE_PERSONAL_MESSAGE_REACTION: 'REMOVE_PERSONAL_MESSAGE_REACTION',
            MUTE_PERSONAL_CONVERSATION: 'MUTE_PERSONAL_CONVERSATION',
            BLOCK_PERSONAL_CONVERSATION: 'BLOCK_PERSONAL_CONVERSATION',
            DELETE_PERSONAL_CONVERSATION: 'DELETE_PERSONAL_CONVERSATION'
        },
        SEND_COMPANION_MESSAGE: {
            NEW_GROUP_COMPANION_MESSAGE: 'NEW_GROUP_COMPANION_MESSAGE',
            AI_COMPANION_GROUP_MESSAGE_STREAMING: 'AI_COMPANION_GROUP_MESSAGE_STREAMING',
            NEW_COMPANION_MESSAGE: 'NEW_COMPANION_MESSAGE',
            AI_COMPANION_MESSAGE_STREAMING: 'AI_COMPANION_MESSAGE_STREAMING'
        }
    },
    AI_COMPANION_SENDER_ID: 'AI_COMPANION',
    AI_COMPANION_GLOBAL_INDEX: 'vaalee-kb',
    KEY_FOR_USER_CONVERSATIONS: 'conversation-list',
    KEY_FOR_CONVERSATION_DETAILS: 'conversation-details',
    KEY_FOR_CONVERSATION_MEMBERS: 'conversation-members',
    KEY_FOR_CONVERSATION_MESSAGES: 'conversation-messages',
    KEY_FOR_USER_DETAILS: 'user-details',
    KEY_FOR_CHILD_DETAILS: 'child-details',
    KEY_FOR_ORGANIZATION_DETAILS: 'organization-details',
    FEED_VERSION_PREFIX: 'FeedVersionPrefix',
    ALLOWED_MESSAGE_SIZE: 125 * 1024, // 125kb
    DEFAULT_PAGE_SIZE: 10,
    AI_MODELS: {
        DEFAULT: 'gpt-4o-mini',
        GPT_4O_MINI: 'gpt-4o-mini',
        GPT_41_NANO: 'gpt-4.1-nano'
    },
    AI_TEMPERATURE: 0,
    CLOUDWATCH_METRIC_NAMESPACE: 'Vaalee/AICompanion',
    CLOUDWATCH_METRIC_NAMES: {
        AI_COMPANION_QUERY_TIME: 'AICompanionQueryTime',
        AI_COMPANION_ACCURACY: 'AICompanionAccuracy'
    },
    AI_COMPANION_FEEDBACK: {
        POSITIVE: 'positive',
        NEGATIVE: 'negative'
    },
    AI_COMPANION_FEEDBACK_VALUES: ['positive', 'negative'],
    DATE_PREFIX: ' Today\'s Date and Time:',
    SOMETHING_WENT_WRONG: 'Something went wrong!'
};
