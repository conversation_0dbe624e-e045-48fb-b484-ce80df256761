/* eslint-disable max-len */
const crypto = require('crypto');
const EmailService = require('./sendEmail');
const secretKey = process.env.HMAC_SECRET_KEY;
const HTTPStatus = require('../util/http-status');


/**
 * This class reprasents common utilities for application
 */
class Utils {
    static errorResponse () {
        return JSON.parse(
            JSON.stringify({
                status: 0,
                data: {},
                message: ''
            })
        );
    }

    static successResponse () {
        return JSON.parse(
            JSON.stringify({
                status: 1,
                data: {},
                message: ''
            })
        );
    }

    /**
     * This function is being used to add pagination for user table
     * @auther Growexx
     * @param {string} error Error Message
     * @param {Object} data Object to send in response
     * @param {Object} res Response Object
     * @param {string} successMessage success message
     * @param {Object} additionalData additional data outside of data object in response
     * @param {string} successMessageVars
     * @since 01/03/2021
     */
    static sendResponse (error, data, res, successMessage, successMessageVars) {
        let responseObject;

        if (error) {
            let status;
            responseObject = Utils.errorResponse();
            if (typeof error === 'object') {
                responseObject.message = error.message
                    ? error.message : res.__('ERROR_MSG');
                status = error.statusCode ? error.statusCode : HTTPStatus.BAD_REQUEST;
            } else {
                responseObject.message = res.__(error);
                status = HTTPStatus.BAD_REQUEST;
            }

            responseObject.data = error.data;
            res.status(status).send(responseObject);
        } else {
            responseObject = Utils.successResponse();
            responseObject.message = successMessageVars
                ? res.__.apply('', [successMessage].concat(successMessageVars))
                : successMessage;
            responseObject.data = data;
            res.status(HTTPStatus.OK).send(responseObject);
        }
    }

    /**
     * This function is being used to generate Otp
     * @auther Growexx
     * @since 19/09/2023
     */
    static generateOtp () {
        if (process.env.NODE_ENV === 'testing') {
            return 1234;
        } else {
            return Math.floor(Math.random() * 900000) + 100000;
        }
    }

    /**
     * @desc This function is being used to send otp to email and number
     * <AUTHOR>
     * @since 16/10/2023
     * @param {Object} reqObj reqObj
     */
    static async sendOtpToMail (otp, reqObj, smsMessage, templateFile, subject) {
        const { firstName, lastName, email } = reqObj;
        const template = `emailTemplates/${templateFile}`;
        const currentDate = new Date();
        const year = currentDate.getFullYear();
        const templateVariables = { otp, year, email: CONSTANTS.CLIENT_INFO.EMAIL, appname: CONSTANTS.APP_NAME, username: `${firstName} ${lastName}` };
        await EmailService.prepareAndSendEmail([email], subject, template, templateVariables);
    }

    /**
     * This function is being used to return user object eliminating sensitive information
     * @auther Growexx
     * @param {Object} user user object from database
     * @since 17/10/2023
     */
    static returnUser (user) {
        return {
            id: user.id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            countryCode: user.countryCode || '',
            phoneNumber: user.phoneNumber || '',
            role: user.role,
            accessLevel: user.accessLevel,
            children: user.children,
            associatedOrganizations: user.associatedOrganizations,
            token: user.token,
            refreshToken: user.refreshToken
        };
    }

    /**
     * Selects specific attributes from an object.
     *
     * @param {string[]} attributes - The names of the attributes to select.
     * @param {Object} object - The object to select attributes from.
     * @returns {Object} An object that only contains the selected attributes.
     */
    static selectAttributes (attributes, object) {
        return attributes.reduce((obj, key) => ({ ...obj, [key]: object[key] }), {});
    }

    static generateHmac (url) {
        const currentDate = MOMENT().utc().format('MMYYYYDD'); // 03202426
        const reqURL = url; // /feed/path
        const message = currentDate + reqURL;
        const calculatedHmac = crypto.createHmac('sha256', secretKey).update(message).digest('hex');
        return calculatedHmac;
    }

    /**
   * This function is being used to overwrite request function for adding reqtoken
   * @auther Growexx
   * @param {import('supertest')} request
   * @since 28/03/2024
   */
    static addCommonReqTokenForHMac (request) {
        const originalEnd = request.Test.prototype.end;

        request.Test.prototype.end = function (callback) {
            const currentParsedUrl = new URL(this.url);
            this.set('reqtoken', Utils.generateHmac(currentParsedUrl.pathname));
            return originalEnd.call(this, callback);
        };
    }
}

module.exports = Utils;
