const UserProfileService = require('./userProfileService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for get user details.
 */
class UserProfileController {
    /**
     * @desc This function is being used to get user details user
     * <AUTHOR>
     * @since 19/10/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async getUserDetails (req, res) {
        try {
            const data = await UserProfileService.getUserDetails(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in getUserDetails', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to change password of user
     * <AUTHOR>
     * @since 22/02/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async changePassword (req, res) {
        try {
            const data = await UserProfileService.changePassword(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS_CHANGE_PASSWORD);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in changePassword', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to update profile of user
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async updateUserProfile (req, res) {
        try {
            const data = await UserProfileService.updateUserProfile(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS_UPDATE_PROFILE);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in updateProfile', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to get connections list of user
     * <AUTHOR>
     * @since 04/03/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async getConnectionsList (req, res) {
        try {
            const data = await UserProfileService.getConnectionsList(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in getConnectionsList', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to delete user
     * <AUTHOR>
     * @since 26/04/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async deleteUser (req, res) {
        try {
            const data = await UserProfileService.deleteUser(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in deleteUser', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to check feed status
     * <AUTHOR>
     * @since 26/07/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async checkIsFeedGenerated (req, res) {
        try {
            const data = await UserProfileService.checkIsFeedGenerated(res.locals.user);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in checking feed status: ' + error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
    /**
     * This function retrieves a list of purchased memberships for a user.
     * <AUTHOR>
     * @since 31/07/2024
     * @param {Object} req Request
     * @param {function} res Response
    */
    static async getPurchasedMembershipsList (req, res) {
        try {
            const data = await UserProfileService.getPurchasedMembershipsList(res.locals.user);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in getPurchasedMembershipsList: ' + error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * This function updates the chat guidelines read status for a user.
     * <AUTHOR>
     * @since 12/09/2024
     * @param {Object} req Request
     * @param {function} res Response
    */
    static async updateChatGuidelinesRead (req, res) {
        try {
            const data = await UserProfileService.updateChatGuidelinesRead(res.locals.user);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in updateChatGuidelinesRead: ' + error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = UserProfileController;
