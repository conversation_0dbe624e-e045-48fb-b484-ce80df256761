const SignUpService = require('./signUpService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for signup.
 */
class SignUpController {
    /**
     * @desc This function is being used to signUp parent user
     * <AUTHOR>
     * @since 16/10/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {String} req.body.firstName firstName
     * @param {String} req.body.lastName lastName
     * @param {String} req.body.phoneNumber phoneNumber
     * @param {String} req.body.countryCode countryCode
     * @param {String} req.body.email email
     * @param {Object} res Response
     */
    static async signUp (req, res) {
        try {
            CONSOLE_LOGGER.time('signup');
            const data = await SignUpService.signUp(req, res.__);
            CONSOLE_LOGGER.timeEnd('signup');
            Utils.sendResponse(null, data, res, MESSAGES.REGISTER_SUCCESS);
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to verify user account
     * <AUTHOR>
     * @since 16/10/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {String} req.body.email email
     * @param {Object} req.body.token token
     * @param {Object} req.body.fcmToken fcmToken
     * @param {Object} res Response
     */
    static async verifyAccount (req, res) {
        try {
            const data = await SignUpService.verifyAccount(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.USER_VERIFY_SUCCESS);
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to resendOTP
     * <AUTHOR>
     * @since 16/10/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {String} req.body.email email
     * @param {Object} res Response
     */
    static async resendOTP (req, res) {
        try {
            const data = await SignUpService.resendOTP(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.RESEND_OTP_SUCCESS);
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = SignUpController;

