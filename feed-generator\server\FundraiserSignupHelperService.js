const RedisUtil = require('./redisUtil');
const MOMENT = require('moment');
const CONSTANTS = require('./constants');
const DBModelHelperService = require('./DBModelHelperService');
const NotificationHelperService = require('./NotificationHelperService');
const Utils = require('./Utils');

class FundraiserSignupHelperService {
    /**
     * @description Add or update the fundraiser signup in the child-registered-events sorted set
     * <AUTHOR>
     * @param {Redis} redis - The redis instance
     * @param {Object} fundraiserSignup - The fundraiser signup object
     * @param {Object} feed - The feed object
     * @param {string} childKey - The child key
     * @param {string} childRegisteredKey - The child registered key
     * @param {Object} childDetails - The child details object
     * @param {boolean} isPopulatingCache - Whether it is populating cache
     * @param {Object} feedReferenceForPopulatingCache - The feed reference for populating the cache
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<Array>} The keys
     */
    static async addOrUpdateRegisteredFundraiser ({
        redis,
        fundraiserSignup,
        feed,
        childKey,
        childRegisteredKey,
        childDetails,
        versionPrefix,
        isPopulatingCache = false,
        feedReferenceForPopulatingCache = null
    }) {
        const timestamp = MOMENT(feed.startDate).toDate().getTime();
        const { guardians = [] } = childDetails;
        const keys = [
            { key: childKey, keyRegistered: childRegisteredKey }
        ];
        const shouldOnlyGenerateRegisterKey = childKey === childRegisteredKey;
        guardians.forEach((guardian) => {
            const registeredUserKey = Utils.getRegisteredUserKey({ versionPrefix, userId: guardian });
            keys.push({
                key: shouldOnlyGenerateRegisterKey
                    ? registeredUserKey
                    : Utils.getUserKey({ versionPrefix, userId: guardian }),
                keyRegistered: registeredUserKey
            });
        });

        if (childKey === childRegisteredKey) {
            const childFundraisers = await RedisUtil.getElementsOfSortedSetByScore(
                redis,
                childKey,
                timestamp,
                CONSTANTS.PLUS_INF
            );

            const fundraiserToUpdate = childFundraisers.find((event) => {
                const parsedEvent = JSON.parse(event);
                return parsedEvent.fundraiserSignupId === fundraiserSignup.id;
            });

            if (fundraiserToUpdate) {
                for (const { key, keyRegistered } of keys) {
                    await this.updateFundraiserSignUpStatus({
                        redis,
                        keyRegistered,
                        fundraiserSignup,
                        timestamp,
                        fundraiser: fundraiserToUpdate,
                        keyForDeletingFeed: key,
                        deleteOldFeed: true
                    });
                }
            }
        } else {
            const childFundraisers = await RedisUtil.getElementsOfSortedSetByScore(
                redis,
                childKey,
                timestamp,
                CONSTANTS.PLUS_INF
            );

            const fundraiserToUpdate = childFundraisers.find((event) => {
                const parsedEvent = JSON.parse(event);
                return parsedEvent.fundraiserId === fundraiserSignup.eventId;
            });

            if (fundraiserToUpdate || isPopulatingCache) {
                for (const { key, keyRegistered } of keys) {
                    if (feed.fundraiserType === CONSTANTS.FUNDRAISER_TYPES.BOOSTER) {
                        await this.updateFundraiserSignUpStatus({
                            redis,
                            keyRegistered,
                            fundraiserSignup,
                            timestamp,
                            isPopulatingCache,
                            feedReferenceForPopulatingCache,
                            fundraiser: fundraiserToUpdate,
                            keyForDeletingFeed: key,
                            deleteOldFeed: true
                        });
                    } else {
                        await this.updateFundraiserSignUpStatus({
                            redis,
                            keyRegistered,
                            fundraiserSignup,
                            timestamp,
                            isPopulatingCache,
                            feedReferenceForPopulatingCache,
                            fundraiser: fundraiserToUpdate
                        });
                    }
                }
            }
        }

        return keys;
    }

    /**
     * @description Update the fundraiser signup status in the child-registered-events sorted set
     * <AUTHOR>
     * @param {Redis} redis - The redis instance
     * @param {string} keyRegistered - The key for the registered events
     * @param {string} fundraiser - The fundraiser object
     * @param {Object} fundraiserSignup - The fundraiser signup object
     * @param {number} timestamp - The timestamp
     * @param {string} keyForDeletingFeed - The key for deleting the feed
     * @param {boolean} deleteOldFeed - Whether to delete the old feed
     * @param {boolean} isPopulatingCache - Whether it is populating cache
     * @param {Object} feedReferenceForPopulatingCache - The feed reference for populating the cache
     * @returns {Promise<string>} The fundraiser id
     */
    static async updateFundraiserSignUpStatus ({
        redis,
        keyRegistered,
        fundraiser,
        fundraiserSignup,
        timestamp,
        keyForDeletingFeed,
        deleteOldFeed = false,
        isPopulatingCache = false,
        feedReferenceForPopulatingCache = null
    }) {
        if (deleteOldFeed) {
            await RedisUtil.removeMemberFromSortedSet(
                redis,
                keyForDeletingFeed,
                fundraiser
            );
        }

        const feedReference = (isPopulatingCache && !fundraiser)
            ? feedReferenceForPopulatingCache
            : JSON.parse(fundraiser);

        await RedisUtil.addEventReferenceToSortedSet(
            redis,
            keyRegistered,
            timestamp,
            JSON.stringify({
                ...feedReference,
                fundraiserSignupId: fundraiserSignup.id,
                isSignedUp: true,
                purchasedProducts: fundraiserSignup.purchasedProducts,
                status: fundraiserSignup.paymentDetails.paymentStatus,
                transactionFee: fundraiserSignup.paymentDetails.transactionFee,
                platformFeeCoveredBy:
                    fundraiserSignup.paymentDetails.platformFeeCoveredBy,
                paymentType: fundraiserSignup.paymentDetails.paymentType,
                membershipDiscount: fundraiserSignup.paymentDetails.membershipDiscount,
                boosterGoalForChild: fundraiserSignup.boosterGoalForChild,
                boosterMessageForChild: fundraiserSignup.boosterMessageForChild
            })
        );
        return feedReference.id;
    }

    /**
     * @description Remove the fundraiser signup feed
     * <AUTHOR>
     * @param {Redis} redis - The redis instance
     * @param {Object} fundraiserSignup - The fundraiser signup object
     * @param {string} childRegisteredKey - The child registered key
     * @param {Object} feed - The feed object
     * @param {Object} childDetails - The child details object
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<Array>} The keys
     */
    static async removeFundraiserSignupFeed ({
        redis,
        fundraiserSignup,
        childRegisteredKey,
        feed,
        childDetails,
        versionPrefix
    }) {
        const { guardians = [] } = childDetails;
        const timestamp = MOMENT(feed.startDate).toDate().getTime();

        const keys = [childRegisteredKey];
        guardians.forEach((guardian) => {
            keys.push(Utils.getRegisteredUserKey({ versionPrefix, userId: guardian }));
        });

        for (const key of keys) {
            const registeredFundraisers =
                await RedisUtil.getElementsOfSortedSetByScore(
                    redis,
                    key,
                    timestamp,
                    CONSTANTS.PLUS_INF
                );

            const matchingFundraisers = registeredFundraisers.filter(
                (fundraiser) => {
                    const parsedFundraiser = JSON.parse(fundraiser);
                    return (
                        parsedFundraiser.fundraiserId === fundraiserSignup.eventId &&
                        parsedFundraiser.fundraiserSignupId === fundraiserSignup.id
                    );
                }
            );

            for (const childFundraiserToRemove of matchingFundraisers) {
                await RedisUtil.removeMemberFromSortedSet(
                    redis,
                    childRegisteredKey,
                    childFundraiserToRemove
                );
            }
        }

        return keys;
    }

    /**
     * @description Handle successful payment for org managed donation
     * <AUTHOR>
     * @param {Redis} redis - The redis instance
     * @param {Object} orgManagedFundraiserBoosterDonation - The org managed fundraiser booster donation object
     * @param {Object} childDetails - The child details object
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<number>} The total amount raised
     */
    static async handleSuccessfulPaymentForOrgManagedDonation ({
        redis,
        orgManagedFundraiserBoosterDonation,
        childDetails,
        versionPrefix
    }) {
        const { childId, fundraiserBoosterId } = orgManagedFundraiserBoosterDonation;

        const childRegisteredKey = Utils.getRegisteredChildKey({ versionPrefix, childId });
        const keys = [{ key: childRegisteredKey, isChildKey: true }];

        const { guardians = [] } = childDetails;
        guardians.forEach((guardian) => {
            keys.push({ key: Utils.getRegisteredUserKey({ versionPrefix, userId: guardian }), isChildKey: false });
        });

        const donations = await DBModelHelperService.getOrgManagedFundraiserBoosterDonationsForChild(
            fundraiserBoosterId,
            childId,
            CONSTANTS.PAYMENT_STATUS.SUCCESS
        );

        const totalAmountRaised = donations.reduce(
            (total, donation) => total + donation.amount,
            0
        );

        const childRegisteredEvents =
            await RedisUtil.getElementsOfSortedSetByScore(
                redis,
                childRegisteredKey,
                CONSTANTS.MINUS_INF,
                CONSTANTS.PLUS_INF
            );

        const registeredEvent = childRegisteredEvents?.find((event) => {
            const feed = JSON.parse(event);
            return (
                feed.fundraiserId === fundraiserBoosterId
            );
        });


        if (registeredEvent) {
            const parsedEvent = JSON.parse(registeredEvent);
            parsedEvent.amountRaised = totalAmountRaised;

            for (const { key, isChildKey } of keys) {
                const score = await RedisUtil.getScoreOfMember(
                    redis,
                    key,
                    registeredEvent
                );

                await RedisUtil.removeMemberFromSortedSet(
                    redis,
                    key,
                    registeredEvent
                );

                await RedisUtil.addEventReferenceToSortedSet(
                    redis,
                    key,
                    score,
                    JSON.stringify(parsedEvent)
                );

                if (isChildKey) {
                    const fundraiserFeed = await RedisUtil.getHashValue(
                        redis,
                        Utils.getFundraiserDetailsKey({
                            versionPrefix,
                            fundraiserId: orgManagedFundraiserBoosterDonation.fundraiserBoosterId
                        }),
                        'details'
                    );

                    await NotificationHelperService.sendNotificationForBoosterDonation(
                        parsedEvent,
                        fundraiserFeed,
                        totalAmountRaised,
                        orgManagedFundraiserBoosterDonation,
                        score
                    );
                }
            }
        }

        return totalAmountRaised;
    }
}

module.exports = FundraiserSignupHelperService;
