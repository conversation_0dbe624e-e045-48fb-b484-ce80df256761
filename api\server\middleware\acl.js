const Utils = require('../util/utilFunctions');
const HTTPStatus = require('../util/http-status');

module.exports = function (req, res, next) {
    const accessList = {
        'app': [{ method: 'GET', path: '/api/user/feeds' }],
        'org_app': [
            { method: 'GET', path: '/api/organization/users' },
            { method: 'POST', path: '/api/organization/user' },
            { method: 'PUT', path: '/api/organization/user' },
            { method: 'DELETE', path: '/api/organization/user' },
            { method: 'GET', path: '/api/organization' },
            { method: 'PUT', path: '/api/organization' },
            { method: 'GET', path: '/api/organization/associatedOrganizations' },
            { method: 'GET', path: '/api/organization/generate-presigned-url' }
        ],
        'root': [
            { method: 'GET', path: '/api/organization/users' },
            { method: 'POST', path: '/api/organization/user' },
            { method: 'PUT', path: '/api/organization/user' },
            { method: 'DELETE', path: '/api/organization/user' },
            { method: 'POST', path: '/api/organization/resend-onboarding' },
            { method: 'POST', path: '/api/organization' },
            { method: 'PUT', path: '/api/organization' },
            { method: 'GET', path: '/api/organization/list' },
            { method: 'GET', path: '/api/organization' },
            { method: 'PUT', path: '/api/organization/status' },
            { method: 'DELETE', path: '/api/organization' },
            { method: 'GET', path: '/api/organization/users' },
            { method: 'POST', path: '/api/organization/user' },
            { method: 'PUT', path: '/api/organization/user' },
            { method: 'DELETE', path: '/api/organization/user' },
            { method: 'GET', path: '/api/organization/associatedOrganizations' },
            { method: 'POST', path: '/api/organization/associatedOrganizations' },
            { method: 'GET', path: '/api/organization/generate-presigned-url' }
        ]
    };
    const accessLevel = res.locals.user.accessLevel;
    const isAllowed = _.find(accessList[accessLevel], {
        method: req.method,
        path: req.originalUrl.split('?')[0]
    });
    if (isAllowed) {
        next();
    } else {
        const responseObject = Utils.errorResponse();
        responseObject.message = res.__('ACCESS_DENIED');
        res.status(HTTPStatus.NOT_ACCEPTABLE).send(responseObject);
        return;
    }
};
