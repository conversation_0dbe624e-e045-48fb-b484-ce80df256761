const sinon = require('sinon');
const AWS = require('aws-sdk');
const AWSMock = require('jest-aws-sdk-mock');
const Redis = require('ioredis');
const CONSOLE_LOGGER = require('../server/logger');
const handler = require('../index');
const RedisUtil = require('../server/redisUtil');
const { beforeEach, afterEach } = require('mocha');
const ConstantModel = require('../server/models/constant.model');
const CONSTANTS = require('../server/constants');

const assert = sinon.assert;

describe('Groups', () => {
    let sandbox;
    let stubRedisConnect;
    let pipelineExecStub;
    let pipelineStub;

    before(() => {
        AWSMock.setSDKInstance(AWS);
        sandbox = sinon.createSandbox();
    });

    after(() => {
        AWSMock.restore();
        sinon.restore();
        sandbox.restore();
        pipelineStub.restore();
    });

    beforeEach(() => {
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        pipelineExecStub = sinon.stub();
        pipelineStub = sinon.stub(Redis.prototype, 'pipeline').returns({ exec: pipelineExecStub });
        sinon.stub(ConstantModel, 'get').withArgs(CONSTANTS.FEED_VERSION_PREFIX).resolves({ value: 'v1' });
    });

    afterEach(() => {
        sinon.restore();
    });

    try {
        it('should insert group details in redis when GROUP is inserted in dynamodb', async () => {
            const setHashValueStub = sinon.stub(RedisUtil, 'setHashValue');
            setHashValueStub.resolves(true);

            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Groups/stream/2025-03-03T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'groupId': { 'S': 'groupId1' },
                        'groupType': { 'S': 'organization' },
                        'organizationId': { 'S': 'organizationId1' },
                        'organizationMetaData': { 'M': { 'name': { 'S': 'organizationName1' }, 'logo': { 'S': 'organizationLogo1' } } },
                        'status': { 'S': 'active' },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            assert.calledOnce(setHashValueStub);
        });

        it('should remove group details from redis when GROUP is deleted in dynamodb', async () => {
            const removeMemberFromHashSetStub = sinon.stub(RedisUtil, 'removeMemberFromHashSet');
            removeMemberFromHashSetStub.resolves(true);

            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Groups/stream/2025-03-03T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'groupId': { 'S': 'groupId1' },
                        'groupType': { 'S': 'organization' },
                        'organizationId': { 'S': 'organizationId1' },
                        'organizationMetaData': { 'M': { 'name': { 'S': 'organizationName1' }, 'logo': { 'S': 'organizationLogo1' } } },
                        'status': { 'S': 'expired' },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' }
                    },
                    OldImage: {
                        'groupId': { 'S': 'groupId1' },
                        'groupType': { 'S': 'organization' },
                        'organizationId': { 'S': 'organizationId1' },
                        'organizationMetaData': { 'M': { 'name': { 'S': 'organizationName1' }, 'logo': { 'S': 'organizationLogo1' } } },
                        'status': { 'S': 'active' },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            assert.calledOnce(removeMemberFromHashSetStub);
        });

        it('should update group details in redis when GROUP is updated in dynamodb', async () => {
            const setHashValueStub = sinon.stub(RedisUtil, 'setHashValue');
            setHashValueStub.resolves(true);

            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Groups/stream/2025-03-03T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'groupId': { 'S': 'groupId1' },
                        'groupType': { 'S': 'organization' },
                        'organizationId': { 'S': 'organizationId1' },
                        'organizationMetaData': { 'M': { 'name': { 'S': 'organizationName1' }, 'logo': { 'S': 'organizationLogo1' } } },
                        'status': { 'S': 'active' },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' }
                    },
                    OldImage: {
                        'groupId': { 'S': 'groupId1' },
                        'groupType': { 'S': 'organization' },
                        'organizationId': { 'S': 'organizationId1' },
                        'organizationMetaData': { 'M': { 'name': { 'S': 'organizationName1' }, 'logo': { 'S': 'organizationLogo1' } } },
                        'status': { 'S': 'active' },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            assert.calledOnce(setHashValueStub);
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('GroupMembers', () => {
    let sandbox;
    let stubRedisConnect;
    let pipelineExecStub;
    let pipelineStub;

    before(() => {
        AWSMock.setSDKInstance(AWS);
        sandbox = sinon.createSandbox();
    });

    after(() => {
        AWSMock.restore();
        sinon.restore();
        sandbox.restore();
        pipelineStub.restore();
    });

    beforeEach(() => {
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        pipelineExecStub = sinon.stub();
        pipelineStub = sinon.stub(Redis.prototype, 'pipeline').returns({ exec: pipelineExecStub });
        sinon.stub(ConstantModel, 'get').withArgs(CONSTANTS.FEED_VERSION_PREFIX).resolves({ value: 'v1' });
    });

    afterEach(() => {
        sinon.restore();
    });

    try {
        it('should insert group member details in redis when GROUP_MEMBER is inserted in dynamodb', async () => {
            const setHashValueStub = sinon.stub(RedisUtil, 'setHashValue');
            setHashValueStub.resolves(true);

            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/GroupMembers/stream/2025-03-03T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'id': { 'S': 'groupMemberId1' },
                        'groupId': { 'S': 'groupId1' },
                        'userId': { 'S': 'userId1' },
                        'isAdmin': { 'S': 'false' },
                        'childrenIds': { 'L': [] },
                        'lastReadMessage': { 'M': {} },
                        'muteConversation': { 'S': 'false' },
                        'status': { 'S': 'active' },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            assert.calledTwice(setHashValueStub);
        });

        it('should update group member details in redis when GROUP_MEMBER is updated in dynamodb', async () => {
            const setHashValueStub = sinon.stub(RedisUtil, 'setHashValue');
            setHashValueStub.resolves(true);

            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/GroupMembers/stream/2025-03-03T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'id': { 'S': 'groupMemberId1' },
                        'groupId': { 'S': 'groupId1' },
                        'userId': { 'S': 'userId1' },
                        'isAdmin': { 'S': 'false' },
                        'childrenIds': { 'L': [] },
                        'lastReadMessage': { 'M': {} },
                        'muteConversation': { 'S': 'false' },
                        'status': { 'S': 'active' },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' }
                    },
                    OldImage: {
                        'id': { 'S': 'groupMemberId1' },
                        'groupId': { 'S': 'groupId1' },
                        'userId': { 'S': 'userId1' },
                        'isAdmin': { 'S': 'false' },
                        'childrenIds': { 'L': [] },
                        'lastReadMessage': { 'M': {} },
                        'muteConversation': { 'S': 'false' },
                        'status': { 'S': 'active' },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            assert.calledTwice(setHashValueStub);
        });

        it('should remove group member details from redis when GROUP_MEMBER is deleted in dynamodb', async () => {
            const removeMemberFromHashSetStub = sinon.stub(RedisUtil, 'removeMemberFromHashSet');
            removeMemberFromHashSetStub.resolves(true);

            const record = {
                eventName: 'REMOVE',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/GroupMembers/stream/2025-03-03T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        'id': { 'S': 'groupMemberId1' },
                        'groupId': { 'S': 'groupId1' },
                        'userId': { 'S': 'userId1' },
                        'isAdmin': { 'S': 'false' },
                        'childrenIds': { 'L': [] },
                        'lastReadMessage': { 'M': {} },
                        'muteConversation': { 'S': 'false' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            assert.calledTwice(removeMemberFromHashSetStub);
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('PersonalConversation', () => {
    let sandbox;
    let stubRedisConnect;
    let pipelineExecStub;
    let pipelineStub;

    before(() => {
        AWSMock.setSDKInstance(AWS);
        sandbox = sinon.createSandbox();
    });

    after(() => {
        AWSMock.restore();
        sinon.restore();
        sandbox.restore();
        pipelineStub.restore();
    });

    beforeEach(() => {
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        pipelineExecStub = sinon.stub();
        pipelineStub = sinon.stub(Redis.prototype, 'pipeline').returns({ exec: pipelineExecStub });
        sinon.stub(ConstantModel, 'get').withArgs(CONSTANTS.FEED_VERSION_PREFIX).resolves({ value: 'v1' });
    });

    afterEach(() => {
        sinon.restore();
    });

    try {
        it('should insert personal conversation details in redis when PERSONAL_CONVERSATION is inserted in dynamodb', async () => {
            const setHashValueStub = sinon.stub(RedisUtil, 'setHashValue');
            setHashValueStub.resolves(true);

            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/PersonalConversation/stream/2025-03-03T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'userAId': { 'S': 'userId1' },
                        'userBId': { 'S': 'userId2' },
                        'conversationId': { 'S': 'conversationId1' },
                        'lastReadMessage': { 'M': {} },
                        'isMuted': { 'S': 'false' },
                        'isBlocked': { 'S': 'false' },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            assert.calledTwice(setHashValueStub);
        });

        it('should update personal conversation details in redis when PERSONAL_CONVERSATION is updated in dynamodb', async () => {
            const setHashValueStub = sinon.stub(RedisUtil, 'setHashValue');
            setHashValueStub.resolves(true);

            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/PersonalConversation/stream/2025-03-03T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'userAId': { 'S': 'userId1' },
                        'userBId': { 'S': 'userId2' },
                        'conversationId': { 'S': 'conversationId1' },
                        'lastReadMessage': { 'M': {} },
                        'isMuted': { 'S': 'false' },
                        'isBlocked': { 'S': 'false' },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' }
                    },
                    OldImage: {
                        'userAId': { 'S': 'userId1' },
                        'userBId': { 'S': 'userId2' },
                        'conversationId': { 'S': 'conversationId1' },
                        'lastReadMessage': { 'M': {} },
                        'isMuted': { 'S': 'false' },
                        'isBlocked': { 'S': 'false' },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            assert.calledTwice(setHashValueStub);
        });

        it('should remove personal conversation details from redis when PERSONAL_CONVERSATION is deleted in dynamodb', async () => {
            const removeMemberFromHashSetStub = sinon.stub(RedisUtil, 'removeMemberFromHashSet');
            removeMemberFromHashSetStub.resolves(true);

            const record = {
                eventName: 'REMOVE',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/PersonalConversation/stream/2025-03-03T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        'userAId': { 'S': 'userId1' },
                        'userBId': { 'S': 'userId2' },
                        'conversationId': { 'S': 'conversationId1' },
                        'lastReadMessage': { 'M': {} },
                        'isMuted': { 'S': 'false' },
                        'isBlocked': { 'S': 'false' },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            assert.callCount(removeMemberFromHashSetStub, 4);
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Group Message', () => {
    let sandbox;
    let stubRedisConnect;
    let pipelineExecStub;
    let pipelineStub;

    before(() => {
        AWSMock.setSDKInstance(AWS);
        sandbox = sinon.createSandbox();
        pipelineExecStub = sinon.stub();
        pipelineStub = sinon.stub(Redis.prototype, 'pipeline').returns({ exec: pipelineExecStub });
    });

    after(() => {
        AWSMock.restore();
        sinon.restore();
        sandbox.restore();
        pipelineStub.restore();
    });

    beforeEach(() => {
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        sinon.stub(ConstantModel, 'get').withArgs(CONSTANTS.FEED_VERSION_PREFIX).resolves({ value: 'v1' });
    });

    afterEach(() => {
        sinon.restore();
    });

    try {
        it('should insert group message details in redis when MESSAGE is inserted in dynamodb', async () => {
            const setHashValueStub = sinon.stub(RedisUtil, 'setHashValue');
            setHashValueStub.resolves(true);

            const getHashValueStub = sinon.stub(RedisUtil, 'getHashValue');
            getHashValueStub.resolves(JSON.stringify(
                Array.from({ length: 11 }, (_, index) => `messageId${index + 1}`)
            ));

            const removeHashValueStub = sinon.stub(RedisUtil, 'removeMemberFromHashSet');
            removeHashValueStub.resolves(true);

            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Message/stream/2025-03-03T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'id': { 'S': 'messageId1' },
                        'groupId': { 'S': 'groupId1' },
                        'senderId': { 'S': 'userId1' },
                        'message': { 'S': 'messageContent1' },
                        'isEdited': { 'BOOL': false },
                        'isDeleted': { 'BOOL': false },
                        'isFlagged': { 'BOOL': false },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            assert.calledOnce(getHashValueStub);
            assert.calledTwice(setHashValueStub);
            assert.calledOnce(removeHashValueStub);
        });

        it('should insert group message if it is the first message in the group', async () => {
            const setHashValueStub = sinon.stub(RedisUtil, 'setHashValue');
            setHashValueStub.resolves(true);
            const getHashValueStub = sinon.stub(RedisUtil, 'getHashValue');
            getHashValueStub.resolves('');

            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Message/stream/2025-03-03T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'id': { 'S': 'messageId1' },
                        'groupId': { 'S': 'groupId1' },
                        'senderId': { 'S': 'userId1' },
                        'message': { 'S': 'messageContent1' },
                        'isEdited': { 'BOOL': false },
                        'isDeleted': { 'BOOL': false },
                        'isFlagged': { 'BOOL': false }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            assert.calledOnce(getHashValueStub);
            assert.calledTwice(setHashValueStub);
        });

        it('should update group message details in redis when MESSAGE is updated in dynamodb', async () => {
            const setHashValueStub = sinon.stub(RedisUtil, 'setHashValue');
            setHashValueStub.resolves(true);

            const getHashValueStub = sinon.stub(RedisUtil, 'getHashValue');
            getHashValueStub.resolves(JSON.stringify(
                Array.from({ length: 11 }, (_, index) => `messageId${index + 1}`)
            ));

            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Message/stream/2025-03-03T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'id': { 'S': 'messageId1' },
                        'groupId': { 'S': 'groupId1' },
                        'senderId': { 'S': 'userId1' },
                        'message': { 'S': 'messageContent1' },
                        'isEdited': { 'BOOL': false },
                        'isDeleted': { 'BOOL': false },
                        'isFlagged': { 'BOOL': false },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' }
                    },
                    OldImage: {
                        'id': { 'S': 'messageId1' },
                        'groupId': { 'S': 'groupId1' },
                        'senderId': { 'S': 'userId1' },
                        'message': { 'S': 'messageContent1' },
                        'isEdited': { 'BOOL': false },
                        'isDeleted': { 'BOOL': false },
                        'isFlagged': { 'BOOL': false },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            assert.calledOnce(getHashValueStub);
            assert.calledOnce(setHashValueStub);
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Personal Message', () => {
    let sandbox;
    let stubRedisConnect;
    let pipelineExecStub;
    let pipelineStub;

    before(() => {
        AWSMock.setSDKInstance(AWS);
        sandbox = sinon.createSandbox();
    });

    after(() => {
        AWSMock.restore();
        sinon.restore();
        sandbox.restore();
        pipelineStub.restore();
    });

    beforeEach(() => {
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        pipelineExecStub = sinon.stub();
        pipelineStub = sinon.stub(Redis.prototype, 'pipeline').returns({ exec: pipelineExecStub });
        sinon.stub(ConstantModel, 'get').withArgs(CONSTANTS.FEED_VERSION_PREFIX).resolves({ value: 'v1' });
    });

    afterEach(() => {
        sinon.restore();
    });

    try {
        it('should insert personal message details in redis when PersonalMessage is inserted in dynamodb', async () => {
            const setHashValueStub = sinon.stub(RedisUtil, 'setHashValue');
            setHashValueStub.resolves(true);

            const getHashValueStub = sinon.stub(RedisUtil, 'getHashValue');
            getHashValueStub.resolves('null');

            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/PersonalMessage/stream/2025-03-03T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'id': { 'S': 'messageId1' },
                        'conversationId': { 'S': 'conversationId1' },
                        'senderId': { 'S': 'userId1' },
                        'message': { 'S': 'messageContent1' },
                        'isEdited': { 'BOOL': false },
                        'isDeleted': { 'BOOL': false },
                        'isFlagged': { 'BOOL': false },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            assert.calledTwice(setHashValueStub);
        });

        it('should not update personal message if the message is not in recent 11 messages', async () => {
            const setHashValueStub = sinon.stub(RedisUtil, 'setHashValue');
            setHashValueStub.resolves(true);

            const getHashValueStub = sinon.stub(RedisUtil, 'getHashValue');
            getHashValueStub.resolves('');

            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/PersonalMessage/stream/2025-03-03T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'id': { 'S': 'messageId19' },
                        'conversationId': { 'S': 'conversationId1' },
                        'senderId': { 'S': 'userId1' },
                        'message': { 'S': 'messageContent19' },
                        'isEdited': { 'BOOL': false },
                        'isDeleted': { 'BOOL': false },
                        'isFlagged': { 'BOOL': false },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' }
                    },
                    OldImage: {
                        'id': { 'S': 'messageId19' },
                        'conversationId': { 'S': 'conversationId1' },
                        'senderId': { 'S': 'userId1' },
                        'message': { 'S': 'messageContent19' },
                        'isEdited': { 'BOOL': false },
                        'isDeleted': { 'BOOL': false },
                        'isFlagged': { 'BOOL': false },
                        'createdAt': { 'S': '2025-03-03T13:01:18.061Z' },
                        'updatedAt': { 'S': '2025-03-03T13:01:18.061Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            assert.calledOnce(getHashValueStub);
            assert.notCalled(setHashValueStub);
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});





