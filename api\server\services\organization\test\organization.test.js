const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const sinon = require('sinon');
const PaymentService = require('../../payment/paymentService');
const User = require('../../../models/user.model');
const Organization = require('../../../models/organization.model');
const OrganizationMembers = require('../../../models/organizationMember.model');
const childModel = require('../../../models/child.model');
const childOrganizationMappingModel = require('../../../models/childOrganizationMapping.model');
const Event = require('../../../models/event.model');
const Cognito = require('../../../util/cognito');
const TestCase = require('./testcaseOrganization');
const jwt = require('jsonwebtoken');
const EmailService = require('../../../util/sendEmail');
const Stripe = require('../../../util/Stripe');
const Utils = require('../../../util/utilFunctions');
const AwsOpenSearchService = require('../../../util/opensearch');
const UploadService = require('../../../util/uploadService');
const organizationMemberModel = require('../../../models/organizationMember.model');
const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
/* User Token */
const user = {
    id: '95285616-3769-4b2e-b36b-898597b8146e',
    sub: '95285616-3769-4b2e-b36b-898597b8146e',
    email: '<EMAIL>',
    isVerified: 1,
    role: 4,
    status: 'active',
    isDeleted: 0,
    accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'testToken', tokenOptionalInfo)
};
const addOrganization = {
    orgName: 'Test Organization',
    adminFirstName: 'Test',
    adminLastName: 'Name',
    category: 'PTO',
    address: 'Organization test address',
    country: 'country',
    city: 'city',
    state: 'state',
    zipCode: '12345',
    phoneNumber: '1111111111',
    countryCode: '+1',
    email: '<EMAIL>',
    allowedPaymentType: {
        cash: true,
        cheque: false,
        stripe: false,
        venmo: false
    },
    parentOrganization: '95285616-3769-4b2e-b36b-898597b8146f'
};
const fakeOrg = {
    id: 'fake-org-id',
    name: 'Test Organization',
    createdBy: 'fake-user-id',
    category: 'PTO',
    address: 'Organization test address',
    country: 'country',
    state: 'state',
    city: 'city',
    zipCode: '12345',
    email: '<EMAIL>'
};
Utils.addCommonReqTokenForHMac(request);
describe('Add Organization', () => {
    try {
        let getStub;
        let emailStub;
        before(async () => {
            emailStub = sinon.stub(EmailService, 'prepareAndSendEmail');
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            emailStub.restore();
            sinon.restore();
        });
        TestCase.addOrganizationAdmin.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
                request(process.env.BASE_URL)
                    .post('/organization')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should add org admin', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const orgScanStub = sinon.stub(Organization, 'scan');
            const orgWhereStub = sinon.stub();
            const orgExecStub = sinon.stub();
            orgScanStub.returns({ where: orgWhereStub });
            orgWhereStub.withArgs('name').returns({ eq: sinon.stub().withArgs(addOrganization.orgName).returns({ where: orgWhereStub }) });
            orgWhereStub.withArgs('zipCode').returns({ eq: sinon.stub().withArgs(addOrganization.zipCode).returns({ exec: orgExecStub }) });
            const userScanStub = sinon.stub(User, 'query');
            userScanStub.withArgs('email')
                .returns({
                    eq: sinon.stub().withArgs(addOrganization.email)
                        .returns({ exec: sinon.stub().resolves({ count: 0 }) })
                });
            userScanStub.withArgs('accessLevel').returns({
                eq: sinon.stub().withArgs(CONSTANTS.ROLE.ORG_ADMIN)
                    .returns({
                        using: sinon.stub().returns({
                            exec: sinon.stub().resolves([{
                                id: 'superAdminUser.id',
                                email: '<EMAIL>',
                                associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN,
                                status: CONSTANTS.STATUS.ACTIVE,
                                firstName: 'superAdminUser.firstName',
                                lastName: 'superAdminUser.lastName',
                                associatedOrganizations: ['test'],
                                save: () => { }
                            }])
                        })
                    })
            });
            const orgMemberGetStub = sinon.stub(OrganizationMembers, 'get');
            orgMemberGetStub.resolves({
                users: [],
                save: () => {}
            });
            const orgCreateStub = sinon.stub(Organization, 'create');
            orgCreateStub.resolves(fakeOrg);
            orgExecStub.resolves({ count: 0 });
            sinon.stub(Cognito, 'signup').resolves({ userSub: 'id' });
            sinon.stub(Cognito, 'updateConfirmStatus').resolves();
            sinon.stub(Cognito, 'login').returns({ AuthenticationResult: { IdToken: 'id-token', RefreshToken: 'refresh-token' } });
            const saveStub = sinon.stub().resolves();
            sinon.replace(User.prototype, 'save', saveStub);
            saveStub.resolves({});
            const updateOrgStub = sinon.stub(Organization, 'update');
            sinon.stub(OrganizationMembers, 'create').resolves();
            updateOrgStub.resolves({});
            const createAccountStub = sinon.stub(PaymentService, 'createAccount');
            createAccountStub.resolves({});
            const getParentOrgStub = sinon.stub(Organization, 'get');
            getParentOrgStub.withArgs({ id: addOrganization.parentOrganization }).
                resolves({ associatedOrganizations: [], save: () => Promise.resolve() });
            request(process.env.BASE_URL)
                .post('/organization')
                .set({ Authorization: requestPayloadUser.token })
                .send(addOrganization)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    sinon.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Add/remove muliple children to organization', () => {
    let getStub;

    before(async () => {
        getStub = sinon.stub(User, 'get');
    });

    after(async () => {
        sinon.restore();
    });

    TestCase.addChildrenToOrganization.forEach((data) => {
        it(data.it, (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            request(process.env.BASE_URL)
                .post('/organization/add-children')
                .set({ Authorization: requestPayloadUser.token })
                .send(data.options)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.body.status, data.status);
                    done();
                });
        });
    });

    it('As a user I should add child to the organization', (done) => {
        getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
        const childStub = sinon.stub(childModel, 'get');
        const childOrgStub = sinon.stub(childOrganizationMappingModel, 'create');
        const child = {
            id: '95285616-3769-4b2e-b36b-898597b8146e',
            associatedOrganizations: [],
            save: () => Promise.resolve()
        };

        childStub.resolves(child);
        childOrgStub.resolves();

        request(process.env.BASE_URL)
            .post('/organization/add-children')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a6',
                addedChildIds: ['95285616-3769-4b2e-b36b-898597b8146e'],
                removedChildIds: []
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.status, 200);
                childStub.restore();
                childOrgStub.restore();
                done();
            });
    });

    it('As a user I should add child to the existing organization', (done) => {
        getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
        const childStub = sinon.stub(childModel, 'get');
        const child = {
            id: '95285616-3769-4b2e-b36b-898597b8146e',
            associatedOrganizations: ['8d40ddbe-996a-4183-a4ef-b32f66e045a6']
        };

        childStub.resolves(child);

        request(process.env.BASE_URL)
            .post('/organization/add-children')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a6',
                addedChildIds: ['95285616-3769-4b2e-b36b-898597b8146e'],
                removedChildIds: []
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.status, 200);
                childStub.restore();
                done();
            });
    });

    it('As a user I should remove child from the organization', (done) => {
        getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
        const childStub = sinon.stub(childModel, 'get');
        const child = {
            id: '95285616-3769-4b2e-b36b-89897b8146e',
            associatedOrganizations: ['8d40ddbe-996a-4183-a4ef-b32f66e045a6'],
            save: () => Promise.resolve()
        };
        childStub.resolves(child);

        const childOrgMappingStub = sinon.stub(childOrganizationMappingModel, 'query');
        childOrgMappingStub.withArgs('organizationId')
            .returns({
                eq: sinon.stub().withArgs('8d40ddbe-996a-4183-a4ef-b32f66e045a6')
                    .returns({
                        using: sinon.stub().withArgs('childId').returns({
                            where: sinon.stub().withArgs('organizationId-index')
                                .returns({
                                    eq: sinon.stub().withArgs('95285616-3769-4b2e-b36b-89897b8146e')
                                        .returns({
                                            exec: sinon.stub().resolves([
                                                {
                                                    id: '123e4567-e89b-12d3-a456-426614174000',
                                                    delete: () => Promise.resolve()
                                                }])
                                        })
                                })
                        })

                    })
            });

        request(process.env.BASE_URL)
            .post('/organization/add-children')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a6',
                addedChildIds: [],
                removedChildIds: ['95285616-3769-4b2e-b36b-89897b8146e']
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.status, 200);
                childStub.restore();
                childOrgMappingStub.restore();
                done();
            });
    });
});

describe('Should test for add Organization', () => {
    try {
        let getStub;
        let emailStub;
        before(async () => {
            emailStub = sinon.stub(EmailService, 'prepareAndSendEmail');
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            emailStub.restore();
            sinon.restore();
        });

        it('As a user I should update org admin if org already exists', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 4,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT, save: () => Promise.resolve()
            });
            const orgScanStub = sinon.stub(Organization, 'scan');
            const orgWhereStub = sinon.stub();
            const orgExecStub = sinon.stub();
            orgScanStub.returns({ where: orgWhereStub });
            orgWhereStub.withArgs('name').returns({ eq: sinon.stub().withArgs(addOrganization.orgName).returns({ where: orgWhereStub }) });
            orgWhereStub.withArgs('zipCode').returns({ eq: sinon.stub().withArgs(addOrganization.zipCode).returns({ exec: orgExecStub }) });
            const userScanStub = sinon.stub(User, 'query');
            userScanStub.withArgs('email')
                .returns({
                    eq: sinon.stub().withArgs(addOrganization.email)
                        .returns({
                            exec: sinon.stub().resolves({
                                count: 1, toJSON: () => {
                                    return [
                                        { id: '1234567890' }];
                                }
                            })
                        })
                });
            userScanStub.withArgs('accessLevel').returns({
                eq: sinon
                    .stub()
                    .withArgs(CONSTANTS.ROLE.ORG_ADMIN)
                    .returns({
                        using: sinon.stub().returns({
                            exec: sinon.stub().resolves([
                                {
                                    id: 'superAdminUser.id',
                                    email: '<EMAIL>',
                                    associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN,
                                    status: CONSTANTS.STATUS.ACTIVE,
                                    firstName: 'superAdminUser.firstName',
                                    lastName: 'superAdminUser.lastName',
                                    associatedOrganizations: ['test'],
                                    save: () => {}
                                }
                            ])
                        })
                    })
            });
            const orgMemberGetStub = sinon.stub(OrganizationMembers, 'get');
            orgMemberGetStub.resolves({
                users: [],
                save: () => {}
            });
            const orgCreateStub = sinon.stub(Organization, 'create');
            orgCreateStub.resolves(fakeOrg);
            orgExecStub.resolves({ count: 0 });
            const saveStub = sinon.stub().resolves();
            sinon.replace(User.prototype, 'save', saveStub);
            saveStub.resolves({});
            const userUpdateStub = sinon.stub(User, 'update');
            userUpdateStub.resolves({});
            const updateOrgStub = sinon.stub(Organization, 'update');
            updateOrgStub.resolves({});
            sinon.stub(OrganizationMembers, 'create').resolves();
            sinon.stub(PaymentService, 'createAccount').resolves();
            const getParentOrgStub = sinon.stub(Organization, 'get');
            getParentOrgStub.withArgs({ id: addOrganization.parentOrganization }).
                resolves({ associatedOrganizations: [], save: () => Promise.resolve() });
            request(process.env.BASE_URL)
                .post('/organization')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...addOrganization, category: 'School' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    sinon.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }

});

describe('Should test for add Organization', () => {
    try {
        let getStub;
        let emailStub;
        before(async () => {
            emailStub = sinon.stub(EmailService, 'prepareAndSendEmail');
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            emailStub.restore();
            sinon.restore();
        });

        it('As a user I should not add org admin if org already exists', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const orgScanStub = sinon.stub(Organization, 'scan');
            const orgWhereStub = sinon.stub();
            orgScanStub.returns({ where: orgWhereStub });
            orgWhereStub.withArgs('name').returns({ eq: sinon.stub().withArgs(addOrganization.orgName).returns({ where: orgWhereStub }) });
            orgWhereStub.withArgs('zipCode')
                .returns({
                    eq: sinon.stub().withArgs(addOrganization.zipCode)
                        .returns({ exec: sinon.stub().resolves({ count: 1 }) })
                });
            const userScanStub = sinon.stub(User, 'query');
            const fakeUserResponse = { count: 0 };
            userScanStub.withArgs('email')
                .returns({
                    eq: sinon.stub().withArgs(addOrganization.email)
                        .returns({ exec: sinon.stub().resolves(fakeUserResponse) })
                });

            request(process.env.BASE_URL)
                .post('/organization')
                .set({ Authorization: requestPayloadUser.token })
                .send(addOrganization)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    sinon.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }

});

describe('Super Admin: Get Organization', () => {
    try {
        const fakeOrgList = [
            {
                'country': 'USA',
                'zipCode': 'A3457F',
                'address': 'Address Line 1',
                'isDeleted': 0,
                'city': 'Illnois',
                'name': 'Jainil PTO',
                'id': '4cbba60d-0a42-4a66-a9ab-99cf9e46edad',
                'state': 'New York',
                'category': 'PTO',
                'createdAt': '*************'
            },
            {
                'country': 'USA',
                'zipCode': 'A3457F',
                'address': 'Address Line 1',
                'isDeleted': 1,
                'city': 'Illnois',
                'name': 'Jainil PTO',
                'id': '4cbba60d-0a42-4a66-a9ab-99cf9e46edad',
                'state': 'New York',
                'category': 'PTO',
                'createdAt': '*************'
            }
        ];

        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a super admin I should get error if category is not valid', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });

            request(process.env.BASE_URL)
                .get('/organization/list?category=123')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a super admin I should get list of Organization', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const orgScanStub = sinon.stub(Organization, 'scan');
            orgScanStub.returns({
                exec: () => {}
            });

            request(process.env.BASE_URL)
                .get('/organization/list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgScanStub.restore();
                    done();
                });
        });

        it('As a super admin I should get organization list if valid category is passed and not data in list', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const organizationAttributes = ['id', 'name', 'country', 'zipCode', 'address', 'city', 'state', 'category', 'isDeleted'];
            const orgScanStub = sinon.stub(Organization, 'query');
            orgScanStub.withArgs('category')
                .returns({
                    eq: sinon.stub().withArgs(CONSTANTS.CATEGORIES.PTO)
                        .returns({
                            using: sinon.stub().returns({
                                attributes: sinon.stub().withArgs(organizationAttributes)
                                    .returns({
                                        exec: sinon.stub().resolves([])
                                    })
                            })
                        })
                });

            request(process.env.BASE_URL)
                .get(`/organization/list?category=${CONSTANTS.CATEGORIES.PTO}`)
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgScanStub.restore();
                    done();
                });
        });

        it('As a super admin I should get empty organization list if valid category is passed', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const organizationAttributes = ['id', 'name', 'country', 'zipCode', 'address', 'city', 'state', 'category', 'isDeleted'];
            const orgScanStub = sinon.stub(Organization, 'query');
            orgScanStub.withArgs('category')
                .returns({
                    eq: sinon.stub().withArgs(CONSTANTS.CATEGORIES.PTO)
                        .returns({
                            using: sinon.stub().returns({
                                attributes: sinon.stub().withArgs(organizationAttributes)
                                    .returns({
                                        exec: sinon.stub().resolves({
                                            populate: () => fakeOrgList
                                        })
                                    })
                            })
                        })
                });

            request(process.env.BASE_URL)
                .get(`/organization/list?category=${CONSTANTS.CATEGORIES.PTO}`)
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgScanStub.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }

});

describe('Super Admin: Get Organization Details', () => {
    try {
        const fakeORGDetails = {
            'country': 'USA',
            'zipCode': 'A3457F',
            'address': 'Address Line 1',
            'isDeleted': 0,
            'city': 'Illnois',
            'name': 'Jainil PTO',
            'id': '4cbba60d-0a42-4a66-a9ab-99cf9e46edad',
            'state': 'New York',
            'category': 'PTO',
            'createdAt': '*************'
        };

        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('orgId must be provided', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });

            request(process.env.BASE_URL)
                .get('/organization')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('orgId must be valid', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });

            request(process.env.BASE_URL)
                .get('/organization?orgId=abcd')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a super admin I should get Organization details for given org id', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.returns({ populate: () => fakeORGDetails });
            const eventScanStub = sinon.stub(Event, 'query');
            eventScanStub.withArgs('organizationId')
                .returns({
                    eq: sinon.stub().withArgs('9e118adb-a4f1-48d1-b32a-7dc46a4d5991')
                        .returns({
                            where: sinon.stub().withArgs('status')
                                .returns({
                                    eq: sinon.stub().withArgs(CONSTANTS.STATUS.PUBLISHED)
                                        .returns({
                                            exec: sinon.stub().resolves([{}])
                                        })
                                })
                        })
                });

            request(process.env.BASE_URL)
                .get('/organization?orgId=9e118adb-a4f1-48d1-b32a-7dc46a4d5991')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgScanStub.restore();
                    eventScanStub.restore();
                    done();
                });
        });

        it('As a super admin I should get empty object if for given id, org doesnot exist', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.returns(undefined);

            request(process.env.BASE_URL)
                .get('/organization?orgId=9e118adb-a4f1-48d1-b32a-7dc46a4d5991')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgScanStub.restore();
                    done();
                });
        });

        it('As a user I should get error if I am not associated with an organization', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP, associatedOrganizations: [{
                    organizationId: '9e118adb-a4f1-48d1-b32a-7dc46a4d5992'
                }] });
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.returns({ populate: () => fakeORGDetails });

            request(process.env.BASE_URL)
                .get('/organization?orgId=9e118adb-a4f1-48d1-b32a-7dc46a4d5991')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgScanStub.restore();
                    done();
                });
        });

        it('As a user I should get error if I am not associated with organization but my role is not admin or super admin', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP, associatedOrganizations: [{
                    organizationId: '9e118adb-a4f1-48d1-b32a-7dc46a4d5991', role: CONSTANTS.ROLE.ORG_EDITOR
                }] });
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.returns({ populate: () => fakeORGDetails });

            request(process.env.BASE_URL)
                .get('/organization?orgId=9e118adb-a4f1-48d1-b32a-7dc46a4d5991')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 403);
                    orgScanStub.restore();
                    done();
                });
        });

        it('As a super admin of an organization I should get details of my organization', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP, associatedOrganizations: [{
                    organizationId: '9e118adb-a4f1-48d1-b32a-7dc46a4d5991', role: CONSTANTS.ROLE.ORG_SUPER_ADMIN
                }] });
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.returns({ populate: () => fakeORGDetails });

            request(process.env.BASE_URL)
                .get('/organization?orgId=9e118adb-a4f1-48d1-b32a-7dc46a4d5991')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgScanStub.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Super Admin: Change Organization Status - Enable/Disable', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('status must be provided', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });

            request(process.env.BASE_URL)
                .put('/organization/status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ orgId: '9e118adb-a4f1-48d1-b32a-7dc46a4d5991' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('status must be valid', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });

            request(process.env.BASE_URL)
                .put('/organization/status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ orgId: '9e118adb-a4f1-48d1-b32a-7dc46a4d5991', status: 3 })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a super admin I should be able to enable Organization', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const orgScanStub = sinon.stub(Organization, 'update');
            orgScanStub.resolves(null);
            request(process.env.BASE_URL)
                .put('/organization/status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ orgId: '9e118adb-a4f1-48d1-b32a-7dc46a4d5991', status: 1 })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgScanStub.restore();
                    done();
                });
        });

        it('As a super admin I should be able to disable Organization', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const orgScanStub = sinon.stub(Organization, 'update');
            orgScanStub.resolves(null);
            const eventScanStub = sinon.stub(Event, 'query');
            eventScanStub.withArgs('organizationId')
                .returns({
                    eq: sinon.stub().withArgs('9e118adb-a4f1-48d1-b32a-7dc46a4d5991')
                        .returns({
                            where: sinon.stub().withArgs('status')
                                .returns({
                                    eq: sinon.stub().withArgs(CONSTANTS.STATUS.PUBLISHED)
                                        .returns({
                                            exec: sinon.stub().resolves([])
                                        })
                                })
                        })
                });
            request(process.env.BASE_URL)
                .put('/organization/status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ orgId: '9e118adb-a4f1-48d1-b32a-7dc46a4d5991', status: 0 })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgScanStub.restore();
                    eventScanStub.restore();
                    done();
                });
        });

        it('As a super admin I should not be able to disable Organization if it has events published', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const orgScanStub = sinon.stub(Organization, 'update');
            orgScanStub.resolves(null);
            const eventScanStub = sinon.stub(Event, 'query');
            eventScanStub.withArgs('organizationId')
                .returns({
                    eq: sinon.stub().withArgs('9e118adb-a4f1-48d1-b32a-7dc46a4d5991')
                        .returns({
                            where: sinon.stub().withArgs('status')
                                .returns({
                                    eq: sinon.stub().withArgs(CONSTANTS.STATUS.PUBLISHED)
                                        .returns({
                                            exec: sinon.stub().resolves([{}])
                                        })
                                })
                        })
                });
            request(process.env.BASE_URL)
                .put('/organization/status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ orgId: '9e118adb-a4f1-48d1-b32a-7dc46a4d5991', status: 0 })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgScanStub.restore();
                    eventScanStub.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Super Admin: Delete Organization', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a super admin I should be able to delete Organization', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const orgScanStub = sinon.stub(Organization, 'update');
            orgScanStub.resolves(null);
            const eventScanStub = sinon.stub(Event, 'query');
            eventScanStub.withArgs('organizationId')
                .returns({
                    eq: sinon.stub().withArgs('9e118adb-a4f1-48d1-b32a-7dc46a4d5991')
                        .returns({
                            where: sinon.stub().withArgs('status')
                                .returns({
                                    eq: sinon.stub().withArgs(CONSTANTS.STATUS.PUBLISHED)
                                        .returns({
                                            exec: sinon.stub().resolves([])
                                        })
                                })
                        })
                });
            const userScanStub = sinon.stub(User, 'query');
            userScanStub.withArgs('email').returns({
                eq: sinon
                    .stub()
                    .withArgs(addOrganization.email)
                    .returns({
                        exec: sinon.stub().resolves({
                            count: 1,
                            toJSON: () => {
                                return [{ id: '1234567890' }];
                            }
                        })
                    })
            });
            userScanStub.withArgs('accessLevel').returns({
                eq: sinon
                    .stub()
                    .withArgs(CONSTANTS.ROLE.ORG_ADMIN)
                    .returns({
                        using: sinon.stub().returns({
                            exec: sinon.stub().resolves([
                                {
                                    id: 'superAdminUser.id',
                                    email: '<EMAIL>',
                                    associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN,
                                    status: CONSTANTS.STATUS.ACTIVE,
                                    firstName: 'superAdminUser.firstName',
                                    lastName: 'superAdminUser.lastName',
                                    associatedOrganizations: ['test'],
                                    save: () => {}
                                }
                            ])
                        })
                    })
            });
            const orgGetStub = sinon.stub(Organization, 'get');
            orgGetStub.withArgs({ id: '9e118adb-a4f1-48d1-b32a-7dc46a4d5991' }).resolves({ parentOrganization: 'parent-org-id' });
            orgGetStub.withArgs({ id: 'parent-org-id' }).resolves({ associatedOrganizations: [], save: () => Promise.resolve() });
            request(process.env.BASE_URL)
                .delete('/organization?orgId=9e118adb-a4f1-48d1-b32a-7dc46a4d5991')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgScanStub.restore();
                    eventScanStub.restore();
                    done();
                });
        });

        it('As a super admin I should not be able to delete addOrganization if it has events published', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const orgScanStub = sinon.stub(Organization, 'update');
            orgScanStub.resolves(null);
            const eventScanStub = sinon.stub(Event, 'query');
            eventScanStub.withArgs('organizationId')
                .returns({
                    eq: sinon.stub().withArgs('9e118adb-a4f1-48d1-b32a-7dc46a4d5991')
                        .returns({
                            where: sinon.stub().withArgs('status')
                                .returns({
                                    eq: sinon.stub().withArgs(CONSTANTS.STATUS.PUBLISHED)
                                        .returns({
                                            exec: sinon.stub().resolves([{}])
                                        })
                                })
                        })
                });
            request(process.env.BASE_URL)
                .delete('/organization?orgId=9e118adb-a4f1-48d1-b32a-7dc46a4d5991')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgScanStub.restore();
                    eventScanStub.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Super Admin: Update addOrganization Details', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        const fakeORGDetails = {
            'country': 'USA',
            'zipCode': 'A3457F',
            'address': 'Address Line 1',
            'isDeleted': 0,
            'city': 'Illnois',
            'name': 'Jainil PTO',
            'id': '4cbba60d-0a42-4a66-a9ab-99cf9e46edad',
            'state': 'New York',
            'category': 'PTO',
            'createdAt': '*************',
            'paymentDetails': {
                'stripeOnboardingStatus': 'inactive',
                'stripeConnectAccountId': ''
            },
            'allowedPaymentType': {
                'cash': true
            }
        };

        const fakeORGDetailsWithInactiveAccount = {
            'country': 'USA',
            'zipCode': 'A3457F',
            'address': 'Address Line 1',
            'isDeleted': 0,
            'city': 'Illnois',
            'name': 'Jainil PTO',
            'id': '4cbba60d-0a42-4a66-a9ab-99cf9e46edad',
            'state': 'New York',
            'category': 'PTO',
            'createdAt': '*************',
            'paymentDetails': {
                'stripeOnboardingStatus': 'inactive',
                'stripeConnectAccountId': '123'
            },
            'allowedPaymentType': {
                'cash': true
            }
        };

        const orgBody = {
            'orgId': '4cbba60d-0a42-4a66-a9ab-99cf9e46edad',
            'adminFirstName': 'Jainil',
            'adminLastName': 'Patel',
            'orgName': 'Jainil Organisation',
            'category': 'PTO',
            'address': 'Address Line',
            'country': 'India',
            'state': 'Gujarat',
            'city': 'Ahmedabad',
            'zipCode': '380008',
            'parentOrganization': '5cbba60d-0a42-4a66-a9ab-99cf9e46edad',
            'allowedPaymentType': {
                'cash': 'true',
                'stripe': 'false',
                'venmo': 'false',
                'cheque': 'false'
            },
            'paymentInstructions': {
                'cashInstruction': 'cash instruction'
            },
            'paymentDetails': {
                stripeConnectAccountId: ''
            },
            'parentOrganizationName': 'Jainil PTO',
            'platformFee': '5',
            'platformFeeCoveredBy': 'organization',
            'minPlatformFeeAmount': 1
        };

        it('org must exist', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const orgGetStub = sinon.stub(Organization, 'get').returns(null);
            request(process.env.BASE_URL)
                .put('/organization')
                .set({ Authorization: requestPayloadUser.token })
                .send(orgBody)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 404);
                    orgGetStub.restore();
                    done();
                });
        });

        it('should be able to update org details with isStripeOnboarding is true and org does not have the stripe account', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const orgGetStub = sinon.stub(Organization, 'get');
            orgGetStub.returns({ populate: () => ({ ...fakeORGDetails, save: () => { } }), save: () => { }, ...fakeORGDetails });
            const createAccountStub = sinon.stub(Stripe, 'createAccount').resolves({ id: '123' });
            const createLinkStub = sinon.stub(Stripe, 'createOnboardingLink').resolves({ url: 'dummyurl' });
            const updateOrgStub = sinon.stub(Organization, 'update');
            updateOrgStub.resolves({});
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN }]
            });
            const newOrgBody = { ...orgBody };
            newOrgBody.allowedPaymentType.stripe = 'true';
            request(process.env.BASE_URL)
                .put('/organization')
                .set({ Authorization: requestPayloadUser.token })
                .send(newOrgBody)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgGetStub.restore();
                    createAccountStub.restore();
                    updateOrgStub.restore();
                    createLinkStub.restore();
                    done();
                });
        });

        it('should be able to update org details with isStripeOnboarding true and stripe status is inactive', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const orgGetStub = sinon.stub(Organization, 'get');
            orgGetStub.returns({
                populate: () =>
                    ({ ...fakeORGDetailsWithInactiveAccount, save: () => { } }), save: () => { }, ...fakeORGDetails
            });
            const createAccountStub = sinon.stub(Stripe, 'createAccount').resolves({ id: '123' });
            const createLinkStub = sinon.stub(Stripe, 'createOnboardingLink').resolves({ url: 'dummyurl' });
            const updateOrgStub = sinon.stub(Organization, 'update');
            updateOrgStub.resolves({});
            const newOrgBody = { ...orgBody };
            request(process.env.BASE_URL)
                .put('/organization')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...newOrgBody })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgGetStub.restore();
                    createAccountStub.restore();
                    createLinkStub.restore();
                    updateOrgStub.restore();
                    done();
                });
        });

        it('should be able to update org details where isStripeOnboarding is false', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const orgGetStub = sinon.stub(Organization, 'get');
            orgGetStub.returns({ populate: () => ({ ...fakeORGDetails, save: () => { } }), save: () => { }, ...fakeORGDetails });
            const newOrgBody = { ...orgBody };
            newOrgBody.allowedPaymentType.stripe = 'false';
            request(process.env.BASE_URL)
                .put('/organization')
                .set({ Authorization: requestPayloadUser.token })
                .send(newOrgBody)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgGetStub.restore();
                    done();
                });
        });
        it('should not be able to update org details without cash instruction', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const orgGetStub = sinon.stub(Organization, 'get');
            orgGetStub.returns({ populate: () => ({ ...fakeORGDetails, save: () => { } }), save: () => { }, ...fakeORGDetails });
            const newOrgBody = { ...orgBody };
            newOrgBody.paymentInstructions.cashInstruction = '';
            request(process.env.BASE_URL)
                .put('/organization')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...newOrgBody })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    done();
                });
        });
        it('should not be able to update org details without venmo instruction', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const orgGetStub = sinon.stub(Organization, 'get');
            orgGetStub.returns({ populate: () => ({ ...fakeORGDetails, save: () => { } }), save: () => { }, ...fakeORGDetails });
            const newOrgBody = { ...orgBody };
            newOrgBody.paymentInstructions.venmoInstruction = '';
            request(process.env.BASE_URL)
                .put('/organization')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...newOrgBody })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    done();
                });
        });
        it('should not be able to update org details without cheque instruction', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const orgGetStub = sinon.stub(Organization, 'get');
            orgGetStub.returns({ populate: () => ({ ...fakeORGDetails, save: () => { } }), save: () => { }, ...fakeORGDetails });
            request(process.env.BASE_URL)
                .put('/organization')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...orgBody, checkInstruction: '', checkAllowed: true })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Super Admin: Update addOrganization Details', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        const fakeORGDetails = {
            'country': 'USA',
            'zipCode': 'A3457F',
            'address': 'Address Line 1',
            'isDeleted': 0,
            'city': 'Illnois',
            'name': 'Jainil PTO',
            'id': '4cbba60d-0a42-4a66-a9ab-99cf9e46edad',
            'state': 'New York',
            'category': 'PTO',
            'createdAt': '*************',
            'paymentDetails': {
                'stripeOnboardingStatus': 'inactive',
                'stripeConnectAccountId': ''
            },
            'allowedPaymentType': {
                'cash': true
            }
        };

        const orgBody = {
            'orgId': '4cbba60d-0a42-4a66-a9ab-99cf9e46edad',
            'adminFirstName': 'Jainil',
            'adminLastName': 'Patel',
            'orgName': 'Jainil Organisation',
            'category': 'PTO',
            'address': 'Address Line',
            'country': 'India',
            'state': 'Gujarat',
            'city': 'Ahmedabad',
            'zipCode': '380008',
            'parentOrganization': '5cbba60d-0a42-4a66-a9ab-99cf9e46edad',
            'allowedPaymentType': {
                'cash': 'true',
                'stripe': 'false',
                'venmo': 'false',
                'cheque': 'false'
            },
            'paymentInstructions': {
                'cashInstruction': 'cash instruction'
            },
            'paymentDetails': {
                stripeConnectAccountId: ''
            },
            'parentOrganizationName': 'Jainil PTO',
            'platformFee': 5
        };

        it('should get error if platformFee is invalid', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const orgGetStub = sinon.stub(Organization, 'get');
            orgGetStub.returns({ populate: () => ({ ...fakeORGDetails, save: () => { } }), save: () => { }, ...fakeORGDetails });
            const createAccountStub = sinon.stub(Stripe, 'createAccount').resolves({ id: '123' });
            const createLinkStub = sinon.stub(Stripe, 'createOnboardingLink').resolves({ url: 'dummyurl' });
            const updateOrgStub = sinon.stub(Organization, 'update');
            updateOrgStub.resolves({});
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN }]
            });
            request(process.env.BASE_URL)
                .put('/organization')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...orgBody, stripeAllowed: true, platformFee: '123' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    createAccountStub.restore();
                    updateOrgStub.restore();
                    createLinkStub.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });
        it(`should get error that 
                Organization can not modify platform fee if logged in user is not admin and try to change platformFee`, (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP, associatedOrganizations: [{
                    organizationId: '4cbba60d-0a42-4a66-a9ab-99cf9e46edad',
                    role: CONSTANTS.ROLE.ORG_SUPER_ADMIN
                }] });
            const orgGetStub = sinon.stub(Organization, 'get');
            orgGetStub.returns({ populate: () => ({ ...fakeORGDetails, save: () => { } }), save: () => { }, ...fakeORGDetails });
            const createAccountStub = sinon.stub(Stripe, 'createAccount').resolves({ id: '123' });
            const createLinkStub = sinon.stub(Stripe, 'createOnboardingLink').resolves({ url: 'dummyurl' });
            const updateOrgStub = sinon.stub(Organization, 'update');
            updateOrgStub.resolves({});
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN }]
            });
            request(process.env.BASE_URL)
                .put('/organization')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...orgBody, stripeAllowed: true, platformFee: 50, minPlatformFeeAmount: 1 })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 403);
                    orgGetStub.restore();
                    createAccountStub.restore();
                    updateOrgStub.restore();
                    createLinkStub.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });
        it('should get error if platformFeeCoveredBy is not passed', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const orgGetStub = sinon.stub(Organization, 'get');
            orgGetStub.returns({ populate: () => ({ ...fakeORGDetails, save: () => { } }), save: () => { }, ...fakeORGDetails });
            const createAccountStub = sinon.stub(Stripe, 'createAccount').resolves({ id: '123' });
            const createLinkStub = sinon.stub(Stripe, 'createOnboardingLink').resolves({ url: 'dummyurl' });
            const updateOrgStub = sinon.stub(Organization, 'update');
            updateOrgStub.resolves({});
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN }]
            });
            request(process.env.BASE_URL)
                .put('/organization')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...orgBody, stripeAllowed: true, platformFee: 50 })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    createAccountStub.restore();
                    updateOrgStub.restore();
                    createLinkStub.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });
        it('should get error if platformFeeCoveredBy is not valid', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const orgGetStub = sinon.stub(Organization, 'get');
            orgGetStub.returns({ populate: () => ({ ...fakeORGDetails, save: () => { } }), save: () => { }, ...fakeORGDetails });
            const createAccountStub = sinon.stub(Stripe, 'createAccount').resolves({ id: '123' });
            const createLinkStub = sinon.stub(Stripe, 'createOnboardingLink').resolves({ url: 'dummyurl' });
            const updateOrgStub = sinon.stub(Organization, 'update');
            updateOrgStub.resolves({});
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN }]
            });
            request(process.env.BASE_URL)
                .put('/organization')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...orgBody, stripeAllowed: true, platformFee: 50, platformFeeCoveredBy: 'test' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    createAccountStub.restore();
                    updateOrgStub.restore();
                    createLinkStub.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Org User get users list', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get error message if orgId is not passed', (done) => {
            getStub.resolves({
                id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
            });
            request(process.env.BASE_URL)
                .get('/organization/users')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error message if orgId doesnt match uuid regex', (done) => {
            getStub.resolves({
                id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
            });
            request(process.env.BASE_URL)
                .get('/organization/users?orgId=1234')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error message if orgId doesnt exists', (done) => {
            getStub.resolves({
                id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
            });
            sinon.stub(OrganizationMembers, 'get').resolves(null);
            request(process.env.BASE_URL)
                .get('/organization/users?orgId=8d40ddbe-996a-4183-a4ef-b32f66e045a5')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a user I should get error message if I am not associated with the organization', (done) => {
            getStub.resolves({
                id: 123, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
            });
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' },
                    { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }]
            });
            request(process.env.BASE_URL)
                .get('/organization/users?orgId=8d40ddbe-996a-4183-a4ef-b32f66e045a5')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a user I should get error message if my role is not admin or super admin', (done) => {
            getStub.resolves({
                id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
            });
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_EDITOR, status: 'active' },
                    { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }]
            });

            request(process.env.BASE_URL)
                .get('/organization/users?orgId=8d40ddbe-996a-4183-a4ef-b32f66e045a5')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a user I should get error message if my status is not active', (done) => {
            getStub.resolves({
                id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
            });
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'deleted' },
                    { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }]
            });
            request(process.env.BASE_URL)
                .get('/organization/users?orgId=8d40ddbe-996a-4183-a4ef-b32f66e045a5')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a user I should get org users list', (done) => {
            getStub.resolves({
                id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
            });
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' },
                    { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }]
            });
            const userScanStub = sinon.stub(User, 'query');
            userScanStub.withArgs('email').returns({
                eq: sinon
                    .stub()
                    .withArgs(addOrganization.email)
                    .returns({
                        exec: sinon.stub().resolves({
                            count: 1,
                            toJSON: () => {
                                return [{ id: '1234567890' }];
                            }
                        })
                    })
            });
            userScanStub.withArgs('accessLevel').returns({
                eq: sinon
                    .stub()
                    .withArgs(CONSTANTS.ROLE.ORG_ADMIN)
                    .returns({
                        using: sinon.stub().returns({
                            exec: sinon.stub().resolves([
                                {
                                    id: 'superAdminUser.id',
                                    email: '<EMAIL>',
                                    associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN,
                                    status: CONSTANTS.STATUS.ACTIVE,
                                    firstName: 'superAdminUser.firstName',
                                    lastName: 'superAdminUser.lastName',
                                    associatedOrganizations: ['test'],
                                    save: () => {}
                                }
                            ])
                        })
                    })
            });
            request(process.env.BASE_URL)
                .get('/organization/users?orgId=8d40ddbe-996a-4183-a4ef-b32f66e045a5')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    OrganizationMembers.get.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Add Organization User', () => {
    let getStub;
    before(async () => {
        getStub = sinon.stub(User, 'get');
    });

    after(async () => {
        sinon.restore();
    });

    TestCase.addOrganizationUser.forEach((data) => {
        it(data.it, (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP });
            request(process.env.BASE_URL)
                .post('/organization/user')
                .set({ Authorization: requestPayloadUser.token })
                .send(data.options)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.body.status, data.status);
                    done();
                });
        });
    });

    it('As a user I should get error message if orgId doesnt exists', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });
        sinon.stub(OrganizationMembers, 'get').resolves(null);
        request(process.env.BASE_URL)
            .post('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                firstName: 'test',
                lastName: 'test',
                email: '<EMAIL>',
                role: 'admin'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                OrganizationMembers.get.restore();
                done();
            });
    });

    it('As a user I should get error message if I am not associated with the organization', (done) => {
        getStub.resolves({
            id: 123, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });
        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' },
                { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }]
        });

        request(process.env.BASE_URL)
            .post('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                firstName: 'test',
                lastName: 'test',
                email: '<EMAIL>',
                role: 'admin'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                OrganizationMembers.get.restore();
                done();
            });
    });

    it('As a user I should get error message if my role is not admin or super admin', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });
        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_EDITOR, status: 'active' },
                { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }]
        });

        request(process.env.BASE_URL)
            .post('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                firstName: 'test',
                lastName: 'test',
                email: '<EMAIL>',
                role: 'admin'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                OrganizationMembers.get.restore();
                done();
            });
    });

    it('As a user I should get error message if my status is not active', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });
        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'deleted' },
                { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }]
        });

        request(process.env.BASE_URL)
            .post('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                firstName: 'test',
                lastName: 'test',
                email: '<EMAIL>',
                role: 'admin'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                OrganizationMembers.get.restore();
                done();
            });
    });

    it('As a user I should get error message if org user already is associated with org', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });

        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' },
                { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }]
        });

        request(process.env.BASE_URL)
            .post('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                firstName: 'test',
                lastName: 'test',
                email: '<EMAIL>',
                role: 'admin'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                OrganizationMembers.get.restore();
                done();
            });
    });

    it('As a user I should add get error message if I try to add super admin user as an admin user', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });

        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' },
                { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }],
            save: () => {}
        });

        request(process.env.BASE_URL)
            .post('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                firstName: 'test',
                lastName: 'test',
                email: '<EMAIL>',
                role: 'super admin'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                OrganizationMembers.get.restore();
                done();
            });
    });

    it('As a user I should add new user to org', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });

        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' },
                { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }],
            save: () => {}
        });

        const userQueryStub = sinon.stub(User, 'query');
        userQueryStub.withArgs('email')
            .returns({
                eq: sinon.stub().withArgs(addOrganization.email)
                    .returns({ exec: sinon.stub().resolves({ count: 0 }) })
            });

        sinon.stub(Cognito, 'signup').resolves({ userSub: 'id' });
        sinon.stub(Cognito, 'updateConfirmStatus').resolves();
        sinon.stub(Cognito, 'login').returns({ AuthenticationResult: { IdToken: 'id-token', RefreshToken: 'refresh-token' } });

        const saveStub = sinon.stub().resolves();
        sinon.replace(User.prototype, 'save', saveStub);
        saveStub.resolves({});

        request(process.env.BASE_URL)
            .post('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                firstName: 'test',
                lastName: 'test',
                email: '<EMAIL>',
                role: 'admin',
                password: 'Test@123'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
                OrganizationMembers.get.restore();
                userQueryStub.restore();
                Cognito.signup.restore();
                Cognito.updateConfirmStatus.restore();
                Cognito.login.restore();
                done();
            });
    });

    it('As a user I should add existing user to org', (done) => {
        getStub.withArgs({ id: '95285616-3769-4b2e-b36b-898597b8146e', email: '<EMAIL>' })
            .resolves({
                id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
            });

        getStub.withArgs({ id: '1234567890', email: '<EMAIL>' })
            .resolves({ id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
                associatedOrganizations: [], save: () => {} });

        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' },
                { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }],
            save: () => {}
        });

        const userQueryStub = sinon.stub(User, 'query');
        userQueryStub.withArgs('email')
            .returns({
                eq: sinon.stub().withArgs('<EMAIL>')
                    .returns({ exec: sinon.stub().resolves({ count: 1, toJSON: () => {
                        return [
                            { id: '1234567890' }];
                    } }) })
            });

        sinon.stub(Cognito, 'signup').resolves({ userSub: 'id' });
        sinon.stub(Cognito, 'updateConfirmStatus').resolves();
        sinon.stub(Cognito, 'login').returns({ AuthenticationResult: { IdToken: 'id-token', RefreshToken: 'refresh-token' } });

        request(process.env.BASE_URL)
            .post('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                firstName: 'test',
                lastName: 'test',
                email: '<EMAIL>',
                role: 'editor',
                password: 'Test@123'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
                OrganizationMembers.get.restore();
                userQueryStub.restore();
                Cognito.signup.restore();
                Cognito.updateConfirmStatus.restore();
                Cognito.login.restore();
                done();
            });
    });

    it('As a user I should be able to add user to org who was previously associated with org', (done) => {
        getStub.withArgs({ id: '95285616-3769-4b2e-b36b-898597b8146e', email: '<EMAIL>' })
            .resolves({
                id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
            });

        getStub.withArgs({ id: '1234567890', email: '<EMAIL>' })
            .resolves({ id: 2, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
                associatedOrganizations: [], save: () => {} });

        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' },
                { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'deleted' }],
            save: () => {}
        });

        const userQueryStub = sinon.stub(User, 'query');
        userQueryStub.withArgs('email')
            .returns({
                eq: sinon.stub().withArgs('<EMAIL>')
                    .returns({ exec: sinon.stub().resolves({ count: 1, toJSON: () => {
                        return [
                            { id: '1234567890' }];
                    } }) })
            });

        userQueryStub.withArgs('id')
            .returns({
                eq: sinon.stub().withArgs('1234567890')
                    .returns({ exec: sinon.stub().resolves(
                        [{ count: 1, toJSON: () => {
                            return [
                                { id: '1234567890' }];
                        }, save: () => {} }]) })
            });

        request(process.env.BASE_URL)
            .post('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                firstName: 'test',
                lastName: 'test',
                email: '<EMAIL>',
                role: 'editor',
                password: 'Test@123'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
                OrganizationMembers.get.restore();
                userQueryStub.restore();
                done();
            });
    });

    it('As a user I should get error message if user is already associated with org', (done) => {
        getStub.withArgs({ id: '95285616-3769-4b2e-b36b-898597b8146e', email: '<EMAIL>' })
            .resolves({
                id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
            });

        getStub.withArgs({ id: '1234567890', email: '<EMAIL>' })
            .resolves({ id: 2, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
                associatedOrganizations: [], save: () => {} });

        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' },
                { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }],
            save: () => {}
        });

        request(process.env.BASE_URL)
            .post('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                firstName: 'test',
                lastName: 'test',
                email: '<EMAIL>',
                role: 'editor',
                password: 'Test@123'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                OrganizationMembers.get.restore();
                done();
            });
    });
});

describe('Update Organization User', () => {
    let getStub;
    before(async () => {
        getStub = sinon.stub(User, 'get');
    });

    after(async () => {
        sinon.restore();
    });

    TestCase.updateOrganizationUser.forEach((data) => {
        it(data.it, (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP });
            request(process.env.BASE_URL)
                .put('/organization/user')
                .set({ Authorization: requestPayloadUser.token })
                .send(data.options)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.body.status, data.status);
                    done();
                });
        });
    });

    it('As a user I should get error I pass my id in userId', (done) => {
        getStub.resolves({
            id: '95285616-3769-4b2e-b36b-898597b8146e',
            email: '<EMAIL>', status: 'active', isVerified: 1, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });

        request(process.env.BASE_URL)
            .put('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '95285616-3769-4b2e-b36b-898597b8146e',
                role: 'editor',
                status: 'active'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.status, 400);
                done();
            });
    });
    it('As a user I should get error message if orgId doesnt exists', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });
        sinon.stub(OrganizationMembers, 'get').resolves(null);
        request(process.env.BASE_URL)
            .put('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '95285616-3769-4b2e-b36b-898597b8146e',
                role: 'editor',
                status: 'active'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                OrganizationMembers.get.restore();
                done();
            });
    });

    it('As a user I should get error message if I am not associated with the organization', (done) => {
        getStub.resolves({
            id: 123, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });
        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' },
                { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }]
        });

        request(process.env.BASE_URL)
            .put('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '95285616-3769-4b2e-b36b-898597b8146e',
                role: 'editor',
                status: 'active'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                OrganizationMembers.get.restore();
                done();
            });
    });

    it('As a user I should get error message if my role is not admin or super admin', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });
        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_EDITOR, status: 'active' },
                { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }]
        });

        request(process.env.BASE_URL)
            .put('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '95285616-3769-4b2e-b36b-898597b8146e',
                role: 'editor',
                status: 'active'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                OrganizationMembers.get.restore();
                done();
            });
    });

    it('As a user I should get error message if my status is not active', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });
        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'deleted' },
                { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }]
        });

        request(process.env.BASE_URL)
            .put('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '95285616-3769-4b2e-b36b-898597b8146e',
                role: 'editor',
                status: 'active'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                OrganizationMembers.get.restore();
                done();
            });
    });

    it('As a user I should get error message if org user doesnt exists', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });

        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' },
                { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }]
        });

        sinon.stub(User, 'query').returns({
            eq: sinon.stub().withArgs(1)
                .returns({ exec: sinon.stub().resolves([
                    {
                        id: '1234567890',
                        associatedOrganizations: []
                    }
                ]) })
        });

        request(process.env.BASE_URL)
            .put('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '95285616-3769-4b2e-b36b-898597b8146e',
                role: 'editor',
                status: 'active'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                OrganizationMembers.get.restore();
                User.query.restore();
                done();
            });
    });

    it('As a user I should add get error message if I try to update super admin user as an admin user', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });

        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' },
                { id: '95285616-3769-4b2e-b36b-898597b8146e', email: '<EMAIL>',
                    associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }],
            save: () => {}
        });

        sinon.stub(User, 'query').returns({
            eq: sinon.stub().withArgs(1)
                .returns({ exec: sinon.stub().resolves([
                    {
                        id: '1234567890',
                        associatedOrganizations: [{
                            id: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                            associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN
                        }]
                    }
                ]) })
        });

        request(process.env.BASE_URL)
            .put('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '95285616-3769-4b2e-b36b-898597b8146e',
                role: 'admin',
                status: 'active'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                OrganizationMembers.get.restore();
                User.query.restore();
                done();
            });
    });

    it('As a user I should add get error message if I try to update an user to super admin user', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });

        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' },
                { id: '95285616-3769-4b2e-b36b-898597b8146e', email: '<EMAIL>',
                    associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' }],
            save: () => {}
        });

        sinon.stub(User, 'query').returns({
            eq: sinon.stub().withArgs(1)
                .returns({ exec: sinon.stub().resolves([
                    {
                        id: '1234567890',
                        associatedOrganizations: [{
                            id: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                            associatedOrgRole: CONSTANTS.ROLE.ORG_EDITOR
                        }]
                    }
                ]) })
        });

        request(process.env.BASE_URL)
            .put('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '95285616-3769-4b2e-b36b-898597b8146e',
                role: 'super admin',
                status: 'active'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                OrganizationMembers.get.restore();
                User.query.restore();
                done();
            });
    });

    it('As a user i should be able to update organization user', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });

        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' },
                { id: '95285616-3769-4b2e-b36b-898597b8146e', email: '<EMAIL>',
                    associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' }],
            save: () => {}
        });

        sinon.stub(User, 'query').returns({
            eq: sinon.stub().withArgs(1)
                .returns({ exec: sinon.stub().resolves([
                    {
                        id: '1234567890',
                        associatedOrganizations: [
                            {
                                organizationId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                                associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN
                            },
                            {
                                organizationId: '8d40ddbe-996a-4183-a4ef-b32f66e045a4',
                                associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN
                            }
                        ],
                        save: () => {}
                    }
                ]) })
        });

        request(process.env.BASE_URL)
            .put('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '95285616-3769-4b2e-b36b-898597b8146e',
                role: 'editor',
                status: 'active'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
                OrganizationMembers.get.restore();
                User.query.restore();
                done();
            });
    });

    it('As a user I should be able to update organization user and makes his status to inactive', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });

        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' },
                { id: '95285616-3769-4b2e-b36b-898597b8146e', email: '<EMAIL>',
                    associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' }],
            save: () => {}
        });

        sinon.stub(User, 'query').returns({
            eq: sinon.stub().withArgs(1)
                .returns({ exec: sinon.stub().resolves([
                    {
                        id: '1234567890',
                        associatedOrganizations: [
                            {
                                organizationId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                                associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN
                            },
                            {
                                organizationId: '8d40ddbe-996a-4183-a4ef-b32f66e045a4',
                                associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN
                            }
                        ],
                        save: () => {}
                    }
                ]) })
        });

        request(process.env.BASE_URL)
            .put('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '95285616-3769-4b2e-b36b-898597b8146e',
                role: 'editor',
                status: 'inactive'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
                OrganizationMembers.get.restore();
                User.query.restore();
                done();
            });
    });

    it('As a user I should be able to update organization user and makes his status to active from inactive', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });

        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' },
                { id: '95285616-3769-4b2e-b36b-898597b8146e', email: '<EMAIL>',
                    associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'inactive' }],
            save: () => {}
        });

        sinon.stub(User, 'query').returns({
            eq: sinon.stub().withArgs(1)
                .returns({ exec: sinon.stub().resolves([
                    {
                        id: '1234567890',
                        associatedOrganizations: [
                            {
                                organizationId: '8d40ddbe-996a-4183-a4ef-b32f66e045a4',
                                associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN
                            }
                        ],
                        save: () => {}
                    }
                ]) })
        });

        request(process.env.BASE_URL)
            .put('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '95285616-3769-4b2e-b36b-898597b8146e',
                role: 'editor',
                status: 'active'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
                OrganizationMembers.get.restore();
                User.query.restore();
                done();
            });
    });

});

describe('Delete Organization', () => {
    let getStub;
    before(async () => {
        getStub = sinon.stub(User, 'get');
    });

    after(async () => {
        sinon.restore();
    });

    TestCase.deleteOrganizationUser.forEach((data) => {
        it(data.it, (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP });
            request(process.env.BASE_URL)
                .delete('/organization/user')
                .set({ Authorization: requestPayloadUser.token })
                .send(data.options)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.body.status, data.status);
                    done();
                });
        });
    });

    it('As a user I should get error I pass my id in userId', (done) => {
        getStub.resolves({
            id: '95285616-3769-4b2e-b36b-898597b8146e',
            email: '<EMAIL>', status: 'active', isVerified: 1, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });

        request(process.env.BASE_URL)
            .delete('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '95285616-3769-4b2e-b36b-898597b8146e'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.status, 400);
                done();
            });
    });
    it('As a user I should get error message if orgId doesnt exists', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });
        sinon.stub(OrganizationMembers, 'get').resolves(null);
        request(process.env.BASE_URL)
            .delete('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '95285616-3769-4b2e-b36b-898597b8146e'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                OrganizationMembers.get.restore();
                done();
            });
    });

    it('As a user I should get error message if I am not associated with the organization', (done) => {
        getStub.resolves({
            id: 123, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });
        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' },
                { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }]
        });

        request(process.env.BASE_URL)
            .delete('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '95285616-3769-4b2e-b36b-898597b8146e'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                OrganizationMembers.get.restore();
                done();
            });
    });

    it('As a user I should get error message if my role is not admin or super admin', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });
        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_EDITOR, status: 'active' },
                { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }]
        });

        request(process.env.BASE_URL)
            .delete('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '95285616-3769-4b2e-b36b-898597b8146e'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                OrganizationMembers.get.restore();
                done();
            });
    });

    it('As a user I should get error message if my status is not active', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });
        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'deleted' },
                { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }]
        });

        request(process.env.BASE_URL)
            .delete('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '95285616-3769-4b2e-b36b-898597b8146e'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                OrganizationMembers.get.restore();
                done();
            });
    });

    it('As a user I should get error message if org user doesnt exists', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });

        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' },
                { id: 2, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }]
        });

        sinon.stub(User, 'query').returns({
            eq: sinon.stub().withArgs(1)
                .returns({ exec: sinon.stub().resolves([
                    {
                        id: '1234567890',
                        associatedOrganizations: []
                    }
                ]) })
        });

        request(process.env.BASE_URL)
            .delete('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '95285616-3769-4b2e-b36b-898597b8146e'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                OrganizationMembers.get.restore();
                User.query.restore();
                done();
            });
    });

    it('As a user I should add get error message if I try to delete super admin user as an admin user', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });

        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' },
                { id: '95285616-3769-4b2e-b36b-898597b8146e', email: '<EMAIL>',
                    associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, status: 'active' }],
            save: () => {}
        });

        sinon.stub(User, 'query').returns({
            eq: sinon.stub().withArgs(1)
                .returns({ exec: sinon.stub().resolves([
                    {
                        id: '1234567890',
                        associatedOrganizations: [{
                            id: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                            associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN
                        }]
                    }
                ]) })
        });

        request(process.env.BASE_URL)
            .delete('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '95285616-3769-4b2e-b36b-898597b8146e'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                OrganizationMembers.get.restore();
                User.query.restore();
                done();
            });
    });

    it('As a user I should be able to delete user', (done) => {
        getStub.resolves({
            id: 1, email: '<EMAIL>', status: 'active', isVerified: 1, role: 3,
            accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });

        sinon.stub(OrganizationMembers, 'get').resolves({
            users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' },
                { id: '95285616-3769-4b2e-b36b-898597b8146e', email: '<EMAIL>',
                    associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN, status: 'active' }],
            save: () => {}
        });

        sinon.stub(User, 'query').returns({
            eq: sinon.stub().withArgs(1)
                .returns({ exec: sinon.stub().resolves([
                    {
                        id: '1234567890',
                        associatedOrganizations: [
                            {
                                organizationId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                                associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN
                            },
                            {
                                organizationId: '8d40ddbe-996a-4183-a4ef-b32f66e045a4',
                                associatedOrgRole: CONSTANTS.ROLE.ORG_ADMIN
                            }
                        ],
                        save: () => {}
                    }
                ]) })
        });

        request(process.env.BASE_URL)
            .delete('/organization/user')
            .set({ Authorization: requestPayloadUser.token })
            .send({
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '95285616-3769-4b2e-b36b-898597b8146e'
            })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
                OrganizationMembers.get.restore();
                done();
            });
    });
});

describe('Organization Search', () => {
    let getStub;
    before(async () => {
        getStub = sinon.stub(User, 'get');
    });

    after(async () => {
        sinon.restore();
    });

    it('As a user I should get error do invalid search', (done) => {
        getStub.resolves({
            id: '95285616-3769-4b2e-b36b-898597b8146e',
            email: '<EMAIL>', status: 'active', isVerified: 1, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });

        request(process.env.BASE_URL)
            .get('/organization/search?search=123')
            .set({ Authorization: requestPayloadUser.token })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.status, 400);
                done();
            });
    });

    it('As a user I should get searched result', (done) => {
        getStub.resolves({
            id: '95285616-3769-4b2e-b36b-898597b8146e',
            email: '<EMAIL>',
            status: 'active',
            isVerified: 1,
            role: 3
        });
        const searchOrgStub = sinon.stub(AwsOpenSearchService, 'searchOrganization');
        searchOrgStub.resolves({
            page: 1,
            organization: addOrganization,
            count: 1,
            totalPages: 1,
            totalItems: 1
        });
        request(process.env.BASE_URL)
            .get('/organization/search?searchValue=123')
            .set({ Authorization: requestPayloadUser.token })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.status, 200);
                searchOrgStub.restore();
                done();
            });
    });
});

describe('Generate presigned url', () => {
    let getStub;
    before(async () => {
        getStub = sinon.stub(User, 'get');
    });

    after(async () => {
        sinon.restore();
    });

    it('should handle error if signed url is not generated', (done) => {
        getStub.resolves({
            id: '95285616-3769-4b2e-b36b-898597b8146e',
            email: '<EMAIL>', status: 'active', isVerified: 1, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });

        sinon.stub(UploadService, 'getPreSignedUrlForUpload').rejects(new Error('Failed to generate signed url'));

        request(process.env.BASE_URL)
            .get('/organization/generate-presigned-url')
            .set({ Authorization: requestPayloadUser.token })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                UploadService.getPreSignedUrlForUpload.restore();
                done();
            });
    });

    it('As a user I should validate if organization members exists', (done) => {
        getStub.resolves({
            id: '95285616-3769-4b2e-b36b-898597b8146e',
            email: '<EMAIL>', status: 'active', isVerified: 1, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });

        sinon.stub(organizationMemberModel, 'get').resolves();

        request(process.env.BASE_URL)
            .get('/organization/generate-presigned-url?organizationId=org-id1')
            .set({ Authorization: requestPayloadUser.token })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);

                organizationMemberModel.get.restore();
                done();
            });
    });

    it('As a user I should validate if I am associated with the organization', (done) => {
        getStub.resolves({
            id: '95285616-3769-4b2e-b36b-898597b8146e',
            email: '<EMAIL>', status: 'active', isVerified: 1, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });

        sinon.stub(organizationMemberModel, 'get').resolves({
            users: [
                { id: 'userId1' }
            ]
        });

        request(process.env.BASE_URL)
            .get('/organization/generate-presigned-url?organizationId=org-id1')
            .set({ Authorization: requestPayloadUser.token })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);

                organizationMemberModel.get.restore();
                done();
            });
    });

    it('As a user I should validate if I am associated with the organization and status is active', (done) => {
        getStub.resolves({
            id: '95285616-3769-4b2e-b36b-898597b8146e',
            email: '<EMAIL>', status: 'active', isVerified: 1, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });

        sinon.stub(organizationMemberModel, 'get').resolves({
            users: [
                { id: '95285616-3769-4b2e-b36b-898597b8146e', status: 'deleted' }
            ]
        });

        request(process.env.BASE_URL)
            .get('/organization/generate-presigned-url?organizationId=org-id1')
            .set({ Authorization: requestPayloadUser.token })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);

                organizationMemberModel.get.restore();
                done();
            });
    });

    it('As a user I should be able to get presigned url', (done) => {
        getStub.resolves({
            id: '95285616-3769-4b2e-b36b-898597b8146e',
            email: '<EMAIL>', status: 'active', isVerified: 1, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
        });

        sinon.stub(organizationMemberModel, 'get').resolves({
            users: [
                { id: '95285616-3769-4b2e-b36b-898597b8146e', status: 'active' }
            ]
        });

        request(process.env.BASE_URL)
            .get('/organization/generate-presigned-url?organizationId=org-id1')
            .set({ Authorization: requestPayloadUser.token })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);

                organizationMemberModel.get.restore();
                done();
            });
    });
});
