const { beforeEach, afterEach } = require('mocha');
const { publishCustomMetric } = require('../cloudwatchMetrics');
const { CloudWatchClient } = require('@aws-sdk/client-cloudwatch');
const sinon = require('sinon');
const assert = sinon.assert;

describe('Cloudwatch Metrics', () => {
    let publishCustomMetricStub;
    beforeEach(() => {
        publishCustomMetricStub = sinon.stub(CloudWatchClient.prototype, 'send').resolves();
    });

    afterEach(() => {
        sinon.restore();
    });

    it('should return without publishing custom metric if metric name is not provided', async () => {
        const value = 1;
        await publishCustomMetric(value);

        assert.notCalled(publishCustomMetricStub);
    });
});
