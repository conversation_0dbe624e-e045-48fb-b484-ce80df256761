const GeneralError = require('../../util/GeneralError');
const validation = require('../../util/validation');
const REQUIRED = 'FIELD_REQUIRED';
const INVALID = 'FIELD_NOT_VALID';

/**
 * Class represents validations for payment.
 */
class PaymentValidator extends validation {
    constructor (body, locale, query) {
        super(locale);
        this.NOT_VALID = INVALID;
        this.REQUIRED = REQUIRED;
        this.body = body;
        this.query = query;
    }

    /**
     * @desc This function is being used to validate request for payment
     * <AUTHOR>
     * @since 17/11/2023
     */
    validate () {
        const { eventId, childId, paymentType, purchasedProducts } = this.body;
        super.field(eventId, 'Event Id');
        super.field(childId, 'Child Id');
        super.field(paymentType, 'Payment Type');
        super.field(purchasedProducts, 'Purchased Products');
        this.verifyPaymentType();
    }

    validateMembershipType (membershipType) {
        if (!['family', 'child'].includes(membershipType)) {
            throw new GeneralError(this.__(INVALID, 'Membership Type'), 400);
        }
    }

    verifyPaymentType () {
        const { paymentType } = this.body;
        if (!['stripe', 'cash', 'cheque', 'venmo', 'free'].includes(paymentType)) {
            throw new GeneralError(this.__(INVALID, 'Payment Type'), 400);
        }
    }

    validateOptionalFee () {
        const { isCoveredByUser } = this.body;
        if (typeof isCoveredByUser !== 'boolean') {
            throw new GeneralError(this.__(INVALID, 'Fees covered by user'), 400);
        }
    }

    validateChildBoosterDetails () {
        super.field(this.body.fundraiserSignupId, 'Fundraiser Sign up Id');
        super.uuid(this.body.fundraiserSignupId, 'Fundraiser Sign up Id');
        super.field(this.body.boosterGoalForChild, 'Booster Goal for Child');
        super.number(this.body.boosterGoalForChild, 'Booster Goal for Child');
        super.field(this.body.boosterMessageForChild, 'Booster Message for Child');
    }

}


module.exports = PaymentValidator;
