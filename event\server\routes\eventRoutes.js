/**
 * This file contains routes used for parent user.
 * Created by Growexx on 19/10/2023.
 * @name parentRoutes
 */
const router = require('express').Router();

const EventController = require('../services/event/eventController');

const AuthMiddleware = require('../middleware/auth');
const AclMiddleware = require('../middleware/acl');
const UploadMiddleWare = require('../middleware/upload');
const HmacMiddleware = require('../middleware/hmac');

router.post('/', HmacMiddleware, AuthMiddleware, AclMiddleware, UploadMiddleWare.any(), EventController.addEvent);
router.post('/multiple', HmacMiddleware, AuthMiddleware, AclMiddleware, EventController.addMultipleEvent);
router.delete('/', HmacMiddleware, AuthMiddleware, AclMiddleware, EventController.deleteEvent);
router.patch('/publish', HmacMiddleware, AuthMiddleware, AclMiddleware, EventController.publishEvent);

router.get('/list', HmacMiddleware, AuthMiddleware, AclMiddleware, EventController.getEventList);
router.get('/', HmacMiddleware, AuthMiddleware, AclMiddleware, EventController.getEventDetails);
router.put('/', HmacMiddleware, AuthMiddleware, AclMiddleware, UploadMiddleWare.any(), EventController.updateEvent);
router.get('/search', HmacMiddleware, AuthMiddleware, EventController.getSearchEventList);

router.get('/participant', HmacMiddleware, AuthMiddleware, AclMiddleware, EventController.getParticipantEventList);
router.put('/signup-status', HmacMiddleware, AuthMiddleware, AclMiddleware, EventController.updatePaymentStatus);

router.post('/start-chunk-upload', HmacMiddleware, AuthMiddleware, AclMiddleware,
    UploadMiddleWare.single('file'), EventController.startChunkUpload);
router.post('/upload-chunk', HmacMiddleware, AuthMiddleware, AclMiddleware, UploadMiddleWare.single('file'), EventController.uploadChunk);
router.post('/complete-chunk-upload', HmacMiddleware, AuthMiddleware, AclMiddleware, EventController.completeChunkUpload);
router.post('/abort-chunk-upload', HmacMiddleware, AuthMiddleware, AclMiddleware, EventController.abortChunkUpload);

router.get('/generate-presigned-url', HmacMiddleware, AuthMiddleware, AclMiddleware, EventController.generatePresignedUrl);

module.exports = router;

