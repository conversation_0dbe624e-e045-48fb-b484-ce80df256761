module.exports = {
    addOrganizationAdmin: [
        {
            it: 'As a user I should validate if orgName is not passed',
            options: {
                orgName: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate if orgName is invalid',
            options: {
                orgName: 'T'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if orgName is invalid',
            options: {
                orgName: 'x'.repeat(205)
            },
            status: 0
        },
        {
            it: 'As a user I should validate if category is not passed',
            options: {
                orgName: 'Test org',
                adminFirstName: 'Test',
                adminLastName: 'Name'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if category is invalid',
            options: {
                orgName: 'Test org',
                adminFirstName: 'Test',
                adminLastName: 'Name',
                category: 'Test'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if address is invalid',
            options: {
                orgName: 'Test org',
                adminFirstName: 'Test',
                adminLastName: 'Name',
                category: 'PTO',
                address: 'Test'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if address is invalid',
            options: {
                orgName: 'Test PTO',
                adminFirstName: 'Test',
                adminLastName: 'Name',
                category: 'PTO',
                address: 'x'.repeat(205)
            },
            status: 0
        },
        {
            it: 'As a user I should validate if zipcode is not passed',
            options: {
                orgName: 'Test PTO',
                adminFirstName: 'Test',
                adminLastName: 'Name',
                category: 'PTO',
                address: 'PTO test address',
                country: 'country',
                city: 'city',
                state: 'state'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if zipcode is invalid',
            options: {
                orgName: 'Test PTO',
                adminFirstName: 'Test',
                adminLastName: 'Name',
                category: 'PTO',
                address: 'PTO test address',
                country: 'country',
                city: 'city',
                state: 'state',
                zipCode: '123'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if countryCode is not passed',
            options: {
                orgName: 'Test PTO',
                adminFirstName: 'Test',
                adminLastName: 'Name',
                category: 'PTO',
                address: 'PTO test address',
                country: 'country',
                city: 'city',
                state: 'state',
                zipCode: '12345',
                phoneNumber: '1111111111',
                countryCode: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate if phoneNumber is invalid',
            options: {
                orgName: 'Test PTO',
                adminFirstName: 'Test',
                adminLastName: 'Name',
                category: 'PTO',
                address: 'PTO test address',
                country: 'country',
                city: 'city',
                state: 'state',
                zipCode: '12345',
                phoneNumber: '1',
                countryCode: '+1',
                email: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate if email is not passed',
            options: {
                orgName: 'Test PTO',
                adminFirstName: 'Test',
                adminLastName: 'Name',
                category: 'PTO',
                address: 'PTO test address',
                country: 'country',
                city: 'city',
                state: 'state',
                zipCode: '12345',
                phoneNumber: '1111111111',
                countryCode: '+1',
                email: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate if email is invalid',
            options: {
                orgName: 'Test PTO',
                adminFirstName: 'Test',
                adminLastName: 'Name',
                category: 'PTO',
                address: 'PTO test address',
                country: 'country',
                city: 'city',
                state: 'state',
                zipCode: '12345',
                phoneNumber: '1111111111',
                countryCode: '+1',
                email: 'test'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if onboarding is invalid',
            options: {
                orgName: 'Test PTO',
                adminFirstName: 'Test',
                adminLastName: 'Name',
                category: 'PTO',
                address: 'PTO test address',
                country: 'country',
                city: 'city',
                state: 'state',
                zipCode: '12345',
                phoneNumber: '1111111111',
                countryCode: '+1',
                email: '<EMAIL>',
                isStripeOnboarding: 'test'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if onboarding is not passed',
            options: {
                orgName: 'Test PTO',
                adminFirstName: 'Test',
                adminLastName: 'Name',
                category: 'PTO',
                address: 'PTO test address',
                country: 'country',
                city: 'city',
                state: 'state',
                zipCode: '12345',
                phoneNumber: '1111111111',
                countryCode: '+1',
                email: '<EMAIL>'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if parentOrganizationId is not valid',
            options: {
                orgName: 'Test PTO',
                adminFirstName: 'Test',
                adminLastName: 'Name',
                category: 'PTO',
                address: 'PTO test address',
                country: 'country',
                city: 'city',
                state: 'state',
                zipCode: '12345',
                phoneNumber: '1111111111',
                countryCode: '+1',
                email: '<EMAIL>',
                isStripeOnboarding: false,
                parentOrganization: '123'
            },
            status: 0
        }
    ],
    addOrganizationUser: [
        {
            it: 'As a user I should validate orgId is passed',
            options: {
                orgId: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate orgId matches uuid regex',
            options: {
                orgId: '1234'
            },
            status: 0
        },
        {
            it: 'As a user I should validate first name is passed',
            options: {
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                firstName: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate first name passed name regex',
            options: {
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                firstName: '  '
            },
            status: 0
        },
        {
            it: 'As a user I should validate last name is passed',
            options: {
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                firstName: 'test',
                lastName: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate last name passed name regex',
            options: {
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                firstName: 'test',
                lastName: '  '
            },
            status: 0
        },
        {
            it: 'As a user I should validate email is passed',
            options: {
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                firstName: 'test',
                lastName: 'test',
                email: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate email passed email regex',
            options: {
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                firstName: 'test',
                lastName: 'test',
                email: 'test'
            },
            status: 0
        },
        {
            it: 'As a user I should validate org role is passed',
            options: {
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                firstName: 'test',
                lastName: 'test',
                email: '<EMAIL>',
                role: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate passed org role',
            options: {
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                firstName: 'test',
                lastName: 'test',
                email: '<EMAIL>',
                role: 'test'
            },
            status: 0
        },
        {
            it: 'As a user I should validate phone number if passed',
            options: {
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                firstName: 'test',
                lastName: 'test',
                email: '<EMAIL>',
                role: 'admin',
                phoneNumber: '123'
            },
            status: 0
        },
        {
            it: 'As a user I should validate country code is passed if phone number passed',
            options: {
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                firstName: 'test',
                lastName: 'test',
                email: '<EMAIL>',
                role: 'admin',
                phoneNumber: '9876543210',
                countryCode: ''
            },
            status: 0
        }
    ],
    updateOrganizationUser: [
        {
            it: 'As a user I should validate orgId is passed',
            options: {
                orgId: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate orgId matches uuid regex',
            options: {
                orgId: '1234'
            },
            status: 0
        },
        {
            it: 'As a user I should validate userId is passed',
            options: {
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate role is passed',
            options: {
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '1344389a-7c86-43e1-81f3-3aa40b05dbdb',
                role: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate role is valid',
            options: {
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '1344389a-7c86-43e1-81f3-3aa40b05dbdb',
                role: 'test'
            },
            status: 0
        },
        {
            it: 'As a user I should validate status is passed',
            options: {
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '1344389a-7c86-43e1-81f3-3aa40b05dbdb',
                role: 'admin',
                status: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate status is valid',
            options: {
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '1344389a-7c86-43e1-81f3-3aa40b05dbdb',
                role: 'admin',
                status: 'test'
            },
            status: 0
        },
        {
            it: 'As a user I should validate email is not passed',
            options: {
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '1344389a-7c86-43e1-81f3-3aa40b05dbdb',
                role: 'admin',
                status: 'active',
                password: 'Test@123'
            },
            status: 0
        },

        {
            it: 'As a user I should validate password is not valid',
            options: {
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: '1344389a-7c86-43e1-81f3-3aa40b05dbdb',
                role: 'admin',
                status: 'active',
                email: '<EMAIL>',
                password: 'test'
            },
            status: 0
        }
    ],
    deleteOrganizationUser: [
        {
            it: 'As a user I should validate orgId is passed',
            options: {
                orgId: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate orgId matches uuid regex',
            options: {
                orgId: '1234'
            },
            status: 0
        },
        {
            it: 'As a user I should validate userId is passed',
            options: {
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                userId: ''
            },
            status: 0
        }
    ],
    addChildrenToOrganization: [
        {
            it: 'As a user I should validate orgId is passed',
            options: {
                orgId: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate orgId matches uuid regex',
            options: {
                orgId: '1234'
            },
            status: 0
        },
        {
            it: 'As a user I should validate addedChildIds is passed',
            options: {
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5'
            },
            status: 0
        },
        {
            it: 'As a user I should validate removeChildIds is passed',
            options: {
                orgId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5',
                addedChildIds: []
            },
            status: 0
        }
    ]
};
