{"name": "vaalee-be", "version": "1.0.0", "description": "", "main": ".eslintrc.js", "directories": {"test": "test"}, "scripts": {"preinstall": "npm install --prefix auth && npm install --prefix api && npm install --prefix event && npm install --prefix feed && npm install --prefix feed-generator  && npm install --prefix notification && npm install --prefix post && npm install --prefix fundraiser && npm install --prefix conversation", "test": "npm test --prefix auth && npm test --prefix api && npm test --prefix event && npm test --prefix feed && npm test --prefix feed-generator  && npm test --prefix notification && npm test --prefix post && npm test --prefix fundraiser && npm test --prefix conversation", "coverage": "nyc npm run test", "setup:local": "node setupLocalEnv.js", "generate:hmac": "node calculateHMAC"}, "repository": {"type": "git", "url": "git+https://github.com/growexx/vaalee-be.git"}, "author": "Growexx", "license": "ISC", "bugs": {"url": "https://github.com/growexx/vaalee-be.git"}, "homepage": "https://github.com/growexx/vaalee-be.git#readme", "dependencies": {"aws-sdk": "^2.1060.0", "dotenv": "^11.0.0", "stripe": "^10.17.0"}, "devDependencies": {"chai": "^4.3.4", "chai-http": "^4.3.0", "eslint": "^8.6.0", "jsdoc": "^3.6.6", "mocha": "^8.3.2", "nyc": "^15.1.0", "sinon": "^10.0.0", "source-map-support": "^0.5.19", "supertest": "^6.1.3"}, "nyc": {"lines": 5, "statements": 5, "functions": 5, "branches": 5, "check-coverage": true, "exclude": ["**/node_modules/**", "**/test/**", "**/coverage/**", "**/migrations/**", "**/testmigrations/**", "**/db", "**/jsdocs/**", "**/.eslintrc.js", "**/getEnvs.js", "**/retrieveSecrets.js", "**/sendEmail.js", "**/migrate-mongo-config.js", "**/util/country.js", "**/util/currency.js", "**/util/timeZone.js", "**/util/languageISO.js", "**/util/http-status.js"], "reporter": ["lcov", "html"], "cache": true, "all": true}}