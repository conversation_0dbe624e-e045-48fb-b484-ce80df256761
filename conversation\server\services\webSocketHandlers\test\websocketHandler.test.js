const { expect } = require('chai');
const sinon = require('sinon');
const jwt = require('jsonwebtoken');
const websocketRoutes = require('../../../webSocketHandler');
const SocketConnectionsModel = require('../../../models/socketConnections.model');
const GroupMembers = require('../../../models/groupMembers.model');
const MessageModel = require('../../../models/message.model');
const SendMessageService = require('../../sendSocketMessageService');
const SQSPushNotificationService = require('../../sqsPushNotificationService');
const UserModel = require('../../../models/user.model');
const ChildModel = require('../../../models/child.model');
const OrganizationModel = require('../../../models/organization.model');
const { beforeEach, afterEach } = require('mocha');
const MessageReactionsHandler = require('../handlers/messageReactionsHandler');
const RedisUtil = require('../../../util/redisUtil');
const Redis = require('ioredis');
const ConstantModel = require('../../../models/constant.model');

const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
/* User Token */
const user = {
    id: '95285616-3769-4b2e-b36b-898597b8146e',
    sub: '95285616-3769-4b2e-b36b-898597b8146e',
    email: '<EMAIL>',
    isVerified: 1,
    role: 1,
    status: 'active',
    isDeleted: 0
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'testToken', tokenOptionalInfo)
};

describe('Connect websocket route', () => {
    try {
        it('should handle error in connect websocket route', async () => {
            sinon.stub(SocketConnectionsModel, 'create').rejects(new Error('Error in connect websocket route'));
            const event = {
                requestContext: {
                    connectionId: 'connectionId1',
                    routeKey: '$connect'
                },
                headers: {
                    Authorization: requestPayloadUser.token
                }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(500);

            SocketConnectionsModel.create.restore();
        });

        it('should handle success in connect websocket route', async () => {
            sinon.stub(SocketConnectionsModel, 'create').resolves({});
            const event = {
                requestContext: {
                    connectionId: 'connectionId1',
                    routeKey: '$connect'
                },
                headers: {},
                queryStringParameters: {
                    token: requestPayloadUser.token.split(' ')[1]
                }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);

            SocketConnectionsModel.create.restore();
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Disconnect websocket route', () => {
    try {
        it('should handle error in disconnect websocket route', async () => {
            sinon.stub(SocketConnectionsModel, 'delete').rejects(new Error('Error in disconnect websocket route'));
            const event = {
                requestContext: {
                    connectionId: 'connectionId1',
                    routeKey: '$disconnect'
                }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(500);

            SocketConnectionsModel.delete.restore();
        });

        it('should handle success in disconnect websocket route', async () => {
            sinon.stub(SocketConnectionsModel, 'delete').resolves({});
            const event = {
                requestContext: {
                    connectionId: 'connectionId1',
                    routeKey: '$disconnect'
                }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);

            SocketConnectionsModel.delete.restore();
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Send message websocket route', () => {
    try {
        it('should handle error in NEW_GROUP_MESSAGE websocket route', async () => {
            sinon.stub(GroupMembers, 'query').rejects(new Error('Error in send message websocket route'));

            const event = {
                body: {
                    action: 'sendMessage',
                    actionType: 'NEW_GROUP_MESSAGE',
                    groupId: 'groupId1',
                    message: 'message1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(500);

            GroupMembers.query.restore();
        });

        it('Should handle is group is not found', async () => {
            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                using: sinon.stub().returns({
                                    exec: sinon.stub().resolves([])
                                })
                            })
                        })
                    })
                })
            });

            const event = {
                body: {
                    action: 'sendMessage',
                    actionType: 'NEW_GROUP_MESSAGE',
                    groupId: 'groupId1',
                    message: 'message1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(404);

            GroupMembers.query.restore();
        });

        it('Should handle send message to group', async () => {
            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                using: sinon.stub().returns({
                                    exec: sinon.stub().resolves([
                                        {
                                            id: 'groupId1',
                                            name: 'groupName1',
                                            save: () => { }
                                        }
                                    ])
                                })
                            })
                        }),
                        attributes: sinon.stub().returns({
                            where: sinon.stub().returns({
                                in: sinon.stub().returns({
                                    exec: sinon.stub().resolves([
                                        {
                                            id: 'groupId1',
                                            name: 'groupName1'
                                        }
                                    ])
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(MessageModel, 'create').resolves({
                createdAt: new Date()
            });

            sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();
            sinon.stub(SQSPushNotificationService, 'sendGroupMessage').resolves();

            const event = {
                body: {
                    action: 'sendMessage',
                    actionType: 'NEW_GROUP_MESSAGE',
                    groupId: 'groupId1',
                    message: 'message1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);

            MessageModel.create.restore();
            GroupMembers.query.restore();
            SendMessageService.sendMessagesToUsers.restore();
            SQSPushNotificationService.sendGroupMessage.restore();
        });

        it('Should handle send message to group with reply message', async () => {
            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                using: sinon.stub().returns({
                                    exec: sinon.stub().resolves([
                                        {
                                            id: 'groupId1',
                                            name: 'groupName1',
                                            save: () => { }
                                        }
                                    ])
                                })
                            })
                        }),
                        attributes: sinon.stub().returns({
                            where: sinon.stub().returns({
                                in: sinon.stub().returns({
                                    exec: sinon.stub().resolves([
                                        {
                                            id: 'groupId1',
                                            name: 'groupName1'
                                        }
                                    ])
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(MessageModel, 'create').resolves({
                createdAt: new Date()
            });

            sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();
            sinon.stub(SQSPushNotificationService, 'sendGroupMessage').resolves();

            sinon.stub(MessageModel, 'get').resolves({
                id: 'messageId1',
                message: 'message1',
                mediaName: 'mediaName1',
                mediaType: 'mediaType1',
                mediaDisplayName: 'mediaDisplayName1'
            });

            const event = {
                body: {
                    action: 'sendMessage',
                    actionType: 'NEW_GROUP_MESSAGE',
                    groupId: 'groupId1',
                    message: 'message1',
                    replyMessage: {
                        messageId: 'messageId1'
                    }
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);

            MessageModel.create.restore();
            GroupMembers.query.restore();
            MessageModel.get.restore();
            SendMessageService.sendMessagesToUsers.restore();
            SQSPushNotificationService.sendGroupMessage.restore();
        });

        it('should handle error in EDIT_GROUP_MESSAGE websocket route', async () => {
            sinon.stub(MessageModel, 'update').rejects(new Error('Error in send message websocket route'));

            const event = {
                body: {
                    action: 'sendMessage',
                    actionType: 'EDIT_GROUP_MESSAGE',
                    messageId: 'messageId1',
                    message: 'message1',
                    groupId: 'groupId1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(500);

            MessageModel.update.restore();
        });

        it('should handle success in EDIT_GROUP_MESSAGE websocket route', async () => {
            sinon.stub(MessageModel, 'update').resolves({});
            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                using: sinon.stub().returns({
                                    exec: sinon.stub().resolves([
                                        {
                                            id: 'groupId1',
                                            name: 'groupName1',
                                            save: () => { }
                                        }
                                    ])
                                })
                            })
                        }),
                        attributes: sinon.stub().returns({
                            where: sinon.stub().returns({
                                in: sinon.stub().returns({
                                    exec: sinon.stub().resolves([
                                        {
                                            id: 'groupId1',
                                            name: 'groupName1'
                                        }
                                    ])
                                })
                            })
                        })
                    })
                })
            });
            sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();

            const event = {
                body: {
                    action: 'sendMessage',
                    actionType: 'EDIT_GROUP_MESSAGE',
                    messageId: 'messageId1',
                    message: 'message1',
                    groupId: 'groupId1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);

            MessageModel.update.restore();
            GroupMembers.query.restore();
            SendMessageService.sendMessagesToUsers.restore();
        });

        it('should handle error in DELETE_GROUP_MESSAGE websocket route', async () => {
            sinon.stub(MessageModel, 'update').rejects(new Error('Error in send message websocket route'));

            const event = {
                body: {
                    action: 'sendMessage',
                    actionType: 'DELETE_GROUP_MESSAGE',
                    messageId: 'messageId1',
                    groupId: 'groupId1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(500);

            MessageModel.update.restore();
        });

        it('should handle success in DELETE_GROUP_MESSAGE websocket route', async () => {
            sinon.stub(MessageModel, 'update').resolves({});
            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                using: sinon.stub().returns({
                                    exec: sinon.stub().resolves([
                                        {
                                            id: 'groupId1',
                                            name: 'groupName1',
                                            save: () => { }
                                        }
                                    ])
                                })
                            })
                        }),
                        attributes: sinon.stub().returns({
                            where: sinon.stub().returns({
                                in: sinon.stub().returns({
                                    exec: sinon.stub().resolves([
                                        {
                                            id: 'groupId1',
                                            name: 'groupName1'
                                        }
                                    ])
                                })
                            })
                        })
                    })
                })
            });
            sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();

            const event = {
                body: {
                    action: 'sendMessage',
                    actionType: 'DELETE_GROUP_MESSAGE',
                    messageId: 'messageId1',
                    groupId: 'groupId1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);

            MessageModel.update.restore();
            GroupMembers.query.restore();
            SendMessageService.sendMessagesToUsers.restore();
        });

        it('should handle error in READ_GROUP_MESSAGE websocket route', async () => {
            sinon.stub(GroupMembers, 'query').rejects(new Error('Error in send message websocket route'));

            const event = {
                body: {
                    action: 'sendMessage',
                    actionType: 'READ_GROUP_MESSAGE',
                    messageId: 'messageId1',
                    groupId: 'groupId1',
                    userId: 'userId1',
                    createdAt: new Date()
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(500);

            GroupMembers.query.restore();
        });

        it('should handle if group is not found', async () => {
            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                using: sinon.stub().returns({
                                    exec: sinon.stub().resolves([])
                                })
                            })
                        })
                    })
                })
            });

            const event = {
                body: {
                    action: 'sendMessage',
                    actionType: 'READ_GROUP_MESSAGE',
                    messageId: 'messageId1',
                    groupId: 'groupId1',
                    userId: 'userId1',
                    createdAt: new Date()
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(404);

            GroupMembers.query.restore();
        });

        it('should handle success in READ_GROUP_MESSAGE websocket route', async () => {
            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                using: sinon.stub().returns({
                                    exec: sinon.stub().resolves([{ id: 'groupId1', name: 'groupName1', save: () => { } }])
                                })
                            })
                        })
                    })
                })
            });

            const event = {
                body: {
                    action: 'sendMessage',
                    actionType: 'READ_GROUP_MESSAGE',
                    messageId: 'messageId1',
                    groupId: 'groupId1',
                    userId: 'userId1',
                    createdAt: new Date()
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);

            GroupMembers.query.restore();
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Get message websocket route', () => {
    try {
        let getReactionsForMessageStub;
        let stubRedisConnect;

        beforeEach(() => {
            getReactionsForMessageStub = sinon.stub(MessageReactionsHandler, 'getReactionsForMessage');
            stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
            stubRedisConnect.callsFake(async function () {
                this.setStatus('connect');
            });
            sinon.stub(ConstantModel, 'get').resolves({ value: 'v1' });
        });

        afterEach(() => {
            getReactionsForMessageStub.restore();
            stubRedisConnect.restore();
            ConstantModel.get.restore();
        });

        it('should handle error in LAZY_LOAD_GROUP_MESSAGES websocket route', async () => {
            sinon.stub(MessageModel, 'query').rejects(new Error('Error in get message websocket route'));

            const event = {
                body: {
                    action: 'getMessage',
                    actionType: 'LAZY_LOAD_GROUP_MESSAGES',
                    groupId: 'groupId1',
                    lastEvaluatedKey: 'lastEvaluatedKey1',
                    clientLastSyncedMessage: {
                        recentFetchedMessage: {
                            messageId: 'messageId1'
                        },
                        lastFetchedMessage: {
                            messageId: 'messageId2'
                        }
                    }
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(500);

            MessageModel.query.restore();
        });

        it('should handle success in LAZY_LOAD_GROUP_MESSAGES websocket route', async () => {
            sinon.stub(MessageModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                startAt: sinon.stub().returns({
                                    exec: sinon.stub().resolves([])
                                })
                            })
                        })
                    })
                })
            });

            const event = {
                body: {
                    action: 'getMessage',
                    actionType: 'LAZY_LOAD_GROUP_MESSAGES',
                    groupId: 'groupId1',
                    lastEvaluatedKey: 'lastEvaluatedKey1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);

            MessageModel.query.restore();
        });

        it('should handle success in LAZY_LOAD_GROUP_MESSAGES websocket route with clientLastSyncedMessage', async () => {
            sinon.stub(MessageModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                exec: sinon.stub().resolves([
                                    {
                                        id: 'messageId1',
                                        message: 'message1',
                                        mediaName: 'mediaName1',
                                        mediaType: 'mediaType1',
                                        mediaDisplayName: 'mediaDisplayName1'
                                    }
                                ])
                            })
                        })
                    })
                })
            });

            const event = {
                body: {
                    action: 'getMessage',
                    actionType: 'LAZY_LOAD_GROUP_MESSAGES',
                    groupId: 'groupId1',
                    clientLastSyncedMessage: {
                        recentFetchedMessage: {
                            messageId: 'messageId1'
                        },
                        lastFetchedMessage: {
                            messageId: 'messageId2'
                        }
                    }
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);

            MessageModel.query.restore();
        });

        it('should handle success in LAZY_LOAD_GROUP_MESSAGES websocket route without lastFetchedMessage', async () => {
            sinon.stub(MessageModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                exec: sinon.stub().resolves([
                                    {
                                        id: 'messageId1',
                                        message: 'message1',
                                        mediaName: 'mediaName1',
                                        mediaType: 'mediaType1',
                                        mediaDisplayName: 'mediaDisplayName1'
                                    }
                                ])
                            })
                        })
                    })
                })
            });

            const event = {
                body: {
                    action: 'getMessage',
                    actionType: 'LAZY_LOAD_GROUP_MESSAGES',
                    groupId: 'groupId1',
                    clientLastSyncedMessage: {
                        recentFetchedMessage: {
                            messageId: 'messageId1'
                        }
                    }
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);

            MessageModel.query.restore();
        });

        it('should handle success in LAZY_LOAD_GROUP_MESSAGES websocket route if recent message is not found', async () => {
            sinon.stub(MessageModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                exec: sinon.stub().resolves([
                                    {
                                        id: 'messageId1',
                                        message: 'message1',
                                        mediaName: 'mediaName1',
                                        mediaType: 'mediaType1',
                                        mediaDisplayName: 'mediaDisplayName1'
                                    }
                                ])
                            })
                        })
                    })
                })
            });

            const event = {
                body: {
                    action: 'getMessage',
                    actionType: 'LAZY_LOAD_GROUP_MESSAGES',
                    groupId: 'groupId1',
                    clientLastSyncedMessage: {
                        recentFetchedMessage: {
                            messageId: 'messageId2'
                        },
                        lastFetchedMessage: {
                            messageId: 'messageId2'
                        }
                    }
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);

            MessageModel.query.restore();
        });

        it('should handle error in GET_GROUP_LIST_AND_MEMBERS websocket route', async () => {
            sinon.stub(GroupMembers, 'query').rejects(new Error('Error in get group list and members websocket route'));

            const event = {
                body: {
                    action: 'getMessage',
                    actionType: 'GET_GROUP_LIST_AND_MEMBERS',
                    userId: 'userId1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(500);

            GroupMembers.query.restore();
        });

        it('should handle success in GET_GROUP_LIST_AND_MEMBERS websocket route', async () => {
            const getAllHashValuesStub = sinon.stub(RedisUtil, 'getAllHashValues');
            getAllHashValuesStub.onFirstCall().resolves({});

            const event = {
                body: {
                    action: 'getMessage',
                    actionType: 'GET_GROUP_LIST_AND_MEMBERS',
                    userId: 'userId1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);

            getAllHashValuesStub.restore();
        });

        it('should handle success in GET_GROUP_LIST_AND_MEMBERS websocket route with personal conversation', async () => {
            const getAllHashValuesStub = sinon.stub(RedisUtil, 'getAllHashValues');
            getAllHashValuesStub.onFirstCall().resolves({
                groupId1: JSON.stringify({
                    groupId: 'groupId1',
                    id: 'groupMemberId1',
                    status: 'active',
                    isPersonalConversation: false
                }),
                groupId2: JSON.stringify({
                    groupId: 'groupId2',
                    id: 'groupMemberId2',
                    status: 'active',
                    isPersonalConversation: false
                }),
                groupId3: JSON.stringify({
                    groupId: 'groupId3',
                    id: 'groupMemberId3',
                    status: 'deleted',
                    isPersonalConversation: false
                }),
                groupId4: JSON.stringify({
                    groupId: 'groupId4',
                    id: 'groupMemberId4',
                    status: 'active',
                    isPersonalConversation: false
                }),
                personalConversationId1: JSON.stringify({
                    conversationId: 'personalConversationId1',
                    userBId: 'userId1',
                    userAId: 'userId2',
                    isPersonalConversation: true
                })
            });

            const groupMembers = {
                groupMemberId1: JSON.stringify({
                    id: 'groupMemberId1',
                    status: 'active',
                    userId: 'userId2',
                    isPersonalConversation: false
                }),
                groupMemberId2: JSON.stringify({
                    id: 'groupMemberId2',
                    userId: 'userId1',
                    isPersonalConversation: false
                }),
                groupMemberId3: JSON.stringify({
                    id: 'groupMemberId3',
                    status: 'deleted',
                    isPersonalConversation: false
                })
            };

            getAllHashValuesStub.onSecondCall().resolves(groupMembers);
            getAllHashValuesStub.onThirdCall().resolves(groupMembers);
            getAllHashValuesStub.onCall(4).resolves(groupMembers);

            const messages = {
                messageId1: JSON.stringify({
                    id: 'messageId1',
                    message: 'message1',
                    mediaName: 'mediaName1'
                }),
                messageIds: JSON.stringify(['messageId1', 'messageId2'])
            };
            getAllHashValuesStub.onCall(5).resolves(messages);
            getAllHashValuesStub.resolves({});

            const getHashValueStub = sinon.stub(RedisUtil, 'getHashValue');
            getHashValueStub.onFirstCall().resolves(JSON.stringify({
                groupId: 'groupId1',
                status: 'active',
                groupType: 'organization',
                organizationMetaData: {
                    logo: 'logo1.png'
                }
            }));
            getHashValueStub.onSecondCall().resolves(JSON.stringify({
                groupId: 'groupId2',
                status: 'expired',
                groupType: 'event',
                eventMetaData: {
                    logo: 'logo2.png'
                }
            }));
            getHashValueStub.onThirdCall().resolves(JSON.stringify({
                groupId: 'groupId3',
                status: 'active',
                groupType: 'event',
                eventMetaData: {
                    image: 'logo2.png'
                }
            }));

            getHashValueStub.onCall(3).resolves(JSON.stringify({
                conversationId: 'personalConversationId1',
                userBId: 'userId1',
                userAId: 'userId2',
                isPersonalConversation: true
            }));

            getHashValueStub.onCall(4).resolves(
                JSON.stringify({ id: 'userId2', firstName: 'firstName2', lastName: 'lastName2', status: 'active' })
            );

            getHashValueStub.resolves('');

            const event = {
                body: {
                    action: 'getMessage',
                    actionType: 'GET_GROUP_LIST_AND_MEMBERS',
                    userId: 'userId1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);

            getAllHashValuesStub.restore();
            getHashValueStub.restore();
        });

        it('should handle error in GET_USER_CHILDREN_LIST websocket route', async () => {
            sinon.stub(GroupMembers, 'get').rejects(new Error('Error in get user children list websocket route'));

            const event = {
                body: {
                    action: 'getMessage',
                    actionType: 'GET_USER_CHILDREN_LIST',
                    groupMemberId: 'userId1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(500);

            GroupMembers.get.restore();
        });

        it('should handle if group member is not found', async () => {
            sinon.stub(GroupMembers, 'get').resolves(null);

            const event = {
                body: {
                    action: 'getMessage',
                    actionType: 'GET_USER_CHILDREN_LIST',
                    groupMemberId: 'userId1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(404);

            GroupMembers.get.restore();
        });

        it('should handle success in GET_USER_CHILDREN_LIST websocket route with no children', async () => {
            sinon.stub(GroupMembers, 'get').resolves({
                id: 'id1',
                userId: 'userId1',
                groupId: 'groupId1',
                isAdmin: false,
                status: 'active'
            });

            const event = {
                body: {
                    action: 'getMessage',
                    actionType: 'GET_USER_CHILDREN_LIST',
                    groupMemberId: 'userId1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);

            GroupMembers.get.restore();
        });

        it('should handle success in GET_USER_CHILDREN_LIST websocket route with children', async () => {
            sinon.stub(GroupMembers, 'get').resolves({
                id: 'id1',
                userId: 'userId1',
                groupId: 'groupId1',
                isAdmin: false,
                status: 'active',
                childrenIds: ['childId1', 'childId2', 'childId3']
            });

            sinon.stub(ChildModel, 'batchGet').resolves([
                { id: 'childId1', name: 'childName1', school: 'school1' },
                { id: 'childId2', name: 'childName2', school: 'school2', homeRoom: 'homeRoom2', photoURL: 'photoURL2' },
                { id: 'childId3', name: 'childName3', school: 'school3', photoURL: 'photoURL3' }
            ]);

            sinon.stub(OrganizationModel, 'batchGet').resolves([
                { id: 'organizationId1', name: 'organizationName1' },
                { id: 'organizationId2', name: 'organizationName2' },
                { id: 'organizationId3', name: 'organizationName3' }
            ]);

            const event = {
                body: {
                    action: 'getMessage',
                    actionType: 'GET_USER_CHILDREN_LIST',
                    groupMemberId: 'userId1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);

            GroupMembers.get.restore();
        });

        it('should handle error in GET_GROUP_MEMBERS_AND_MESSAGES websocket route', async () => {
            sinon.stub(GroupMembers, 'query').rejects(new Error('Error in get group members and messages websocket route'));

            const event = {
                body: {
                    action: 'getMessage',
                    actionType: 'GET_GROUP_MEMBERS_AND_MESSAGES',
                    groupId: 'groupId1',
                    userId: 'userId1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(500);

            GroupMembers.query.restore();
        });

        it('should throw error if user is not a member of the group', async () => {
            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    in: sinon.stub().returns({
                                        exec: sinon.stub().resolves([])
                                    })
                                })
                            })
                        })
                    })
                })
            });

            const event = {
                body: {
                    action: 'getMessage',
                    actionType: 'GET_GROUP_MEMBERS_AND_MESSAGES',
                    groupId: 'groupId1',
                    userId: 'userId1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(403);

            GroupMembers.query.restore();
        });

        it('should handle success in GET_GROUP_MEMBERS_AND_MESSAGES websocket route', async () => {
            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    in: sinon.stub().returns({
                                        exec: sinon.stub().resolves([{ id: 'groupId1', name: 'groupName1' }])
                                    })
                                })
                            })
                        }),
                        attributes: sinon.stub().returns({
                            exec: sinon.stub().resolves([
                                { id: 'groupId1', name: 'groupName1', userId: 'userId1' },
                                { name: 'groupName1', userId: 'userId2' }
                            ])
                        })
                    })
                })
            });

            sinon.stub(UserModel, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub()
                            .onFirstCall().resolves([{ id: 'userId1', firstName: 'firstName1', lastName: 'lastName1', status: 'active' }])
                            .onSecondCall().resolves([{ id: 'userId2', firstName: 'firstName2', lastName: 'lastName2', status: 'active' }])
                    })
                })
            });

            sinon.stub(MessageModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ id: 'messageId1', message: 'message1' }])
                            })
                        })
                    })
                })
            });

            const event = {
                body: {
                    action: 'getMessage',
                    actionType: 'GET_GROUP_MEMBERS_AND_MESSAGES',
                    groupId: 'groupId1',
                    userId: 'userId1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);

            GroupMembers.query.restore();
            UserModel.query.restore();
            MessageModel.query.restore();
        });

        it('should throw 400 error if the actionType is not valid', async () => {
            const event = {
                body: {
                    action: 'getMessage',
                    actionType: 'INVALID_ACTION_TYPE',
                    groupId: 'groupId1',
                    userId: 'userId1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(400);
        });

        it('should throw 400 error if the event is not valid', async () => {
            const event = {
                body: {
                    action: 'invalidAction',
                    actionType: 'INVALID_ACTION_TYPE',
                    groupId: 'groupId1',
                    userId: 'userId1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(400);
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

