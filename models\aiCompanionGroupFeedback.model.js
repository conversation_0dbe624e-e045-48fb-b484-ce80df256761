const dynamoose = require('dynamoose');
const CONSTANTS = require('../util/constants');

const aiCompanionGroupFeedbackSchema = new dynamoose.Schema({
    aiResponseMessageId: {
        type: String,
        hashKey: true
    },
    userId: {
        type: String,
        rangeKey: true,
        index: {
            name: 'userId-index',
            global: true,
            project: true
        }
    },
    userQueryMessageId: {
        type: String,
        required: true
    },
    groupId: {
        type: String,
        required: true,
        index: {
            name: 'groupId-index',
            global: true,
            project: true
        }
    },
    feedback: {
        type: String,
        required: true,
        enum: CONSTANTS.AI_COMPANION_FEEDBACK_VALUES
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('AiCompanionGroupFeedbacks', aiCompanionGroupFeedbackSchema);
