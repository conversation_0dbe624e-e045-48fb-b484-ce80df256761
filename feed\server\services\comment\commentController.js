
const CommentService = require('./commentService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for comment routes.
 */
class CommentController {
    /**
     * @desc This function is being used to add comment
     * <AUTHOR>
     * @since 27/11/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async addComment (req, res) {
        try {
            const data = await CommentService.addComment(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.COMMENT_ADDED_SUCCESSFULLY);
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to list comment
     * <AUTHOR>
     * @since 29/11/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async getCommentList (req, res) {
        try {
            const data = await CommentService.getCommentList(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to delete comment
     * <AUTHOR>
     * @since 29/11/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async deleteComment (req, res) {
        try {
            const data = await CommentService.deleteComment(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.COMMENT_DELETED_SUCCESSFULLY);
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = CommentController;
