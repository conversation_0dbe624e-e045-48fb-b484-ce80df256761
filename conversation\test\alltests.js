const dotenv = require('dotenv');
const env = process.env.NODE_ENV || 'testing';
dotenv.config({ path: process.env.PWD + '/' + env + '.env' });
global.logger = require('../server/util/logger');
const chai = require('chai');
const chaiHttp = require('chai-http');
const app = require('../index');
chai.use(chaiHttp);
const request = require('supertest');
request(app);

// Conversation testing
require('../server/services/conversation/groups/test/conversation.test');
require('../server/services/webSocketHandlers/handlers/test/getUserConnectionListHandler.test');
require('../server/services/conversation/messages/test/message.test');
require('../server/services/webSocketHandlers/test/handleSendPersonalMessage.unitTest');
require('../server/services/webSocketHandlers/test/handleGetPersonalMessage.unitTest');
require('../server/services/webSocketHandlers/test/handleSendGroupMessage.test');
require('../server/services/webSocketHandlers/test/websocketHandler.test');
require('../server/services/webSocketHandlers/test/sendSocketMessageService.test');
require('../server/services/webSocketHandlers/test/sqsPushNotificationService.test');
require('../server/services/webSocketHandlers/handlers/test/messageReactionsHandler.test');
require('../server/services/webSocketHandlers/test/handleCompanionMessage.test');
require('../server/services/aiCompanionGroup/test/aiCompanionGroup.test');
require('../server/services/aiCompanionScreen/test/aiCompanionScreen.test');
require('../server/util/test/cloudwatchMetrics.test');

describe('Stop server in end', () => {
    it('Server should stop manually to get code coverage', done => {
        app.close();
        done();
    });
});
