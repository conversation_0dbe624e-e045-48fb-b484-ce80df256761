const FundraiserService = require('./fundraiserService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for fundraiser routes.
 */
class FundraiserController {
    /**
   * @desc This function is being used to add fundraiser
   * <AUTHOR>
   * @since 08/11/2023
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async addFundraiser (req, res) {
        try {
            CONSOLE_LOGGER.info('in here add fund');
            const data = await FundraiserService.addFundraiser(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.ADD_FUNDRAISER_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in add fundraiser', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }


    /**
   * @desc This function is being used to update fundraiser
   * <AUTHOR>
   * @since 16/11/2023
   * @param {Object} req Request
   * @param {Object} res Response
   */
    static async updateFundraiser (req, res) {
        try {
            const data = await FundraiserService.updateFundraiser(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.FUNDRAISER_UPDATE_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in update fundraiser', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }


    /* @desc This function is being used to add fundraiser
    * <AUTHOR>
    * @since 08/11/2023
    * @param {Object} req Request
    * @param {function} res Response
    */
    static async addProduct (req, res) {
        try {
            const data = await FundraiserService.addProduct(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.ADD_FUNDRAISER_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in add fundraiser product', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }


    /**
    * @desc This function is being used to update fundraiser
    * <AUTHOR>
    * @since 16/11/2023
    * @param {Object} req Request
    * @param {Object} res Response
    */
    static async updateProduct (req, res) {
        try {
            const data = await FundraiserService.updateProduct(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.FUNDRAISER_UPDATE_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in update fundraiser product', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to get list of fundraisers
   * <AUTHOR>
   * @since 15/11/2023
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async getFundraiserList (req, res) {
        try {
            const data = await FundraiserService.getFundraiserList(req, res.locals.user);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get fundraiser list', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to get list of fundraisers of participant
   * <AUTHOR>
   * @since 20/11/2023
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async getParticipantFundraiserList (req, res) {
        CONSOLE_LOGGER.info('in getParticipantFundraiserList');
        try {
            const data = await FundraiserService.getParticipantFundraiserList(
                req,
                res.locals.user,
                res.__
            );
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in getting participant list', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to get fundraiser details
   * <AUTHOR>
   * @since 15/11/2023
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async getFundraiserDetails (req, res) {
        try {
            const data = await FundraiserService.getFundraiserDetails(
                req,
                res.locals.user,
                res.__
            );
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get fundraiser details', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to update status of fundraiser to published
   * <AUTHOR>
   * @since 15/11/2023
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async publishFundraiser (req, res) {
        try {
            const data = await FundraiserService.publishFundraiser(
                req,
                res.locals.user,
                res.__
            );
            Utils.sendResponse(null, data, res, MESSAGES.FUNDRAISER_PUBLISH_SUCCESS);
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to delete fundraiser
   * <AUTHOR>
   * @since 15/11/2023
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async deleteFundraiser (req, res) {
        try {
            CONSOLE_LOGGER.error('in delete');
            const data = await FundraiserService.deleteFundraiser(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.FUNDRAISER_DELETE_SUCCESS);
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to delete fundraiser
   * <AUTHOR>
   * @since 22/11/2023
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async updatePaymentStatus (req, res) {
        try {
            const data = await FundraiserService.updatePaymentStatus(
                req,
                res.locals.user,
                res.__
            );
            Utils.sendResponse(
                null,
                data,
                res,
                MESSAGES.PAYMENT_STATUS_UPDATE_SUCCESS
            );
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }


    /**
     * @desc This function is being used to get searched list of fundraisers
     * <AUTHOR>
     * @since 20/02/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async getSearchFundraiserList (req, res) {
        try {
            const data = await FundraiserService.getSearchFundraiserList(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get fundraiser list', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to update fulfilled status of fundraiser
     * <AUTHOR>
     * @since 17/10/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async updateFulfilledStatus (req, res) {
        try {
            const data = await FundraiserService.updateFundraiserFulfilledStatus(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in update fulfilled status', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to generate presigned url
     * <AUTHOR>
     * @since 18/12/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async generatePresignedUrl (req, res) {
        try {
            const data = await FundraiserService.generatePresignedUrl(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in generate presigned url', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = FundraiserController;
