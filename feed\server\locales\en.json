{"SUCCESS": "Success", "REGISTER_SUCCESS": "User registration successful", "EVENT_REGISTER_SUCCESS": "You have successfully registered for the event!", "ALREADY_REGISTER": "This user is already registered with us.", "INACTIVE_USER": "Please activate your user by verify email that has sent earlier", "INVALID_OTP": "The otp that has entered is incorrect", "USER_VERIFY_SUCCESS": "Email is verified successfully", "USER_NOT_FOUND": "Invalid user request", "INVALID_REQUEST": "Request is invalid", "RESEND_OTP_SUCCESS": "Email resend successful", "LOGIN_SUCCESS": "User successfully logged in", "LOGIN_FAILED": "Invalid user credentials.", "FIELD_REQUIRED": "%s can't be blank", "FIELD_NOT_VALID": "%s is not valid.", "PORTAL_EXISTS": "Jira portal is already exists", "ERROR_MSG": "Something went wrong. please try again.", "ACCESS_DENIED": "You are not authorized to access this resource.", "DEACTIVATE_ACCOUNT_BY_ADMIN": "You account has been deactivate.", "PHOTO_DELETE_SUCCESS": "Your profile picture has been deleted successfully.", "INVALID_JIRA_CREDENTIALS": "The entered email and token are not correct. Please verify it.", "SELECT_EMPLOYEE": "Select the employee first", "INVALID_PORTAL_ID": "Jira portal id is invalid.", "PASSWORD_NOT_MATCH": "The passwords do not match", "CHANGE_PASSWORD_SUCCESS": "Password changed successfully", "FILE_NOT_FOUND": "File not found", "TEMPLATE_NAME_REQUIRED": "Template name is required", "TEMPLATE_SUBJECT_REQUIRED": "Template subject is required", "FORGOT_PASSWORD_LINK_SENT_SUCCESS": "An email has been sent. Please follow instructions on it.", "LINK_IS_VALID": "<PERSON> validated successfully.", "RESET_PASSWORD_SUCCESS": "Your password has been reset successfully.", "SIGNIN_SUCCESS": "User successfully logged in.", "ADD_EVENT_SUCCESS": "Event added successfully.", "MAIL_SENT_SUCCESS_PTO_ONBOARD": "You are not onboarded on stripe so an email has been sent successfully for the stripe onboarding.", "PTO_REONBOARD": "Your stripe account is inactive. Please contact admin to get reonboard.", "INVALID_FILE_FORMAT": "Invalid file format", "EVENT_PUBLISH_SUCCESS": "Event status changed to published.", "EVENT_NOT_FOUND": "Event doesn't exists.", "EVENT_ALREADY_PUBLISHED": "Event is already published.", "EVENT_CANT_BE_DELETED": "Cannot delete published events.", "EVENT_DELETE_SUCCESS": "Event deleted successfully.", "NO_CHILD_FOUND": "No Child found with given id", "NO_CHILD_ADDED": "Add Child first to generate feeds", "COMMENT_ADDED_SUCCESSFULLY": "Comment added successfully", "COMMENT_NOT_FOUND": "Comment not found", "COMMENT_NOT_AUTHORIZED": "You are not authorized to delete this comment", "COMMENT_DELETED_SUCCESSFULLY": "Comment deleted successfully", "NO_EVENT_FOUND": "No event found with given id", "INVALID_DATE": "Invalid date time is passed", "SEARCH_VALUE_REQUIRED": "Please enter text to search for feeds", "INVALID_LIMIT": "Invalid limit is passed", "INVALID_PAGE": "Invalid page is passed"}