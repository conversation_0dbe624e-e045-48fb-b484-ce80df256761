const validation = require('../../util/validation');
const GeneralError = require('../../util/GeneralError');

/**
 * Class represents validations for parent.
 */
class EventValidator extends validation {
    constructor (body, locale, files = [], query) {
        super(locale);
        this.body = body;
        this.files = files;
        this.query = query;
    }

    /**
     * @desc This function is being used to validate request for add event
     * <AUTHOR>
     * @since 08/11/2023
     */
    validate () {
        super.field(this.body.title, 'Event Title');
        super.field(this.body.startDate, 'Start Date');
        super.field(this.body.startTime, 'Start Time');
        super.field(this.body.endDate, 'End Date');
        super.field(this.body.endTime, 'End Time');
        super.field(this.body.eventDescription, 'Event Description');
        super.field(this.body.isRecurring, 'Event Category');
        super.field(this.body.isPaid, 'Is paid');
        if (this.body.eventType && this.body.eventType.trim() && !CONSTANTS.EVENT_TYPE.includes(this.body.eventType.trim())) {
            throw new GeneralError(MESSAGES.INVALID_EVENT_TYPE, 400);
        }
        if (this.body.isPaid.trim() === 'true') {
            super.field(this.body.fee.trim(), 'Event Fee');
            if (isNaN(Number(this.body.fee.trim())) || Number(this.body.fee.trim()) <= 0) {
                throw new GeneralError(MESSAGES.EVENT_FEE_INVALID, 400);
            }
        }
        if (this.body.isMembershipEnabled?.trim() === 'true') {
            super.field(this.body.membershipBenefitAmount.trim(), 'Membership benefit percentage');
            if (isNaN(Number(this.body.membershipBenefitAmount.trim())) || Number(this.body.membershipBenefitAmount.trim()) <= 0) {
                throw new GeneralError(MESSAGES.MEMBERSHIP_BENEFIT_INVALID, 400);
            }
        }
        super.field(this.body.volunteerRequired, 'Volunteer required');
        if (this.body.volunteerRequired.trim() === 'true') {
            super.field(this.body.volunteerSignupURL, 'Volunteer signup URL');
        }
        if (this.body.isRecurring.trim() === 'true') {
            super.field(this.body.recurringFrequency, 'Recurring Frequency');
        }
        if (this.body.eventStatus && !CONSTANTS.ALLOWED_EVENT_STATUS.includes(this.body.eventStatus?.trim())) {
            throw new GeneralError(MESSAGES.INVALID_EVENT_STATUS, 400);
        }
        if (this.body.participantsLimit &&
            (isNaN(Number(this.body.participantsLimit?.trim())) || Number(this.body.participantsLimit?.trim()) <= 0)) {
            throw new GeneralError(MESSAGES.PARTICIPANT_LIMIT_INVALID, 400);
        }
        if (this.body.quantityType || this.body.quantityInstruction) {
            super.field(this.body.quantityType, 'Quantity Type');
            super.field(this.body.quantityInstruction, 'Quantity Instruction');
            this.validateQuantityInstruction(this.body.quantityInstruction);
            super.enum(this.body.quantityType, CONSTANTS.QUANTITY_TYPE, 'Quantity Type');
        }
        if (this.files.length > 0) {
            this.fileType(this.files);
        }
    }
    validateMultiEvents () {
        super.field(this.body.title, 'Event Title');
        super.field(this.body.startDate, 'Start Date');
        super.field(this.body.startTime, 'Start Time');
        super.field(this.body.endDate, 'End Date');
        super.field(this.body.endTime, 'End Time');
        super.field(this.body.eventDescription, 'Event Description');
    }



    /**
     * @desc This function is being used to validate request for add event
     * <AUTHOR>
     * @since 27/11/2023
     */
    validateAddEvent () {
        this.validate();
        super.field(this.body.venue, 'Event Venue');
        super.field(this.body.organizationId, 'Organization Id');
    }
    validateAddMultiEvent () {
        this.validateMultiEvents();
        super.field(this.body.organizationId, 'Organization Id');
    }

    /**
     * @desc This function is being used to validate request for update event
     * <AUTHOR>
     * @since 17/11/2023
     */
    validateUpdateEvent () {
        super.param(this.body.eventId, 'Event Id');
        super.uuid(this.body.eventId, 'Event Id');
        this.validate();
    }

    /**
     * @desc This function is being used to validate event image fileType
     * <AUTHOR>
     * @since 01/03/2021
     * @param {String} mimeType mimeType
     */
    fileType (files) {
        for (const file of files) {
            if (file.fieldname === 'image') {
                if (!file.mimetype || CONSTANTS.EVENT_IMAGE.ALLOWED_TYPE.indexOf(file.mimetype) === -1) {
                    throw {
                        message: MESSAGES.INVALID_FILE_FORMAT,
                        statusCode: 400
                    };
                }
            } else if (file.fieldname === 'documents' &&
                (!file.mimetype || CONSTANTS.USER_DOCUMENT_FILE.ALLOWED_TYPE.indexOf(file.mimetype) === -1)) {
                throw {
                    message: MESSAGES.INVALID_FILE_FORMAT,
                    statusCode: 400
                };
            } else {
                // Do nothing
            }
        }
    }

    /**
     * @desc This function is being used to validate if event id is sent in params
     * <AUTHOR>
     * @since 10/11/2023
     */
    validateEventId () {
        super.uuid(this.query.eventId, 'Event Id');
    }

    /**
     * @desc This function is being used to validate length of quantity instruction field
     * <AUTHOR>
     * @since 31/01/2024
     */
    validateQuantityInstruction (quantityInstruction) {
        if (quantityInstruction.length < 4 || quantityInstruction.length > 255) {
            throw new GeneralError(MESSAGES.QUANTITY_INSTRUCTION_LENGTH, 400);
        }
    }

    /**
     * @desc This function is being used to validate request for start chunk upload
     * <AUTHOR>
     * @since 06/12/2024
     */
    validateStartChunkUpload () {
        super.field(this.body.organizationId, 'Organization Id');
        super.uuid(this.body.organizationId, 'Organization Id');
        super.field(this.body.fileSize, 'File Size');
        super.field(this.body.originalname, 'File Name');
    }

    /**
     * @desc This function is being used to validate request for upload chunk
     * <AUTHOR>
     * @since 06/12/2024
     */
    validateUploadChunk () {
        super.field(this.body.fileName, 'File Name');
        super.field(this.body.uploadId, 'Upload Id');
        super.field(this.body.partNumber, 'Part Number');
    }

    /**
     * @desc This function is being used to validate request for complete chunk upload
     * <AUTHOR>
     * @since 06/12/2024
     */
    validateCompleteChunkUpload () {
        super.field(this.body.fileName, 'File Name');
        super.field(this.body.uploadId, 'Upload Id');
        super.field(this.body.parts, 'Parts');
    }

    /**
     * @desc This function is being used to validate request for abort chunk upload
     * <AUTHOR>
     * @since 06/12/2024
     */
    validateAbortChunkUpload () {
        super.field(this.body.fileName, 'File Name');
        super.field(this.body.uploadId, 'Upload Id');
    }

    /**
     * @desc This function is being used to validate if file is present in request
     * <AUTHOR>
     * @since 06/12/2024
     */
    validateFilePresence (file) {
        if (!file) {
            throw new GeneralError(MESSAGES.FILE_REQUIRED, 400);
        }
    }

    /**
     * @desc This function is being used to validate request for generate presigned url
     * <AUTHOR>
     * @since 10/12/2024
     */
    validateGeneratePresignedUrl () {
        super.field(this.query.fileName, 'File Name');
        super.field(this.query.organizationId, 'Organization Id');
    }
}


module.exports = EventValidator;
