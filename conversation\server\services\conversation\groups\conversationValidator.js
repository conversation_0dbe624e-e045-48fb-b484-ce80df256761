const validation = require('../../../util/validation');

/**
 * Class represents validations for child.
 */
class ConversationValidator extends validation {
    constructor (body, locale, query) {
        super(locale);
        this.body = body;
        this.query = query;
        this.locale = locale;
    }

    /**
     * @desc This function is being used to validate create conversation
     * <AUTHOR>
     * @since 14/10/2024
     */
    validateGetConversationMembers () {
        const { groupId } = this.query;
        super.field(groupId, 'Group Id');
        super.uuid(groupId, 'Group Id');
    }

    /**
     * @desc This function is being used to validate generate presigned url
     * <AUTHOR>
     * @since 20/12/2024
     */
    validateGeneratePresignedUrl () {
        const { isGroupMessage, conversationId, messageId, isThumbnail, organizationId, groupId } = this.query;
        super.field(messageId, 'Message Id');
        super.field(isThumbnail, 'Is Thumbnail');
        super.field(isGroupMessage, 'Is Group Message');
        if (isGroupMessage !== 'true') {
            super.field(conversationId, 'Conversation Id');
        } else {
            super.field(organizationId, 'Organization Id');
            super.uuid(organizationId, 'Organization Id');
            super.field(groupId, 'Group Id');
            super.uuid(groupId, 'Group Id');
        }
    }
}

module.exports = ConversationValidator;
