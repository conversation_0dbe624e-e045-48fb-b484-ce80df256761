module.exports = {
    generatePresignedUrl: [
        {
            it: 'As a user I should validate if messageId is not passed',
            options: {},
            status: 0
        },
        {
            it: 'As a user I should validate if isThumbnail is not passed',
            options: {
                messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if conversationId is not passed',
            options: {
                messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                isThumbnail: 'true',
                isGroupMessage: 'false'
            },
            status: 0
        }
    ]
};
