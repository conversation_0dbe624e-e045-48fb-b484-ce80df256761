const Event = require('../server/models/event.model');
const CONSOLE_LOGGER = require('../server/util/logger');

exports.up = async function () {
    const event =
        { id: '0fd6a871-a6f5-4690-b06a-d786f1361eef', title: 'Test event' };
    try {
        await Event.create(event);
        CONSOLE_LOGGER.debug('All organizations created successfully');
    } catch (error) {
        CONSOLE_LOGGER.error('Error creating organizations:', error);
    }
};

exports.down = async function () {
    try {
        const id =
            '0fd6a871-a6f5-4690-b06a-d786f1361eef';
        await Event.delete(id);
        CONSOLE_LOGGER.debug('Organizations deleted successfully');
    } catch (error) {
        CONSOLE_LOGGER.error('Error deleting users:', error);
    }
};

