{"name": "vaalee-conversation", "version": "0.1.0", "description": "Vaalee Conversation", "main": "index.js", "scripts": {"prestart": "./prestart.sh", "start": "NODE_ENV=local nodemon .", "start:dev": "NODE_ENV=development node index.js", "test": "NODE_ENV=testing nyc mocha test/alltests.js --exit", "jsdoc": "./node_modules/.bin/jsdoc server/* -r  --destination jsdocs/jsdocs", "commit": "git-cz"}, "author": "Growexx", "license": "ISC", "dependencies": {"@aws-sdk/s3-request-presigner": "^3.420.0", "@incubyte/ai": "^0.0.6", "body-parser": "^1.17.2", "compression": "^1.7.4", "cookie-parser": "^1.4.3", "cors": "^2.8.3", "dotenv": "^14.2.0", "dynamoose": "^3.2.1", "express": "^4.15.3", "helmet": "^3.21.3", "i18n": "^0.8.3", "ioredis": "^5.3.2", "jsonwebtoken": "^8.2.1", "lodash": "^4.17.21", "method-override": "^2.3.9", "moment": "^2.19.2", "morgan": "^1.9.1", "multer": "^1.4.2", "serverless-http": "^3.2.0", "sharp": "^0.33.5", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1"}, "devDependencies": {"@aws-sdk/client-apigatewaymanagementapi": "^3.675.0", "@aws-sdk/client-cloudwatch": "^3.830.0", "@aws-sdk/client-dynamodb": "^3.427.0", "@aws-sdk/client-s3": "^3.420.0", "@aws-sdk/client-sqs": "^3.460.0", "chai": "^4.3.8", "chai-http": "^4.4.0", "jest": "^29.7.0", "jest-aws-sdk-mock": "^1.0.2", "jsdoc": "^4.0.2", "mocha": "^10.2.0", "nyc": "^15.1.0", "sinon": "^15.2.0", "supertest": "^6.3.3", "ws": "^8.18.0"}, "nyc": {"lines": 5, "statements": 5, "functions": 5, "branches": 5, "check-coverage": true, "exclude": ["node_modules", "**/test/**", "coverage", "migrations", "jsdocs", ".eslintrc.js", ".scannerwork", "server/connection.js", "server/logger.js", "index.js", "server/middleware/serverless.js", "server/util/http-status.js", "server/util/logger.js", "server/util/uploadService.js"], "reporter": ["lcov", "html"], "cache": true, "all": true}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "git+https://bitbucket.org/Growexx-master/Growexx-api.git"}, "release": {"repositoryUrl": "https://bitbucket.org/Growexx-master/Growexx-api.git", "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git"], "publish": [{"path": "@semantic-release/npm", "npmPublish": false, "tarballDir": "dist"}]}, "homepage": "https://bitbucket.org/ZsigDevelopment/semver-demo.git#readme"}