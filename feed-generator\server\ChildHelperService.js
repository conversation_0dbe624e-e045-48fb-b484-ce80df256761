const MOMENT = require('moment');
const CONSTANTS = require('./constants');
const RedisUtil = require('./redisUtil');
const CONSOLE_LOGGER = require('./logger');
const Utils = require('./Utils');
const OpensearchHelperService = require('./OpensearchHelperService');
const DBModelHelperService = require('./DBModelHelperService');
const User = require('./models/user.model');
const NotificationHelperService = require('./NotificationHelperService');

class ChildHelperService {
    /**
     * @description Adds events to the feed
     * <AUTHOR>
     * @param {Object} redis - The Redis client
     * @param {Array} events - The events
     * @param {Object} child - The child
     * @param {String} versionPrefix - The version prefix
     * @param {Boolean} isUpdate - Whether the events are being updated
     * @returns {Promise<Array>} The event ids
     */
    static async addEventsToFeed ({ redis, events, child, versionPrefix, isUpdate = false }) {
        const eventIds = [];
        const { id: childId, guardians } = child;

        const childKey = Utils.getChildKey({ versionPrefix, childId });
        const childCalendarKey = Utils.getCalendarChildKey({ versionPrefix, childId });
        const childRegisteredKey = Utils.getRegisteredChildKey({ versionPrefix, childId });

        await this.updateChildDetailsInFeed(redis, child, versionPrefix);

        const childRegisteredEvents = await RedisUtil.getElementsOfSortedSetByScore(
            redis,
            childRegisteredKey,
            undefined,
            CONSTANTS.PLUS_INF
        );

        for (const event of events) {
            const { id: eventId, details, eventType } = event;

            const value = JSON.stringify({
                eventId,
                childId
            });

            const childRegisteredEvent = childRegisteredEvents?.find((childEvent) => {
                const feed = JSON.parse(childEvent);
                return eventId === feed.eventId;
            });

            CONSOLE_LOGGER.info(
                'Child registered event',
                childRegisteredEvent,
                eventId,
                childId,
                childRegisteredKey
            );

            const score = MOMENT(details.startDateTime).toDate().getTime();
            if (
                !childRegisteredEvent
                && MOMENT(details.endDateTime).isAfter(MOMENT().utc())
                && eventType === CONSTANTS.EVENT_TYPE.EVENT
            ) {
                eventIds.push(eventId);

                await Utils.upsertChildAndUserEventsToSortedSet({
                    guardians,
                    redis,
                    score,
                    value,
                    keyForChild: childKey,
                    keyForGuardian: Utils.getUserKey({ versionPrefix, shouldGenerateKeyPrefix: true })
                });
            }
            if (event.eventType === CONSTANTS.EVENT_TYPE.CALENDAR) {
                await Utils.upsertChildAndUserEventsToSortedSet({
                    guardians,
                    redis,
                    score,
                    value,
                    keyForChild: childCalendarKey,
                    keyForGuardian: Utils.getCalendarUserKey({ versionPrefix, shouldGenerateKeyPrefix: true })
                });
            }
        }
        return await OpensearchHelperService.addEventsInOpensearch(
            eventIds,
            childId,
            child,
            isUpdate
        );
    }

    /**
     * @description Adds posts to the feed
     * <AUTHOR>
     * @param {Object} redis - The Redis client
     * @param {Array} posts - The posts
     * @param {Object} child - The child
     * @param {String} versionPrefix - The version prefix
     * @param {Boolean} isUpdate - Whether the posts are being updated
     * @returns {Promise<Array>} The post ids
     */
    static async addPostsToFeed ({ redis, posts, child, versionPrefix, isUpdate = false }) {
        const { id: childId, guardians } = child;

        const childKey = Utils.getChildKey({ versionPrefix, childId });

        await this.updateChildDetailsInFeed(redis, child, versionPrefix);

        const postIds = [];
        CONSOLE_LOGGER.info('addPostsToFeed Posts', posts);
        const expirationDays = await DBModelHelperService.getPostExpirationDays();

        for (const post of posts) {
            const { id: postId, publishedDate } = post;

            const score = MOMENT(publishedDate).toDate().getTime();

            const value = JSON.stringify({
                eventId: postId,
                isPost: true,
                childId,
                postId
            });


            if (
                MOMENT(publishedDate)
                    .add(expirationDays, 'days')
                    .isAfter(MOMENT().utc())
            ) {
                postIds.push(postId);
                await Utils.upsertChildAndUserEventsToSortedSet({
                    guardians,
                    redis,
                    score,
                    value,
                    keyForChild: childKey,
                    keyForGuardian: Utils.getUserKey({ versionPrefix, shouldGenerateKeyPrefix: true })
                });
            }
        }
        CONSOLE_LOGGER.info('addPostsToFeed PostIds', postIds);
        return await OpensearchHelperService.addPostsInOpensearch(postIds, childId, child, isUpdate);
    }

    /**
     * @description Adds fundraisers to the feed
     * <AUTHOR>
     * @param {Object} redis - The Redis client
     * @param {Array} fundraisers - The fundraisers
     * @param {Object} child - The child
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<Array>} The fundraiser ids
    */
    static async addFundraisersToFeed (redis, fundraisers, child, versionPrefix) {
        const { id: childId, guardians } = child;

        const childKey = Utils.getChildKey({ versionPrefix, childId });
        const childRegisteredKey = Utils.getRegisteredChildKey({ versionPrefix, childId });

        await this.updateChildDetailsInFeed(redis, child, versionPrefix);

        const childRegisteredEvents = await RedisUtil.getElementsOfSortedSetByScore(
            redis,
            childRegisteredKey,
            undefined,
            CONSTANTS.PLUS_INF
        );

        const fundraiserIds = [];
        for (const fundraiser of fundraisers) {
            const { startDate, endDate, id: fundraiserId, fundraiserType } = fundraiser;
            const value = JSON.stringify({
                fundraiserId,
                childId,
                isFundraiser: true,
                eventId: fundraiserId
            });

            const childRegisteredEvent = childRegisteredEvents?.find((childEvent) => {
                const feed = JSON.parse(childEvent);
                return fundraiserId === feed.fundraiserId;
            });

            const score = MOMENT(startDate).toDate().getTime();

            if (
                MOMENT(endDate).isAfter(MOMENT().utc()) &&
                (
                    (fundraiserType === CONSTANTS.FUNDRAISER_TYPES.BOOSTER && !childRegisteredEvent) ||
                    fundraiserType !== CONSTANTS.FUNDRAISER_TYPES.BOOSTER
                )
            ) {

                fundraiserIds.push(fundraiserId);

                await Utils.upsertChildAndUserEventsToSortedSet({
                    guardians,
                    redis,
                    score,
                    value,
                    keyForChild: childKey,
                    keyForGuardian: Utils.getUserKey({ versionPrefix, shouldGenerateKeyPrefix: true })
                });
            }
        }
        return await OpensearchHelperService.addFundRaisersInOpensearch(fundraiserIds, child);
    }

    /**
     * @description Updates the user is feeds generated
     * <AUTHOR>
     * @param {String} userId - The user id
     * @returns {Promise<Array>} The user
     */
    static async updateUserIsFeedGenerated (userId) {
        const user = await User.query(
            { id: userId },
            { attributes: ['id', 'isFeedsGenerated'] }
        ).exec();
        if (user?.length > 0 && user?.[0]?.isFeedsGenerated === false) {
            user[0].isFeedsGenerated = true;
            await user[0].save();
        }
        return user;
    }

    /**
     * @description Removes the events, fundraisers and posts from the feed
     * <AUTHOR>
     * @param {Object} redis - The Redis client
     * @param {Object} child - The child
     * @param {Array} organizations - The organizations
     * @param {String} versionPrefix - The version prefix
    */
    static async removeEventsFromFeed (redis, child, organizations, versionPrefix) {
        const { id: childId, guardians = [] } = child;

        const childKey = Utils.getChildKey({ versionPrefix, childId });
        const childCalendarKey = Utils.getCalendarChildKey({ versionPrefix, childId });

        const organizationsSet = new Set(organizations);

        const keys = [childKey, childCalendarKey];

        guardians.forEach((guardian) => {
            const guardianKey = Utils.getUserKey({ versionPrefix, userId: guardian });
            const guardianCalendarKey = Utils.getCalendarUserKey({ versionPrefix, userId: guardian });

            keys.push(guardianKey, guardianCalendarKey);
        });

        const events = {};
        const fundraisers = {};
        const posts = {};

        for (const key of keys) {
            const keyEvents = await RedisUtil.getElementsOfSortedSetByScore(
                redis,
                key,
                CONSTANTS.MINUS_INF,
                CONSTANTS.PLUS_INF
            );
            keyEvents.forEach((event) => {
                const parsedEvent = JSON.parse(event);
                const { eventId, isPost, isFundraiser } = parsedEvent;

                if (isPost) {
                    posts[eventId] = posts[eventId] ?? [];
                    posts[eventId].push({ key, event: parsedEvent });
                } else if (isFundraiser) {
                    fundraisers[eventId] = fundraisers[eventId] ?? [];
                    fundraisers[eventId].push({ key, event: parsedEvent });
                } else {
                    events[eventId] = events[eventId] ?? [];
                    events[eventId].push({ key, event: parsedEvent });
                }
            });
        }

        const eventIds = [
            ...new Set(Object.keys(events))
        ].filter(Boolean);

        const fundraiserIds = [
            ...new Set(Object.keys(fundraisers))
        ].filter(Boolean);

        const postIds = [
            ...new Set(Object.keys(posts))
        ].filter(Boolean);


        async function removeEventFromSortedSet (
            redis,
            events,
            eventFromDb,
            organizationsSet
        ) {
            if (organizationsSet.has(eventFromDb.organizationId)) {
                const foundEvents = events[eventFromDb.id];
                if (foundEvents?.length > 0) {
                    for (const { key, event } of foundEvents) {
                        if (event.childId === childId) {
                            const eventToRemove = JSON.stringify(event);
                            await RedisUtil.removeMemberFromSortedSet(redis, key, eventToRemove);
                        }
                    }
                }
            }
        }

        const attributesToFetch = ['id', 'organizationId'];

        if (eventIds.length > 0) {
            const eventsFromDb = await DBModelHelperService.batchGetEventsWithAttributes(
                eventIds,
                attributesToFetch
            );

            for (const eventFromDb of eventsFromDb) {
                await removeEventFromSortedSet(
                    redis,
                    events,
                    eventFromDb,
                    organizationsSet
                );
            }
        }

        if (fundraiserIds.length > 0) {
            const fundraisersFromDb = await DBModelHelperService.batchGetFundraisersWithAttributes(
                fundraiserIds,
                attributesToFetch
            );

            for (const fundraiserFromDb of fundraisersFromDb) {
                await removeEventFromSortedSet(
                    redis,
                    fundraisers,
                    fundraiserFromDb,
                    organizationsSet
                );
            }
        }

        if (postIds.length > 0) {
            const postsFromDb = await DBModelHelperService.batchGetPostsWithAttributes(
                postIds,
                attributesToFetch
            );

            for (const postFromDb of postsFromDb) {
                await removeEventFromSortedSet(
                    redis,
                    posts,
                    postFromDb,
                    organizationsSet
                );
            }
        }
        return child.id;
    }

    /**
     * @description Handles the organization changes
     * <AUTHOR>
     * @param {Object} redis - The Redis client
     * @param {Object} oldChild - The old child
     * @param {Object} newChild - The new child
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<Object>} The old and new child organizations
     */
    static async handleOrganizationChanges (redis, oldChild, newChild, versionPrefix) {
        const oldChildOrganizations = oldChild.associatedOrganizations;
        const newChildOrganizations = newChild.associatedOrganizations;

        const addedOrganizations = newChildOrganizations.filter(
            (org) => !oldChildOrganizations.includes(org)
        );
        const removedOrganizations = oldChildOrganizations.filter(
            (org) => !newChildOrganizations.includes(org)
        );

        const addedEvents = await DBModelHelperService.getEventsForOrganizations(
            addedOrganizations
        );
        await this.addEventsToFeed({ redis, versionPrefix, events: addedEvents, child: newChild, isUpdate: true });

        const addedFundraisers = await DBModelHelperService.getFundraisersForOrganizations(
            addedOrganizations
        );
        await this.addFundraisersToFeed(redis, addedFundraisers, newChild, versionPrefix);

        const addedPosts = await DBModelHelperService.getPostsForOrganizations(addedOrganizations);
        await this.addPostsToFeed({ redis, versionPrefix, posts: addedPosts, child: newChild, isUpdate: true });

        removedOrganizations.length &&
            (await this.removeEventsFromFeed(redis, oldChild, removedOrganizations, versionPrefix));

        return { oldChildOrganizations, newChildOrganizations };
    }

    /**
     * @description Handles the connection changes
     * <AUTHOR>
     * @param {Object} oldChild - The old child
     * @param {Object} newChild - The new child
     * @returns {Promise<Object>} The old and new child connections
     */
    static async handleConnectionChanges (oldChild, newChild) {
        const oldConnections = oldChild.connections;
        const newConnections = newChild.connections;
        const changedConnections = newConnections.filter((newConnection) => {
            const oldConnection = oldConnections.find(
                (conn) => conn.childId === newConnection.childId
            );
            return !oldConnection || oldConnection.status !== newConnection.status;
        });

        for (const connection of changedConnections) {
            const oldConnection = oldConnections.find(
                (conn) => conn.childId === connection.childId
            );

            if (connection.status === CONSTANTS.CONNECTION_STATUS.REQUESTED_BY) {
                await NotificationHelperService.sendNotificationInQueue({
                    topicKey: `connection-${connection.childId}`,
                    trigger: CONSTANTS.CONNECTION_STATUS.REQUESTED_BY,
                    childId: connection.childId,
                    title: CONSTANTS.CONNECTION_TITLE.CONNECTION_REQUEST,
                    message: CONSTANTS.CONNECTION_MESSAGE.CONNECTION_REQUEST
                });
            }
            if (
                connection.status === CONSTANTS.CONNECTION_STATUS.CONNECTED &&
                oldConnection &&
                oldConnection.status === CONSTANTS.CONNECTION_STATUS.REQUESTED_TO
            ) {
                await NotificationHelperService.sendNotificationInQueue({
                    topicKey: `connection-${connection.childId}`,
                    trigger: CONSTANTS.CONNECTION_STATUS.CONNECTED,
                    childId: connection.childId,
                    title: CONSTANTS.CONNECTION_TITLE.CONNECTION_ACCEPTED,
                    message: CONSTANTS.CONNECTION_MESSAGE.CONNECTION_ACCEPTED
                });
            }
        }

        return { oldConnections, newConnections };
    }

    /**
     * @description Updates the child details in the feed
     * <AUTHOR>
     * @param {Object} redis - The Redis client
     * @param {Object} child - The child
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<Object>} The child
     */
    static async updateChildDetailsInFeed (redis, child, versionPrefix) {
        const { id: childId } = child;

        const childKey = Utils.getChildDetailsKey({ versionPrefix, childId });

        await Utils.upsertChildDetailsToHashSet({
            redis,
            key: childKey,
            field: 'details',
            childDetails: child
        });

        return childId;
    }

    /**
     * @description Handles the guardians changes
     * <AUTHOR>
     * @param {Object} redis - The Redis client
     * @param {Object} oldChild - The old child
     * @param {Object} newChild - The new child
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<Object>} The added and removed guardians
     */
    static async handleGuardiansChanges (redis, oldChild, newChild, versionPrefix) {
        const childId = newChild.id;

        const oldChildGuardians = oldChild.guardians;
        const newChildGuardians = newChild.guardians;

        const addedGuardians = newChildGuardians.filter(
            (guardian) => !oldChildGuardians.includes(guardian)
        );
        const removedGuardians = oldChildGuardians.filter(
            (guardian) => !newChildGuardians.includes(guardian)
        );

        const childKey = Utils.getChildKey({ versionPrefix, childId });
        const childCalendarKey = Utils.getCalendarChildKey({ versionPrefix, childId });
        const childRegisteredKey = Utils.getRegisteredChildKey({ versionPrefix, childId });

        const keys = [childKey, childCalendarKey, childRegisteredKey];

        const getKeyForGuardian = (key) => {
            if (key === childKey) {
                return Utils.getUserKey({ versionPrefix, shouldGenerateKeyPrefix: true });
            } else if (key === childCalendarKey) {
                return Utils.getCalendarUserKey({ versionPrefix, shouldGenerateKeyPrefix: true });
            } else {
                return Utils.getRegisteredChildKey({ versionPrefix, shouldGenerateKeyPrefix: true });
            }
        };

        for (const key of keys) {
            const keyForGuardian = getKeyForGuardian(key);
            const keyEvents = await RedisUtil.getElementsOfSortedSetWithScores(
                redis,
                key,
                CONSTANTS.MINUS_INF,
                CONSTANTS.PLUS_INF
            );

            for (let i = 0; i < keyEvents.length; i += 2) {
                for (const guardian of addedGuardians) {
                    const guardianKey = `${keyForGuardian}:${guardian}`;

                    await RedisUtil.addEventReferenceToSortedSet(
                        redis,
                        guardianKey,
                        keyEvents[i + 1],
                        keyEvents[i]
                    );
                }

                for (const guardian of removedGuardians) {
                    const guardianKey = `${keyForGuardian}:${guardian}`;
                    await RedisUtil.removeMemberFromSortedSet(redis, guardianKey, keyEvents[i]);
                }
            }
        }

        return { addedGuardians, removedGuardians };
    }
}

module.exports = ChildHelperService;
