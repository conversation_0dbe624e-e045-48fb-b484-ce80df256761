const NotificationModel = require('../../models/notification.model');
const ChildModel = require('../../models/child.model');
const OrganizationModel = require('../../models/organization.model');
const EventModel = require('../../models/event.model');
const EventSignupModel = require('../../models/eventSignup.model');
const ChildOrganizationMappingModel = require('../../models/childOrganizationMapping.model');
const NotificationValidator = require('./notificationValidator');
const UploadService = require('../../util/uploadService');
const GeneralError = require('../../util/GeneralError');
const Notification = require('../../util/notification');
const EmailService = require('../../util/sendEmail');
const { google } = require('googleapis');
const GoogleSheetsService = require('../../util/googleSheets');

/**
 * Class represents services for notification.
 */
class NotificationService {
    /**
     * @desc This function is being used to get notification list
     * <AUTHOR>
     * @since 31/01/2024
     * @param {Object} req Request
     * @param {Object} user User
     * @param {Object} locale Locale
     */
    static async getNotificationList (req, user) {
        const { nextIndex, nextCreatedAt, pageSize } = req.query;
        const page = Number(pageSize) || CONSTANTS.DEFAULT_PAGE_SIZE;

        let query = NotificationModel
            .query('userId').eq(user.id)
            .using('userId-index')
            .sort('descending')
            .limit(page + 1);

        if (nextIndex && !isNaN(nextCreatedAt)) {
            query = query.startAt({ userId: user.id, id: nextIndex, createdAt: Number(nextCreatedAt) });
        }

        const data = await query.exec();

        let newNextIndex = null;
        const notifications = data;

        if (data.length > page) {
            newNextIndex = { id: data[page - 1].id, createdAt: MOMENT(data[page - 1].createdAt).valueOf() };
            notifications.pop();
        }

        const notificationList = [];

        for (const notification of notifications) {
            notificationList.push(await this.formatNotificationResponse(notification));
        }

        return {
            nextIndex: newNextIndex ? newNextIndex.id : null,
            nextCreatedAt: newNextIndex ? newNextIndex.createdAt : null,
            notifications: notificationList
        };
    }

    /**
     * @desc This function is being used to create notification
     * <AUTHOR>
     * @since 31/01/2024
     * @param {Object} req Request
     * @param {Object} user User
     * @param {Object} locale Locale
     */
    static async createNotification (req, user, locale) {
        const Validator = new NotificationValidator(req.body, locale);
        Validator.validateCreateNotification();
        const { title, description, associatedChildId, notificationAction, payload } = req.body;

        if (!user.children.includes(associatedChildId)) {
            throw new GeneralError(locale(MESSAGES.CHILD_NOT_FOUND), 404);
        }

        const notification = new NotificationModel({
            userId: user.id,
            title,
            description,
            associatedChildId,
            notificationAction,
            payload
        });

        const savedNotification = await notification.save();
        return await this.formatNotificationResponse(savedNotification);
    }

    /**
     * @desc This function is being used to update notification read status
     * <AUTHOR>
     * @since 31/01/2024
     * @param {Object} req Request
     * @param {Object} user User
     * @param {Object} locale Locale
     */
    static async updateNotification (req, user, locale) {
        const Validator = new NotificationValidator(req.body, locale, req.query);
        Validator.validateUpdateNotification();
        const { notificationId } = req.query;

        const notification = await NotificationModel.get(notificationId);

        if (!notification) {
            throw new GeneralError(locale(MESSAGES.NOTIFICATION_NOT_FOUND), 404);
        }

        if (notification.userId !== user.id) {
            throw new GeneralError(locale(MESSAGES.NOTIFICATION_NOT_FOUND), 404);
        }

        if (notification.readStatus) {
            throw new GeneralError(locale(MESSAGES.NOTIFICATION_ALREADY_READ), 200);
        }

        notification.readStatus = true;

        const updatedNotification = await notification.save();
        return await this.formatNotificationResponse(updatedNotification);
    }

    /**
     * @desc This function is being used to send notification to user or group of users
     * @param {Object} req Request
     * @param {Object} user User
     * @param {Object} locale Locale
     */
    static async sendNotification (req, user, locale) {
        const Validator = new NotificationValidator(req.body, locale);
        Validator.validateSendNotification();
        const { title, description, children, allParticipants, allOrgChildren, eventId, organizationId } = req.body;

        const orgAssociation = await this.validateOrganizationAssociation(organizationId, user);

        if (orgAssociation.role !== CONSTANTS.ORG_ROLE.SUPER_ADMIN
            && orgAssociation.role !== CONSTANTS.ORG_ROLE.ADMIN) {
            throw new GeneralError(MESSAGES.ACCESS_DENIED, 403);
        }

        const notificationEntries = [];
        const notificationPromises = [];

        if (allOrgChildren) {
            const children = await ChildOrganizationMappingModel.query('organizationId').eq(organizationId)
                .using('organizationId-index').exec();

            const childrenData = await ChildModel.batchGet(children.map(child => child.childId));
            await this.getNotificationEntriesAndPromises(childrenData, notificationEntries, notificationPromises, description, title);
        } else if (allParticipants) {
            let eventSignups = await EventSignupModel.query('eventId').eq(eventId).using('eventId-index').exec();
            eventSignups = eventSignups.filter(
                signup => signup.paymentDetails.paymentStatus !== CONSTANTS.PAYMENT_STATUS.PAYMENT_INITIATED
            );

            const childrenData = await ChildModel.batchGet(eventSignups.map(signup => signup.childId));

            await this.getNotificationEntriesAndPromises(
                childrenData, notificationEntries, notificationPromises, description, title, eventId);
        } else {
            const childrenData = await ChildModel.batchGet(children);
            await this.getNotificationEntriesAndPromises(
                childrenData, notificationEntries, notificationPromises, description, title, eventId);
        }

        await NotificationModel.batchPut(notificationEntries);
        await Promise.all(notificationPromises);
    }

    /**
     * @desc This function is being used to validate organization association
     * <AUTHOR>
     * @since 21/02/2024
     * @param {String} organizationId organization id
     */
    static async validateOrganizationAssociation (organizationId, user) {
        const orgAssociation = user.associatedOrganizations.find(org => org.organizationId === organizationId);
        if (!orgAssociation) {
            throw new GeneralError(MESSAGES.ORGANIZATION_NOT_FOUND, 404);
        }
        return orgAssociation;
    }

    /**
     * @desc This function is being used to get notification entries and promises
     * @param {Array} children Children
     * @param {Array} notificationEntries Notification entries
     * @param {Array} notificationPromises Notification promises
     * @param {String} title Title
     * @param {String} description Description
     */
    static async getNotificationEntriesAndPromises (
        children, notificationEntries, notificationPromises,
        description, title, eventId) {
        title = title || CONSTANTS.DEFAULT_NOTIFICATION_TITLE;
        let event;
        eventId && (event = await EventModel.get(eventId, { attributes: ['id', 'details'] }));
        for (const child of children) {
            let payload;
            eventId && (payload = {
                details: JSON.stringify({
                    associatedChild: { id: child.id },
                    id: event.id,
                    score: MOMENT(event.details.startDateTime).valueOf(),
                    startDateTime: MOMENT(event.details.startDateTime).valueOf(),
                    endDateTime: MOMENT(event.details.endDateTime).valueOf()
                }),
                child: JSON.stringify({
                    id: child.id,
                    firstName: child.firstName,
                    lastName: child.lastName,
                    associatedColor: child.associatedColor,
                    photoURL: child.photoURL
                }),
                route: CONSTANTS.NOTIFICATION_ROUTE.EVENT_DETAILS
            });
            for (const guardian of child.guardians) {
                const entry = {
                    userId: guardian,
                    associatedChildId: child.id,
                    title,
                    description,
                    payload
                };
                notificationEntries.push(entry);
            }
            notificationPromises.push(this.createNotificationForChild(child.id, title, description, payload));

        }
    }

    /**
     * @desc This function is being used to create notification for child
     * @param {String} childId Child Id
     * @param {String} title Title
     * @param {String} description Description
     */
    static async createNotificationForChild (childId, title, description, data) {
        const notificationObject = Notification.createNotificationObject({
            title,
            data,
            topic: `childId-${childId}`,
            body: description
        });

        await Notification.sendNotification(notificationObject);
    }

    /**
     * @desc This function is being used to format notification response object
     * @param {Object} notification Notification
     * <AUTHOR>
     * @since 01/02/2024
     */
    static async formatNotificationResponse (notification) {
        const child = await this.getChild(notification.associatedChildId);
        if (notification.payload && notification.payload.child) {
            notification.payload.child = JSON.stringify(child);
        }
        return {
            id: notification.id,
            title: notification.title,
            description: notification.description,
            userId: notification.userId,
            associatedChild: notification.payload && child,
            notificationAction: notification.notificationAction,
            payload: notification.payload,
            readStatus: notification.readStatus,
            createdAt: MOMENT(notification.createdAt).valueOf(),
            updatedAt: MOMENT(notification.updatedAt).valueOf()
        };
    }

    /**
     * @desc This function is being used to get child details by child id
     * @param {String} childId Child Id
     * @returns {Object} Child details object
     * <AUTHOR>
     * @since 01/02/2024
     */
    static async getChild (childId) {
        const child = await ChildModel.get(
            childId,
            { attributes: ['id', 'firstName', 'lastName', 'associatedColor', 'photoURL', 'school', 'homeRoom'] }
        );
        child.photoURL = child?.photoURL ? await UploadService.getSignedUrl(child.photoURL) : null;
        child.school = await this.getOrganizationName(child.school);
        child.homeRoom = child.homeRoom ? await this.getOrganizationName(child.homeRoom) : null;
        return child;
    }

    /**
     * @desc This function is being used to get organization name by organization id
     * @param {String} organizationId Organization Id
     * @returns {String} Organization name string
     * <AUTHOR>
     * @since 01/02/2024
     */
    static async getOrganizationName (organizationId) {
        const organization = await OrganizationModel.get(organizationId, { attributes: ['name'] });
        return organization.name;
    }

    /**
     * @desc This function is being used to notify
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async notify (req) {
        await Notification.sendNotification(req.body);
    }

    /**
     * @desc This function is being used to send email to admin and write data to google sheets
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async contactUs (req) {
        try {
            const { firstName,lastName, email,phone, message } = req.body;
            const template = "emailTemplates/contactUs.html";
            const templateVariables = {
                firstName: firstName,
                lastName: lastName,
                email: email,
                phone: phone,
                message: message,
                appname: CONSTANTS.APP_NAME
            }
            await EmailService.prepareAndSendEmail([CONSTANTS.CONTACT_EMAIL], CONSTANTS.CONTACT_US_SUBJECT, template, templateVariables);

            const sheetsService = new GoogleSheetsService(
              "./server/util/credentials.json",
              "1ZmeeKwjsWnM7jJFuCbet4Y6872eDn9EHyZniVl1-iPQ"
            );

            const currentTime = MOMENT().format("YYYY-MM-DD HH:mm:ss");
            await sheetsService.appendRow("Sheet1", [
              firstName,
              lastName,
              email,
              phone,
              message,
              currentTime,
            ]);

            return {
                message: "Email sent successfully!"
            }
          } catch (error) {
            CONSOLE_LOGGER.error('Error:', error);
            throw new GeneralError(MESSAGES.INTERNAL_SERVER_ERROR, 500);
          }
    }
}

module.exports = NotificationService;
