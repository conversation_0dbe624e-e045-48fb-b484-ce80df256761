const sinon = require('sinon');
const assert = require('assert');
const PushNotificationService = require('../pushNotificationService');
const PendingPushNotification = require('../../models/pendingPushNotification.model');
const axios = require('axios');
const { google } = require('googleapis');
const NotificationModel = require('../../models/notification.model');
const ChildModel = require('../../models/child.model');

describe('PushNotificationService', () => {
    before(() => {
        process.env.NOTIFICATION_PRIVATE_KEY = 'testkey';
    });

    after(() => {
        sinon.restore();
    });

    it('should send notification for 24hour trigger for single day event', async () => {
        const pendingNotification = [
            {
                id: '1',
                pushNotificationTime: new Date(),
                payload: {
                    startDateTime: new Date(),
                    endDateTime: new Date(),
                    title: 'title',
                    triggerAt: '24'
                },
                notificationAction: 'action',
                associatedChildId: 'child1',
                title: 'title'
            }
        ];
        const scanStub = sinon.stub(PendingPushNotification, 'scan').returns({
            exec: sinon.stub().resolves(pendingNotification)
        });
        const childModelGetStub = sinon.stub(ChildModel, 'get').resolves({
            id: 'child1', firstName: 'child1', lastName: 'child1', photoURL: 'photoURL', associatedColor: 'associatedColor'
        });
        const jwtClientAuthorizeStub = sinon.stub(google.auth.JWT.prototype, 'authorize').callsFake((callback) => {
            callback(null, { access_token: 'fake_token' });
        });
        const axiosPostStub = sinon.stub(axios, 'post').resolves({ data: {} });
        const pushInNotificationStub = sinon.stub(NotificationModel, 'create').resolves();
        const deleteStub = sinon.stub(PendingPushNotification, 'delete').resolves();
        await PushNotificationService.pushNotfication();
        assert(scanStub.calledOnce);
        jwtClientAuthorizeStub.restore();
        scanStub.restore();
        deleteStub.restore();
        axiosPostStub.restore();
        pushInNotificationStub.restore();
        childModelGetStub.restore();
    });

    it('should send notification for 24hour trigger for multiday event', async () => {
        const pendingNotification = [
            {
                id: '1',
                pushNotificationTime: new Date(),
                payload: {
                    startDateTime: new Date('2022-01-01T10:00:00Z'),
                    endDateTime: new Date('2022-01-02T18:00:00Z'),
                    title: 'title',
                    triggerAt: '24'
                },
                notificationAction: 'action',
                associatedChildId: 'child1',
                title: 'title'
            }
        ];
        const scanStub = sinon.stub(PendingPushNotification, 'scan').returns({
            exec: sinon.stub().resolves(pendingNotification)
        });
        const deleteStub = sinon.stub(PendingPushNotification, 'delete').resolves();
        const axiosPostStub = sinon.stub(axios, 'post').resolves({ data: {} });
        const jwtClientAuthorizeStub = sinon.stub(google.auth.JWT.prototype, 'authorize').callsFake((callback) => {
            callback(null, { access_token: 'fake_token' });
        });
        const childModelGetStub = sinon.stub(ChildModel, 'get').resolves({
            id: 'child1', firstName: 'child1', lastName: 'child1', photoURL: 'photoURL', associatedColor: 'associatedColor'
        });
        const pushInNotificationStub = sinon.stub(NotificationModel, 'create').resolves();

        await PushNotificationService.pushNotfication();

        assert(scanStub.calledOnce);
        jwtClientAuthorizeStub.restore();
        scanStub.restore();
        deleteStub.restore();
        pushInNotificationStub.restore();
        axiosPostStub.restore();
        childModelGetStub.restore();
    });

    it('should send notification for 72hour trigger for single day event', async () => {
        const pendingNotification = [
            {
                id: '1',
                pushNotificationTime: new Date(),
                payload: {
                    startDateTime: new Date(),
                    endDateTime: new Date(),
                    title: 'title',
                    triggerAt: '72'
                },
                notificationAction: 'action',
                associatedChildId: 'child1',
                title: 'title'
            }
        ];
        const scanStub = sinon.stub(PendingPushNotification, 'scan').returns({
            exec: sinon.stub().resolves(pendingNotification)
        });
        const childModelGetStub = sinon.stub(ChildModel, 'get').resolves({
            id: 'child1', firstName: 'child1', lastName: 'child1', photoURL: 'photoURL', associatedColor: 'associatedColor'
        });
        const deleteStub = sinon.stub(PendingPushNotification, 'delete').resolves();
        const axiosPostStub = sinon.stub(axios, 'post').resolves({ data: {} });
        const jwtClientAuthorizeStub = sinon.stub(google.auth.JWT.prototype, 'authorize').callsFake((callback) => {
            callback(null, { access_token: 'fake_token' });
        });
        const pushInNotificationStub = sinon.stub(NotificationModel, 'create').resolves();

        await PushNotificationService.pushNotfication();

        assert(scanStub.calledOnce);
        jwtClientAuthorizeStub.restore();
        scanStub.restore();
        deleteStub.restore();
        pushInNotificationStub.restore();
        axiosPostStub.restore();
        childModelGetStub.restore();
    });

    it('should send notification for 72hour trigger for multiday event', async () => {
        const pendingNotification = [
            {
                id: '1',
                pushNotificationTime: new Date(),
                payload: {
                    startDateTime: new Date('2022-01-01T10:00:00Z'),
                    endDateTime: new Date('2022-01-09T18:00:00Z'),
                    title: 'title',
                    triggerAt: '72'
                },
                notificationAction: 'action',
                associatedChildId: 'child1',
                title: 'title'
            }
        ];
        const scanStub = sinon.stub(PendingPushNotification, 'scan').returns({
            exec: sinon.stub().resolves(pendingNotification)
        });
        const childModelGetStub = sinon.stub(ChildModel, 'get').resolves({
            id: 'child1', firstName: 'child1', lastName: 'child1', photoURL: 'photoURL', associatedColor: 'associatedColor'
        });
        const deleteStub = sinon.stub(PendingPushNotification, 'delete').resolves();
        const axiosPostStub = sinon.stub(axios, 'post').resolves({ data: {} });
        const jwtClientAuthorizeStub = sinon.stub(google.auth.JWT.prototype, 'authorize').callsFake((callback) => {
            callback(null, { access_token: 'fake_token' });
        });
        const pushInNotificationStub = sinon.stub(NotificationModel, 'create').resolves();

        await PushNotificationService.pushNotfication();

        assert(scanStub.calledOnce);
        jwtClientAuthorizeStub.restore();
        scanStub.restore();
        deleteStub.restore();
        pushInNotificationStub.restore();
        axiosPostStub.restore();
        childModelGetStub.restore();
    });

    it('should handle errors correctly', async () => {
        const errorMessage = 'Test error';
        const scanStub = sinon.stub(PendingPushNotification, 'scan').throws(new Error(errorMessage));
        const consoleErrorStub = sinon.stub(console, 'error');
        try {
            await PushNotificationService.pushNotfication();
        } catch (error) {
            assert(consoleErrorStub.calledWith(errorMessage, 'Error sending notification'));
        } finally {
            scanStub.restore();
            consoleErrorStub.restore();
        }
    });
});
