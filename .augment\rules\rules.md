---
type: "always_apply"
---

# Code Walkthrough Rules

## Overview

I want Augment to help me understand the codebase in a structured and deep way. For each file I open, please follow this comprehensive analysis framework.

## Analysis Framework

### 1. File-Level Analysis

For every code file, provide:

#### **Purpose & Context**

- What does this file do in the overall system?
- What role does it play in the application architecture?
- How does it fit into the broader codebase structure?

#### **Dependencies & Imports**

- Analyze all imports/requires at the top
- Explain what each dependency provides
- Identify any custom vs third-party dependencies
- Note any circular dependency risks

#### **Overall Structure**

- Describe the file's organization (classes, functions, exports)
- Explain the chosen architectural pattern (MVC, service layer, etc.)
- Identify any design patterns being used

### 2. Function/Component-Level Analysis

For each function, method, or component:

#### **Functionality Deep Dive**

- What does this function/component do?
- What are its inputs and outputs?
- What business logic does it implement?
- What side effects does it have?

#### **Design Decisions**

- Why is it written this way?
- What patterns or principles are being followed?
- Are there any trade-offs being made?
- How does it handle edge cases and errors?

#### **Syntax & Implementation**

- Explain complex syntax or language features used
- Break down any advanced JavaScript/Node.js concepts
- Explain async/await patterns, promises, callbacks
- Describe any functional programming concepts
- Explain object destructuring, spread operators, etc.

#### **Code Quality Assessment**

- Is the code following best practices?
- Are there any code smells or anti-patterns?
- How is error handling implemented?
- Is the code testable and maintainable?

#### **Improvement Suggestions**

- What could be refactored or improved?
- Are there performance optimizations possible?
- Could the code be more readable or maintainable?
- Are there security considerations?
- Should any functionality be extracted or consolidated?

### 3. Progressive Exploration Guide

#### **Within File Navigation**

After analyzing each function/component:

- Tell me which function/component to explore next within the same file
- Prioritize by:
  1. Foundational/utility functions first
  2. Main business logic functions
  3. Helper/auxiliary functions last
- Explain why that function should be explored next

#### **Cross-File Navigation**

When a file is complete:

- Recommend the next logical file to explore
- Explain the relationship between the current file and the suggested next file
- Consider exploration paths like:
  1. Following the data flow (models → services → controllers → routes)
  2. Following the dependency chain (utilities → services → main logic)
  3. Following the user journey (auth → core features → supporting features)

### 4. Context Preservation

Throughout the walkthrough:

- Reference previously analyzed files when relevant
- Build connections between different parts of the codebase
- Maintain awareness of the overall system architecture
- Point out recurring patterns across files

### 5. Learning Objectives

Focus on helping me understand:

- **Architecture**: How the system is structured and why
- **Patterns**: What design patterns are used and their benefits
- **Best Practices**: What makes this code good or how it could be better
- **Relationships**: How different parts of the system work together
- **Evolution**: How the code could be improved or extended

## Response Format

Structure your response as:

```markdown
## File: [filename]

### Purpose & Architecture

[File-level analysis]

### Dependencies Analysis

[Import/require analysis]

---

## Function: [function_name]

### What it does

[Functionality explanation]

### Why it's written this way

[Design decisions and patterns]

### How the syntax works

[Technical implementation details]

### Improvements & Best Practices

[Suggestions and observations]

---

## Next Steps

**Next function in this file:** [function_name] - [reason]
**OR**
**Next file to explore:** [filename] - [reason and relationship]
```

## Special Considerations

### For Different File Types

- **Models**: Focus on data structure, validation, relationships
- **Controllers**: Focus on request handling, response formatting, error handling
- **Services**: Focus on business logic, data processing, external integrations
- **Routes**: Focus on endpoint design, middleware usage, request flow
- **Utilities**: Focus on reusability, pure functions, helper logic
- **Middleware**: Focus on request/response transformation, security, validation
- **Configuration**: Focus on environment management, security implications

### For Complex Codebases

- Maintain a mental map of the system architecture
- Track common patterns and their variations
- Note inconsistencies that might indicate technical debt
- Identify areas where documentation might be helpful

This rule ensures comprehensive understanding while maintaining logical progression through the codebase.
