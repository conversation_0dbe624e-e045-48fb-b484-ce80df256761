version: 0.2
environment_variables:
  plaintext:
     S3_BUCKET: "vaalee-dev-be-artifacts"
     FUNCTION_NAME: "dev-vaalee-api-function"
     projectKey: "vaalee-api-be"
     projectVersion: "master"
     projectName: "vaalee-api-be"
env:
  parameter-store:
     SONAR_TOKEN: 'SONAR_TOKEN'
     SONAR_HOST: 'SONAR_HOST'
phases:
  install:
      runtime-versions:
       nodejs: 18

  pre_build:
    commands:
      - echo install node packages and pre-build commands
      - curl ifconfig.co
      - cd api  
      - npm install
      - echo run test 
      - npm test
      - echo test completed
      - pwd  
      - rm -R node_modules/
      - wget -q https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-4.0.0.1744-linux.zip
      - unzip -q sonar-scanner-cli-4.0.0.1744-linux.zip
      - ls -ltrah && pwd
      - aws s3 cp s3://$S3_BUCKET/sonarqube/sonar-scanner.properties .
      - ls -ltrah && pwd
      - mv sonar-scanner.properties sonar-scanner-4.0.0.1744-linux/conf/sonar-scanner.properties
      - ./sonar-scanner-4.0.0.1744-linux/bin/sonar-scanner -X -Dsonar.host.url=$SONAR_HOST -Dsonar.login=$SONAR_TOKEN -Dsonar.projectKey=$projectKey -Dsonar.projectVersion=$projectVersion

  build:
    commands:
      - npm install --prod
      - zip -r dev-vaalee-api-function.zip index.js node_modules emailTemplates server prestart.sh
      - ls -la
      - pwd

  post_build:
    commands:
      - echo Entering Post_Build Phase
      - aws s3 cp dev-vaalee-api-function.zip s3://$S3_BUCKET/api/
      - aws lambda update-function-code --function-name "$FUNCTION_NAME" --s3-bucket $S3_BUCKET --s3-key api/dev-vaalee-api-function.zip  

cache:
   paths:
    - '/root/.sonar/**/*'
      
artifacts:
   files:
    - '**/*'
