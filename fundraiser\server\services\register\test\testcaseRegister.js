module.exports = {
    registerForEvent: [
        {
            it: 'As a user I should validate if eventId is not passed',
            options: {
                eventId: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate if organizationId is invalid',
            options: {
                eventId: 'eventId',
                organizationId: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate if parentId is not passed',
            options: {
                eventId: 'eventId',
                organizationId: 'organizationId',
                parentId: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate if paymentType is invalid',
            options: {
                eventId: 'eventId',
                organizationId: 'organizationId',
                parentId: 'parentId',
                childId: 'childId',
                paymentType: 'paymentType',
                fee: 0
            },
            status: 0
        },
        {
            it: 'As a user I should validate if quantity is invalid',
            options: {
                eventId: 'eventId',
                organizationId: 'organizationId',
                parentId: 'parentId',
                childId: 'childId',
                paymentType: 'stripe',
                fee: 0,
                quantity: 'test'
            },
            status: 0
        }
    ]
};
