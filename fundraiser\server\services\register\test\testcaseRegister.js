module.exports = {
    registerForEvent: [
        {
            it: 'As a user I should validate if eventId is not passed',
            options: {
                eventId: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate if organizationId is invalid',
            options: {
                eventId: 'eventId',
                organizationId: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate if parentId is not passed',
            options: {
                eventId: 'eventId',
                organizationId: 'organizationId',
                parentId: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate if paymentType is invalid',
            options: {
                eventId: 'eventId',
                organizationId: 'organizationId',
                parentId: 'parentId',
                childId: 'childId',
                paymentType: 'paymentType',
                fee: 0
            },
            status: 0
        },
        {
            it: 'As a user I should validate if quantity is invalid',
            options: {
                eventId: 'eventId',
                organizationId: 'organizationId',
                parentId: 'parentId',
                childId: 'childId',
                paymentType: 'stripe',
                fee: 0,
                quantity: 'test'
            },
            status: 0
        }
    ],
    registerForFundraiserAsGuest: [
        {
            it: 'As a user I should validate if recaptchaToken is not passed',
            options: {
                eventId: 'eventId',
                organizationId: 'organizationId',
                purchasedProducts: '[]',
                paymentType: 'stripe',
                isGuestSignup: 'true'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if recaptchaAction is not passed',
            options: {
                eventId: 'eventId',
                organizationId: 'organizationId',
                purchasedProducts: '[]',
                paymentType: 'stripe',
                isGuestSignup: 'true',
                recaptchaToken: 'recaptchaToken'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if recaptchaAction is not same as fundraiser_register',
            options: {
                eventId: 'eventId',
                organizationId: 'organizationId',
                purchasedProducts: '[]',
                paymentType: 'stripe',
                isGuestSignup: 'true',
                recaptchaToken: 'recaptchaToken',
                recaptchaAction: 'event_register'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if user email is not passed',
            options: {
                eventId: 'eventId',
                organizationId: 'organizationId',
                purchasedProducts: '[]',
                paymentType: 'stripe',
                isGuestSignup: 'true',
                recaptchaToken: 'recaptchaToken',
                recaptchaAction: 'fundraiser_register'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if user email is not valid',
            options: {
                eventId: 'eventId',
                organizationId: 'organizationId',
                purchasedProducts: '[]',
                paymentType: 'stripe',
                isGuestSignup: 'true',
                userEmail: 'test'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if child first name is not passed',
            options: {
                eventId: 'eventId',
                organizationId: 'organizationId',
                purchasedProducts: '[]',
                paymentType: 'stripe',
                isGuestSignup: 'true',
                userEmail: '<EMAIL>',
                childFirstName: '',
                recaptchaToken: 'recaptchaToken',
                recaptchaAction: 'fundraiser_register'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if child last name is not passed',
            options: {
                eventId: 'eventId',
                organizationId: 'organizationId',
                purchasedProducts: '[]',
                paymentType: 'stripe',
                isGuestSignup: 'true',
                userEmail: '<EMAIL>',
                childFirstName: 'test',
                childLastName: '',
                recaptchaToken: 'recaptchaToken',
                recaptchaAction: 'fundraiser_register'
            },
            status: 0
        }
    ]
};
