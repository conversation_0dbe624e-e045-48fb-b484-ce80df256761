/* eslint-disable max-len */
const HTTPStatus = require('../util/http-status');
const crypto = require('crypto');
const secretKey = process.env.HMAC_SECRET_KEY;
const CONSTANTS = require('./constants');
const moment = require('moment');
const { publishCustomMetric } = require('./cloudwatchMetrics');

/**
 * This class reprasents common utilities for application
 */
class Utils {
    static errorResponse () {
        return JSON.parse(
            JSON.stringify({
                status: 0,
                data: {},
                message: ''
            })
        );
    }

    static successResponse () {
        return JSON.parse(
            JSON.stringify({
                status: 1,
                data: {},
                message: ''
            })
        );
    }

    /**
   * This function is being used to add pagination for user table
   * @auther Growexx
   * @param {string} error Error Message
   * @param {Object} data Object to send in response
   * @param {Object} res Response Object
   * @param {string} successMessage success message
   * @param {Object} additionalData additional data outside of data object in response
   * @param {string} successMessageVars
   * @since 01/03/2021
   */
    static sendResponse (error, data, res, successMessage, successMessageVars) {
        let responseObject;

        if (error) {
            let status;
            responseObject = Utils.errorResponse();
            if (typeof error === 'object') {
                responseObject.message = error.message
                    ? error.message
                    : res.__('ERROR_MSG');
                status = error.statusCode ? error.statusCode : HTTPStatus.BAD_REQUEST;
            } else {
                responseObject.message = res.__(error);
                status = HTTPStatus.BAD_REQUEST;
            }

            responseObject.data = error.data;
            res.status(status).send(responseObject);
        } else {
            responseObject = Utils.successResponse();
            responseObject.message = successMessageVars
                ? res.__.apply('', [successMessage].concat(successMessageVars))
                : successMessage;
            responseObject.data = data;
            res.status(HTTPStatus.OK).send(responseObject);
        }
    }

    static generateHmac (url) {
        const currentDate = MOMENT().utc().format('MMYYYYDD'); // 03202426
        const reqURL = url; // /feed/path
        const message = currentDate + reqURL;
        return crypto.createHmac('sha256', secretKey).update(message).digest('hex');
    }

    /**
   * This function is being used to overwrite request function for adding reqtoken
   * @auther Growexx
   * @param {import('supertest')} request
   * @since 28/03/2024
   */
    static addCommonReqTokenForHMac (request) {
        const originalEnd = request.Test.prototype.end;

        request.Test.prototype.end = function (callback) {
            const currentParsedUrl = new URL(this.url);
            this.set('reqtoken', Utils.generateHmac(`${currentParsedUrl.pathname}`));
            return originalEnd.call(this, callback);
        };
    }

    /**
     * @description Generates a prefix for a key
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} keyPrefix - The key prefix
     * @returns {String} The prefix
    */
    static generatePrefixForKey ({ versionPrefix, keyPrefix }) {
        return `${versionPrefix}:${keyPrefix}`;
    }

    /**
     * @description Generates a key
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} keyPrefix - The key prefix
     * @param {String} id - The id
     * @returns {String} The key
    */
    static generateKey ({ versionPrefix, keyPrefix, id }) {
        return `${this.generatePrefixForKey({ versionPrefix, keyPrefix })}:${id}`;
    }

    /**
     * @description Generates a key for user details
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} userId - The user id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key prefix
     * @returns {String} The key
     */
    static getUserDetailsKey ({ versionPrefix, userId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_USER_DETAILS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_USER_DETAILS, id: userId });
    }

    /**
     * @description Generates a key for conversation members
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} groupId - The group id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key prefix
     * @returns {String} The key
    */
    static getConversationMembersKey ({ versionPrefix, groupId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CONVERSATION_MEMBERS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CONVERSATION_MEMBERS, id: groupId });
    }

    /**
     * @description Generates a key for user conversations
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} userId - The user id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key prefix
     * @returns {String} The key
    */
    static getUserConversationsKey ({ versionPrefix, userId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_USER_CONVERSATIONS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_USER_CONVERSATIONS, id: userId });
    }

    /**
     * @description Generates a key for conversation details
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} conversationId - The conversation id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key prefix
     * @returns {String} The key
    */
    static getConversationDetailsKey ({ versionPrefix, conversationId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CONVERSATION_DETAILS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CONVERSATION_DETAILS, id: conversationId });
    }

    /**
     * @description Generates a key for conversation messages
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} conversationId - The conversation id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key prefix
     * @returns {String} The key
    */
    static getConversationMessagesKey ({ versionPrefix, conversationId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CONVERSATION_MESSAGES })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CONVERSATION_MESSAGES, id: conversationId });
    }

    /**
     * @description Generates a key for a child details
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} childId - The child id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getChildDetailsKey ({ versionPrefix, childId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CHILD_DETAILS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CHILD_DETAILS, id: childId });
    }

    /**
     * @description Generates a key for a organization details
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} organizationId - The organization id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getOrganizationDetailsKey ({ versionPrefix, organizationId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_ORGANIZATION_DETAILS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_ORGANIZATION_DETAILS, id: organizationId });
    }

    /**
     * @description Adds metric for query time
     * @param {moment} startTime - The start time
     */
    static async addMetricForQueryTime (startTime, isEvalRun = false) {
        if (!isEvalRun) {
            const endTime = moment();
            const duration = endTime.diff(startTime, 'milliseconds');
            await publishCustomMetric(duration, {
                metricName: CONSTANTS.CLOUDWATCH_METRIC_NAMES.AI_COMPANION_QUERY_TIME,
                unit: 'Milliseconds'
            });
        }
    }

    /**
     * @description Adds metric for accuracy
     * @param {string} feedback - The feedback (positive or negative)
     */
    static async addMetricForAccuracy (feedback) {
        const accuracy = feedback === CONSTANTS.AI_COMPANION_FEEDBACK.POSITIVE ? 1 : 0;
        await publishCustomMetric(accuracy, {
            metricName: CONSTANTS.CLOUDWATCH_METRIC_NAMES.AI_COMPANION_ACCURACY,
            unit: 'Count'
        });
    }

    /**
     * @description Parses a value
     * @param {string} value - The value to parse
     * @returns {Object} The parsed value
     */
    static getParsedValue (value) {
        try {
            return JSON.parse(value);
        } catch (error) {
            return null;
        }
    }
}

module.exports = Utils;
