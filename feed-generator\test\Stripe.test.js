const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const Stripe = require('../server/Stripe');
const sinon = require('sinon');
describe('Stripe', () => {
    it('should retrieve payment intent', async () => {
        const paymentIntent = {
            id: 'test_payment_intent_id',
            amount: 1000,
            status: 'succeeded'
        };

        sinon.stub(stripe.paymentIntents, 'retrieve').resolves(paymentIntent);

        await Stripe.retrievePaymentIntent(stripe, 'test_payment_intent_id');

        sinon.assert.calledWith(stripe.paymentIntents.retrieve, 'test_payment_intent_id');

        stripe.paymentIntents.retrieve.restore();
    });
});
