/**
 *  routes and schema for SignIn
 */

/**
 * @openapi
 * components:
 *  schemas:
 *      userSignIn:
 *          type: object
 *          required:
 *              - email
 *              - password
 *          properties:
 *              email:
 *                  type: string
 *                  description: user email address
 *              password:
 *                  type: string
 *                  description: password for login
 *              fcmToken:
 *                  type: string
 *                  description: fcm token of device
 *          example:
 *              email: <EMAIL>
 *              password: Password@123
 *              fcmToken: testFcmToken
 */

/**
 * @openapi
 * /auth/signin:
 *  post:
 *      tags: [Authentication]
 *      summary: user signin
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/userSignIn'
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successLogin'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Wrong Credentials
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessLogin'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */
