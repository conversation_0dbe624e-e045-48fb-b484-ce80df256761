const Utils = require('../util/utilFunctions');
const HTTPStatus = require('../util/http-status');

module.exports = function (req, res, next) {
    const accessList = {
        'app': [],
        'org_app': [
            { method: 'PATCH', path: '/conversation/enable-commenter' },
            { method: 'GET', path: '/conversation/flag-message-list' },
            { method: 'GET', path: '/conversation/disabled-commenter-list' },
            { method: 'PATCH', path: '/conversation/update-flagged-message-status' }
        ],
        'root': [
            { method: 'PATCH', path: '/conversation/enable-commenter' },
            { method: 'GET', path: '/conversation/flag-message-list' },
            { method: 'GET', path: '/conversation/disabled-commenter-list' },
            { method: 'PATCH', path: '/conversation/update-flagged-message-status' }
        ]
    };
    const accessLevel = res.locals.user.accessLevel;
    const isAllowed = _.find(accessList[accessLevel], {
        method: req.method,
        path: req.originalUrl.split('?')[0]
    });
    if (isAllowed) {
        next();
    } else {
        const responseObject = Utils.errorResponse();
        responseObject.message = res.__('ACCESS_DENIED');
        res.status(HTTPStatus.NOT_ACCEPTABLE).send(responseObject);
        return;
    }
};
