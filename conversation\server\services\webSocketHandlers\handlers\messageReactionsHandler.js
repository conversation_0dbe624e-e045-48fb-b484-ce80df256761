const GroupMembers = require('../../../models/groupMembers.model');
const MessageReactions = require('../../../models/messageReactions.model');
const SendMessageService = require('../../sendSocketMessageService');

const handleAddOrUpdateReaction = async ({ eventBody, isGroupMessage, isAddReaction }) => {
    const { actionType, action } = eventBody;
    try {
        const { messageId, userId, reaction, groupId, receiverId, conversationId } = eventBody;

        validateReaction({ reaction, action, actionType });

        await MessageReactions.update({ messageId, userId }, { reaction });

        const usersToSendMessage = await getUsersToSendMessage({ isGroupMessage, groupId, receiverId, userId });

        const data = {
            messageId,
            userId,
            reaction,
            groupId,
            receiverId,
            conversationId
        };

        await SendMessageService.sendMessagesToUsers(
            usersToSendMessage,
            {
                data,
                actionType
            }
        );

        return {
            statusCode: 200,
            message: isAddReaction ? 'Reaction added successfully' : 'Reaction updated successfully',
            data: JSON.stringify(data),
            action,
            actionType
        };
    } catch (error) {
        if (error.statusCode) {
            return error;
        }

        return {
            statusCode: 500,
            message: isAddReaction ? 'Failed to add reaction' : 'Failed to update reaction',
            action,
            actionType
        };
    }
};

const validateReaction = ({ reaction, action, actionType }) => {
    if (!CONSTANTS.ALLOWED_REACTIONS.includes(reaction)) {
        throw {
            statusCode: 400,
            message: 'Invalid reaction',
            action,
            actionType
        };
    }
};

const getUsersToSendMessage = async ({ isGroupMessage, groupId, receiverId, userId }) => {
    let usersToSendMessage = [];
    if (isGroupMessage) {
        const groupMembers = await GroupMembers.query('groupId').eq(groupId).using('groupId-index').attributes(['userId']).exec();
        usersToSendMessage = groupMembers.map((member) => member.userId);
    } else {
        usersToSendMessage = [userId, receiverId];
    }

    return usersToSendMessage;
};

const handleRemoveReaction = async ({ eventBody, isGroupMessage }) => {
    const { actionType, action } = eventBody;
    try {
        const { messageId, userId, groupId, receiverId, conversationId } = eventBody;
        await MessageReactions.delete({ messageId, userId });

        const usersToSendMessage = await getUsersToSendMessage({ isGroupMessage, groupId, receiverId, userId });

        const data = {
            messageId,
            userId,
            groupId,
            receiverId,
            conversationId
        };

        await SendMessageService.sendMessagesToUsers(
            usersToSendMessage,
            {
                actionType,
                data
            }
        );

        return {
            statusCode: 200,
            message: 'Reaction removed successfully',
            data: JSON.stringify(data),
            action,
            actionType
        };
    } catch (error) {
        return {
            statusCode: 500,
            message: 'Failed to remove reaction',
            action,
            actionType
        };
    }
};

const getReactionsForMessage = async ({ messageIds = [] }) => {
    try {
        if (messageIds.length === 0) {
            return {};
        }

        const reactionsForMessagesPromise = [];
        for (const messageId of messageIds) {
            reactionsForMessagesPromise.push(MessageReactions.query('messageId').eq(messageId).exec());
        }

        const reactionsForMessages = await Promise.all(reactionsForMessagesPromise);
        const reactions = reactionsForMessages.flat().reduce((acc, reaction) => {
            (acc[reaction.messageId] ?? (acc[reaction.messageId] = {}))[reaction.userId] = reaction;
            return acc;
        }, {});

        return reactions;
    } catch (error) {
        return {};
    }
};

module.exports = {
    handleAddOrUpdateReaction,
    handleRemoveReaction,
    getReactionsForMessage
};
