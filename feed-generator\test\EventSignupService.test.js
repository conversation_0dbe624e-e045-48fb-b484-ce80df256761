const sinon = require('sinon');
const AWS = require('aws-sdk');
const AWSMock = require('jest-aws-sdk-mock');
const Redis = require('ioredis');
const CONSOLE_LOGGER = require('../server/logger');
const handler = require('../index');
const RedisUtil = require('../server/redisUtil');
const GroupMembers = require('../server/models/groupMembers.model');
const Groups = require('../server/models/groups.model');
const { beforeEach, afterEach } = require('mocha');
const ConversationService = require('../server/ConversationService');
const Child = require('../server/models/child.model');
const User = require('../server/models/user.model');
const Message = require('../server/models/message.model');
const ConstantModel = require('../server/models/constant.model');
const PendingPushNotification = require('../server/models/pendingPushNotification.model');
const { SQSClient } = require('@aws-sdk/client-sqs');
const CONSTANTS = require('../server/constants');

describe('Event signup service', () => {
    let sandbox;
    let stubRedisConnect;
    let pipelineExecStub;
    let pipelineStub;
    let sendStub;
    let loggerStub;

    before(() => {
        AWSMock.setSDKInstance(AWS);
        sandbox = sinon.createSandbox();
        sendStub = sandbox.stub(SQSClient.prototype, 'send');
    });

    after(() => {
        AWSMock.restore();
        sinon.restore();
        sandbox.restore();
        pipelineStub.restore();
        sendStub.restore();
        loggerStub.restore();
    });

    beforeEach(() => {
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
        pipelineExecStub = sinon.stub();
        pipelineStub = sinon.stub(Redis.prototype, 'pipeline').returns({ exec: pipelineExecStub });
        loggerStub = sinon.stub(CONSOLE_LOGGER, 'error');
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        sinon.stub(ConversationService, 'getReactionsForMessage').resolves({});
        sinon.stub(ConversationService, 'getActiveSocketConnections').resolves([]);
        sinon.stub(ConversationService, 'sendMessagesToConnections').resolves();
        sendStub.resolves();
        sinon.stub(ConstantModel, 'get').withArgs(CONSTANTS.FEED_VERSION_PREFIX).resolves({ value: 'v1' });
    });

    afterEach(() => {
        sinon.restore();
    });

    try {
        it('should update events if user register for event and events contains child and user id', async () => {
            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/EventSignups/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'eventId': { 'S': '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        'organizationId': { 'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc' },
                        'paymentDetails': {
                            'M': {
                                'stripeConnectAccountId': { 'S': 'accountId' },
                                'stripeCustomerId': { 'S': 'customerId' },
                                'stripePaymentIntentId': { 'S': 'intentId' },
                                'paymentStatus': { 'S': 'pending' },
                                'paymentType': { 'S': 'stripe' }
                            }
                        },
                        'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                        'createdBy': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' }
                    }

                }
            };

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                title: 'Test event', details: { startDateTime: '2020-01-01T00:00:00.000Z' },
                comments: 'Test comment', organizationId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                eventType: 'Test event type', eventScope: 'Test event scope',
                sheetUrl: 'Test sheet url', fee: '100',
                status: 'unpublished', photoURL: 'Test photo url',
                createdAt: '2020-01-01T00:00:00.000Z', updatedAt: '2020-01-01T00:00:00.000Z'
            }));

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([JSON.stringify({
                eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                isSignedUp: false, status: ''
            })]);

            sinon.stub(RedisUtil, 'getScoreOfMember').resolves(10000000);

            sinon.stub(Groups, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ id: 'group1' }])
                            })
                        })
                    })
                })
            });

            sinon.stub(Child, 'get').resolves({ guardians: [{ id: 'a967e334-60d4-4efe-8a9d-c71ecc525977' }] });

            const groupMemberSaveStub = sinon.stub();

            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([{
                                    id: 'groupMember1',
                                    childrenIds: ['ace0149e-447d-4f7f-9402-95a96963de9b'],
                                    save: groupMemberSaveStub
                                }])
                            })
                        }),
                        exec: sinon.stub().resolves([{
                            id: 'groupMember1',
                            childrenIds: ['ace0149e-447d-4f7f-9402-95a96963de9b']
                        }])
                    })
                })
            });

            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([
                            {
                                id: 'user1',
                                firstName: 'John',
                                lastName: 'Doe',
                                associatedColor: '#FACD01',
                                photoURL: 'Test photo url'
                            }
                        ])
                    })
                })
            });

            sinon.stub(Message, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ id: 'message1' }])
                            })
                        })
                    })
                })
            });

            const addEventReferenceToSortedSetStub = sinon.stub(RedisUtil, 'addEventReferenceToSortedSet');

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(addEventReferenceToSortedSetStub);
            sinon.assert.called(groupMemberSaveStub);
        });
        it('should update events if user register for event if payment status is payment initiated', async () => {
            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/EventSignups/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'eventId': { 'S': '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        'organizationId': { 'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc' },
                        'paymentDetails': {
                            'M': {
                                'stripeConnectAccountId': { 'S': 'accountId' },
                                'stripeCustomerId': { 'S': 'customerId' },
                                'stripePaymentIntentId': { 'S': 'intentId' },
                                'paymentStatus': { 'S': 'payment-initiated' },
                                'paymentType': { 'S': 'stripe' }
                            }
                        },
                        'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                        'createdBy': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' }
                    }

                }
            };

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                title: 'Test event', details: { startDateTime: '2020-01-01T00:00:00.000Z' },
                comments: 'Test comment', organizationId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                eventType: 'Test event type', eventScope: 'Test event scope',
                sheetUrl: 'Test sheet url', fee: '100',
                status: 'unpublished', photoURL: 'Test photo url',
                createdAt: '2020-01-01T00:00:00.000Z', updatedAt: '2020-01-01T00:00:00.000Z'
            }));

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([JSON.stringify({
                eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                isSignedUp: false, status: ''
            })]);

            sinon.stub(RedisUtil, 'getScoreOfMember').resolves(10000000);

            sinon.stub(Groups, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ id: 'group1' }])
                            })
                        })
                    })
                })
            });

            sinon.stub(Child, 'get').resolves({ guardians: [{ id: 'a967e334-60d4-4efe-8a9d-c71ecc525977' }] });

            const groupMemberSaveStub = sinon.stub();

            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([{
                                    id: 'groupMember1',
                                    childrenIds: ['ace0149e-447d-4f7f-9402-95a96963de9b'],
                                    save: groupMemberSaveStub
                                }])
                            })
                        }),
                        exec: sinon.stub().resolves([{
                            id: 'groupMember1',
                            childrenIds: ['ace0149e-447d-4f7f-9402-95a96963de9b']
                        }])
                    })
                })
            });

            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([
                            {
                                id: 'user1',
                                firstName: 'John',
                                lastName: 'Doe',
                                associatedColor: '#FACD01',
                                photoURL: 'Test photo url'
                            }
                        ])
                    })
                })
            });

            sinon.stub(Message, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ id: 'message1' }])
                            })
                        })
                    })
                })
            });

            const addEventReferenceToSortedSetStub = sinon.stub(RedisUtil, 'addEventReferenceToSortedSet');

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.notCalled(addEventReferenceToSortedSetStub);
        });
        it('should update events if user register for event', async () => {
            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/EventSignups/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'eventId': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' },
                        'organizationId': { 'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc' },
                        'paymentDetails': {
                            'M': {
                                'stripeConnectAccountId': { 'S': 'accountId' },
                                'stripeCustomerId': { 'S': 'customerId' },
                                'stripePaymentIntentId': { 'S': 'intentId' },
                                'paymentStatus': { 'S': 'approved' },
                                'paymentType': { 'S': 'stripe' }
                            }
                        },
                        'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                        'createdBy': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' }
                    }
                }
            };

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                id: 'c16c88cd-681a-4676-bacd-0058d0efe99b',
                title: 'Test event', details: { startDateTime: '2020-01-01T00:00:00.000Z' },
                comments: 'Test comment', organizationId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                eventType: 'Test event type', eventScope: 'Test event scope',
                sheetUrl: 'Test sheet url', fee: '100',
                status: 'unpublished', photoURL: 'Test photo url',
                createdAt: '2020-01-01T00:00:00.000Z', updatedAt: '2020-01-01T00:00:00.000Z'
            }));

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([JSON.stringify({
                eventId: 'c16c88cd-681a-4676-bacd-0058d0efe99b',
                childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                isSignedUp: false, status: ''
            })]);

            sinon.stub(RedisUtil, 'getScoreOfMember').resolves(10000000);

            sinon.stub(Groups, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ id: 'group1' }])
                            })
                        })
                    })
                })
            });

            sinon.stub(Child, 'get').resolves({ guardians: [{ id: 'a967e334-60d4-4efe-8a9d-c71ecc525977' }] });

            const groupMemberSaveStub = sinon.stub();

            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([{
                                    id: 'groupMember1',
                                    childrenIds: ['ace0149e-447d-4f7f-9402-95a96963de9b'],
                                    save: groupMemberSaveStub
                                }])
                            })
                        }),
                        exec: sinon.stub().resolves([{
                            id: 'groupMember1',
                            childrenIds: ['ace0149e-447d-4f7f-9402-95a96963de9b']
                        }])
                    })
                })
            });

            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([
                            {
                                id: 'user1',
                                firstName: 'John',
                                lastName: 'Doe',
                                associatedColor: '#FACD01',
                                photoURL: 'Test photo url'
                            }
                        ])
                    })
                })
            });

            sinon.stub(Message, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ id: 'message1' }])
                            })
                        })
                    })
                })
            });

            const addEventReferenceToSortedSetStub = sinon.stub(RedisUtil, 'addEventReferenceToSortedSet');

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(addEventReferenceToSortedSetStub);
            sinon.assert.called(groupMemberSaveStub);
        });
        it('should update events if user register for event and add to notification table', async () => {
            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/EventSignups/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'eventId': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' },
                        'organizationId': { 'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc' },
                        'paymentDetails': {
                            'M': {
                                'stripeConnectAccountId': { 'S': 'accountId' },
                                'stripeCustomerId': { 'S': 'customerId' },
                                'stripePaymentIntentId': { 'S': 'intentId' },
                                'paymentStatus': { 'S': 'approved' },
                                'paymentType': { 'S': 'stripe' }
                            }
                        },
                        'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                        'createdBy': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' }
                    }
                }
            };

            sinon.stub(PendingPushNotification, 'create').resolves();

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                title: 'Test event',
                details: { startDateTime: '2020-01-01T00:00:00.000Z' },
                comments: 'Test comment', organizationId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                eventType: 'Test event type', eventScope: 'Test event scope',
                sheetUrl: 'Test sheet url', fee: '100', status: 'unpublished',
                photoURL: 'Test photo url',
                createdAt: '2020-01-01T00:00:00.000Z', updatedAt: '2020-01-01T00:00:00.000Z',
                participants: [
                    { id: 'ace0149e-447d-4f7f-9402-95a96963de9b', firstName: 'test', lastName: 'user', associatedColor: '#000000' }
                ],
                startDateTime: '2028-01-01T00:00:00.000Z'
            }));

            const getElementsOfSortedSetStub = sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([JSON.stringify({
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                title: 'Test event',
                details: { startDateTime: '2020-01-01T00:00:00.000Z' },
                comments: 'Test comment', organizationId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                eventType: 'Test event type', eventScope: 'Test event scope',
                sheetUrl: 'Test sheet url', fee: '100', status: 'unpublished',
                photoURL: 'Test photo url', createdAt: '2020-01-01T00:00:00.000Z', updatedAt: '2020-01-01T00:00:00.000Z'
            })]);

            const event = {
                Records: [record]
            };

            sinon.stub(Child, 'get').resolves(
                {
                    id: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    firstName: 'test', lastName: 'user', associatedColor: '#000000', guardians: ['userId', 'userId2']
                }
            );

            sinon.stub(Groups, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ id: 'group1' }])
                            })
                        })
                    })
                })
            });

            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([
                                    { id: 'groupMember1', childrenIds: ['ace0149e-447d-4f7f-9402-95a96963de9b'], save: () => { } }
                                ])
                            })
                        }),
                        exec: sinon.stub().resolves([
                            {
                                id: 'groupMember1',
                                childrenIds: ['ace0149e-447d-4f7f-9402-95a96963de9b']
                            }
                        ])
                    })
                })
            });

            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ id: 'userId', firstName: 'test', lastName: 'user', associatedColor: '#000000' }])
                    })
                })
            });

            sinon.stub(Message, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ id: 'message1' }])
                            })
                        })
                    })
                })
            });

            await handler.handler(event);

            sinon.assert.called(getElementsOfSortedSetStub);
        });

        it('should add event to registered key so i can get my feeds', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/EventSignups/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'eventId': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' },
                        'organizationId': { 'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc' },
                        'paymentDetails': {
                            'M': {
                                'paymentStatus': { 'S': 'approved' },
                                'paymentType': { 'S': 'cash' }
                            }
                        },
                        'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                        'createdBy': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' }
                    },
                    OldImage: {
                        'eventId': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' },
                        'organizationId': { 'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc' },
                        'paymentDetails': {
                            'M': {
                                'paymentStatus': { 'S': 'pending' },
                                'paymentType': { 'S': 'cash' }
                            }
                        },
                        'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                        'createdBy': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' }
                    }
                }
            };

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                title: 'Test event',
                details: { startDateTime: '2020-01-01T00:00:00.000Z' },
                comments: 'Test comment', organizationId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                eventType: 'Test event type', eventScope: 'Test event scope'
            }));

            const getElementsOfSortedSetStub = sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([JSON.stringify({
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                title: 'Test event',
                details: { startDateTime: '2020-01-01T00:00:00.000Z' },
                comments: 'Test comment', organizationId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                eventType: 'Test event type', eventScope: 'Test event scope'
            })]);

            const event = {
                Records: [record]
            };

            sinon.stub(Child, 'get').resolves(
                { id: 'ace0149e-447d-4f7f-9402-95a96963de9b', firstName: 'test', lastName: 'user', associatedColor: '#000000' }
            );

            await handler.handler(event);

            sinon.assert.called(getElementsOfSortedSetStub);
        });

        it('should refresh registered events if the payment is canceled', async () => {
            stubRedisConnect.callsFake(async function () {
                this.setStatus('connect');
            });
            const record = {
                eventName: 'REMOVE',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/EventSignups/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        'eventId': { 'S': '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        'organizationId': { 'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc' },
                        'paymentDetails': {
                            'M': {
                                'stripeConnectAccountId': { 'S': 'accountId' },
                                'stripeCustomerId': { 'S': 'customerId' },
                                'stripePaymentIntentId': { 'S': 'intentId' },
                                'paymentStatus': { 'S': 'pending' },
                                'paymentType': { 'S': 'stripe' }
                            }
                        },
                        'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                        'createdBy': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' }
                    }
                }
            };

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                participants: [],
                title: 'Test event',
                details: { startDateTime: '2020-01-01T00:00:00.000Z' },
                comments: 'Test comment', organizationId: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
            }));

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([JSON.stringify({
                eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                isSignedUp: false,
                status: ''
            })]);


            const removeRegisteredEventsStub = sinon.stub(RedisUtil, 'removeMemberFromSortedSet');

            sinon.stub(Groups, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ id: 'group1' }])
                            })
                        })
                    })
                })
            });

            sinon.stub(Child, 'get').resolves({ guardians: ['guardian1'] });

            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([
                                    { id: 'groupMember1', childrenIds: ['ace0149e-447d-4f7f-9402-95a96963de9b'], save: () => { } }
                                ])
                            })
                        }),
                        attributes: sinon.stub().returns({
                            exec: sinon.stub().resolves([
                                { id: 'groupMember1', childrenIds: ['ace0149e-447d-4f7f-9402-95a96963de9b'], save: () => { } }
                            ])
                        })
                    })
                })
            });

            const event = {
                Records: [record]
            };

            await handler.handler(event);
            sinon.assert.called(removeRegisteredEventsStub);
        });

        it('should handle if event not found for updating', async () => {
            const record = {
                eventName: 'REMOVE',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/EventSignups/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        'eventId': { 'S': '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        'organizationId': { 'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc' },
                        'paymentDetails': {
                            'M': {
                                'stripeConnectAccountId': { 'S': 'accountId' },
                                'stripeCustomerId': { 'S': 'customerId' },
                                'stripePaymentIntentId': { 'S': 'intentId' },
                                'paymentStatus': { 'S': 'pending' },
                                'paymentType': { 'S': 'stripe' }
                            }
                        },
                        'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                        'createdBy': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' }
                    }
                }
            };

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                title: 'Test event',
                details: { startDateTime: '2020-01-01T00:00:00.000Z' },
                comments: 'Test comment', organizationId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                participants: [
                    { id: 'ace0149e-447d-4f7f-9402-95a96963de9b', firstName: 'test', lastName: 'user', associatedColor: '#000000' }
                ]
            }));

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([
                JSON.stringify({
                    eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                    childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    isSignedUp: false,
                    status: ''
                })]);

            sinon.stub(Groups, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ id: 'group1' }])
                            })
                        })
                    })
                })
            });

            sinon.stub(Child, 'get').resolves({ guardians: ['guardian1'] });

            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([
                                    { id: 'groupMember1', childrenIds: ['ace0149e-447d-4f7f-9402-95a96963de9b'], save: () => { } }
                                ])
                            })
                        }),
                        attributes: sinon.stub().returns({
                            exec: sinon.stub().resolves([
                                { id: 'groupMember1', childrenIds: ['ace0149e-447d-4f7f-9402-95a96963de9b'], save: () => { } }
                            ])
                        })
                    })
                })
            });

            const event = {
                Records: [record]
            };

            await handler.handler(event);
            sinon.assert.called(RedisUtil.getElementsOfSortedSetByScore);
        });
    } catch (error) {
        CONSOLE_LOGGER.error('Error in EventSignupService.test.js', error);
    }
});
