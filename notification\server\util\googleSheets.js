const { google } = require('googleapis');

class GoogleSheetsService {
  constructor(keyFilePath, spreadsheetId) {
    this.keyFilePath = keyFilePath;
    this.spreadsheetId = spreadsheetId;
    this.auth = null;
    this.sheets = null;
  }

  async initialize() {
    const credentials = JSON.parse(process.env['CREDS']);
    this.auth = new google.auth.GoogleAuth({
      credentials,
      scopes: ["https://www.googleapis.com/auth/spreadsheets"],
    });
    const client = await this.auth.getClient();
    this.sheets = google.sheets({ version: 'v4', auth: client });
  }

  async getSheet(sheetName) {
    if (!this.sheets) {
      await this.initialize();
    }

    return this.sheets.spreadsheets.values.get({
      auth: this.auth,
      spreadsheetId: this.spreadsheetId,
      range: sheetName,
    });
  }

  async appendRow(sheetName, values) {
    if (!this.sheets) {
      await this.initialize();
    }

    return this.sheets.spreadsheets.values.append({
      auth: this.auth,
      spreadsheetId: this.spreadsheetId,
      range: sheetName,
      valueInputOption: 'USER_ENTERED',
      resource: {
        values: [values]
      }
    });
  }
}

module.exports = GoogleSheetsService;