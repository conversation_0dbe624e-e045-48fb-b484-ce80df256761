/* eslint-disable max-len */
const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const sinon = require('sinon');
const User = require('../../../models/user.model');
const Fundraiser = require('../../../models/fundraiser.model');
const TestCase = require('./testcaseFundraiser');
const jwt = require('jsonwebtoken');
const Stripe = require('../../../util/Stripe');
const organizationModel = require('../../../models/organization.model');
const OrganizationMembers = require('../../../models/organizationMember.model');
const FundraiserSignup = require('../../../models/fundraiserSignup.model');
const Children = require('../../../models/child.model');
const childOrganizationMappingModel = require('../../../models/childOrganizationMapping.model');
const Utils = require('../../../util/utilFunctions');
const FundraiserService = require('../fundraiserService');
const AwsOpenSearchService = require('../../../util/opensearch');
const FundraiserValidator = require('../fundraiserValidator');
const Validator = require('../../../util/validation');

const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
/* User Token */
const user = {
    id: '95285616-3769-4b2e-b36b-898597b8146e',
    email: '<EMAIL>'
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'testingjwt', tokenOptionalInfo)
};


const data = {
    fundraiserType: 'spiritwearFundraiser',
    title: 'Test',
    description: 'some description',
    startDate: '11/18/2027',
    endDate: '11/18/2027',
    organizationId: '123',
    status: 'published',
    products: '[{"itemId":0,"options":[{"optionId":"123"}],"data":[{"itemName":"Test","id":"123","itemCost":"35"}],"images":[],"imageCount":1,"itemName":"Test","imageURLs":["test image"]}]',
    productsWithoutImage: '[{}]'
};

const eventList = [{
    'eventScope': 'public',
    'fee': 200,
    'eventType': 'fundraiser',
    'title': 'Community Cleanup Day',
    'isDeleted': 0,
    'details': {
        'venue': 'City Park',
        'startDateTime': '2023-11-10T09:00:00.000Z',
        'recurringFrequency': 'month',
        'details': 'Join us for a community cleanup day at City Park. Let\'s make our city cleaner and greener!',
        'isRecurring': true,
        'endDateTime': '2023-11-10T17:00:00.000Z',
        'documentURLs': ['test document']
    },
    'id': '54404ec7-15af-4ab8-afb3-8db63203e05e',
    'status': 'unpublished',
    'organizationId': '5f5f7e5f7e5f7e5f7e5f7e5f',
    'photoURL': 'test image',
    'products': '[{"itemId":0,"options":[{"optionId":"123"}],"data":[{"itemName":"Test","id":"123","itemCost":"35"}],"images":["test.jpg"],"imageCount":1,"itemName":"Test","imageURLs":["test image"]}]'
}];

const eventListWithoutImage = [{
    'eventScope': 'public',
    'fee': 200,
    'eventType': 'fundraiser',
    'title': 'Community Cleanup Day',
    'isDeleted': 0,
    'details': {
        'venue': 'City Park',
        'startDateTime': '2023-11-10T09:00:00.000Z',
        'recurringFrequency': 'month',
        'details': 'Join us for a community cleanup day at City Park. Let\'s make our city cleaner and greener!',
        'isRecurring': true,
        'endDateTime': '2023-11-10T17:00:00.000Z'
    },
    'id': '54404ec7-15af-4ab8-afb3-8db63203e05e',
    'status': 'published',
    'organizationId': '5f5f7e5f7e5f7e5f7e5f7e5f',
    'products': '[{"itemId":0,"options":[{"optionId":"123"}],"data":[{"itemName":"Test","id":"123","itemCost":"35"}],"images":[],"itemName":"Test","imageURLs":[]}]'
}];
Utils.addCommonReqTokenForHMac(request);
describe('Add Fundraiser', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });
        TestCase.addEvent.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                    associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });
                request(process.env.BASE_URL)
                    .post('/fundraiser')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should handle error if token is not passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .post('/fundraiser')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and correct token is passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .post('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and is verified', (done) => {
            getStub.resolves({ status: 'active', isVerified: 0, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });
            request(process.env.BASE_URL)
                .post('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 423);
                    done();
                });
        });

        it('As a user I should validate the role before creating fundraiser', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });
            request(process.env.BASE_URL)
                .post('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 406);
                    done();
                });
        });

        it('As a user I should check if the pto is onboarded on the stripe for the paid fundraiser if stripeConnectAccountId is not present',
            (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP, paymentDetails: { stripeConnectAccountId: '' },
                    associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
                });

                sinon.stub(OrganizationMembers, 'get').resolves({
                    users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
                });

                const organizationInstance = {
                    id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                    paymentDetails: { stripeConnectAccountId: '' },
                    save: sinon.stub().callsFake(async function () {
                        this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                        return Promise.resolve(this);
                    }),
                    toJSON: function () {
                        return { paymentDetails: { stripeConnectAccountId: '' } };
                    }
                };

                const orgGetStub = sinon.stub(organizationModel, 'get');
                orgGetStub.resolves(organizationInstance);
                const createAccountStub = sinon.stub(Stripe, 'createAccount');
                createAccountStub.resolves({ id: 'mocked_account_id' });
                const createOnboardingLinkStub = sinon.stub(Stripe, 'createOnboardingLink');
                createOnboardingLinkStub.resolves({ url: 'mocked_onboarding_url' });
                sinon.stub(Fundraiser, 'create').resolves();

                request(process.env.BASE_URL)
                    .post('/fundraiser')
                    .set({ Authorization: requestPayloadUser.token })
                    .field('title', data.title)
                    .field('startDate', data.startDate)
                    .field('endDate', data.endDate)
                    .field('description', data.description)
                    .field('status', data.status)
                    .field('products', data.products)
                    .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                    .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.statusCode, 400);
                        orgGetStub.restore();
                        Fundraiser.create.restore();
                        done();
                    });
            });

        it('As a user I should check if the pto is onboarded on the stripe connect for the paid fundraiser and throw error if user is inactive',
            (done) => {
                getStub.resolves({
                    status: 'active', isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP, paymentDetails: {
                        stripeOnboardingStatus: 'inactive'
                    }, associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
                });

                const orgGetStub = sinon.stub(organizationModel, 'get');
                orgGetStub.resolves({
                    id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                    toJSON: () => { return { paymentDetails: { stripeOnboardingStatus: 'inactive' } }; }
                });

                sinon.stub(Fundraiser, 'create').resolves();

                request(process.env.BASE_URL)
                    .post('/fundraiser')
                    .set({ Authorization: requestPayloadUser.token })
                    .field('title', data.title)
                    .field('startDate', data.startDate)
                    .field('endDate', data.endDate)
                    .field('description', data.description)
                    .field('status', data.status)
                    .field('products', data.products)
                    .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                    .attach('imageURL', 'test/mock-data/valid_profile_pic.jpg')
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.statusCode, 400);
                        orgGetStub.restore();
                        Fundraiser.create.restore();
                        done();
                    });
            });

        it('As a user I should validate start date is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Fundraiser, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', '11/18/2000')
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('imageURL', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Fundraiser.create.restore();
                    done();
                });
        });

        it('As a user I should validate start time is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Fundraiser, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', 'abcd')
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('imageURL', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Fundraiser.create.restore();
                    done();
                });
        });

        it('As a user I should validate end date is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Fundraiser, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', '11/18/2000')
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('imageURL', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Fundraiser.create.restore();
                    done();
                });
        });

        it('As a user I should validate end time is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Fundraiser, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('imageURL', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Fundraiser.create.restore();
                    done();
                });
        });

        it('As a user I should validate start date time is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Fundraiser, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('imageURL', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Fundraiser.create.restore();
                    done();
                });
        });

        it('As a user I should validate end date time is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Fundraiser, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('imageURL', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Fundraiser.create.restore();
                    done();
                });
        });

        it('As a user I should validate that I am associated with an organization', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(null);
            sinon.stub(Fundraiser, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('imageURL', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Fundraiser.create.restore();
                    done();
                });
        });

        it('As a user I should validate that image is of correct format', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Fundraiser, 'create').resolves();
            request(process.env.BASE_URL)
                .post('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Fundraiser.create.restore();
                    done();
                });
        });

        it('As a user I should validate that documents are of correct format', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });
            sinon.stub(Fundraiser, 'create').resolves();
            const childOrganizationMapping = sinon.stub(childOrganizationMappingModel, 'query');
            childOrganizationMapping.withArgs('organizationId').returns({
                eq: () => ({
                    exec: () => Promise.resolve([{ childId: 123 }])
                })
            });
            request(process.env.BASE_URL)
                .post('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('productImages', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Fundraiser.create.restore();
                    done();
                });
        });

        it('As a user I should be able to add fundraiser without image', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Fundraiser, 'create').resolves({ ...eventList[0], status: 'published' });

            request(process.env.BASE_URL)
                .post('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .set('Content-Type', 'application/json')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgGetStub.restore();
                    Fundraiser.create.restore();
                    done();
                });
        });

        it('As a user I should be validate membershipBenefitAmount is number and is greater than 0', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Fundraiser, 'create').resolves({ ...eventList[0], status: 'published' });

            request(process.env.BASE_URL)
                .post('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .set('Content-Type', 'application/json')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .field('isMembershipEnabled', 'true')
                .field('membershipBenefitAmount', '0')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Fundraiser.create.restore();
                    done();
                });
        });

        it('As a user I should be able to add fundraiser with membership benefits enabled', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Fundraiser, 'create').resolves({ ...eventList[0], status: 'published' });

            request(process.env.BASE_URL)
                .post('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .set('Content-Type', 'application/json')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .field('isMembershipEnabled', 'true')
                .field('membershipBenefitAmount', '5')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgGetStub.restore();
                    Fundraiser.create.restore();
                    done();
                });
        });

        it('As a user I should be able to add fundraiser with image', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Fundraiser, 'create').resolves({ ...eventList[0], status: 'published' });

            request(process.env.BASE_URL)
                .post('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .set('Content-Type', 'application/json')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.productsWithoutImage)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgGetStub.restore();
                    Fundraiser.create.restore();
                    done();
                });
        });

        it('As a user I should be able to add fundraiser with documents', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            sinon.stub(Fundraiser, 'create').resolves(eventList[0]);

            request(process.env.BASE_URL)
                .post('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('productImages', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgGetStub.restore();
                    Fundraiser.create.restore();
                    done();
                });
        });


        it('As a user I should delete the uploaded image on S3 if fundraiser is not created successfully', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });

            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);
            request(process.env.BASE_URL)
                .post('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Edit Fundraiser', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should validate that fundraiser id is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            request(process.env.BASE_URL)
                .put('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', 'abcd')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('Fundraiser with provided fundraiser id must exist', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Fundraiser, 'get').resolves(null);

            request(process.env.BASE_URL)
                .put('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('imageURL', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 404);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a user I should be able to edit fundraiser with no deleted images or documents', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Fundraiser, 'get').resolves({ photoURL: '',
                organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', details: { documentURLs: new Set() }, save: () => {
                    return eventList[0];
                } });
            const organizationInstance = {
                id: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' },
                save: sinon.stub().callsFake(async function () {
                    this.paymentDetails.stripeConnectAccountId = 'mocked_account_id';
                    return Promise.resolve(this);
                }),
                toJSON: function () {
                    return { paymentDetails: { stripeConnectAccountId: '123', stripeOnboardingStatus: 'active' } };
                }
            };

            const orgGetStub = sinon.stub(organizationModel, 'get');
            orgGetStub.resolves(organizationInstance);

            request(process.env.BASE_URL)
                .put('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .attach('productImages', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a user I should be able to edit fundraiser with no images or documents', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Fundraiser, 'get').resolves({ photoURL: '',
                organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', details: { documentURLs: new Set() }, save: () => {
                    return eventList[0];
                } });

            request(process.env.BASE_URL)
                .put('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a user I should be able to edit fundraiser with image attached to incorrect key', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Fundraiser, 'get').resolves({ photoURL: '',
                organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', details: { documentURLs: new Set() }, save: () => {
                    return eventList[0];
                } });

            request(process.env.BASE_URL)
                .put('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.productsWithoutImage)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('imageURL', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a user I should check if fee is passed', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Fundraiser, 'get').resolves({ photoURL: '1.png', isPaid: true,
                details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });

            request(process.env.BASE_URL)
                .put('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a user I should check if fee passed is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Fundraiser, 'get').resolves({ photoURL: '1.png', isPaid: true,
                details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });

            request(process.env.BASE_URL)
                .put('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a user I should check if volunteer signup url is passed', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Fundraiser, 'get').resolves({ photoURL: '1.png', isPaid: true, volunteerRequired: true,
                details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });

            request(process.env.BASE_URL)
                .put('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a user I should validate start date is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Fundraiser, 'get').resolves({ photoURL: '1.png', isPaid: true, volunteerRequired: true,
                details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });
            request(process.env.BASE_URL)
                .put('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a user I should validate start time is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Fundraiser, 'get').resolves({ photoURL: '1.png', isPaid: true, volunteerRequired: true,
                details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });

            request(process.env.BASE_URL)
                .put('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a user I should validate end date is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Fundraiser, 'get').resolves({ photoURL: '1.png', isPaid: true, volunteerRequired: true,
                details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });

            request(process.env.BASE_URL)
                .put('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a user I should validate end time is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Fundraiser, 'get').resolves({ photoURL: '1.png', isPaid: true, volunteerRequired: true,
                details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });

            request(process.env.BASE_URL)
                .put('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a user I should validate start date time is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Fundraiser, 'get').resolves({ photoURL: '1.png', isPaid: true, volunteerRequired: true,
                details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });

            request(process.env.BASE_URL)
                .put('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a user I should validate end date time is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Fundraiser, 'get').resolves({ photoURL: '1.png', isPaid: true, volunteerRequired: true,
                details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });
            request(process.env.BASE_URL)
                .put('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('status', data.status)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a user I should validate status cannot be changed from published to unpublished', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Fundraiser, 'get').resolves({ photoURL: '1.png', isPaid: true, volunteerRequired: true,
                status: 'published', details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });
            request(process.env.BASE_URL)
                .put('/fundraiser')
                .set({ Authorization: requestPayloadUser.token })
                .field('eventId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('startDate', data.startDate)
                .field('endDate', data.endDate)
                .field('description', data.description)
                .field('products', data.products)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5f')
                .attach('image', 'test/mock-data/valid_profile_pic.jpg')
                .field('status', 'unpublished')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Fundraiser.get.restore();
                    done();
                });
        });


    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Get Fundraiser List', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get fundraiser list that are created under my organization', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            const queryStub = sinon.stub(Fundraiser, 'query');
            const usingStub = sinon.stub();
            const attributesStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: eqStub.withArgs('organizationId').returns({ using: usingStub }) });
            usingStub.returns({ attributes: attributesStub });
            attributesStub.returns({ exec: execStub });
            execStub.resolves(eventList);

            request(process.env.BASE_URL)
                .get('/fundraiser/list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    queryStub.restore();
                    done();
                });
        });

        it('As a user I should get fundraiser list that are created under my organization with the passed status', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', role: 'admin' }]
            });

            const queryStub = sinon.stub(Fundraiser, 'query');
            const usingStub = sinon.stub();
            const attributesStub = sinon.stub();
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: eqStub.withArgs('organizationId').returns({ using: usingStub }) });
            usingStub.returns({ attributes: attributesStub });
            attributesStub.returns({ where: whereStub.returns({ eq: eqStub.withArgs('draft').returns({ exec: execStub }) }) });
            execStub.resolves(eventList);

            request(process.env.BASE_URL)
                .get('/fundraiser/list?status=draft')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    queryStub.restore();
                    done();
                });
        });

        it('As a user I should get empty fundraiser list if none exists', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            const queryStub = sinon.stub(Fundraiser, 'query');
            const usingStub = sinon.stub();
            const attributesStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: eqStub.withArgs('organizationId').returns({ using: usingStub }) });
            usingStub.returns({ attributes: attributesStub });
            attributesStub.returns({ exec: execStub });
            execStub.resolves([]);

            request(process.env.BASE_URL)
                .get('/fundraiser/list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    queryStub.restore();
                    done();
                });
        });

        it('As a user I should fundraiser list for an organization', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', role: 'admin' }]
            });

            const queryStub = sinon.stub(Fundraiser, 'query');
            const usingStub = sinon.stub();
            const attributesStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: eqStub.withArgs('organizationId').returns({ using: usingStub }) });
            usingStub.returns({ attributes: attributesStub });
            attributesStub.returns({ exec: execStub });
            execStub.resolves([]);

            request(process.env.BASE_URL)
                .get('/fundraiser/list?organizationId=5f5f7e5f7e5f7e5f7e5f7e5f')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    queryStub.restore();
                    done();
                });
        });

        it('As a user I should fundraiser list for an organization with the passed status', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', role: 'admin' }]
            });

            const queryStub = sinon.stub(Fundraiser, 'query');
            const usingStub = sinon.stub();
            const attributesStub = sinon.stub();
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: eqStub.withArgs('organizationId').returns({ using: usingStub }) });
            usingStub.returns({ attributes: attributesStub });
            attributesStub.returns({ where: whereStub.returns({ eq: eqStub.withArgs('draft').returns({ exec: execStub }) }) });
            execStub.resolves(eventList);

            request(process.env.BASE_URL)
                .get('/fundraiser/list?status=draft&organizationId=5f5f7e5f7e5f7e5f7e5f7e5f')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    queryStub.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a user I should get error is passed organizationId is not associated with me', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5a', role: 'admin' }]
            });

            sinon.stub(OrganizationMembers, 'get').resolves(null);

            const queryStub = sinon.stub(Fundraiser, 'query');
            const usingStub = sinon.stub();
            const attributesStub = sinon.stub();
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: eqStub.withArgs('organizationId').returns({ using: usingStub }) });
            usingStub.returns({ attributes: attributesStub });
            attributesStub.returns({ where: whereStub.returns({ eq: eqStub.withArgs('draft').returns({ exec: execStub }) }) });
            execStub.resolves(eventList);

            request(process.env.BASE_URL)
                .get('/fundraiser/list?status=draft&organizationId=5f5f7e5f7e5f7e5f7e5f7e5f')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    queryStub.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Handle Error in getting list', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get error if something went wrong', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            const queryStub = sinon.stub();
            const inStub = sinon.stub();
            const attributesStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: inStub });
            inStub.returns({ attributes: attributesStub });
            attributesStub.returns({ exec: execStub });
            execStub.rejects();
            sinon.replace(Fundraiser, 'query', queryStub);

            request(process.env.BASE_URL)
                .get('/fundraiser/list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Get Fundraiser Details', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should validate the eventId passed', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            request(process.env.BASE_URL)
                .get('/fundraiser?eventId=abcd')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error message if fundraiser doesnt exists', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(Fundraiser, 'get').resolves(null);

            request(process.env.BASE_URL)
                .get('/fundraiser?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a user I should get the fundraiser details', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(Fundraiser, 'get').resolves(eventListWithoutImage[0]);

            request(process.env.BASE_URL)
                .get('/fundraiser?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a user I should get the fundraiser details with signed urls of image and documents', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(Fundraiser, 'get').resolves(eventList[0]);

            request(process.env.BASE_URL)
                .get('/fundraiser?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Fundraiser.get.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a user I should get error message if fundraiser doesn\'t exists, is deleted or if it doesn\'t belong to organization id',
            (done) => {
                getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                    associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5' }] });

                sinon.stub(OrganizationMembers, 'get').resolves(null);

                sinon.stub(Fundraiser, 'get').resolves(eventList[0]);

                request(process.env.BASE_URL)
                    .get('/fundraiser?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                    .set({ Authorization: requestPayloadUser.token })
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.statusCode, 400);
                        Fundraiser.get.restore();
                        done();
                    });
            });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Publish Fundraiser', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should be able to publish fundraiser', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            sinon.stub(Fundraiser, 'get').resolves({ ...eventList[0], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .patch('/fundraiser/publish?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a user I should get message if fundraiser is already published', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(Fundraiser, 'get').resolves({ ...eventListWithoutImage[0], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .patch('/fundraiser/publish?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Fundraiser.get.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a user I should get error message if I am not associated with organization', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5a' }] });

            sinon.stub(Fundraiser, 'get').resolves({ ...eventListWithoutImage[0], save: () => Promise.resolve() });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'deleted' }]
            });

            request(process.env.BASE_URL)
                .patch('/fundraiser/publish?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a user I should get error if fundraiser doesn\'t exists, is deleted or if it doesn\'t belong to organization id', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5' }] });

            sinon.stub(Fundraiser, 'get').resolves(null);

            request(process.env.BASE_URL)
                .patch('/fundraiser/publish?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Fundraiser.get.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Delete Fundraiser', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get be able to delete fundraiser', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            sinon.stub(Fundraiser, 'get').resolves({ ...eventList[0], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .delete('/fundraiser?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Fundraiser.get.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As an editor I should delete fundraiser', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: 'editor', status: 'active' }]
            });

            sinon.stub(Fundraiser, 'get').resolves({ ...eventList[0], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .delete('/fundraiser?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a editor I should get error message if I try to delete published fundraiser', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(Fundraiser, 'get').resolves({ ...eventListWithoutImage[0], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .delete('/fundraiser?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 403);
                    Fundraiser.get.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a admin I should get error message if fundraiser is already published', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            sinon.stub(Fundraiser, 'get').resolves({ ...eventListWithoutImage[0], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .delete('/fundraiser?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a user I should get error if fundraiser doesn\'t exists, is deleted or if it doesn\'t belong to organization id', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(Fundraiser, 'get').resolves({ ...eventListWithoutImage[0], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .delete('/fundraiser?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a user I should get error I doesnt belong to that organization while deleting fundraiser', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5a' }] });

            sinon.stub(Fundraiser, 'get').resolves({ ...eventListWithoutImage[0], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .delete('/fundraiser?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Fundraiser.get.restore();
                    done();
                });
        });

        it('As a user I should get error if fundraiser doesnt exists', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5a' }] });

            sinon.stub(Fundraiser, 'get').resolves(null);

            request(process.env.BASE_URL)
                .delete('/fundraiser?eventId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Fundraiser.get.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Get List of Fundraiser participants', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get error in get participants list', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const eventGetStub = sinon.stub(Fundraiser, 'get');
            eventGetStub.resolves();
            request(process.env.BASE_URL)
                .get('/fundraiser/participant?eventId=123')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    eventGetStub.restore();
                    done();
                });
        });

        it('As a user I should get fundraiser participant list that are created under my organization', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            const queryStub = sinon.stub(FundraiserSignup, 'query');
            const whereStub = sinon.stub();
            const usingStub = sinon.stub();
            const eqStub = sinon.stub();
            const inStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({ eq: eqStub.withArgs('eventId').returns({ using: usingStub }) });
            usingStub.returns({ where: whereStub });
            whereStub.withArgs('organizationId').returns({ eq: inStub.withArgs(['5f5f7e5f7e5f7e5f7e5f7e5f']).returns({ exec: execStub }) });
            const eventList = [
                { id: 'event1', eventId: '123', organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                    paymentDetails: {}, parentId: 'parent1', childId: 'child1' },
                { id: 'event2', eventId: '123', organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                    paymentDetails: { paymentType: 'cheque' }, parentId: 'parent2', childId: 'child2' }
            ];
            execStub.resolves(eventList);

            const userQueryStub = sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ firstName: 'user', lastName: 'Name' }])
                    })
                }).withArgs('parent1').returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ firstName: 'user1', lastName: 'Name1' }])
                    })
                }).withArgs('parent2').returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ firstName: 'user2', lastName: 'Name2' }])
                    })
                })
            });

            sinon.stub(organizationModel, 'batchGet').resolves([{ id: 'organizationId', name: 'Test' }]);

            const eventGetStub = sinon.stub(Fundraiser, 'get');
            eventGetStub
                .resolves({ id: '123', title: 'Fundraiser Title', eventType: 'Some Type', eventScope: 'Some Scope', status: 'Active', membershipBenefitDetails: {
                    benefitDiscount: 10, isOnlyForMembers: true
                } });

            sinon.stub(Children, 'batchGet').resolves(
                [
                    { id: 'child1', name: 'Child Name 1' },
                    { id: 'child1', name: 'Child Name 1', homeRoom: '121456' },
                    { id: 'child2', name: 'Child Name 2' }
                ]);

            request(process.env.BASE_URL)
                .get('/fundraiser/participant?eventId=123')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    queryStub.restore();
                    eventGetStub.restore();
                    userQueryStub.restore();
                    organizationModel.batchGet.restore();
                    done();
                });
        });

        it('As a user I should get empty fundraiser participant list that are created under my organization', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });

            const queryStub = sinon.stub(FundraiserSignup, 'query');
            const usingStub = sinon.stub();
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const inStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({ eq: eqStub.withArgs('eventId').returns({ using: usingStub }) });
            usingStub.returns({ where: whereStub });
            whereStub.withArgs('organizationId').returns({ eq: inStub.withArgs(['5f5f7e5f7e5f7e5f7e5f7e5f']).returns({ exec: execStub }) });
            const eventList = [];
            execStub.resolves(eventList);
            const eventGetStub = sinon.stub(Fundraiser, 'get');
            eventGetStub
                .resolves({ id: '123', title: 'Fundraiser Title', eventType: 'Some Type', eventScope: 'Some Scope', status: 'Active' });
            request(process.env.BASE_URL)
                .get('/fundraiser/participant?eventId=123')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    queryStub.restore();
                    eventGetStub.restore();
                    done();
                });
        });

        it('As a user I should get error if eventId is not valid', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5ff' }]
            });
            const queryStub = sinon.stub(FundraiserSignup, 'query');
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const inStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({ eq: eqStub.withArgs('eventId').returns({ where: whereStub }) });
            whereStub.withArgs('organizationId').returns({ eq: inStub.withArgs(['5f5f7e5f7e5f7e5f7e5f7e5f']).returns({ exec: execStub }) });
            const eventList = [];
            execStub.rejects(eventList);
            const eventGetStub = sinon.stub(Fundraiser, 'get');
            eventGetStub
                .resolves({ id: '123', title: 'Fundraiser Title', eventType: 'Some Type', eventScope: 'Some Scope', status: 'Active' });
            request(process.env.BASE_URL)
                .get('/fundraiser/participant?eventId=123')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    eventGetStub.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Update payment status of Fundraiser participants', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a PTO admin I should update status of participants if that is paid via cash', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });
            const scanStub = sinon.stub(FundraiserSignup, 'get');
            scanStub.returns({
                organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: {
                    paymentStatus: 'pending', paymentType: 'cash'
                }, save: () => Promise.resolve()
            });

            const userQueryStub = sinon.stub(User, 'query');
            userQueryStub.returns({
                eq: () => ({
                    exec: () => ({
                        toJSON: () => [{
                            email: '<EMAIL>'
                        }]
                    })
                })
            });

            const childrenQueryStub = sinon.stub(Children, 'get').resolves({
                firstName: 'John',
                lastName: 'Doe'
            });

            const eventQueryStub = sinon.stub(Fundraiser, 'get').resolves({
                title: 'Fundraiser Title',

                startDate: '2023-11-16T12:00:00Z',
                endDate: '2023-11-16T14:00:00Z',


                save: () => Promise.resolve()
            });

            request(process.env.BASE_URL)
                .put('/fundraiser/signup-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: '123', status: 'approved' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    scanStub.restore();
                    userQueryStub.restore();
                    childrenQueryStub.restore();
                    eventQueryStub.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a PTO admin I should update status of participants if that is paid via cash and fundraiser type is membership', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });
            const scanStub = sinon.stub(FundraiserSignup, 'get');
            scanStub.returns({
                organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: {
                    paymentStatus: 'pending', paymentType: 'cash'
                },
                purchasedProducts: JSON.stringify([{ membershipType: CONSTANTS.MEMBERSHIP_TYPES.CHILD }]),
                save: () => Promise.resolve()
            });

            const userQueryStub = sinon.stub(User, 'query');
            userQueryStub.returns({
                eq: () => ({
                    exec: () => ([{
                        email: '<EMAIL>',
                        save: () => {}
                    }])
                })
            });

            const childrenQueryStub = sinon.stub(Children, 'get').resolves({
                firstName: 'John',
                lastName: 'Doe',
                save: () => {}
            });

            const eventQueryStub = sinon.stub(Fundraiser, 'get').resolves({
                title: 'Fundraiser Title',
                fundraiserType: CONSTANTS.FUNDRAISER_TYPES.MEMBERSHIP,
                startDate: '2023-11-16T12:00:00Z',
                endDate: '2023-11-16T14:00:00Z',
                save: () => Promise.resolve()
            });

            request(process.env.BASE_URL)
                .put('/fundraiser/signup-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: '123', status: 'approved' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    scanStub.restore();
                    userQueryStub.restore();
                    childrenQueryStub.restore();
                    eventQueryStub.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a PTO admin I should update status of participants if that is paid via cash and fundraiser type is family membership', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{
                    role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f'
                }]
            });
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });
            const scanStub = sinon.stub(FundraiserSignup, 'get');
            scanStub.returns({
                organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: {
                    paymentStatus: 'pending', paymentType: 'cash'
                },
                purchasedProducts: JSON.stringify([{ membershipType: CONSTANTS.MEMBERSHIP_TYPES.FAMILY }]),
                save: () => Promise.resolve()
            });

            const userQueryStub = sinon.stub(User, 'query');
            userQueryStub.returns({
                eq: () => ({
                    exec: () => ([{
                        email: '<EMAIL>',
                        membershipsPurchased: [{ organizationId: 'org-id', membershipType: 'family', startDate: '11/18/2000', endDate: '11/18/2027' }],
                        save: () => {}
                    }])
                })
            });

            const childrenQueryStub = sinon.stub(Children, 'get').resolves({
                firstName: 'John',
                lastName: 'Doe'
            });

            const eventQueryStub = sinon.stub(Fundraiser, 'get').resolves({
                title: 'Fundraiser Title',
                fundraiserType: CONSTANTS.FUNDRAISER_TYPES.MEMBERSHIP,
                startDate: '2023-11-16T12:00:00Z',
                endDate: '2023-11-16T14:00:00Z',
                save: () => Promise.resolve()
            });

            request(process.env.BASE_URL)
                .put('/fundraiser/signup-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: '123', status: 'approved' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    scanStub.restore();
                    userQueryStub.restore();
                    childrenQueryStub.restore();
                    eventQueryStub.restore();
                    done();
                });
        });

        it('As a PTO admin I should update status of participants if that is paid via cheque', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const scanStub = sinon.stub(FundraiserSignup, 'get');
            scanStub.returns({
                organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: {
                    paymentStatus: 'pending', paymentType: 'cheque'
                }, save: () => Promise.resolve()
            });

            const userQueryStub = sinon.stub(User, 'query');
            userQueryStub.returns({
                eq: () => ({
                    exec: () => ({
                        toJSON: () => [{
                            email: '<EMAIL>'
                        }]
                    })
                })
            });

            const childrenQueryStub = sinon.stub(Children, 'get').resolves({
                firstName: 'John',
                lastName: 'Doe'
            });

            const eventQueryStub = sinon.stub(Fundraiser, 'get').resolves({
                title: 'Fundraiser Title',
                details: {
                    startDateTime: '2023-11-16T12:00:00Z',
                    endDateTime: '2023-11-16T14:00:00Z',
                    venue: 'Fundraiser Venue'
                },
                save: () => Promise.resolve()
            });

            request(process.env.BASE_URL)
                .put('/fundraiser/signup-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: '123', status: 'approved' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    scanStub.restore();
                    userQueryStub.restore();
                    childrenQueryStub.restore();
                    eventQueryStub.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a PTO editor I should get error if I try to update status of participants', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5a', role: 'admin' }]
            });
            const scanStub = sinon.stub(FundraiserSignup, 'get');
            scanStub.returns({
                organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: {
                    paymentStatus: 'pending', paymentType: 'cheque'
                }
            });
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: 'editor', status: 'active' }]
            });
            request(process.env.BASE_URL)
                .put('/fundraiser/signup-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: '123', status: 'approved' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 403);
                    scanStub.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a PTO admin I should get error if associated org not found', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5a', role: 'admin' }]
            });
            const scanStub = sinon.stub(FundraiserSignup, 'get');
            scanStub.returns({
                organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: {
                    paymentStatus: 'pending', paymentType: 'cheque'
                }
            });
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: 'super admin', status: 'active' }]
            });
            request(process.env.BASE_URL)
                .put('/fundraiser/signup-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: '123', status: 'approved' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    scanStub.restore();
                    done();
                });
        });

        it('As a PTO admin I should get error if participant already paid for the fundraiser', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const scanStub = sinon.stub(FundraiserSignup, 'get');
            scanStub.returns({
                organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: {
                    paymentStatus: 'approved', paymentType: 'cheque'
                }
            });
            request(process.env.BASE_URL)
                .put('/fundraiser/signup-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: '123', status: 'approved' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    scanStub.restore();
                    done();
                });
        });

        it('As a PTO admin I should get error if fundraiser doesnt exists', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const eventGetStub = sinon.stub(FundraiserSignup, 'get');
            eventGetStub.returns(null);
            request(process.env.BASE_URL)
                .put('/fundraiser/signup-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: '123', status: 'approved' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    eventGetStub.restore();
                    done();
                });
        });


        it('As a PTO admin I should throw error if payment type was stripe', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const scanStub = sinon.stub(FundraiserSignup, 'get');
            scanStub.returns({
                organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: {
                    paymentStatus: 'pending', paymentType: 'stripe'
                }, save: () => Promise.resolve()
            });

            request(process.env.BASE_URL)
                .put('/fundraiser/signup-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: '123', status: 'pending' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    scanStub.restore();
                    done();
                });
        });

        it('As a PTO admin I should get error if status passed is not from allowed list', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });

            request(process.env.BASE_URL)
                .put('/fundraiser/signup-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: '123', status: 'register' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('update fundraiser fulfilled status', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('should get an error if fundraiser signup id is not valid as a user', (done) => {
            getStub.resolves({
                status: 'active',
                id: 1,
                isVerified: 1,
                isDeleted: 0,
                role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const fundraiserSignupGetStub = sinon.stub(FundraiserSignup, 'get');
            fundraiserSignupGetStub.returns(null);
            request(process.env.BASE_URL)
                .patch('/fundraiser/signup-fulfilled-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ fundraiserSignupId: '123' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    fundraiserSignupGetStub.restore();
                    done();
                });
        });

        it('should get an error if fundraiser signup is already fulfilled as a user', (done) => {
            getStub.resolves({
                status: 'active',
                id: 1,
                isVerified: 1,
                isDeleted: 0,
                role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const fundraiserSignupGetStub = sinon.stub(FundraiserSignup, 'get');
            fundraiserSignupGetStub.returns({
                isFulfilled: true
            });
            request(process.env.BASE_URL)
                .patch('/fundraiser/signup-fulfilled-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ fundraiserSignupId: '123' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    fundraiserSignupGetStub.restore();
                    done();
                });
        });

        it('should get an error if fundraiser signup is not approved as a user', (done) => {
            getStub.resolves({
                status: 'active',
                id: 1,
                isVerified: 1,
                isDeleted: 0,
                role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const fundraiserSignupGetStub = sinon.stub(FundraiserSignup, 'get');
            fundraiserSignupGetStub.returns({
                paymentDetails: {
                    paymentStatus: 'pending'
                }
            });
            request(process.env.BASE_URL)
                .patch('/fundraiser/signup-fulfilled-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ fundraiserSignupId: '123' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    fundraiserSignupGetStub.restore();
                    done();
                });
        });

        it('should get an error if user does not have admin or super admin access', (done) => {
            getStub.resolves({
                status: 'active',
                id: 1,
                isVerified: 1,
                isDeleted: 0,
                role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.MEMBER, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const fundraiserSignupGetStub = sinon.stub(FundraiserSignup, 'get');
            fundraiserSignupGetStub.returns({
                organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f',
                paymentDetails: {
                    paymentStatus: 'approved'
                }
            });
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: 'member', status: 'active' }]
            });
            request(process.env.BASE_URL)
                .patch('/fundraiser/signup-fulfilled-status')
                .set({ Authorization: requestPayloadUser.token })
                .send({ fundraiserSignupId: '123' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 403);
                    fundraiserSignupGetStub.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('search fundraisers', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get error if search query is not valid', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3 });
            request(process.env.BASE_URL)
                .get('/fundraiser/search?searchQuery=123')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get the search results for the query passed', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1, children: ['child-id1'] });

            sinon.stub(AwsOpenSearchService, 'getAllChildEvents').resolves([{
                _source: { id: 'child-id1', childEvents: ['event-id1'], childCalendarEvents: ['event-id2'], school: '123', homeRoom: '222' }
            }]);

            sinon.stub(AwsOpenSearchService, 'getAllEvents').resolves([{
                _source: { id: 'event-id1', details: { startDateTime: '2023-11-10T09:00:00.000Z' } }
            }]);

            sinon.stub(organizationModel, 'get').resolves({ name: 'org-name' });

            request(process.env.BASE_URL)
                .get('/fundraiser/search?query=Event')
                .set({ Authorization:
                    requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    AwsOpenSearchService.getAllChildEvents.restore();
                    AwsOpenSearchService.getAllEvents.restore();
                    organizationModel.get.restore();
                    done();
                });
        });

        it('As a user I should get the search results for the query passed with childId passed', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1, children: ['child-id1'] });

            sinon.stub(AwsOpenSearchService, 'getAllChildEvents').resolves([{
                _source: {
                    id: 'child-id1', childEvents: ['event-id1'], photoURL: 'test-url.com',
                    childCalendarEvents: ['event-id2'], school: '123'
                }
            }]);

            sinon.stub(AwsOpenSearchService, 'getAllEvents').resolves([{
                _source: { id: 'event-id1', details: { startDateTime: '2023-11-10T09:00:00.000Z' } }
            }]);

            sinon.stub(organizationModel, 'get').resolves({ name: 'org-name' });

            request(process.env.BASE_URL)
                .get('/fundraiser/search?query=Event&childId=child-id1')
                .set({ Authorization:
                    requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    AwsOpenSearchService.getAllChildEvents.restore();
                    AwsOpenSearchService.getAllEvents.restore();
                    organizationModel.get.restore();
                    done();
                });
        });

        it('As a user I should get empty array if there is no childEvents and childCalendarEvents', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1, children: ['child-id1'] });

            sinon.stub(AwsOpenSearchService, 'getAllChildEvents').resolves([{
                _source: {
                    id: 'child-id1', photoURL: 'test-url.com', school: '123'
                }
            }]);

            sinon.stub(AwsOpenSearchService, 'getAllEvents').resolves([{
                _source: { id: 'event-id1', details: { startDateTime: '2023-11-10T09:00:00.000Z' } }
            }]);

            sinon.stub(organizationModel, 'get').resolves({ name: 'org-name' });

            request(process.env.BASE_URL)
                .get('/fundraiser/search?query=Event&childId=child-id1')
                .set({ Authorization:
                    requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    AwsOpenSearchService.getAllChildEvents.restore();
                    AwsOpenSearchService.getAllEvents.restore();
                    organizationModel.get.restore();
                    done();
                });
        });

        it('As a user I should get the search results for the query passed returning empty array', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1, children: ['child-id1'] });

            request(process.env.BASE_URL)
                .get('/fundraiser/search?query=Event&childId=child-id1')
                .set({ Authorization:
                    requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Other functions', () => {
    try {
        it('Should return error if event is not valid', async () => {
            const event = {
                title: 'Test Event',
                description: 'Test Description',
                startDate: '2023-11-16T12:00:00Z',
                endDate: '2023-11-16T14:00:00Z',
                venue: 'Test Venue',
                eventType: 'Test Type',
                eventScope: 'Test Scope',
                status: 'published'
            };
            try {
                await FundraiserService.validateEvent(event, 'unpublished');
            } catch (error) {
                expect(error.message).to.eq('Published fundraiser status cannot be changed from published to unpublished.');
            }
        });

        it('should delete document from s3 bucket', async () => {
            const event = {
                title: 'Test Event',
                description: 'Test Description',
                startDate: '2023-11-16T12:00:00Z',
                endDate: '2023-11-16T14:00:00Z',
                venue: 'Test Venue',
                eventType: 'Test Type',
                eventScope: 'Test Scope',
                status: 'published',
                details: {
                    documentURLs: new Set(['test.pdf'])
                }
            };
            await FundraiserService.handleDocumentDeletion('["test.pdf"]', event, []);
        });

        it('should send email to user if event is published', async () => {
            await FundraiserService.sendEventRegistrationMail({
                childName: 'test',
                eventName: 'Test',
                eventStartDate: '2023-11-16T12:00:00Z',
                eventEndDate: '2023-11-16T14:00:00Z',
                userEmail: '<EMAIL>'
            }, 'eventRegistration.html',
            'Event Registration for Test');
        });

        it('should parse string to number', () => {
            const number = FundraiserService.parseNumber('123');
            expect(number).to.equal(123);
        });

        it('should return number if it is a number', () => {
            const number = FundraiserService.parseNumber(123);
            expect(number).to.equal(123);
        });

        it('should return undefined if the value is null or undefined', () => {
            const number = FundraiserService.parseNumber(null);
            expect(number).to.equal(undefined);
        });

        it('should parse string to boolean', () => {
            const boolean = FundraiserService.parseBoolean('true');
            expect(boolean).to.equal(true);
        });

        TestCase.otherFunctions.forEach((data) => {
            it(data.it, () => {
                try {
                    new FundraiserValidator(data.options).validate();
                } catch (error) {
                    expect(error.statusCode).to.equal(400);
                }
            });
        });

        it('should validate if bannerImageName is passed and fundraiserType is booster', () => {
            new FundraiserValidator({
                title: 'test',
                description: 'test',
                startDate: '2023-11-16T12:00:00Z',
                endDate: '2023-11-16T14:00:00Z',
                bannerImageName: 'bannerImageName',
                fundraiserType: 'booster',
                boosterGoal: 100
            }).validate();
        });

        it('should validate multiple events', () => {
            new FundraiserValidator({
                title: 'test',
                description: 'test',
                startDate: '2023-11-16T12:00:00Z',
                endDate: '2023-11-16T14:00:00Z'
            }).validateMultiEvents();
        });

        TestCase.validateFileType.forEach((data) => {
            it(data.it, () => {
                try {
                    new FundraiserValidator({}).fileType(data.options);
                } catch (error) {
                    expect(error.statusCode).to.equal(400);
                }
            });
        });

        it('should validate fileType', () => {
            new FundraiserValidator({}).fileType([
                {
                    fieldname: 'image',
                    mimetype: 'image/png'
                },
                {
                    fieldname: 'documents',
                    mimetype: 'application/pdf'
                }
            ]);
        });

        TestCase.isStringEmpty.forEach((data) => {
            it(data.it, () => {
                const result = Utils.isEmpty(data.options);
                expect(result).to.equal(data.response);
            });
        });

        it('should validate if the passed value is number', () => {
            try {
                new Validator().number('test', 'test');
            } catch (error) {
                expect(error).to.not.be.null;
            }
        });

        it('should validate if param is passed', () => {
            try {
                new Validator().param(undefined, 'test');
            } catch (error) {
                expect(error).to.not.be.null;
            }
        });

        it('should validate if the passed value is boolean', () => {
            try {
                new Validator().boolean('', 'test');
            } catch (error) {
                expect(error).to.not.be.null;
            }
        });

        it('should validate if passed value is true', () => {
            try {
                new Validator().boolean(true, 'test');
            } catch (error) {
                expect(error).to.not.be.null;
            }
        });

        it('should validate if value passed is from the enums', () => {
            try {
                new Validator().enum('test', ['test', 'test2'], 'test');
            } catch (error) {
                expect(error).to.not.be.null;
            }
        });

        it('should throw error if value passed is not from the enums', () => {
            try {
                new Validator().enum('test3', ['test', 'test2'], 'test');
            } catch (error) {
                expect(error).to.not.be.null;
            }
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Generate Presigned Url', () => {
    const queryParams = {
        fileName: 'fileName',
        organizationId: 'organizationId'
    };
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.generatePresignedUrl.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                    accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
                });

                request(process.env.BASE_URL)
                    .get('/fundraiser/generate-presigned-url')
                    .set({ Authorization: requestPayloadUser.token })
                    .query(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        done();
                    });
            });
        });

        it('should return presigned url', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP
            });

            sinon.stub(FundraiserService, 'validateOrganizationAssociation').resolves();

            request(process.env.BASE_URL)
                .get('/fundraiser/generate-presigned-url')
                .set({ Authorization: requestPayloadUser.token })
                .query(queryParams)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
