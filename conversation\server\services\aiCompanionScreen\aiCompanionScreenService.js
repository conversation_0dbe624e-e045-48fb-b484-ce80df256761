/**
 * This file contains service for AI Companion Screen.
 * <AUTHOR>
 * @since 12/06/2025
 * @name aiCompanionScreenService
 */

const AiCompanionScreenFeedbackModel = require('../../models/aiCompanionScreenFeedback.model');
const AiCompanionScreenValidator = require('./aiCompanionScreenValidator');
const Utils = require('../../util/utilFunctions');
const CONSTANTS = require('../../util/constants');

/**
 * Class represents service for AI Companion Screen.
 */
class AiCompanionScreenService {
    /**
     * @desc This function is being used to give feedback for AI Companion Screen
     * <AUTHOR>
     * @since 12/06/2025
     * @param {Object} req Request
     * @param {Object} user User
     * @param {Function} locale Translation function
     * @returns {Object} Data
     */
    static async addUpdateAiCompanionScreenFeedback (req, user, locale) {
        const validator = new AiCompanionScreenValidator({ body: req.body, locale });
        validator.validateAddUpdateAiCompanionScreenFeedback();

        const {
            aiResponseMessageId,
            userQueryMessageId,
            userQueryMessage,
            aiResponseMessage,
            aiResponseMessageContexts,
            feedback
        } = req.body;

        const aiCompanionScreenFeedback = await AiCompanionScreenFeedbackModel.get({
            aiResponseMessageId,
            userId: user.id
        });

        if (aiCompanionScreenFeedback) {
            aiCompanionScreenFeedback.feedback = feedback;
            await Utils.addMetricForAccuracy(feedback);
            return await aiCompanionScreenFeedback.save();
        }

        await Utils.addMetricForAccuracy(feedback);

        return await AiCompanionScreenFeedbackModel.create({
            userId: user.id,
            aiResponseMessageId,
            userQueryMessageId,
            userQueryMessage,
            aiResponseMessage,
            aiResponseMessageContexts,
            feedback
        });
    }

    /**
     * @desc This function is being used to get page size
     * <AUTHOR>
     * @since 12/06/2025
     * @param {String} pageSize Page Size
     * @returns {Number} Page
     */
    static getPageSize (pageSize) {
        return isNaN(Number(pageSize)) ? CONSTANTS.DEFAULT_PAGE_SIZE : Number(pageSize);
    }

    /**
     * @desc This function is being used to get feedback list for AI Companion Screen
     * <AUTHOR>
     * @since 12/06/2025
     * @param {Object} req Request
     * @param {Object} user User
     * @param {Function} locale Translation function
     * @returns {Object} Data
     */
    static async getAiCompanionScreenFeedbackList (req, user, locale) {
        const validator = new AiCompanionScreenValidator({ query: req.query, locale });
        validator.validateGetAiCompanionScreenFeedbackList();

        const { startAiResponseMessageId, pageSize } = req.query;
        const userId = user.id;
        const page = this.getPageSize(pageSize);

        const query = AiCompanionScreenFeedbackModel
            .query('userId')
            .eq(userId)
            .using('userId-index')
            .limit(page);

        if (startAiResponseMessageId) {
            query.startAt({
                aiResponseMessageId: startAiResponseMessageId,
                userId
            });
        }

        const data = await query.exec();

        if (data.length === 0) {
            return {
                startAiResponseMessageId: null,
                feedbackList: []
            };
        }

        const formattedFeedbackList = this.formatFeedbackResponse(data);

        let nextStartAt = null;

        if (data.lastKey) {
            nextStartAt = {
                aiResponseMessageId: data.lastKey.aiResponseMessageId,
                userId: data.lastKey.userId
            };
        }

        return {
            startAiResponseMessageId: nextStartAt?.aiResponseMessageId ?? null,
            feedbackList: formattedFeedbackList
        };
    }

    /**
     * @desc This function is being used to format feedback response
     * <AUTHOR>
     * @since 12/06/2025
     * @param {Array} feedbackList Feedback List
     * @returns {Object} Data
     */
    static formatFeedbackResponse (feedbackList) {
        const formattedFeedbackList = [];

        for (const feedback of feedbackList) {
            formattedFeedbackList.push({
                userQueryMessage: {
                    id: feedback.userQueryMessageId,
                    senderId: feedback.userId,
                    message: feedback.userQueryMessage
                },
                aiResponseMessage: {
                    id: feedback.aiResponseMessageId,
                    senderId: CONSTANTS.AI_COMPANION_SCREEN_SENDER_ID,
                    message: feedback.aiResponseMessage,
                    contexts: feedback.aiResponseMessageContexts,
                    userQueryMessageId: feedback.userQueryMessageId
                },
                feedback: feedback.feedback
            });
        }

        return formattedFeedbackList;
    }
}

module.exports = AiCompanionScreenService;
