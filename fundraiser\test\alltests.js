const dotenv = require('dotenv');
const env = process.env.NODE_ENV || 'testing';
dotenv.config({ path: process.env.PWD + '/' + env + '.env' });
global.logger = require('../server/util/logger');
const chai = require('chai');
const chaiHttp = require('chai-http');
const app = require('../index');
chai.use(chaiHttp);
const request = require('supertest');
request(app);

// Event testing
require('../server/services/fundraiser/test/fundraiser.test');
require('../server/services/fundraiser/donation/orgManaged/test/orgFundraiserBooster.test');
// Register service
require('../server/services/register/test/register.test');

// utils test
require('../server/util/test/stripe.test');
require('../server/util/test/server.test');

describe('Stop server in end', () => {
    it('Server should stop manually to get code coverage', done => {
        app.close();
        done();
    });
});
