const EventService = require('./eventService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for event routes.
 */
class EventController {
    /**
   * @desc This function is being used to add event
   * <AUTHOR>
   * @since 08/11/2023
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async addEvent (req, res) {
        try {
            const data = await EventService.addEvent(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.ADD_EVENT_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in add event: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
    /**
   * @desc This function is being used to add multiple events
   * <AUTHOR>
   * @since 18/01/2024
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async addMultipleEvent (req, res) {
        try {
            const data = await EventService.addMultipleEvent(
                req,
                res.locals.user,
                res.__
            );
            Utils.sendResponse(null, data, res, MESSAGES.ADD_EVENT_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in add event', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to update event
   * <AUTHOR>
   * @since 16/11/2023
   * @param {Object} req Request
   * @param {Object} res Response
   */
    static async updateEvent (req, res) {
        try {
            const data = await EventService.updateEvent(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.EVENT_UPDATE_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in update event', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to get list of events
   * <AUTHOR>
   * @since 15/11/2023
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async getEventList (req, res) {
        try {
            const data = await EventService.getEventList(req, res.locals.user);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get event list', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to get list of events of participant
   * <AUTHOR>
   * @since 20/11/2023
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async getParticipantEventList (req, res) {
        try {
            const data = await EventService.getParticipantEventList(
                req,
                res.locals.user,
                res.__
            );
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in getting participant list', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to get event details
   * <AUTHOR>
   * @since 15/11/2023
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async getEventDetails (req, res) {
        try {
            const data = await EventService.getEventDetails(
                req,
                res.locals.user,
                res.__
            );
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get event details', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to update status of event to published
   * <AUTHOR>
   * @since 15/11/2023
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async publishEvent (req, res) {
        try {
            const data = await EventService.publishEvent(
                req,
                res.locals.user,
                res.__
            );
            Utils.sendResponse(null, data, res, MESSAGES.EVENT_PUBLISH_SUCCESS);
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to delete event
   * <AUTHOR>
   * @since 15/11/2023
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async deleteEvent (req, res) {
        try {
            const data = await EventService.deleteEvent(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.EVENT_DELETE_SUCCESS);
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to delete event
   * <AUTHOR>
   * @since 22/11/2023
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async updatePaymentStatus (req, res) {
        try {
            const data = await EventService.updatePaymentStatus(
                req,
                res.locals.user,
                res.__
            );
            Utils.sendResponse(
                null,
                data,
                res,
                MESSAGES.PAYMENT_STATUS_UPDATE_SUCCESS
            );
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }


    /**
     * @desc This function is being used to get searched list of events
     * <AUTHOR>
     * @since 20/02/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async getSearchEventList (req, res) {
        try {
            const data = await EventService.getSearchEventList(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get event list', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to start chunk upload
     * <AUTHOR>
     * @since 05/12/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async startChunkUpload (req, res) {
        try {
            const data = await EventService.startChunkUpload(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in start chunk upload', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to upload chunk
     * <AUTHOR>
     * @since 05/12/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async uploadChunk (req, res) {
        try {
            const data = await EventService.uploadChunk(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in upload chunk', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to complete chunk upload
     * <AUTHOR>
     * @since 05/12/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async completeChunkUpload (req, res) {
        try {
            const data = await EventService.completeChunkUpload(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in complete chunk upload', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to abort chunk upload
     * <AUTHOR>
     * @since 05/12/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async abortChunkUpload (req, res) {
        try {
            const data = await EventService.abortChunkUpload(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in abort chunk upload', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to generate presigned url
     * <AUTHOR>
     * @since 10/12/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async generatePresignedUrl (req, res) {
        try {
            const data = await EventService.generatePresignedUrl(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in generate presigned url', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = EventController;
