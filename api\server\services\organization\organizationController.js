
const OrganizationService = require('./organizationService');
const OrganizationBulkService = require('./organizationBulkService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for Organization user routes.
 */
class OrganizationController {
    /**
     * @desc This function is being used to add Organization admin by super admin
     * <AUTHOR>
     * @since 07/11/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async addOrganization (req, res) {
        try {
            const data = await OrganizationService.addOrganization(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.ADD_ORGANIZATION_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in adding organization: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to update Organization details
     * <AUTHOR>
     * @since 15/11/2023
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async updateOrganizationDetails (req, res) {
        try {
            const data = await OrganizationService.updateOrganizationDetails(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.UPDATE_ORGANIZATION_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in updating organization details: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to get list of Organization
     * <AUTHOR>
     * @since 08/11/2023
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async getOrganizationList (req, res) {
        try {
            const data = await OrganizationService.getOrganizationList(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in getting organization list: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to get Organization details
     * <AUTHOR>
     * @since 09/11/2023
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async getOrganizationDetails (req, res) {
        try {
            const data = await OrganizationService.getOrganizationDetails(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in getting organization details: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to update Organization status enable/disable
     * <AUTHOR>
     * @since 10/11/2023
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async updateOrganizationStatus (req, res) {
        try {
            const data = await OrganizationService.updateOrganizationStatus(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.ORGANIZATION_STATUS_CHANGE_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in updating organization status: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to delete Organization
     * <AUTHOR>
     * @since 10/11/2023
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async deleteOrganization (req, res) {
        try {
            const data = await OrganizationService.deleteOrganization(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.DELETE_ORGANIZATION_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in deleting organization: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to get Organization users
     * <AUTHOR>
     * @since 10/11/2023
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async getOrganizationUsers (req, res) {
        try {
            const data = await OrganizationService.getOrganizationUsers(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in getting org users: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
    static async getOrganizationSearch (req, res) {
        try {
            const data = await OrganizationService.getOrganizationSearch(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in getting org users: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to add Organization users
     * <AUTHOR>
     * @since 14/12/2023
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async addOrganizationUser (req, res) {
        try {
            const data = await OrganizationService.addOrganizationUser(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.ADD_ORG_USER_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in adding org user: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to update Organization user
     * <AUTHOR>
     * @since 14/12/2023
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async updateOrganizationUser (req, res) {
        try {
            const data = await OrganizationService.updateOrganizationUser(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.UPDATE_ORG_USER_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in updating org user: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to delete Organization user
     * <AUTHOR>
     * @since 14/12/2023
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async deleteOrganizationUser (req, res) {
        try {
            const data = await OrganizationService.deleteOrganizationUser(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.DELETE_ORG_USER_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in deleting org user: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to join organization for multiple child
     * <AUTHOR>
     * @since 03/06/2024
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async addChildrenToOrganization (req, res) {
        try {
            const data = await OrganizationService.addChildrenToOrganization(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.ADD_CHILDREN_ORGANIZATION);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in deleting org user: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    static async getAssociatedOrganizations (req, res) {
        try {
            const data = await OrganizationService.getAssociatedOrganizations(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in getting associated organizations: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    static async addAssociatedOrganizations (req, res) {
        try {
            const data = await OrganizationBulkService.addAssociatedOrganizations(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in getting associated organizations: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to generate presigned url for banner image
     * <AUTHOR>
     * @since 20/12/2024
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async generatePresignedUrl (req, res) {
        try {
            const data = await OrganizationService.generatePresignedUrl(req, res.locals.user);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in generate presigned url', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = OrganizationController;
