/**
 * This file is used to auth API's routes.
 * Created by Growexx on 16/10/2023.
 * @name authRoutes
 */
const router = require('express').Router();

const SignUpController = require('../services/signup/signUpController');
const ForgotPasswordController = require('../services/forgotPassword/forgotPasswordController');
const SignInController = require('../services/signin/signInController');

const HmacMiddleware = require('../middleware/hmac');

router.post('/signup', HmacMiddleware, SignUpController.signUp);
router.post('/verify-account', HmacMiddleware, SignUpController.verifyAccount);
router.post('/resend-otp', HmacMiddleware, SignUpController.resendOTP);
router.post('/signin', HmacMiddleware, SignInController.login);
router.post('/forgot-password', HmacMiddleware, ForgotPasswordController.forgotPassword);
router.post('/verify-otp', HmacMiddleware, ForgotPasswordController.verifyOtp);
router.post('/reset-password', HmacMiddleware, ForgotPasswordController.resetPassword);
router.post('/social-signin', HmacMiddleware, SignInController.sociallogin);

module.exports = router;
