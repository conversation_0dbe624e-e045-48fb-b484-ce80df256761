const postSchema = {
    'index': 'posts',
    'body': {
        'mappings': {
            'properties': {
                'id': {
                    'type': 'keyword',
                    'index': true
                },
                'title': {
                    'type': 'text',
                    'fields': {
                        'keyword': {
                            'type': 'keyword',
                            'ignore_above': 256
                        }
                    }
                },
                'subTitle': {
                    'type': 'text',
                    'fields': {
                        'keyword': {
                            'type': 'keyword',
                            'ignore_above': 256
                        }
                    }
                },
                'publishedDate': {
                    'type': 'date',
                    'format': 'strict_date_optional_time||epoch_millis'
                },
                'content': {
                    'type': 'text'
                },
                'organizationId': {
                    'type': 'keyword'
                },
                'status': {
                    'type': 'keyword'
                }
            }
        }
    }
};

module.exports = postSchema;
