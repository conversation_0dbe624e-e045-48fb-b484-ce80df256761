const GeneralError = require('../util/GeneralError');
const MSGS = require('../../server/locales/en.json');
const REQUIRED = 'FIELD_REQUIRED';
const INVALID = 'FIELD_NOT_VALID';
const INVALID_NAME = 'NAME_NOT_VALID';
const PARAM_REQUIRED = 'PARAM_REQUIRED';
const INVALID_ENUM_VALUE = 'INVALID_ENUM_VALUE';

/**
 * Created by Growexx on 04/06/2020
 * @name validator
 */
class Validator {
    constructor (locale) {
        this.NOT_VALID = INVALID;
        this.REQUIRED = REQUIRED;
        this.INVALID_NAME = INVALID_NAME;

        if (locale) {
            this.__ = locale;
        } else {
            this.__ = (key, value) => {
                return MSGS[key].replace('%s', value);
            };
        }
    }

    /**
     * @desc This function is being used to validate first name and last name
     * <AUTHOR>
     * @param {string} name name
     * @param {string} field field name
     */
    name (name, field) {
        if (!name) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        }
        if (!CONSTANTS.REGEX.NAME.test(name.trim())) {
            throw new GeneralError(this.__(INVALID_NAME, field), 400);
        }
    }

    /**
     * @desc This function is being used to validate if field exists
     * <AUTHOR>
     * @param {Number|String} value field value
     * @param {String} name field name
     * @since 04/10/2023
     */
    field (value, name) {
        if (!value) {
            throw new GeneralError(this.__(REQUIRED, name), 400);
        }
    }

    /**
     * @desc This function is being used to validate if field is a number
     * <AUTHOR>
     * @param {Number} value value
     * @param {String} field field name
     * @since 26/07/2023
     */
    number (value, field) {
        if (isNaN(value)) {
            throw new GeneralError(this.__(INVALID, field), 400);
        }
    }

    /**
     * @desc This function is being used to validate email address
     * <AUTHOR>
     * @param {string} email Email
     */
    email (email) {
        if (!email) {
            throw new GeneralError(this.__(REQUIRED, 'Email'), 400);
        }
        if (!CONSTANTS.REGEX.EMAIL.test(email.trim())) {
            throw new GeneralError(this.__(INVALID, 'Email'), 400);
        }
    }

    /**
     * @desc This function is being used to check mobile
     * <AUTHOR>
     * @param {string} mobile mobile
     */
    mobile (mobile, key) {
        if (!CONSTANTS.REGEX.MOBILE.test(mobile)) {
            throw new GeneralError(this.__(INVALID, key), 400);
        }
    }

    /**
    * @desc This function is being used to validate if query param exists
    * <AUTHOR>
    * @param {Number|String} value field value
    * @param {String} name field name
    * @since 08/11/2023
    */
    param (value, name) {
        if (value === undefined) {
            throw new GeneralError(this.__(PARAM_REQUIRED, name), 400);
        }
    }

    /**
     * @desc This function is being used to validate uuid
     * <AUTHOR>
     * @param {String} uuid uuid
     * @param {String} field field name
     * @since 21/09/2023
     */
    uuid (uuid, field) {
        if (!CONSTANTS.REGEX.UUID.test(uuid)) {
            throw new GeneralError(this.__(INVALID, field), 400);
        }
    }

    /**
     * @desc This function is being used to validate status values
     * <AUTHOR>
     * @param {Number} status status value
     * @since 25/09/2023
     */
    status (status) {
        if (status !== 0 && status !== 1) {
            throw new GeneralError(this.__(INVALID, 'Status'), 400);
        }
    }

    /**
     * @desc This function is being used to validate enum values
     * @param {String} value value
     * @param {Array} enumValues enum values
     * @param {String} field field name
     */
    enum (value, enumValues, field) {
        if (!enumValues.includes(value)) {
            throw new GeneralError(this.__(INVALID_ENUM_VALUE, field, enumValues.toString()), 400);
        }
    }

    /**
     * @desc This function is being used to check password
     * <AUTHOR>
     * @param {string} password Password
     */
    password (password) {
        if (!password) {
            throw new GeneralError(this.__(REQUIRED, 'Password'), 400);
        }
        if (!CONSTANTS.REGEX.PASSWORD.test(password)) {
            throw new GeneralError(this.__(INVALID, 'Password'), 400);
        }
    }

    /**
     * @desc This function is being used to check if field is an array
     * @param {Array} array array
     * @param {String} field field name
     */
    array (array, field) {
        if (!Array.isArray(array)) {
            throw new GeneralError(this.__(INVALID, field), 400);
        }
    }
}

module.exports = Validator;
