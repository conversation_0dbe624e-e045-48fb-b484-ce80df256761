const dynamoose = require('dynamoose');
const { v4: uuidv4 } = require('uuid');

const eventDetails = {
    'details': {
        'type': String
    },
    'startDateTime': {
        'type': Date,
        'required': true
    },
    'endDateTime': {
        'type': Date,
        'required': true
    },
    'venue': {
        'type': String,
        'required': true
    },
    'isRecurring': {
        'type': Boolean,
        'default': false
    },
    'recurringFrequency': {
        'type': String
    },
    'notes': {
        'type': Set,
        'schema': [String]
    },
    'faq': {
        'type': Array,
        'schema': [{
            'type': Object,
            'schema': {
                'question': {
                    'type': String,
                    'required': true
                },
                'answer': {
                    'type': String,
                    'required': true
                }
            }
        }],
        'default': []
    },
    'documentURLs': {
        'type': Set,
        'schema': [String]
    }
};

const comments = {
    id: {
        type: String,
        default: () => uuidv4()
    },
    parentId: {
        type: String,
        required: true
    },
    childId: {
        type: String,
        required: true
    },
    message: {
        type: String,
        required: true
    },
    isDeleted: {
        type: Number,
        enum: [0, 1],
        default: 0
    }
};

const product = {
    itemId: Number,
    options: {
        type: Array,
        schema: [{
            type: Object,
            schema: {
                optionId: Number,
                variants: {
                    type: Array,
                    schema: [String]
                },
                name: String
            }
        }]
    },
    optionPrices: {
        type: Array,
        schema: [{
            type: Object,
            schema: {
                itemName: String,
                id: String,
                itemCost: Number
            }
        }]
    },
    images: {
        type: Array,
        schema: [String]
    },
    itemName: String,
    imageCount: Number
};

const eventSchema = new dynamoose.Schema({
    id: {
        hashKey: true,
        type: String,
        default: () => uuidv4()
    },
    title: {
        type: String,
        required: true
    },
    details: {
        type: Object,
        schema: eventDetails,
        required: true
    },
    photoURL: {
        type: String
    },
    eventType: {
        type: String,
        enum: ['event', 'post', 'calendar'],
        default: 'event'
    },
    eventScope: {
        type: String,
        enum: ['public', 'private'],
        default: 'public'
    },
    participantsLimit: {
        type: Number
    },
    volunteerDetails: {
        type: Set,
        schema: [String]
    },
    volunteerRequired: {
        type: Boolean,
        required: true,
        default: false
    },
    volunteerSignupUrl: {
        type: String
    },
    isPaid: {
        type: Boolean,
        required: true,
        default: false
    },
    isDonatable: {
        type: Boolean,
        default: false
    },
    fee: {
        type: Number
    },
    status: {
        type: String,
        enum: ['published', 'draft', 'cancelled'],
        default: 'draft'
    },
    comments: {
        type: Array,
        schema: [{
            type: Object,
            schema: comments
        }],
        default: []
    },
    organizationId: {
        type: String,
        required: true,
        index: {
            name: 'organizationId-index',
            global: true,
            project: true
        }
    },
    quantityType: {
        type: String,
        enum: ['People', 'Items'],
        default: null
    },
    quantityInstruction: {
        type: String,
        default: null
    },
    membershipBenefitDetails: {
        type: Object,
        schema: {
            'benefitDiscount': {
                'type': String
            },
            'isOnlyForMembers': {
                'type': Boolean
            }
        }
    },
    products: {
        type: Array,
        schema: [{
            type: Object,
            schema: product
        }]
    },
    participantsCount: {
        type: Number,
        default: 0
    },
    createdBy: {
        type: String,
        required: true
    },
    updatedBy: {
        type: String
    },
    isDeleted: {
        type: Number,
        enum: [0, 1],
        default: 0
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    },
    saveUnknown: [
        'products.*.optionPrices.**'
    ]
});

module.exports = dynamoose.model('Events', eventSchema);
