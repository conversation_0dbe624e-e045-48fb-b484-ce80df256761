const User = require('../../models/user.model');

/**
 * Class represents services for logout user.
 */
class LogoutService {
    /**
    * @desc This function is being used to logout user.
    * <AUTHOR>
    * @since 19/10/2023
    * @param {Object} req Request
    */
    static async logout (user) {
        await User.update({ id: user.id, email: user.email }, { '$REMOVE': ['fcmToken'] });
    }
}

module.exports = LogoutService;
