const { Chat, Message, MessageRole } = require('@incubyte/ai');
const SendMessageService = require('../sendSocketMessageService');
const CONSTANTS = require('../../util/constants');
const { v4: uuidv4 } = require('uuid');
const { generateOrganizationCompanionPrompt, getChildrenResponseAggregatorPrompt,
    fetchUserAndChildrenDetails,
    getCurrentTimestamp,
    getChatMessagesForFinalResponse } = require('../../util/companionUtils');
const MessageModel = require('../../models/message.model');
const GeneralError = require('../../util/GeneralError');
const { ChatOpenAI } = require('@langchain/openai');
const { HumanMessage, SystemMessage } = require('@langchain/core/messages');
const ChildResponseService = require('../aiCompanionHelper/ChildResponseService');
const ChildQueryPlanningService = require('../aiCompanionHelper/ChildQueryPlanningService');
const ChatResponseHandler = require('../aiCompanionHelper/ChatResponseHandler');
const Utils = require('../../util/utilFunctions');
const ConstantModel = require('../../models/constant.model');
const RedisUtil = require('../../util/redisUtil');
const GroupMembers = require('../../models/groupMembers.model');

module.exports = async (event) => {
    const eventBody = event.body;

    switch (eventBody.actionType) {
        case CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_GROUP_COMPANION_MESSAGE:
            return await handleNewGroupCompanionMessage(eventBody, event.requestContext.connectionId);
        case CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_COMPANION_MESSAGE:
            return await handleNewCompanionMessage(eventBody, event.requestContext.connectionId);
        default:
            return {
                statusCode: 400,
                body: 'Invalid action type'
            };
    }
};

/**
 * @description Gets the user group details
 * @param {string} groupMemberId - The group member ID
 * @returns {Promise<Object>} The user group details
 */
const getUserGroupDetails = async ({ groupMemberId }) => {
    return await GroupMembers.get(groupMemberId);
};

const fetchActiveGroupMemberUserIds = async ({ groupId, versionPrefix }) => {
    const groupMembersKey = Utils.getConversationMembersKey({ versionPrefix, groupId });
    const groupMembers = await RedisUtil.getAllHashValues(groupMembersKey);
    const parsedGroupMembers = [];

    for (const groupMember of groupMembers) {
        const parsedGroupMember = Utils.getParsedValue(groupMember);
        if ([CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE, CONSTANTS.GROUP_MEMBER_STATUS.DISABLED].includes(parsedGroupMember?.status)) {
            parsedGroupMembers.push(parsedGroupMember);
        }
    }

    return parsedGroupMembers.map((member) => member.userId);
};

/**
 * @description Streams AI response from the AI service
 * @param {string} tenantId - The tenant ID
 * @param {string} groupId - The group ID
 * @param {Object} chatParams - The chat parameters
 * @param {Function} onMessage - The callback function to handle the message
 * @returns {Promise<Object>} - The full message and contexts
 */
const streamAIResponseForGroups = async ({ tenantId, groupId, chatParams, onMessage }) => {
    try {
        const chat = new Chat();
        const streamPromise = await chat.sendChatStream(tenantId, groupId, chatParams);
        let fullMessage = '';
        const contexts = [];

        if (streamPromise && Symbol.asyncIterator in Object(streamPromise)) {
            for await (const chunk of streamPromise) {
                if (chunk.type === 'message') {
                    fullMessage += chunk.data;
                    if (onMessage) await onMessage(chunk.data);
                } else if (chunk.type === 'context') {
                    contexts.push(chunk.data);
                } else {
                    // Do Nothing
                }
            }
            return { fullMessage, contexts: contexts.flat() };
        } else {
            throw new GeneralError('AI service stream configuration error', 200);
        }
    } catch (error) {
        if (error.message && error.message.includes('index_not_found_exception')) {
            throw new GeneralError('Companion not setup for this group!', 200);
        }
        throw error;
    }
};

const streamAIResponseForCompanion = async ({ messages, onMessage }) => {
    try {
        let fullMessage = '';

        const model = new ChatOpenAI({
            model: CONSTANTS.AI_MODEL_NAME,
            temperature: CONSTANTS.AI_TEMPERATURE,
            streaming: true
        });

        const streamPromise = await model.stream(messages);

        if (streamPromise && Symbol.asyncIterator in Object(streamPromise)) {
            for await (const chunk of streamPromise) {
                if (chunk.content) {
                    fullMessage += chunk.content;
                    if (onMessage) await onMessage(chunk.content);
                }
            }
            return { fullMessage };
        } else {
            throw new GeneralError('AI service stream configuration error', 200);
        }
    } catch (error) {
        if (error.message && error.message.includes('index_not_found_exception')) {
            throw new GeneralError('Companion not setup for this group!', 200);
        }
        throw error;
    }
};

/**
 * @description Handles error while generating companion message
 * @param {Error} error - The error object
 * @param {string} connectionId - The connection ID
 * @param {string} messageId - The message ID
 * @param {string} aiMessageId - The AI message ID
 * @param {string} groupId - The group ID
 * @param {string} action - The action
 * @param {string} actionTypeToSendToClient - The action type to send to the client
 * @param {string} actionTypeToReturn - The action type to return to the caller
 * @returns {Promise<Object>} - The error message and data
 */
const handleError = async ({
    error,
    connectionId,
    messageId,
    aiMessageId,
    groupId,
    action,
    actionTypeToSendToClient,
    actionTypeToReturn,
    startTime
}) => {
    CONSOLE_LOGGER.error('Error while generating companion message:', error);
    const errorMessage = error.message || 'Something went wrong!';
    const messageData = {
        id: aiMessageId,
        senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
        userQueryMessageId: messageId,
        message: errorMessage,
        createdAt: getCurrentTimestamp(),
        updatedAt: getCurrentTimestamp(),
        groupId
    };
    const statusCode = error.statusCode || 500;
    await SendMessageService.sendMessageToConnection({
        connectionId,
        statusCode,
        messageData: {
            data: messageData,
            actionType: actionTypeToSendToClient,
            action
        }
    });
    await Utils.addMetricForQueryTime(startTime);
    return {
        message: errorMessage,
        data: messageData,
        actionType: actionTypeToReturn,
        statusCode,
        action
    };
};

/**
 * @description Handles message creation
 * @param {Object} params - The parameters
 * @param {string} [params.messageId] - The message ID
 * @param {string} [params.senderId] - The sender ID
 * @param {string} [params.groupId] - The group ID
 * @param {string} [params.message] - The message
 * @param {boolean} [params.isExistingMessage] - Whether the message is existing
 * @param {Array} [params.groupMemberUserIds] - The group member user IDs
 * @param {Object} [params.userGroupDetails] - The user group details
 */
const handleMessageCreation = async ({
    messageId,
    senderId,
    groupId,
    message,
    isExistingMessage,
    groupMemberUserIds,
    userGroupDetails
}) => {
    if (!isExistingMessage) {
        let userQueryMessage;
        try {
            userQueryMessage = await MessageModel
                .create({
                    id: messageId, senderId, groupId, message
                });

            userGroupDetails.lastReadMessage = { messageId, createdAt: userQueryMessage.createdAt };
            await userGroupDetails.save();
        } catch (error) {
            CONSOLE_LOGGER.error('Error while creating user query message:', error);
            const errorMessage = 'Something went wrong!';
            const data = {
                statusCode: error.statusCode || 500,
                message: errorMessage,
                action: 'sendCompanionMessage',
                actionType: CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.NEW_GROUP_MESSAGE,
                data: {
                    id: messageId,
                    message: errorMessage,
                    createdAt: getCurrentTimestamp(),
                    updatedAt: getCurrentTimestamp(),
                    senderId,
                    groupId
                }
            };

            const errToThrow = new Error(errorMessage);
            errToThrow.isHandled = true;
            errToThrow.data = data;
            throw errToThrow;
        }
        await SendMessageService.sendMessagesToUsers(groupMemberUserIds, {
            data: userQueryMessage,
            actionType: CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.NEW_GROUP_MESSAGE
        });
    }
};

/**
 * @description Handles AI response generation
 * @param {Object} params - The parameters
 * @param {string} [params.groupId] - The group ID
 * @param {string} [params.messageId] - The message ID
 * @param {string} [params.senderId] - The sender ID
 * @param {string} [params.message] - The message
 * @param {boolean} [params.isExistingMessage] - Whether the message is existing
 * @param {string} [params.organizationId] - The organization ID
 * @param {string} [params.aiMessageId] - The AI message ID
 * @param {string} [params.connectionId] - The connection ID
 * @param {Object} [params.userGroupDetails] - The user group details
 * @param {Date} [params.startTime] - The start time
 * @param {string} [params.versionPrefix] - The version prefix
 */
const handleAIResponseGeneration = async ({
    groupId,
    messageId,
    senderId,
    message,
    isExistingMessage,
    organizationId,
    aiMessageId,
    connectionId,
    userGroupDetails,
    startTime,
    versionPrefix
}) => {
    try {
        const groupMemberUserIds = await fetchActiveGroupMemberUserIds({ groupId, versionPrefix });
        await handleMessageCreation({
            messageId,
            senderId,
            groupId,
            message,
            isExistingMessage,
            groupMemberUserIds,
            userGroupDetails
        });

        const userDetails = await fetchUserAndChildrenDetails({ userId: senderId, versionPrefix });
        const systemPrompt = generateOrganizationCompanionPrompt(userDetails, organizationId);
        const chatContext = {
            messages: [
                new Message(MessageRole.SYSTEM, systemPrompt)
            ]
        };
        const chatParams = {
            query: message + ' Today\'s Date and Time:' + getCurrentTimestamp(),
            context: chatContext
        };

        aiMessageId = aiMessageId ?? uuidv4();

        const { fullMessage, contexts } = await streamAIResponseForGroups({
            groupId,
            chatParams,
            tenantId: organizationId,
            onMessage: async (chunkData) => {
                await SendMessageService.sendMessagesToUsers([senderId], {
                    data: {
                        groupId,
                        id: aiMessageId,
                        senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
                        userQueryMessageId: messageId,
                        message: chunkData,
                        createdAt: getCurrentTimestamp(),
                        updatedAt: getCurrentTimestamp()
                    },
                    actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.AI_COMPANION_GROUP_MESSAGE_STREAMING,
                    action: 'sendCompanionMessage'
                });
            }
        });

        const completedMessage = await MessageModel.create({
            groupId,
            id: aiMessageId,
            senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
            userQueryMessageId: messageId,
            message: fullMessage,
            contexts: JSON.stringify(contexts)
        });

        await SendMessageService.sendMessagesToUsers(groupMemberUserIds, {
            data: completedMessage,
            actionType: CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.NEW_GROUP_MESSAGE
        });

        await Utils.addMetricForQueryTime(startTime);
        return {
            statusCode: 200,
            message: 'AI response generated successfully',
            action: 'sendCompanionMessage',
            actionType: CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.NEW_GROUP_MESSAGE,
            data: completedMessage
        };
    } catch (error) {
        if (error.isHandled) {
            return error.data;
        }
        return await handleError({
            error,
            connectionId,
            messageId,
            aiMessageId,
            groupId,
            action: 'sendCompanionMessage',
            actionTypeToSendToClient: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.AI_COMPANION_GROUP_MESSAGE_STREAMING,
            actionTypeToReturn: CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.NEW_GROUP_MESSAGE,
            startTime
        });
    }
};

/**
* @description Gets the version prefix for redis keys
* <AUTHOR>
* @returns {Promise<String>} The version prefix
*/
const getVersionPrefixForRedisKeys = async () => {
    const versionPrefixConstant = await ConstantModel.get(
        CONSTANTS.FEED_VERSION_PREFIX
    );
    return versionPrefixConstant?.value ?? '';
};

const handleNewGroupCompanionMessage = async (eventBody, connectionId) => {
    const startTime = MOMENT();

    try {
        const { messageId, groupId, senderId, message, organizationId, groupMemberId, isExistingMessage } = eventBody;
        const { aiMessageId } = eventBody;

        const versionPrefix = await getVersionPrefixForRedisKeys();

        const userGroupDetails = await getUserGroupDetails({ groupMemberId });
        if (!userGroupDetails) {
            CONSOLE_LOGGER.error('--> Group member not found!');
            await Utils.addMetricForQueryTime(startTime);
            return {
                statusCode: 404,
                message: 'Group member not found!',
                action: 'sendCompanionMessage',
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_GROUP_COMPANION_MESSAGE,
                data: { messageId, senderId, groupId, message }
            };
        }

        return await handleAIResponseGeneration({
            groupId,
            messageId,
            senderId,
            message,
            isExistingMessage,
            organizationId,
            aiMessageId,
            connectionId,
            userGroupDetails,
            startTime,
            versionPrefix
        });
    } catch (error) {
        await Utils.addMetricForQueryTime(startTime);
        CONSOLE_LOGGER.error('Error handling new group companion message:', error);
        return {
            statusCode: error.statusCode || 500,
            body: error.message || 'Internal server error'
        };
    }
};

const handleNewCompanionMessage = async (eventBody, connectionId) => {
    const startTime = MOMENT();

    try {
        const versionPrefix = await getVersionPrefixForRedisKeys();

        const { senderId, message: queryMessage, context } = eventBody;
        let { aiMessageId } = eventBody;
        const userDetails = await fetchUserAndChildrenDetails({ userId: senderId, versionPrefix });
        const childQueryPlans = await planRelevantChildQueries(userDetails.children, queryMessage);
        const { allChildContext, childRelatedMessages } =
            await generateResponsesForAllChildren(userDetails, childQueryPlans, queryMessage, context);
        const systemPrompt = await getChildrenResponseAggregatorPrompt(childRelatedMessages);
        const systemMessage = new SystemMessage(systemPrompt);

        const messages = [systemMessage, ...getChatMessagesForFinalResponse(context),
            new HumanMessage(queryMessage + '\nToday\'s date and time: ' + getCurrentTimestamp())];
        aiMessageId = aiMessageId ?? uuidv4();

        try {
            const { fullMessage } = await streamAIResponseForCompanion({
                messages,
                onMessage: async (chunkData) => {
                    await SendMessageService.sendMessageToConnection({
                        connectionId,
                        messageData: {
                            data: {
                                id: aiMessageId,
                                senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
                                message: chunkData,
                                createdAt: getCurrentTimestamp(),
                                updatedAt: getCurrentTimestamp()
                            },
                            actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.AI_COMPANION_MESSAGE_STREAMING,
                            action: 'sendCompanionMessage'
                        }
                    });
                }
            });
            const flattenedChildContext = allChildContext.flat().map(contextItem => {
                // eslint-disable-next-line no-unused-vars
                const { pageContent, ...rest } = contextItem;
                return rest;
            });
            const completedMessage = {
                id: aiMessageId,
                senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
                message: fullMessage,
                contexts: JSON.stringify(flattenedChildContext),
                createdAt: getCurrentTimestamp(),
                updatedAt: getCurrentTimestamp()
            };

            await Utils.addMetricForQueryTime(startTime);
            return {
                statusCode: 200,
                message: 'AI response generated successfully',
                action: 'sendCompanionMessage',
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_COMPANION_MESSAGE,
                data: completedMessage
            };
        } catch (error) {
            return await handleError({
                error,
                connectionId,
                aiMessageId,
                action: 'sendCompanionMessage',
                actionTypeToSendToClient: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.AI_COMPANION_MESSAGE_STREAMING,
                actionTypeToReturn: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_COMPANION_MESSAGE,
                startTime
            });
        }
    } catch (error) {
        CONSOLE_LOGGER.error('Error handling new companion message:', error);
        await Utils.addMetricForQueryTime(startTime);
        return {
            statusCode: error.statusCode || 500,
            body: error.message || 'Internal server error'
        };
    }
};

const generateResponsesForAllChildren = async (userDetails, childQueryPlans, queryMessage, context) => {
    const responseService = new ChildResponseService(userDetails, context);
    const responseHandler = new ChatResponseHandler();

    if (childQueryPlans?.length > 0) {
        await Promise.all(childQueryPlans.map(async ({ childName, associatedOrganizations }) => {
            const chatResponse = await responseService.handleSpecificChildResponse(
                childName,
                associatedOrganizations,
                queryMessage
            );
            responseHandler.processResponse(chatResponse);
        }));
    } else {
        const chatResponse = await responseService.handleGeneralResponse(queryMessage);
        responseHandler.processResponse(chatResponse);
    }

    return responseHandler.getResults();
};

const planRelevantChildQueries = async (childrenList, queryMessage, context) => {
    const planningService = new ChildQueryPlanningService();
    return await planningService.generateQueryPlan(childrenList, queryMessage, context);
};
