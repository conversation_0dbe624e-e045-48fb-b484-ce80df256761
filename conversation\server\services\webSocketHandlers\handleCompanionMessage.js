const { Chat, Message, MessageRole } = require('@incubyte/ai');
const SendMessageService = require('../sendSocketMessageService');
const CONSTANTS = require('../../util/constants');
const { v4: uuidv4 } = require('uuid');
const { generateOrganizationCompanionPrompt, getChildrenResponseAggregatorPrompt,
    fetchUserAndChildrenDetails,
    getCurrentTimestamp,
    getChatMessagesForFinalResponse } = require('../../util/companionUtils');
const MessageModel = require('../../models/message.model');
const GeneralError = require('../../util/GeneralError');
const { ChatOpenAI } = require('@langchain/openai');
const { HumanMessage, SystemMessage } = require('@langchain/core/messages');
const ChildResponseService = require('../aiCompanionHelper/ChildResponseService');
const ChildQueryPlanningService = require('../aiCompanionHelper/ChildQueryPlanningService');
const ChatResponseHandler = require('../aiCompanionHelper/ChatResponseHandler');
const Utils = require('../../util/utilFunctions');
const ConstantModel = require('../../models/constant.model');
const RedisUtil = require('../../util/redisUtil');
const GroupMembers = require('../../models/groupMembers.model');
const mockUserDetails = require('../../util/mockUserDetails');

const isEvalRun = process.env.npm_lifecycle_event === 'eval';

async function companionMessageHandler (event) {
    const eventBody = event.body;

    switch (eventBody.actionType) {
        case CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_GROUP_COMPANION_MESSAGE:
            return await handleNewGroupCompanionMessage(eventBody, event.requestContext.connectionId);
        case CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_COMPANION_MESSAGE:
            return await handleNewCompanionMessage(eventBody, event.requestContext.connectionId);
        default:
            return {
                statusCode: 400,
                body: 'Invalid action type'
            };
    }
}

/**
 * @description Gets the user group details
 * @param {string} groupMemberId - The group member ID
 * @returns {Promise<Object>} The user group details
 */
const getUserGroupDetails = async ({ groupMemberId }) => {
    return await GroupMembers.get(groupMemberId);
};

const fetchActiveGroupMemberUserIds = async ({ groupId, versionPrefix }) => {
    const groupMembersKey = Utils.getConversationMembersKey({ versionPrefix, groupId });
    const groupMembers = await RedisUtil.getAllHashValues(groupMembersKey);
    const parsedGroupMembers = [];

    for (const groupMember of groupMembers) {
        const parsedGroupMember = Utils.getParsedValue(groupMember);
        if ([CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE, CONSTANTS.GROUP_MEMBER_STATUS.DISABLED].includes(parsedGroupMember?.status)) {
            parsedGroupMembers.push(parsedGroupMember);
        }
    }

    return parsedGroupMembers.map((member) => member.userId);
};

/**
 * @description Streams AI response from the AI service
 * @param {string} tenantId - The tenant ID
 * @param {string} groupId - The group ID
 * @param {Object} chatParams - The chat parameters
 * @param {Function} onMessage - The callback function to handle the message
 * @returns {Promise<Object>} - The full message and contexts
 */
const streamAIResponseForGroups = async ({ tenantId, groupId, chatParams, onMessage }) => {
    try {
        const chat = new Chat();
        const streamPromise = await chat.sendChatStream(tenantId, groupId, chatParams);
        let fullMessage = '';
        const contexts = [];

        if (isAsyncIterable(streamPromise)) {
            for await (const chunk of streamPromise) {
                if (chunk.type === 'message') {
                    fullMessage += chunk.data;
                    if (onMessage) await onMessage(chunk.data);
                } else if (chunk.type === 'context') {
                    contexts.push(chunk.data);
                } else {
                    // Do Nothing
                }
            }
            return { fullMessage, contexts: contexts.flat() };
        } else {
            throw new GeneralError('AI service stream configuration error', 200);
        }
    } catch (error) {
        if (error.message && error.message.includes('index_not_found_exception')) {
            throw new GeneralError('Companion not setup for this group!', 200);
        }
        throw error;
    }
};

const streamAIResponseForCompanion = async ({ messages = [], onMessage, preResolvedStream = null }) => {
    try {
        let fullMessage = '';
        const contexts = [];

        const streamPromise = preResolvedStream ??
            await new ChatOpenAI({
                model: CONSTANTS.AI_MODELS.DEFAULT,
                temperature: CONSTANTS.AI_TEMPERATURE,
                streaming: true
            }).stream(messages);

        if (isAsyncIterable(streamPromise)) {
            for await (const chunk of streamPromise) {
                if (chunk.type === 'message') {
                    fullMessage += chunk.data;
                    if (onMessage) await onMessage(chunk.data);
                }
                if (preResolvedStream && chunk.type === 'context') {
                    contexts.push(chunk.data);
                }
                if (chunk.content) {
                    fullMessage += chunk.content;
                    if (onMessage) await onMessage(chunk.content);
                }
            }
        } else {
            throw new GeneralError('AI service stream configuration error', 200);
        }
        return { fullMessage, contexts };
    } catch (error) {
        if (error.message && error.message.includes('index_not_found_exception')) {
            throw new GeneralError('Companion not setup for this group!', 200);
        }
        throw error;
    }
};

/**
 * @description Handles error while generating companion message
 * @param {Error} error - The error object
 * @param {string} connectionId - The connection ID
 * @param {string} messageId - The message ID
 * @param {string} aiMessageId - The AI message ID
 * @param {string} groupId - The group ID
 * @param {string} action - The action
 * @param {string} actionTypeToSendToClient - The action type to send to the client
 * @param {string} actionTypeToReturn - The action type to return to the caller
 * @returns {Promise<Object>} - The error message and data
 */
const handleError = async ({
    error,
    connectionId,
    messageId,
    aiMessageId,
    groupId,
    action,
    actionTypeToSendToClient,
    actionTypeToReturn,
    startTime
}) => {
    CONSOLE_LOGGER.error('Error while generating companion message:', error);
    const errorMessage = error.message || CONSTANTS.SOMETHING_WENT_WRONG;
    const messageData = {
        id: aiMessageId,
        senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
        userQueryMessageId: messageId,
        message: errorMessage,
        createdAt: getCurrentTimestamp(),
        updatedAt: getCurrentTimestamp(),
        groupId
    };
    const statusCode = error.statusCode || 500;
    await SendMessageService.sendMessageToConnection({
        connectionId,
        statusCode,
        messageData: {
            data: messageData,
            actionType: actionTypeToSendToClient,
            action
        }
    });
    await Utils.addMetricForQueryTime(startTime);
    return {
        message: errorMessage,
        data: messageData,
        actionType: actionTypeToReturn,
        statusCode,
        action
    };
};

/**
 * @description Handles message creation
 * @param {Object} params - The parameters
 * @param {string} [params.messageId] - The message ID
 * @param {string} [params.senderId] - The sender ID
 * @param {string} [params.groupId] - The group ID
 * @param {string} [params.message] - The message
 * @param {boolean} [params.isExistingMessage] - Whether the message is existing
 * @param {Array} [params.groupMemberUserIds] - The group member user IDs
 * @param {Object} [params.userGroupDetails] - The user group details
 */
const handleMessageCreation = async ({
    messageId,
    senderId,
    groupId,
    message,
    isExistingMessage,
    groupMemberUserIds,
    userGroupDetails
}) => {
    if (!isExistingMessage) {
        let userQueryMessage;
        try {
            userQueryMessage = await MessageModel
                .create({
                    id: messageId, senderId, groupId, message
                });

            userGroupDetails.lastReadMessage = { messageId, createdAt: userQueryMessage.createdAt };
            await userGroupDetails.save();
        } catch (error) {
            CONSOLE_LOGGER.error('Error while creating user query message:', error);
            const errorMessage = CONSTANTS.SOMETHING_WENT_WRONG;
            const data = {
                statusCode: error.statusCode || 500,
                message: errorMessage,
                action: 'sendCompanionMessage',
                actionType: CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.NEW_GROUP_MESSAGE,
                data: {
                    id: messageId,
                    message: errorMessage,
                    createdAt: getCurrentTimestamp(),
                    updatedAt: getCurrentTimestamp(),
                    senderId,
                    groupId
                }
            };

            const errToThrow = new Error(errorMessage);
            errToThrow.isHandled = true;
            errToThrow.data = data;
            throw errToThrow;
        }
        await SendMessageService.sendMessagesToUsers(groupMemberUserIds, {
            data: userQueryMessage,
            actionType: CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.NEW_GROUP_MESSAGE
        });
    }
};

/**
 * @description Handles AI response generation
 * @param {Object} params - The parameters
 * @param {string} [params.groupId] - The group ID
 * @param {string} [params.messageId] - The message ID
 * @param {string} [params.senderId] - The sender ID
 * @param {string} [params.message] - The message
 * @param {boolean} [params.isExistingMessage] - Whether the message is existing
 * @param {string} [params.organizationId] - The organization ID
 * @param {string} [params.aiMessageId] - The AI message ID
 * @param {string} [params.connectionId] - The connection ID
 * @param {Object} [params.userGroupDetails] - The user group details
 * @param {Date} [params.startTime] - The start time
 * @param {string} [params.versionPrefix] - The version prefix
 */
const handleAIResponseGeneration = async ({
    groupId,
    messageId,
    senderId,
    message,
    isExistingMessage,
    organizationId,
    aiMessageId,
    connectionId,
    userGroupDetails,
    startTime,
    versionPrefix
}) => {
    try {
        const groupMemberUserIds = await fetchActiveGroupMemberUserIds({ groupId, versionPrefix });
        await handleMessageCreation({
            messageId,
            senderId,
            groupId,
            message,
            isExistingMessage,
            groupMemberUserIds,
            userGroupDetails
        });

        const userDetails = await fetchUserAndChildrenDetails({ userId: senderId, versionPrefix });
        const systemPrompt = generateOrganizationCompanionPrompt(userDetails, organizationId);
        const chatContext = {
            messages: [
                new Message(MessageRole.SYSTEM, systemPrompt)
            ]
        };
        const chatParams = {
            query: message + CONSTANTS.DATE_PREFIX + getCurrentTimestamp(),
            context: chatContext
        };

        aiMessageId = aiMessageId ?? uuidv4();

        const { fullMessage, contexts } = await streamAIResponseForGroups({
            groupId,
            chatParams,
            tenantId: organizationId,
            onMessage: async (chunkData) => {
                await SendMessageService.sendMessagesToUsers([senderId], {
                    data: {
                        groupId,
                        id: aiMessageId,
                        senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
                        userQueryMessageId: messageId,
                        message: chunkData,
                        createdAt: getCurrentTimestamp(),
                        updatedAt: getCurrentTimestamp()
                    },
                    actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.AI_COMPANION_GROUP_MESSAGE_STREAMING,
                    action: 'sendCompanionMessage'
                });
            }
        });

        const completedMessage = await MessageModel.create({
            groupId,
            id: aiMessageId,
            senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
            userQueryMessageId: messageId,
            message: fullMessage,
            contexts: JSON.stringify(contexts)
        });

        await SendMessageService.sendMessagesToUsers(groupMemberUserIds, {
            data: completedMessage,
            actionType: CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.NEW_GROUP_MESSAGE
        });

        await Utils.addMetricForQueryTime(startTime);
        return {
            statusCode: 200,
            message: 'AI response generated successfully',
            action: 'sendCompanionMessage',
            actionType: CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.NEW_GROUP_MESSAGE,
            data: completedMessage
        };
    } catch (error) {
        if (error.isHandled) {
            return error.data;
        }
        return await handleError({
            error,
            connectionId,
            messageId,
            aiMessageId,
            groupId,
            action: 'sendCompanionMessage',
            actionTypeToSendToClient: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.AI_COMPANION_GROUP_MESSAGE_STREAMING,
            actionTypeToReturn: CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.NEW_GROUP_MESSAGE,
            startTime
        });
    }
};

/**
* @description Gets the version prefix for redis keys
* <AUTHOR>
* @returns {Promise<String>} The version prefix
*/
const getVersionPrefixForRedisKeys = async () => {
    const versionPrefixConstant = await ConstantModel.get(
        CONSTANTS.FEED_VERSION_PREFIX
    );
    return versionPrefixConstant?.value ?? '';
};

const handleNewGroupCompanionMessage = async (eventBody, connectionId) => {
    const startTime = MOMENT();

    try {
        const { messageId, groupId, senderId, message, organizationId, groupMemberId, isExistingMessage } = eventBody;
        const { aiMessageId } = eventBody;

        const versionPrefix = await getVersionPrefixForRedisKeys();

        const userGroupDetails = await getUserGroupDetails({ groupMemberId });
        if (!userGroupDetails) {
            CONSOLE_LOGGER.error('--> Group member not found!');
            await Utils.addMetricForQueryTime(startTime);
            return {
                statusCode: 404,
                message: 'Group member not found!',
                action: 'sendCompanionMessage',
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_GROUP_COMPANION_MESSAGE,
                data: { messageId, senderId, groupId, message }
            };
        }

        return await handleAIResponseGeneration({
            groupId,
            messageId,
            senderId,
            message,
            isExistingMessage,
            organizationId,
            aiMessageId,
            connectionId,
            userGroupDetails,
            startTime,
            versionPrefix
        });
    } catch (error) {
        await Utils.addMetricForQueryTime(startTime);
        CONSOLE_LOGGER.error('Error handling new group companion message:', error);
        return {
            statusCode: error.statusCode || 500,
            body: error.message || 'Internal server error'
        };
    }
};

const handleNewCompanionMessage = async (eventBody, connectionId) => {
    const startTime = MOMENT();

    try {
        const versionPrefix = await getVersionPrefixForRedisKeys();

        const { senderId, message: userQuery, context } = eventBody;
        let { aiMessageId } = eventBody;
        aiMessageId = aiMessageId ?? uuidv4();
        const userDetails = await getUserDetails(senderId, versionPrefix);
        let finalAiMessage;

        const childQueryPlans = await planRelevantChildQueries(userDetails.children, userQuery);

        const childQueryResults =
            await generateResponsesForAllChildren(userDetails, childQueryPlans, userQuery, context);

        try {
            if (childQueryResults.isStreaming) {
                finalAiMessage = await handleStreamingChildResponse({ childQueryResults, connectionId, aiMessageId });
            } else {
                finalAiMessage =
                await handleNonStreamingChildResponse({ childQueryResults, context, userQuery, connectionId, aiMessageId });
            }
            await Utils.addMetricForQueryTime(startTime, isEvalRun);

            return {
                statusCode: 200,
                message: 'AI response generated successfully',
                action: 'sendCompanionMessage',
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_COMPANION_MESSAGE,
                data: finalAiMessage
            };
        } catch (error) {
            return await handleError({
                error,
                connectionId,
                aiMessageId,
                action: 'sendCompanionMessage',
                actionTypeToSendToClient: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.AI_COMPANION_MESSAGE_STREAMING,
                actionTypeToReturn: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_COMPANION_MESSAGE,
                startTime
            });
        }
    } catch (error) {
        CONSOLE_LOGGER.error('Error handling new companion message:', error);
        await Utils.addMetricForQueryTime(startTime, isEvalRun);
        return {
            statusCode: error.statusCode || 500,
            body: error.message || 'Internal server error'
        };
    }
};

const generateResponsesForAllChildren = async (userDetails, childQueryPlans, queryMessage, context) => {
    const responseService = new ChildResponseService(userDetails, context);
    const responseHandler = new ChatResponseHandler();

    // Streaming mode when only one child matched
    if (childQueryPlans?.length === 1) {
        const { childName, associatedOrganizations } = childQueryPlans[0];
        const stream = await responseService.handleSingleChildResponse(childName, associatedOrganizations, queryMessage);
        return { isStreaming: true, stream };
    }

    // Non-streaming mode when multiple children matched or no children matched
    if (childQueryPlans?.length > 0) {
        await Promise.all(childQueryPlans.map(async ({ childName, associatedOrganizations }) => {
            const chatResponse = await responseService.handleSpecificChildResponse(
                childName,
                associatedOrganizations,
                queryMessage
            );
            responseHandler.processResponse(chatResponse);
        }));
        return { isStreaming: false, ...responseHandler.getResults() };
    }

    // Fallback: general response when no child matches
    const chatResponse = await responseService.handleGeneralResponse(queryMessage);
    responseHandler.processResponse(chatResponse);
    return { isStreaming: false, ...responseHandler.getResults() };
};

const planRelevantChildQueries = async (childrenList, queryMessage, context) => {
    const planningService = new ChildQueryPlanningService();
    return await planningService.generateQueryPlan(childrenList, queryMessage, context);
};

/**
 * @description Handles non-streaming response for child queries
 * @param {Object} params - The parameters
 * @param {Object} params.childQueryResults - The child query results
 * @param {Object} params.context - The context
 * @param {string} params.queryMessage - The query message
 * @param {string} params.connectionId - The connection ID
 * @param {string} params.aiMessageId - The AI message ID
 * @return {Promise<Object>} - The final AI message object
 */
const handleNonStreamingChildResponse = async ({ childQueryResults, context, queryMessage, connectionId, aiMessageId }) => {
    const { allChildContext, childRelatedMessages, childQuestions } = childQueryResults;
    const systemPrompt = await getChildrenResponseAggregatorPrompt(childRelatedMessages);
    const systemMessage = new SystemMessage(systemPrompt);

    const messages = [
        systemMessage,
        ...getChatMessagesForFinalResponse(context),
        new HumanMessage(queryMessage + CONSTANTS.DATE_PREFIX + getCurrentTimestamp())
    ];

    const { fullMessage } = await streamAIResponseForCompanion({
        messages,
        onMessage: async (chunkData) => {
            if (!isEvalRun) {
                await SendMessageService.sendMessageToConnection({
                    connectionId,
                    messageData: buildStreamingChunkMessage(chunkData, aiMessageId)
                });
            }
        }
    });

    return {
        id: aiMessageId,
        senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
        ...(isEvalRun && { childQuestions }),
        message: fullMessage,
        contexts: JSON.stringify(flattenContexts(allChildContext)),
        createdAt: getCurrentTimestamp(),
        updatedAt: getCurrentTimestamp()
    };
};

/**
 * @description Handles streaming response for child queries
 * @param {Object} params - The parameters
 * @param {Object} params.childQueryResults - The child query results
 * @param {string} params.connectionId - The connection ID
 * @param {string} params.aiMessageId - The AI message ID
 * @return {Promise<Object>} - The final AI message object
 */
const handleStreamingChildResponse = async ({ childQueryResults, connectionId, aiMessageId }) => {
    const { stream } = childQueryResults;

    const { fullMessage, contexts } = await streamAIResponseForCompanion({
        preResolvedStream: stream,
        onMessage: async (chunkData) => {
            if (!isEvalRun) {
                await SendMessageService.sendMessageToConnection({
                    connectionId,
                    messageData: buildStreamingChunkMessage(chunkData, aiMessageId)
                });
            }
        }
    });

    return {
        id: aiMessageId,
        senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
        message: fullMessage,
        contexts: JSON.stringify(flattenContexts(contexts)),
        createdAt: getCurrentTimestamp(),
        updatedAt: getCurrentTimestamp()
    };
};

/**
 * @description Checks if the stream promise is an async iterable
 * @param {Promise} streamPromise - The stream promise to check
 * @returns {boolean} - True if the stream promise is an async iterable, false otherwise
 */
const isAsyncIterable = (streamPromise) =>
    streamPromise != null && typeof streamPromise[Symbol.asyncIterator] === 'function';

/**
 * @description Builds a streaming chunk message
 * @param {string} chunkData - The chunk data to include in the message
 * @param {string} aiMessageId - The AI message ID
 * @returns {Object} - The streaming chunk message object
 */
const buildStreamingChunkMessage = (chunkData, aiMessageId) => ({
    data: {
        id: aiMessageId,
        senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
        message: chunkData,
        createdAt: getCurrentTimestamp(),
        updatedAt: getCurrentTimestamp()
    },
    actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.AI_COMPANION_MESSAGE_STREAMING,
    action: 'sendCompanionMessage'
});

/**
 * @description Flattens contexts by removing pageContent and returning the rest of the properties
 * @param {Array} contexts - The contexts to flatten
 * @returns {Array} - The flattened contexts
 */
const flattenContexts = (contexts) =>
    // eslint-disable-next-line no-unused-vars
    contexts.flat().map(({ pageContent, ...rest }) => rest);

/**
 * @description Gets user details based on the sender ID and version prefix
 * @param {string} senderId - The sender ID
 * @param {string} versionPrefix - The version prefix for redis keys
 * @returns {Promise<Object>} - The user details
 */
const getUserDetails = (senderId, versionPrefix) =>
    isEvalRun ? mockUserDetails : fetchUserAndChildrenDetails({ userId: senderId, versionPrefix });

module.exports = {
    companionMessageHandler,
    handleNewCompanionMessage
};
