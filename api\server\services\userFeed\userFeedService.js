const Utils = require('../../util/utilFunctions');

/**
 * Class represents services for user profile.
 */
class UserFeedService {
    /**
     * @desc This function is being used to get user feeds
     * <AUTHOR>
     * @since 19/10/2023
     * @param {Object} user user
     */
    static async getUserFeeds (req) {
        const feeds = Utils.getFeeds();
        const page = parseInt(req.query.page, 10) || 1;
        const pageSize = parseInt(req.query.pageSize, 10) || 10;
        const totalFeeds = feeds.length;
        const totalPages = Math.ceil(totalFeeds / pageSize);

        const startIndex = (page - 1) * pageSize;
        const endIndex = Math.min(startIndex + pageSize - 1, totalFeeds - 1);

        const paginatedFeeds = feeds.slice(startIndex, endIndex + 1);

        const hasPrevPage = page > 1;
        const hasNextPage = page < totalPages;

        return {
            docs: paginatedFeeds,
            currentPage: page,
            total: totalFeeds,
            pageSize,
            totalPages,
            hasPrevPage,
            hasNextPage
        };
    }
}

module.exports = UserFeedService;
