/* eslint-disable max-len */
module.exports = {
    // For AES, this is always 16
    IV_LENGTH: 16,
    LOG_LEVEL: 'debug',
    EVENT_IMAGE: {
        ALLOWED_TYPE: ['image/jpg', 'image/jpeg', 'image/png']
    },
    USER_DOCUMENT_FILE: {
        ALLOWED_TYPE: ['image/jpg', 'image/jpeg', 'image/png', 'application/pdf']
    },
    REGEX: {
        EMAIL: /^[A-Za-z0-9](\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\.([a-z]{1,6}\.)?[a-z]{2,6}$/,
        NAME: /^[a-zA-Z0-9,'~._^ -]{3,100}$/,
        ALPHA_ONLY: /^[a-zA-Z']*$/,
        ALPHA_SPECIAL_CHAR: /^[ A-Za-z0-9_@./#&+-]*$/,
        ALPHA_SPECIAL_CHAR_EXCEPT_NUMBER: /^[ A-Za-z_@./#&+-]*$/,
        FULL_ACCESS: /^[^<> ?//\\]+$/,
        ALPHA_NUMARIC: /^[\w@ ]+$/,
        templateName: /^[ A-Za-z0-9_@./#&+-]*$/,
        SUBJECT: /^[ A-Za-z0-9_@./#&+-]*$/,
        URL: /(http(s)?:\/\/www\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\.[a-z]{2,4}\b([-a-zA-Z0-9@:%+.~#?&//=]*)/,
        MOBILE: /^([+]\d{1,2})?\d{10}$/,
        PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
        UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    },
    OTPLENGTH: 6,
    OTP_EXPIRY_DURATION: 30,
    VERIFIED: {
        PENDING: 0,
        ACTIVE: 1
    },
    EMAIL_TEMPLATE: {
        REGISTER: 'verificationOtpMail.html',
        FORGOTPASSWORD: 'forgotPassword.html'
    },
    OTP_TYPE: ['register', 'forgotPassword'],
    STATUS: {
        INACTIVE: 'inactive',
        ACTIVE: 'active',
        SUSPENDED: 'suspended',
        FREEZED: 'freezed',
        PUBLISHED: 'published'
    },
    DEFAULT_PAGE_SIZE: 10,
    ENVIRONMENT: {
        TESTING: 'testing',
        LOCAL: 'local',
        DEV: 'dev',
        PRODUCTION: 'production'
    },
    DEVELOPERS_EMAIL: '<EMAIL>',
    PAYMENT_STATUS: {
        APPROVED: 'approved',
        PENDING: 'pending'
    },
    QUANTITY_TYPE: ['People', 'Items'],
    SES_HOST: 'email-smtp.us-east-2.amazonaws.com',
    CLIENT_INFO: {
        EMAIL: '<EMAIL>',
        HELP_EMAIL: '<EMAIL>'
    },
    APP_NAME: 'Vaalee',
    ROLE: {
        USER: 1,
        ADMIN: 4
    },
    ACCESS_LEVEL: {
        APP: 'app',
        ORG_APP: 'org_app',
        ROOT: 'root'
    },
    AWS_S3_PUBLIC_BUCKET: 'vaalee-assets',
    COMPRESSION_QUALITY: 60,
    OPEN_SEARCH: {
        COLLECTION: {
            EVENT: 'events',
            CHILD: 'children',
            POST: 'posts',
            FUNDRAISER: 'fundraisers'
        },
        SEARCHABLE_FIELDS: {
            'events': ['title', 'details.details'],
            'children': ['firstName', 'lastName'],
            'posts': ['title', 'subTitle'],
            'fundraisers': ['title', 'description']
        },
        INDICES: {
            'events': 'eventSchema',
            'children': 'childSchema',
            'posts': 'postSchema',
            'fundraisers': 'fundraiserSchema'
        }
    },
    MEMBERSHIP_TYPES: {
        CHILD: 'child',
        FAMILY: 'family'
    },
    DEFAULT_MIN_PLATFORM_FEE_AMOUNT: 1,
    FUNDRAISER_TYPE: {
        BOOSTER: 'booster'
    },
    KEY_FOR_USER_EVENTS: 'user-events',
    KEY_FOR_USER_REGISTERED_EVENTS: 'user-registered-events',
    KEY_FOR_USER_CALENDAR_EVENTS: 'user-calendar-events',
    KEY_FOR_CHILD_EVENTS: 'child-events',
    KEY_FOR_CHILD_REGISTERED_EVENTS: 'child-registered-events',
    KEY_FOR_CHILD_CALENDAR_EVENTS: 'child-calendar-events',
    KEY_FOR_CHILD_DETAILS: 'child-details',
    KEY_FOR_EVENT_DETAILS: 'event-details',
    KEY_FOR_FUNDRAISER_DETAILS: 'fundraiser-details',
    KEY_FOR_POST_DETAILS: 'post-details',
    KEY_FOR_ORGANIZATION_DETAILS: 'organization-details',
    FEED_VERSION_PREFIX: 'FeedVersionPrefix'
};
