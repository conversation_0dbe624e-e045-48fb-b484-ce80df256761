const handler = require('../index');
const AWS = require('aws-sdk');
const AWSMock = require('jest-aws-sdk-mock');
const sinon = require('sinon');
const Groups = require('../server/models/groups.model');
const Redis = require('ioredis');
const CONSOLE_LOGGER = require('../server/logger');
const organizationMemberModel = require('../server/models/organizationMember.model');
const ConstantModel = require('../server/models/constant.model');
const CONSTANTS = require('../server/constants');

describe('Organization', () => {
    let sandbox;
    let stubRedisConnect;
    let pipelineExecStub;
    let pipelineStub;

    before(() => {
        AWSMock.setSDKInstance(AWS);
        sandbox = sinon.createSandbox();
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
        pipelineExecStub = sinon.stub();
        pipelineStub = sinon.stub(Redis.prototype, 'pipeline').returns({ exec: pipelineExecStub });
    });

    after(() => {
        AWSMock.restore();
        sinon.restore();
        sandbox.restore();
        pipelineStub.restore();
    });

    it('Should process INSERT organization for super organization', async () => {
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });

        sinon.stub(ConstantModel, 'get').withArgs(CONSTANTS.FEED_VERSION_PREFIX).resolves({ value: 'v1' });

        const loggerStub = sinon.stub(CONSOLE_LOGGER, 'info');

        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Organization/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    id: { S: '3e4406aa-b95b-489e-9e58-cfa746eed023' },
                    name: { S: 'Test Organization' },
                    category: { S: 'Super Organization' },
                    createdAt: { S: '2023-11-20T11:20:09.158Z' },
                    updatedAt: { S: '2023-11-20T11:20:09.158Z' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);
        loggerStub.restore();
    });

    it('should process INSERT organization and create organization group', async () => {
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });

        sinon.stub(Groups, 'create').resolves();
        sinon.stub(organizationMemberModel, 'get').resolves(null);

        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Organization/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    id: { S: '3e4406aa-b95b-489e-9e58-cfa746eed023' },
                    name: { S: 'Test Organization' },
                    category: { S: 'School' },
                    createdAt: { S: '2023-11-20T11:20:09.158Z' },
                    updatedAt: { S: '2023-11-20T11:20:09.158Z' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(Groups.create);

        Groups.create.restore();
        organizationMemberModel.get.restore();
    });

    it('should process INSERT organization and create organization group and insert admins to group', async () => {
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });

        sinon.stub(Groups, 'create').resolves();
        sinon.stub(organizationMemberModel, 'get').resolves({
            users: [
                {
                    id: '123',
                    role: 'admin',
                    status: 'active'
                },
                {
                    id: '456',
                    role: 'admin',
                    status: 'deleted'
                }
            ]
        });

        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Organization/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    id: { S: '3e4406aa-b95b-489e-9e58-cfa746eed023' },
                    name: { S: 'Test Organization' },
                    category: { S: 'School' },
                    createdAt: { S: '2023-11-20T11:20:09.158Z' },
                    updatedAt: { S: '2023-11-20T11:20:09.158Z' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(Groups.create);

        Groups.create.restore();
        organizationMemberModel.get.restore();
    });

    it('Should handle error when inserting organization', async () => {
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });

        sinon.stub(Groups, 'create').rejects();
        const loggerStub = sinon.stub(CONSOLE_LOGGER, 'error');

        const record = {
            eventName: 'INSERT',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Organization/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                NewImage: {
                    id: { S: '3e4406aa-b95b-489e-9e58-cfa746eed023' },
                    name: { S: 'Test Organization' },
                    category: { S: 'School' },
                    createdAt: { S: '2023-11-20T11:20:09.158Z' },
                    updatedAt: { S: '2023-11-20T11:20:09.158Z' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        Groups.create.restore();
        loggerStub.restore();
    });

    it('should process MODIFY organization', async () => {
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });

        sinon.stub(Groups, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    where: sinon.stub().returns({
                        in: sinon.stub().returns({
                            exec: sinon.stub().resolves([{ id: 'group1', save: () => { } }])
                        })
                    })
                })
            })
        });

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Organization/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                OldImage: {
                    id: { S: '3e4406aa-b95b-489e-9e58-cfa746eed023' },
                    name: { S: 'Test Organization' },
                    category: { S: 'School' },
                    createdAt: { S: '2023-11-20T11:20:09.158Z' },
                    updatedAt: { S: '2023-11-20T11:20:09.158Z' }
                },
                NewImage: {
                    id: { S: '3e4406aa-b95b-489e-9e58-cfa746eed023' },
                    name: { S: 'Test Organization 1' },
                    category: { S: 'School' },
                    createdAt: { S: '2023-11-20T11:20:09.158Z' },
                    updatedAt: { S: '2023-11-20T11:20:09.158Z' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(Groups.query);

        Groups.query.restore();
    });

    it('should process MODIFY organization and handle if group doesn\'t exist', async () => {
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });

        sinon.stub(Groups, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    where: sinon.stub().returns({
                        in: sinon.stub().returns({
                            exec: sinon.stub().resolves([])
                        })
                    })
                })
            })
        });

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Organization/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                OldImage: {
                    id: { S: '3e4406aa-b95b-489e-9e58-cfa746eed023' },
                    name: { S: 'Test Organization' },
                    category: { S: 'School' },
                    createdAt: { S: '2023-11-20T11:20:09.158Z' },
                    updatedAt: { S: '2023-11-20T11:20:09.158Z' }
                },
                NewImage: {
                    id: { S: '3e4406aa-b95b-489e-9e58-cfa746eed023' },
                    name: { S: 'Test Organization' },
                    category: { S: 'School' },
                    logo: { S: 'Test logo URL' },
                    createdAt: { S: '2023-11-20T11:20:09.158Z' },
                    updatedAt: { S: '2023-11-20T11:20:09.158Z' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(Groups.query);

        Groups.query.restore();
    });

    it('Should process MODIFY organization but no change in name or logo', async () => {
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });

        sinon.stub(Groups, 'query').resolves();

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Organization/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                OldImage: {
                    id: { S: '3e4406aa-b95b-489e-9e58-cfa746eed023' },
                    name: { S: 'Test Organization' },
                    category: { S: 'School' },
                    logo: { S: 'Test logo URL' },
                    createdAt: { S: '2023-11-20T11:20:09.158Z' },
                    updatedAt: { S: '2023-11-20T11:20:09.158Z' }
                },
                NewImage: {
                    id: { S: '3e4406aa-b95b-489e-9e58-cfa746eed023' },
                    name: { S: 'Test Organization' },
                    category: { S: 'School' },
                    logo: { S: 'Test logo URL' },
                    createdAt: { S: '2023-11-20T11:20:09.158Z' },
                    updatedAt: { S: '2023-11-20T11:20:09.158Z' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.notCalled(Groups.query);

        Groups.query.restore();
    });

    it('Should handle error when modifying organization', async () => {
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });

        sinon.stub(Groups, 'query').rejects();
        const loggerStub = sinon.stub(CONSOLE_LOGGER, 'error');

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Organization/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                OldImage: {
                    id: { S: '3e4406aa-b95b-489e-9e58-cfa746eed023' },
                    name: { S: 'Test Organization' },
                    category: { S: 'School' },
                    logo: { S: 'Test logo URL' }
                },
                NewImage: {
                    id: { S: '3e4406aa-b95b-489e-9e58-cfa746eed023' },
                    name: { S: 'Test Organization 1' },
                    category: { S: 'School' },
                    logo: { S: 'Test logo URL 1' }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        Groups.query.restore();
        loggerStub.restore();
    });

    it('Should handle redis connection error', async () => {
        stubRedisConnect.callsFake(async function () {
            this.setStatus('error');
        });

        const loggerStub = sinon.stub(CONSOLE_LOGGER, 'error');

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Organization/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                OldImage: {},
                NewImage: {}
            }
        };

        const event = { Records: [record] };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        loggerStub.restore();
    });
});
