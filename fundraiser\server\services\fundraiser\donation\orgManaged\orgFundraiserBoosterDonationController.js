const OrgFundraiserBoosterDonation = require('./orgFundraiserBoosterDonationService');
const Utils = require('../../../../util/utilFunctions');

/**
 * Class represents controller for org managed fundraiser booster routes.
 */
class OrgFundraiserBoosterDonationController {
    /**
   * @desc This function is being used to add donation for org managed fundraiser booster
   * <AUTHOR>
   * @since 13/09/2024
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async addDonation (req, res) {
        try {
            CONSOLE_LOGGER.info('in here add donations for org managed fundraiser booster');
            const data = await OrgFundraiserBoosterDonation.addDonation(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.ADD_FUNDRAISER_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in add donation for org managed fundraiser booster', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to get org managed fundraiser booster details
   * <AUTHOR>
   * @since 13/09/2024
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async getBoosterDetails (req, res) {
        try {
            CONSOLE_LOGGER.info('in here get org managed fundraiser booster details');
            const data = await OrgFundraiserBoosterDonation.getBoosterDetails(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.ADD_FUNDRAISER_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get org managed fundraiser booster details', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    static async createStripeSession (req, res) {
        try {
            CONSOLE_LOGGER.info('in here get Stripe Session for org managed fundraiser booster');
            const data = await OrgFundraiserBoosterDonation.createStripeSession(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.ADD_FUNDRAISER_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get Stripe Session for org managed fundraiser booster', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    static async getDonations (req, res) {
        try {
            CONSOLE_LOGGER.info('in here get donation for org managed fundraiser booster');
            const data = await OrgFundraiserBoosterDonation.getDonations(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.ADD_FUNDRAISER_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get donations for org managed fundraiser booster', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to manually add offline donations for fundraiser booster
   * <AUTHOR>
   * @since 01/10/2024
   * @param {Object} req Request
   * @param {function} res Response
   */
    static async addManualDonations (req, res) {
        try {
            CONSOLE_LOGGER.info('in here donate for booster');
            const data = await OrgFundraiserBoosterDonation.addManualDonations(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.ADD_FUNDRAISER_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in donate for booster', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to get session details for donation transaction
     * <AUTHOR>
     * @since 03/10/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async getSessionDetails (req, res) {
        try {
            CONSOLE_LOGGER.info('in here get session details for donation transaction');
            const data = await OrgFundraiserBoosterDonation.getSessionDetails(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.ADD_FUNDRAISER_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get session details for org managed fundraiser booster', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = OrgFundraiserBoosterDonationController;
