/**
 *  routes and schema for User
 */


/**
 * @openapi
 * components:
 *  schemas:
 *      resendOnboardingMail:
 *          type: object
 *          required:
 *              - id
 *          properties:
 *              id:
 *                  type: string
 *                  description: id of the organization
 *          example:
 *                   id: 'organization id'
 *
 *      successResendOnboarding:
 *          type: object
 *          properties:
 *              status:
 *                  type: number
 *                  description: status if data exists
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Mail sent successfully
 */

/**
 * @openapi
 * /organization/resend-onboarding:
 *  post:
 *      security:
 *        - bearerAuth: []
 *      tags: [Organization]
 *      summary: Resend mail to the organization admin for onboarding
 *      parameters:
 *          - in: query
 *            name: orgId
 *            schema:
 *                type: string
 *            description: uuid of the organization
 *
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successResendOnboarding'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */
