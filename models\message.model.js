const dynamoose = require('dynamoose');
const { v4: uuidv4 } = require('uuid');

const messageSchema = new dynamoose.Schema({
    id: {
        hashKey: true,
        type: String,
        default: () => uuidv4()
    },
    groupId: {
        type: String,
        required: true,
        index: {
            global: true,
            rangeKey: 'createdAt',
            name: 'groupId-createdAt-index',
            project: true
        }
    },
    senderId: {
        type: String,
        required: true
    },
    userQueryMessageId: {
        type: String
    },
    message: {
        type: String
    },
    mediaName: {
        type: String
    },
    mediaType: {
        type: String
    },
    mediaDisplayName: {
        type: String
    },
    mediaThumbnailName: {
        type: String
    },
    replyMessage: {
        type: Object,
        required: false,
        schema: {
            messageId: {
                type: String,
                required: true
            },
            senderId: {
                type: String,
                required: true
            },
            message: {
                type: String
            },
            mediaName: {
                type: String
            },
            mediaType: {
                type: String
            },
            mediaDisplayName: {
                type: String
            }
        },
        default: null
    },
    isEdited: {
        type: Boolean,
        default: false
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isFlagged: {
        type: Boolean,
        default: false
    },
    flagCount: {
        type: Number,
        default: 0
    },
    contexts: {
        type: String
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('Message', messageSchema);
