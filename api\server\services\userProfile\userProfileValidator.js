const validation = require('../../util/validation');
const GeneralError = require('../../util/GeneralError');

/**
 * Class represents validations for child.
 */
class UserProfileValidator extends validation {
    constructor (body, locale) {
        super(locale);
        this.body = body;
        this.locale = locale;
    }

    /**
     * @desc This function is being used to validate change password of user
     * <AUTHOR>
     * @since 20/12/2023
     */
    validateChangePassword () {
        const { oldPassword, newPassword } = this.body;
        /** super.field(oldPassword, 'Old Password'); */
        super.password(newPassword, 'New Password');
        if (oldPassword === newPassword) {
            throw new GeneralError(this.locale(MESSAGES.OLD_NEW_PASSWORD_SAME), 400);
        }
    }

    /**
     * @desc This function is being used to validate update profile of user
     */
    validateUpdateProfile () {
        const { firstName, lastName } = this.body;
        super.name(firstName, 'First Name');
        super.name(lastName, 'Last Name');
    }
}

module.exports = UserProfileValidator;
