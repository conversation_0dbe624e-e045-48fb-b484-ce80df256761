const sinon = require('sinon');
const AWS = require('aws-sdk');
const AWSMock = require('jest-aws-sdk-mock');
const Redis = require('ioredis');
const CONSOLE_LOGGER = require('../server/logger');
const handler = require('../index');
const RedisUtil = require('../server/redisUtil');
const { beforeEach, afterEach } = require('mocha');
const Child = require('../server/models/child.model');
const Fundraiser = require('../server/models/fundraiser.model');
const Organization = require('../server/models/organization.model');
const ConstantModel = require('../server/models/constant.model');
const { SQSClient } = require('@aws-sdk/client-sqs');
const orgManagedFundraiserBoosterDonationModel = require('../server/models/orgManagedFundraiserBoosterDonation.model');
const Stripe = require('../server/Stripe');
const CONSTANTS = require('../server/constants');
const constants = require('../server/constants');

describe('Fundraiser signup service', () => {
    let sandbox;
    let stubRedisConnect;
    let pipelineExecStub;
    let pipelineStub;
    let sendStub;
    let loggerStub;

    before(() => {
        AWSMock.setSDKInstance(AWS);
        sandbox = sinon.createSandbox();
        sendStub = sandbox.stub(SQSClient.prototype, 'send');
    });

    after(() => {
        AWSMock.restore();
        sinon.restore();
        sandbox.restore();
        pipelineStub.restore();
        sendStub.restore();
        loggerStub.restore();
    });

    beforeEach(() => {
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
        pipelineExecStub = sinon.stub();
        pipelineStub = sinon.stub(Redis.prototype, 'pipeline').returns({ exec: pipelineExecStub });
        loggerStub = sinon.stub(CONSOLE_LOGGER, 'error');
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        sendStub.resolves();
        sinon.stub(Child, 'get').resolves({
            id: 'ace0149e-447d-4f7f-9402-95a96963de9b',
            associatedImageOrColor: '#FACD01',
            guardians: ['user1', 'user2']
        });
        sinon.stub(ConstantModel, 'get').withArgs(CONSTANTS.FEED_VERSION_PREFIX).resolves({ value: 'v1' });
    });

    afterEach(() => {
        sinon.restore();
    });

    try {
        it('should update fundraiser if user register for fundraiser and fundraiser contains child and user id', async () => {
            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/FundraiserSignup/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'eventId': { 'S': '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        'organizationId': { 'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc' },
                        'paymentDetails': {
                            'M': {
                                'stripeConnectAccountId': { 'S': 'accountId' },
                                'stripeCustomerId': { 'S': 'customerId' },
                                'stripePaymentIntentId': { 'S': 'intentId' },
                                'paymentStatus': { 'S': 'pending' },
                                'paymentType': { 'S': 'stripe' }
                            }
                        },
                        'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                        'createdBy': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' }
                    }

                }
            };

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify(
                {
                    id: '0fd6a871-a6f5-4690-b06a-d786f1361eef', title: 'Test fundraiser',
                    startDate: '2020-01-01T00:00:00.000Z', organizationId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                    eventType: 'Test event type', eventScope: 'Test event scope',
                    status: 'unpublished', photoURL: 'Test photo url',
                    createdAt: '2020-01-01T00:00:00.000Z', updatedAt: '2020-01-01T00:00:00.000Z'
                }));

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([
                JSON.stringify({
                    fundraiserSignupId: '470f3434-7cb5-402f-81fc-ef3db68b73ce',
                    fundraiserId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                    childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    isSignedUp: false, status: 'pending'
                })]);

            sinon.stub(RedisUtil, 'getScoreOfMember').resolves(10000000);

            const addFundraiserReferenceToSortedSetStub = sinon.stub(RedisUtil, 'addEventReferenceToSortedSet');
            sinon.stub(Fundraiser, 'get').resolves({ fundraiserType: constants.FUNDRAISER_TYPES.BOOSTER, save: () => { } });

            addFundraiserReferenceToSortedSetStub.resolves();

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(addFundraiserReferenceToSortedSetStub);
        });
        it('should update fundraiser if user register for fundraiser', async () => {
            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/FundraiserSignup/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'eventId': { 'S': '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        'organizationId': {
                            'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc'
                        },
                        'paymentDetails': {
                            'M': {
                                'stripeConnectAccountId': { 'S': 'accountId' },
                                'stripeCustomerId': { 'S': 'customerId' },
                                'stripePaymentIntentId': { 'S': 'intentId' },
                                'paymentStatus': { 'S': 'approved' },
                                'paymentType': { 'S': 'stripe' }
                            }
                        },
                        'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                        'createdBy': {
                            'S':
                                'a967e334-60d4-4efe-8a9d-c71ecc525977'
                        }
                    }
                }
            };

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                title: 'Test fundraiser',
                startDate: '2020-01-01T00:00:00.000Z',
                organizationId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                fundraiserType: 'booster',
                eventScope: 'Test event scope',
                status: 'unpublished',
                photoURL: 'Test photo url',
                createdAt: '2020-01-01T00:00:00.000Z',
                updatedAt: '2020-01-01T00:00:00.000Z'
            }));

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([
                JSON.stringify({
                    fundraiserSignupId: '470f3434-7cb5-402f-81fc-ef3db68b73ce',
                    fundraiserId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                    childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    isSignedUp: false,
                    status: 'pending'
                })]);

            sinon.stub(RedisUtil, 'getScoreOfMember').resolves(10000000);

            const addFundraiserReferenceToSortedSetStub = sinon.stub(RedisUtil, 'addEventReferenceToSortedSet');
            sinon.stub(Fundraiser, 'get').resolves({ fundraiserType: constants.FUNDRAISER_TYPES.BOOSTER, save: () => { } });

            addFundraiserReferenceToSortedSetStub.resolves();

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(addFundraiserReferenceToSortedSetStub);
        });
        it('should add fundraiser to registered key so i can get my feeds', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/FundraiserSignup/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'eventId': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' },
                        'organizationId': {
                            'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc'
                        },
                        'paymentDetails': {
                            'M': {
                                'paymentStatus': { 'S': 'approved' },
                                'paymentType': { 'S': 'cash' }
                            }
                        },
                        'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                        'createdBy': {
                            'S':
                                'a967e334-60d4-4efe-8a9d-c71ecc525977'
                        }
                    },
                    OldImage: {
                        'eventId': { 'S': 'c16c88cd-681a-4676-bacd-0058d0efe99b' },
                        'organizationId': {
                            'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc'
                        },
                        'paymentDetails': {
                            'M': {
                                'paymentStatus': { 'S': 'pending' },
                                'paymentType': { 'S': 'cash' }
                            }
                        },
                        'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                        'createdBy': {
                            'S':
                                'a967e334-60d4-4efe-8a9d-c71ecc525977'
                        }
                    }
                }
            };

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                title: 'Test fundraiser',
                startDate: '2020-01-01T00:00:00.000Z',
                organizationId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                eventType: 'Test event type',
                eventScope: 'Test event scope', status: 'unpublished',
                photoURL: 'Test photo url',
                createdAt: '2020-01-01T00:00:00.000Z', updatedAt: '2020-01-01T00:00:00.000Z'
            }));

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([
                JSON.stringify({
                    fundraiserSignupId: '470f3434-7cb5-402f-81fc-ef3db68b73ce',
                    fundraiserId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                    childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    isSignedUp: false,
                    status: ''
                })]);

            const addFundraiserReferenceToSortedSetStub = sinon.stub(RedisUtil, 'addEventReferenceToSortedSet');

            addFundraiserReferenceToSortedSetStub.resolves();

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(addFundraiserReferenceToSortedSetStub);
        });

        it('should refresh registered fundraisers if the payment is canceled', async () => {
            const record = {
                eventName: 'REMOVE',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/FundraiserSignup/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        'eventId': { 'S': '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        'organizationId': {
                            'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc'
                        },
                        'paymentDetails': {
                            'M': {
                                'stripeConnectAccountId': { 'S': 'accountId' },
                                'stripeCustomerId': { 'S': 'customerId' },
                                'stripePaymentIntentId': { 'S': 'intentId' },
                                'paymentStatus': { 'S': 'pending' },
                                'paymentType': { 'S': 'stripe' }
                            }
                        },
                        'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                        'createdBy': {
                            'S':
                                'a967e334-60d4-4efe-8a9d-c71ecc525977'
                        }
                    }
                }
            };

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                participants: [],
                title: 'Test event',
                details: { startDateTime: '2020-01-01T00:00:00.000Z' },
                comments: 'Test comment',
                organizationId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                eventType: 'Test event type',
                eventScope: 'Test event scope',
                sheetUrl: 'Test sheet url',
                fee: '100',
                status: 'unpublished',
                photoURL: 'Test photo url',
                createdAt: '2020-01-01T00:00:00.000Z',
                updatedAt: '2020-01-01T00:00:00.000Z'
            }));

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([JSON.stringify({
                fundraiserId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                fundraiserSignupId: '470f3434-7cb5-402f-81fc-ef3db68b73ce',
                childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                isSignedUp: false,
                status: ''
            })]);

            const removeRegisteredEventsStub = sinon.stub(RedisUtil, 'removeMemberFromSortedSet');

            const event = {
                Records: [record]
            };

            await handler.handler(event);
            sinon.assert.called(removeRegisteredEventsStub);
        });

        it('should handle if fundraiser not found for updating', async () => {
            const record = {
                eventName: 'REMOVE',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/FundraiserSignup/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        'eventId': { 'S': '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        'organizationId': {
                            'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc'
                        },
                        'paymentDetails': {
                            'M': {
                                'stripeConnectAccountId': { 'S': 'accountId' },
                                'stripeCustomerId': { 'S': 'customerId' },
                                'stripePaymentIntentId': { 'S': 'intentId' },
                                'paymentStatus': { 'S': 'pending' },
                                'paymentType': { 'S': 'stripe' }
                            }
                        },
                        'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                        'createdBy': {
                            'S':
                                'a967e334-60d4-4efe-8a9d-c71ecc525977'
                        }
                    }
                }
            };

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                participants: [],
                title: 'Test event',
                details: { startDateTime: '2020-01-01T00:00:00.000Z' },
                comments: 'Test comment',
                organizationId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                eventType: 'Test event type',
                eventScope: 'Test event scope',
                sheetUrl: 'Test sheet url',
                fee: '100',
                status: 'unpublished',
                photoURL: 'Test photo url',
                createdAt: '2020-01-01T00:00:00.000Z',
                updatedAt: '2020-01-01T00:00:00.000Z'
            }));

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([
                JSON.stringify({
                    eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eeg',
                    childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    isSignedUp: false,
                    status: ''
                })
            ]);

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(RedisUtil.getElementsOfSortedSetByScore);
        });

        it('should update total amount raised in fundraiser feed', async () => {
            const record = {
                eventName: 'INSERT',
                // eslint-disable-next-line max-len
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/OrgManagedFundraiserBoosterDonation/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'fundraiserBoosterId': { 'S': '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        'organizationId': { 'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc' },
                        'amount': { 'N': '100' },
                        'stripePaymentIntentId': { 'S': 'pi_test' },
                        'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                        'createdBy': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'status': { 'S': 'success' }
                    }
                }
            };

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([JSON.stringify(
                {
                    fundraiserBoosterId: '470f3434-7cb5-402f-81fc-ef3db68b73ce',
                    fundraiserId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                    child: { id: 'ace0149e-447d-4f7f-9402-95a96963de9b', associatedImageOrColor: '#FACD01' },
                    isSignedUp: false,
                    status: ''
                })]);

            sinon.stub(orgManagedFundraiserBoosterDonationModel, 'query')
                .returns({
                    eq: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        exec: sinon.stub().resolves([{ amount: 100 }, { amount: 200 }])
                                    })
                                })
                            })
                        })
                    })
                });

            sinon.stub(Fundraiser, 'get').resolves({
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                title: 'Test fundraiser',
                organizationId: 'fdaab0cb-859d-400b-b788-0369e2e822bc'
            });

            sinon.stub(Organization, 'get').resolves({
                id: 'fdaab0cb-859d-400b-b788-0369e2e822bc',
                name: 'Test organization'
            });

            sinon.stub(Stripe, 'retrievePaymentIntent').returns({
                payment_method: {
                    billing_details: {
                        email: '<EMAIL>',
                        name: 'test'
                    },
                    type: 'card',
                    card: {
                        last4: '1234'
                    }
                },
                created: 1715606400,
                id: 'pi_test',
                status: 'succeeded'
            });

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(sendStub);
        });

        it('should update total amount raised in fundraiser feed and should not send email if email is not present', async () => {
            const record = {
                eventName: 'INSERT',
                // eslint-disable-next-line max-len
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/OrgManagedFundraiserBoosterDonation/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'fundraiserBoosterId': { 'S': '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        'organizationId': { 'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc' },
                        'amount': { 'N': '100' },
                        'stripePaymentIntentId': { 'S': 'pi_test' },
                        'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                        'createdBy': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'status': { 'S': 'success' }
                    }
                }
            };

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([JSON.stringify(
                {
                    fundraiserBoosterId: '470f3434-7cb5-402f-81fc-ef3db68b73ce',
                    fundraiserId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                    child: { id: 'ace0149e-447d-4f7f-9402-95a96963de9b', associatedImageOrColor: '#FACD01' },
                    isSignedUp: false,
                    status: ''
                })]);

            sinon.stub(orgManagedFundraiserBoosterDonationModel, 'query')
                .returns({
                    eq: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        exec: sinon.stub().resolves([{ amount: 100 }, { amount: 200 }])
                                    })
                                })
                            })
                        })
                    })
                });

            sinon.stub(Fundraiser, 'get').resolves({
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                title: 'Test fundraiser',
                organizationId: 'fdaab0cb-859d-400b-b788-0369e2e822bc'
            });

            sinon.stub(Stripe, 'retrievePaymentIntent').returns({
                payment_method: {
                    billing_details: {
                    },
                    type: 'card',
                    card: {
                        last4: '1234'
                    }
                },
                created: 1715606400,
                id: 'pi_test',
                status: 'succeeded'
            });

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(sendStub);
        });

        it('should update total amount raised in fundraiser feed and should not send email if fundraiser is not found', async () => {
            const record = {
                eventName: 'INSERT',
                // eslint-disable-next-line max-len
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/OrgManagedFundraiserBoosterDonation/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'fundraiserBoosterId': { 'S': '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        'organizationId': { 'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc' },
                        'amount': { 'N': '100' },
                        'stripePaymentIntentId': { 'S': 'pi_test' },
                        'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                        'createdBy': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'status': { 'S': 'success' }
                    }
                }
            };

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([JSON.stringify(
                {
                    fundraiserBoosterId: '470f3434-7cb5-402f-81fc-ef3db68b73ce',
                    fundraiserId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                    child: { id: 'ace0149e-447d-4f7f-9402-95a96963de9b', associatedImageOrColor: '#FACD01' },
                    isSignedUp: false,
                    status: ''
                })]);

            sinon.stub(orgManagedFundraiserBoosterDonationModel, 'query')
                .returns({
                    eq: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        exec: sinon.stub().resolves([{ amount: 100 }, { amount: 200 }])
                                    })
                                })
                            })
                        })
                    })
                });

            sinon.stub(Fundraiser, 'get').resolves(null);

            sinon.stub(Stripe, 'retrievePaymentIntent').returns({
                payment_method: {
                    billing_details: {
                        email: '<EMAIL>',
                        name: 'test'
                    },
                    type: 'card',
                    card: {
                        last4: '1234'
                    }
                },
                created: 1715606400,
                id: 'pi_test',
                status: 'succeeded'
            });

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(sendStub);
        });

        it('should handle if status is not success', async () => {
            const record = {
                eventName: 'INSERT',
                // eslint-disable-next-line max-len
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/OrgManagedFundraiserBoosterDonation/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'fundraiserBoosterId': { 'S': '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        'organizationId': { 'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc' },
                        'amount': { 'N': '100' },
                        'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                        'createdBy': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'status': { 'S': 'failed' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);
        });

        it('should handle if feed is not found', async () => {
            const record = {
                eventName: 'INSERT',
                // eslint-disable-next-line max-len
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:************:table/OrgManagedFundraiserBoosterDonation/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        'fundraiserBoosterId': { 'S': '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        'organizationId': { 'S': 'fdaab0cb-859d-400b-b788-0369e2e822bc' },
                        'amount': { 'N': '100' },
                        'updatedAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'createdAt': { 'S': '2023-11-28T11:11:54.036Z' },
                        'parentId': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'childId': { 'S': 'ace0149e-447d-4f7f-9402-95a96963de9b' },
                        'id': { 'S': '470f3434-7cb5-402f-81fc-ef3db68b73ce' },
                        'createdBy': { 'S': 'a967e334-60d4-4efe-8a9d-c71ecc525977' },
                        'status': { 'S': 'success' }
                    }
                }
            };

            sinon.stub(orgManagedFundraiserBoosterDonationModel, 'query')
                .returns({
                    eq: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        exec: sinon.stub().resolves([{ amount: 100 }, { amount: 200 }])
                                    })
                                })
                            })
                        })
                    })
                });

            sinon.stub(Fundraiser, 'get').resolves({ fundraiserType: constants.FUNDRAISER_TYPES.BOOSTER, save: () => { } });

            const event = {
                Records: [record]
            };

            await handler.handler(event);
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
