#!/bin/bash
ln -f ../models/user.model.js ./server/models/user.model.js
ln -f ../models/child.model.js ./server/models/child.model.js
ln -f ../models/groupMembers.model.js ./server/models/groupMembers.model.js
ln -f ../models/groups.model.js ./server/models/groups.model.js
ln -f ../models/organization.model.js ./server/models/organization.model.js
ln -f ../models/personalConversation.model.js ./server/models/personalConversation.model.js
ln -f ../models/personalMessage.model.js ./server/models/personalMessage.model.js
ln -f ../models/constant.model.js ./server/models/constant.model.js
ln -f ../models/aiCompanionGroupFeedback.model.js ./server/models/aiCompanionGroupFeedback.model.js
ln -f ../models/aiCompanionScreenFeedback.model.js ./server/models/aiCompanionScreenFeedback.model.js

ln -f ../shared/logger.js ./server/util/logger.js
ln -f ../shared/http-status.js ./server/util/http-status.js
ln -f ../shared/uploadService.js ./server/util/uploadService.js