const chai = require('chai');
const expect = chai.expect;
const sinon = require('sinon');
const handleGetPersonalMessage = require('../handleGetPersonalMessage');
const PersonalMessage = require('../../../models/personalMessage.model');
const { beforeEach, afterEach } = require('mocha');
const websocketRoutes = require('../../../webSocketHandler');
const UserModel = require('../../../models/user.model');
const organizationModel = require('../../../models/organization.model');
const childModel = require('../../../models/child.model');
const MessageReactionsHandler = require('../handlers/messageReactionsHandler');
const PersonalConversationModel = require('../../../models/personalConversation.model');

describe('Lazy Load Personal Messages', () => {
    let personalMessageQueryStub;
    let getReactionsForMessageStub;
    let personalConversationStub;
    try {
        beforeEach(() => {
            personalMessageQueryStub = sinon.stub(PersonalMessage, 'query');
            getReactionsForMessageStub = sinon.stub(MessageReactionsHandler, 'getReactionsForMessage');
            personalConversationStub = sinon.stub(PersonalConversationModel, 'get');
        });

        afterEach(() => {
            sinon.restore();
        });

        it('should throw 400 error if the event is not valid', async () => {
            const event = {
                body: {
                    action: 'getPersonalMessage',
                    actionType: 'INVALID_ACTION_TYPE',
                    userId: 'userId1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(400);
        });

        it('should handle error in LAZY_LOAD_PERSONAL_MESSAGES action', async () => {
            personalConversationStub.throws(new Error('test error'));

            const event = {
                body: {
                    action: 'getPersonalMessage',
                    actionType: 'LAZY_LOAD_PERSONAL_MESSAGES',
                    userId: 'userId1',
                    senderId: 'senderId1',
                    receiverId: 'receiverId1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(500);
        });

        it('should handle error personal conversation not found in LAZY_LOAD_PERSONAL_MESSAGES action', async () => {
            personalConversationStub.resolves(null);

            const event = {
                body: {
                    action: 'getPersonalMessage',
                    actionType: 'LAZY_LOAD_PERSONAL_MESSAGES',
                    userId: 'userId1',
                    senderId: 'senderId1',
                    receiverId: 'receiverId1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(404);
        });

        it('Should lazy load messages without last evaluated key', async () => {
            const conversationId = 'test_conversation_id';
            const event = {
                body: {
                    actionType: 'LAZY_LOAD_PERSONAL_MESSAGES',
                    conversationId,
                    limit: 1
                },
                requestContext: { connectionId: 'connectionId1' }
            };
            const messages = [{
                messageId: 'test_message_id',
                conversationId,
                senderId: 'test_sender_id',
                message: 'Hello, how are you?',
                createdAt: '2024-12-17T12:34:56.789Z',
                updatedAt: '2024-12-17T12:34:56.789Z',
                isDeleted: false
            }];

            personalMessageQueryStub.returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                exec: sinon.stub().returns(messages)
                            })
                        })
                    })
                })
            });

            getReactionsForMessageStub.resolves({});
            personalConversationStub.resolves({ lastMessageIdBeforeDeleted: 'lastMessageIdBeforeDeleted' });

            const response = await handleGetPersonalMessage(event);

            sinon.assert.calledOnceWithExactly(personalMessageQueryStub, 'conversationId');
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq, conversationId);
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using, 'conversationId-createdAt-index');
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort, 'descending');
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort().limit, 1);
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort().limit().exec);
            sinon.assert.calledOnce(getReactionsForMessageStub);

            expect(response.statusCode).to.equal(200);
            expect(response.message).to.equal('Get personal messages successful!');
            expect(response.actionType).to.equal('LAZY_LOAD_PERSONAL_MESSAGES');
            expect(response.action).to.equal('getPersonalMessage');
            const parsedData = response.data;
            expect(parsedData.conversationId).to.equal(conversationId);
            expect(parsedData.messages).to.deep.equal(messages);
        });

        it('Should lazy load messages with last evaluated key', async () => {
            const conversationId = 'test_conversation_id';
            const event = {
                body: {
                    actionType: 'LAZY_LOAD_PERSONAL_MESSAGES',
                    conversationId,
                    lastEvaluatedKey: {
                        messageId: 'test_last_message_id',
                        createdAt: '2024-12-17T12:34:56.789Z'
                    },
                    limit: 1
                },
                requestContext: { connectionId: 'connectionId1' }
            };
            const messages = [{
                messageId: 'test_message_id',
                conversationId,
                senderId: 'test_sender_id',
                message: 'Hello, how are you?',
                createdAt: '2024-12-17T12:34:56.789Z',
                updatedAt: '2024-12-17T12:34:56.789Z',
                isDeleted: false
            }];

            personalMessageQueryStub.returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                startAt: sinon.stub().returns({
                                    exec: sinon.stub().returns(messages)
                                })
                            })
                        })
                    })
                })
            });

            personalConversationStub.resolves({ lastMessageIdBeforeDeleted: 'lastMessageIdBeforeDeleted' });

            const response = await handleGetPersonalMessage(event);

            sinon.assert.calledOnceWithExactly(personalMessageQueryStub, 'conversationId');
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq, conversationId);
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using, 'conversationId-createdAt-index');
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort, 'descending');
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort().limit, 1);
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort().limit().startAt, event.body.lastEvaluatedKey);
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort().limit().startAt().exec);

            expect(response.statusCode).to.equal(200);
            expect(response.message).to.equal('Get personal messages successful!');
            expect(response.actionType).to.equal('LAZY_LOAD_PERSONAL_MESSAGES');
            expect(response.action).to.equal('getPersonalMessage');
            const parsedData = response.data;
            expect(parsedData.conversationId).to.equal(conversationId);
            expect(parsedData.messages).to.deep.equal(messages);
        });

        it('Should lazy load messages with last evaluated key and filter deleted message', async () => {
            const conversationId = 'test_conversation_id';
            const event = {
                body: {
                    actionType: 'LAZY_LOAD_PERSONAL_MESSAGES',
                    conversationId,
                    lastEvaluatedKey: {
                        messageId: 'test_last_message_id',
                        createdAt: '2024-12-17T12:34:56.789Z'
                    },
                    limit: 2
                },
                requestContext: { connectionId: 'connectionId1' }
            };
            const messages = [
                {
                    messageId: 'test_message_id',
                    conversationId,
                    senderId: 'test_sender_id',
                    message: 'Hello, how are you?',
                    createdAt: '2024-12-17T12:34:56.789Z',
                    updatedAt: '2024-12-17T12:34:56.789Z',
                    isDeleted: false
                },
                {
                    messageId: 'lastMessageIdBeforeDeleted',
                    conversationId,
                    senderId: 'test_sender_id',
                    message: 'I am fine, thank you!',
                    createdAt: '2024-12-17T12:34:56.789Z',
                    updatedAt: '2024-12-17T12:34:56.789Z',
                    isDeleted: false
                }
            ];
            messages.lastKey = {
                messageId: 'lastMessageIdBeforeDeleted',
                createdAt: '2024-12-17T12:34:56.789Z',
                conversationId
            };

            personalMessageQueryStub.returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                startAt: sinon.stub().returns({
                                    exec: sinon.stub().returns(messages)
                                })
                            })
                        })
                    })
                })
            });

            personalConversationStub.resolves({ lastMessageIdBeforeDeleted: 'lastMessageIdBeforeDeleted' });

            const response = await handleGetPersonalMessage(event);

            sinon.assert.calledOnceWithExactly(personalMessageQueryStub, 'conversationId');
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq, conversationId);
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using, 'conversationId-createdAt-index');
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort, 'descending');
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort().limit, 2);
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort().limit().startAt, event.body.lastEvaluatedKey);
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort().limit().startAt().exec);

            expect(response.statusCode).to.equal(200);
            expect(response.message).to.equal('Get personal messages successful!');
            expect(response.actionType).to.equal('LAZY_LOAD_PERSONAL_MESSAGES');
            expect(response.action).to.equal('getPersonalMessage');
            const parsedData = response.data;
            expect(parsedData.conversationId).to.equal(conversationId);
            expect(parsedData.messages).to.deep.equal([messages[0]]);
            expect(parsedData.lastEvaluatedKey).to.deep.equal({
                messageId: 'test_message_id',
                createdAt: 1734438896789,
                conversationId
            });
        });

        it('Should lazy load messages if all the above messages are deleted', async () => {
            const conversationId = 'test_conversation_id';
            const event = {
                body: {
                    actionType: 'LAZY_LOAD_PERSONAL_MESSAGES',
                    conversationId,
                    lastEvaluatedKey: {
                        messageId: 'test_last_message_id',
                        createdAt: '2024-12-17T12:34:56.789Z'
                    },
                    limit: 1
                },
                requestContext: { connectionId: 'connectionId1' }
            };
            const messages = [
                {
                    messageId: 'lastMessageIdBeforeDeleted',
                    conversationId,
                    senderId: 'test_sender_id',
                    message: 'I am fine, thank you!',
                    createdAt: '2024-12-17T12:34:56.789Z',
                    updatedAt: '2024-12-17T12:34:56.789Z',
                    isDeleted: false
                }
            ];

            messages.lastKey = {
                messageId: 'lastMessageIdBeforeDeleted',
                createdAt: '2024-12-17T12:34:56.789Z',
                conversationId
            };

            personalMessageQueryStub.returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                startAt: sinon.stub().returns({
                                    exec: sinon.stub().returns(messages)
                                })
                            })
                        })
                    })
                })
            });

            personalConversationStub.resolves({ lastMessageIdBeforeDeleted: 'lastMessageIdBeforeDeleted' });

            const response = await handleGetPersonalMessage(event);

            sinon.assert.calledOnceWithExactly(personalMessageQueryStub, 'conversationId');
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq, conversationId);
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using, 'conversationId-createdAt-index');
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort, 'descending');
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort().limit, 1);
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort().limit().startAt, event.body.lastEvaluatedKey);
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort().limit().startAt().exec);

            expect(response.statusCode).to.equal(200);
            expect(response.message).to.equal('Get personal messages successful!');
            expect(response.actionType).to.equal('LAZY_LOAD_PERSONAL_MESSAGES');
            expect(response.action).to.equal('getPersonalMessage');
            const parsedData = response.data;
            expect(parsedData.conversationId).to.equal(conversationId);
            expect(parsedData.messages).to.deep.equal([]);
            expect(parsedData.lastEvaluatedKey).to.equal(null);
        });

        it('Should lazy load messages with client last synced message and complete gap filling', async () => {
            const conversationId = 'test_conversation_id';
            const lastFetchedMessage = {
                messageId: 'client_last_message_id',
                createdAt: '2024-12-17T12:20:00.000Z'
            };
            const event = {
                body: {
                    actionType: 'LAZY_LOAD_PERSONAL_MESSAGES',
                    conversationId,
                    clientLastSyncedMessage: {
                        recentFetchedMessage: {
                            messageId: 'client_recent_message_id',
                            createdAt: '2024-12-17T12:35:00.000Z'
                        },
                        lastFetchedMessage
                    },
                    limit: 2
                },
                requestContext: { connectionId: 'connectionId1' }
            };
            const lastKey = {
                messageId: 'db_last_message_id',
                createdAt: '2024-12-17T12:31:00.000Z'
            };
            const messages =
            [{
                messageId: 'test_message_id',
                conversationId,
                senderId: 'test_sender_id',
                message: 'Hello, how are you?',
                createdAt: '2024-12-17T12:35:56.789Z',
                updatedAt: '2024-12-17T12:35:56.789Z',
                isDeleted: false
            },
            {
                messageId: 'client_recent_message_id',
                conversationId,
                senderId: 'test_sender_id',
                message: 'I am fine, thank you!',
                createdAt: '2024-12-17T12:35:00.000Z',
                updatedAt: '2024-12-17T12:35:00.000Z',
                isDeleted: false
            }];
            messages.lastKey = lastKey;

            personalMessageQueryStub.returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                exec: sinon.stub().returns(messages)
                            })
                        })
                    })
                })
            });

            personalConversationStub.resolves({ lastMessageIdBeforeDeleted: 'lastMessageIdBeforeDeleted' });

            const response = await handleGetPersonalMessage(event);

            sinon.assert.calledOnceWithExactly(personalMessageQueryStub, 'conversationId');
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq, conversationId);
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using, 'conversationId-createdAt-index');
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort, 'descending');
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort().limit, 2);
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort().limit().exec);

            expect(response.statusCode).to.equal(200);
            expect(response.message).to.equal('Get personal messages successful!');
            expect(response.actionType).to.equal('LAZY_LOAD_PERSONAL_MESSAGES');
            expect(response.action).to.equal('getPersonalMessage');
            const parsedData = response.data;
            expect(parsedData.conversationId).to.equal(conversationId);
            expect(parsedData.messages).to.deep.equal(messages);
            expect(parsedData.isRecentFetchedMessageFound).to.be.true;
            expect(parsedData.lastEvaluatedKey).to.deep.equal({ ...lastFetchedMessage, conversationId });
        });

        it('Should lazy load messages with client last synced message and incomplete gap filling', async () => {
            const conversationId = 'test_conversation_id';
            const event = {
                body: {
                    actionType: 'LAZY_LOAD_PERSONAL_MESSAGES',
                    conversationId,
                    clientLastSyncedMessage: {
                        recentFetchedMessage: {
                            messageId: 'client_recent_message_id',
                            createdAt: '2024-12-17T12:35:00.000Z'
                        },
                        lastFetchedMessage: {
                            messageId: 'client_last_message_id',
                            createdAt: '2024-12-17T12:20:00.000Z'
                        }
                    },
                    limit: 2
                },
                requestContext: { connectionId: 'connectionId1' }
            };
            const lastKey = {
                messageId: 'test_message_id2',
                createdAt: 1734438900000,
                conversationId
            };
            const messages =
            [{
                messageId: 'test_message_id1',
                conversationId,
                senderId: 'test_sender_id',
                message: 'Hello, how are you?',
                createdAt: '2024-12-17T12:35:56.789Z',
                updatedAt: '2024-12-17T12:35:56.789Z',
                isDeleted: false
            },
            {
                messageId: 'test_message_id2',
                conversationId,
                senderId: 'test_sender_id',
                message: 'I am fine, thank you!',
                createdAt: '2024-12-17T12:35:00.000Z',
                updatedAt: '2024-12-17T12:35:00.000Z',
                isDeleted: false
            }];
            messages.lastKey = lastKey;

            personalMessageQueryStub.returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                exec: sinon.stub().returns(messages)
                            })
                        })
                    })
                })
            });

            personalConversationStub.resolves({ lastMessageIdBeforeDeleted: 'lastMessageIdBeforeDeleted' });

            const response = await handleGetPersonalMessage(event);

            sinon.assert.calledOnceWithExactly(personalMessageQueryStub, 'conversationId');
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq, conversationId);
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using, 'conversationId-createdAt-index');
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort, 'descending');
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort().limit, 2);
            sinon.assert.calledOnceWithExactly(personalMessageQueryStub().eq().using().sort().limit().exec);

            expect(response.statusCode).to.equal(200);
            expect(response.message).to.equal('Get personal messages successful!');
            expect(response.actionType).to.equal('LAZY_LOAD_PERSONAL_MESSAGES');
            expect(response.action).to.equal('getPersonalMessage');
            const parsedData = response.data;
            expect(parsedData.conversationId).to.equal(conversationId);
            expect(parsedData.messages).to.deep.equal(messages);
            expect(parsedData.isRecentFetchedMessageFound).to.be.false;
            expect(parsedData.lastEvaluatedKey).to.deep.equal(lastKey);
        });

        it('should handle error in GET_USER_CONNECTED_CHILDREN action', async () => {
            sinon.stub(UserModel, 'query').throws(new Error('test error'));

            const event = {
                body: {
                    action: 'getPersonalMessage',
                    actionType: 'GET_USER_CONNECTED_CHILDREN',
                    userId: 'userId1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(500);

            UserModel.query.restore();
        });

        it('should give empty array if no connected children are found', async () => {
            sinon.stub(UserModel, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub()
                            .onFirstCall().resolves([
                                {
                                    id: 'userId1',
                                    children: ['childId1', 'childId2']
                                }
                            ])
                            .onSecondCall().resolves([
                                {
                                    id: 'userId2'
                                }
                            ])
                    })
                })
            });

            sinon.stub(organizationModel, 'batchGet').resolves([]);

            const event = {
                body: {
                    action: 'getPersonalMessage',
                    actionType: 'GET_USER_CONNECTED_CHILDREN',
                    senderId: 'userId1',
                    receiverId: 'userId2'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);

            UserModel.query.restore();
            organizationModel.batchGet.restore();
        });

        it('should get user\'s connected children and populate school and home room name', async () => {
            sinon.stub(UserModel, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub()
                            .onFirstCall().resolves([
                                {
                                    id: 'userId1',
                                    children: ['childId1', 'childId2']
                                }
                            ])
                            .onSecondCall().resolves([
                                {
                                    id: 'userId2',
                                    children: ['childId3', 'childId4']
                                }
                            ])
                    })
                })
            });

            sinon.stub(childModel, 'batchGet').resolves([
                {
                    id: 'childId1', school: 'schoolId1', homeRoom: 'homeRoomId1',
                    connections: [{ childId: 'childId1' }, { childId: 'childId2' }]
                },
                {
                    id: 'childId2', school: 'schoolId2', photoURL: 'photoUrl1',
                    connections: [{ childId: 'childId2' }, { childId: 'childId1' }]
                },
                { id: 'childId3', school: 'schoolId3', homeRoom: 'homeRoomId3', connections: [] }
            ]);

            sinon.stub(organizationModel, 'batchGet').resolves([
                { id: 'schoolId1', name: 'School 1' },
                { id: 'schoolId2', name: 'School 2' },
                { id: 'homeRoomId1', name: 'Home Room 1' }
            ]);

            const event = {
                body: {
                    action: 'getPersonalMessage',
                    actionType: 'GET_USER_CONNECTED_CHILDREN',
                    senderId: 'userId1',
                    receiverId: 'userId2'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);

            UserModel.query.restore();
            organizationModel.batchGet.restore();
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
