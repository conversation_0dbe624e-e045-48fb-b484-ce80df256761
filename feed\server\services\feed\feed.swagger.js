/**
 *  routes and schema for feed routes
 */

/**
 * @openapi
 * components:
 *  schemas:
 *      successFeedsList:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: array
 *                  description: array of feed Object
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data: {
 *                  feeds: [{
 *                  id: feed id,
 *                  photoURL: photo url of event,
 *                  isPaid: is paid or not,
 *                  fee: fee,
 *                  associatedChild: {
 *                      id: child id,
 *                      firstName: child first name,
 *                      lastName: child last name,
 *                      associatedImageOrColor: associatedColorOrImage
 *                  },
 *                  isSignedUp: is signed up or not,
 *                  paymentStatus: payment status,
 *                  startDateTime: start date time,
 *                  endDateTime: end date time,
 *                  participantCount: participant count,
 *                  title: event title,
 *                  description: event description,
 *                  organizationName: organization name,
 *                  eventType: event type,
 *                  eventScope: event scope,
 *                  commentsCount: comment count,
 *                  participantsCount: participant count,
 *                  quantityType: quantity type,
 *                  quantityInstruction: quantity instruction,
 *                  venue: venue
 *                  }],
 *                  nextIndex: next cursor pointer for pagination,
 *                  nextChildId: next child id for pagination
 *              }
 *              message: Success
 *
 *      successCalendarFeedsList:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: array
 *                  description: array of feed Object
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data: [{
 *                  id: feed id,
 *                  photoURL: photo url of event,
 *                  documentURLs: document urls,
 *                  isPaid: is paid or not,
 *                  fee: fee,
 *                  associatedChild: {
 *                      id: child id,
 *                      firstName: child first name,
 *                      lastName: child last name,
 *                      associatedImageOrColor: associatedColorOrImage,
 *                      associatedColor: associated color,
 *                      photoURL: photo url of child
 *                  },
 *                  isSignedUp: is signed up or not,
 *                  paymentStatus: payment status,
 *                  startDateTime: start date time,
 *                  endDateTime: end date time,
 *                  title: event title,
 *                  description: event description,
 *                  organizationName: organization name,
 *                  eventType: event type,
 *                  eventScope: event scope,
 *                  venue: venue,
 *                  score: score of event
 *                  }]
 *              message: Success
 *
 *      successFeedDetails:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: array
 *                  description: event Object
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data: {
 *                  id: feed id,
 *                  photoURL: photo url of event,
 *                  documentURLs: document urls,
 *                  isPaid: is paid or not,
 *                  fee: fee,
 *                  associatedChild: {
 *                      id: child id,
 *                      firstName: child first name,
 *                      lastName: child last name,
 *                      associatedImageOrColor: associatedColorOrImage
 *                  },
 *                  isSignedUp: is signed up or not,
 *                  paymentStatus: payment status,
 *                  startDateTime: start date time,
 *                  endDateTime: end date time,
 *                  participants: [{
 *                      parentId: parent uid,
 *                      childId: child uid,
 *                      firstName: child first name,
 *                      lastName: child last name,
 *                      associatedImageOrColor: associatedColorOrImage
 *                  }],
 *                  title: event title,
 *                  description: event description,
 *                  organizationName: organization name,
 *                  eventType: event type,
 *                  eventScope: event scope,
 *                  commentsCount: comment count,
 *                  participantsCount: participant count,
 *                  quantityType: quantity type,
 *                  quantityInstruction: quantity instruction,
 *                  venue: venue,
 *                  organization: {
 *                     name: organization name,
 *                     paymentDetails: paymentDetails,
 *                     paymentInstructions: paymentInstructions,
 *                     allowedPaymentType: allowedPaymentType,
 *                     platformFee: platformFee,
 *                     platformFeeCoveredBy: platformFeeCoveredBy}
 *                  }
 *              message: Success
 */

/**
 * @openapi
 * /events:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Feed]
 *      summary: get feed list
 *      parameters:
 *          - in: query
 *            name: childId
 *            schema:
 *                type: string
 *            description: uuid of the child
 *          - in: query
 *            name: pageSize
 *            schema:
 *                type: string
 *            description: page size for the pagination
 *          - in: query
 *            name: nextIndex
 *            schema:
 *                type: string
 *            description: index for the pagination
 *          - in: query
 *            name: nextChildId
 *            schema:
 *                type: string
 *            description: next child id for pagination
 *
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successFeedsList'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /registered-events:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Feed]
 *      summary: get feed list where i am registered
 *      parameters:
 *          - in: query
 *            name: childId
 *            schema:
 *                type: string
 *            description: uuid of the child
 *          - in: query
 *            name: pageSize
 *            schema:
 *                type: string
 *            description: page size for the pagination
 *          - in: query
 *            name: nextIndex
 *            schema:
 *                type: string
 *            description: index for the pagination
 *          - in: query
 *            name: nextChildId
 *            schema:
 *                type: string
 *            description: next child id for pagination
 *
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successFeedsList'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /details:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Feed]
 *      summary: get feed details
 *      parameters:
 *          - in: query
 *            name: childId
 *            schema:
 *                type: string
 *            description: uuid of the child
 *          - in: query
 *            name: eventId
 *            schema:
 *                type: string
 *            description: uuid of the event
 *          - in: query
 *            name: score
 *            schema:
 *                type: string
 *            description: score of event
 *
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successFeedDetails'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /calendar-events:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Feed]
 *      summary: get calendar event list
 *
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successCalendarFeedsList'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * components:
 *  schemas:
 *    successSearchFeedsList:
 *      type: object
 *      properties:
 *        status:
 *          type: integer
 *          example: 1
 *        data:
 *          type: array
 *          items:
 *            type: object
 *            properties:
 *              id:
 *                type: string
 *              title:
 *                type: string
 *              eventType:
 *                type: string
 *              details:
 *                type: object
 *                properties:
 *                  details:
 *                    type: string
 *                  startDateTime:
 *                    type: string
 *                    format: date-time
 *                  endDateTime:
 *                    type: string
 *                    format: date-time
 *                  venue:
 *                    type: string
 *              photoURL:
 *                type: string
 *              organizationId:
 *                type: string
 *              isPaid:
 *                type: boolean
 *              fee:
 *                type: number
 *              eventScope:
 *                type: string
 *              status:
 *                type: string
 *              associatedChild:
 *                type: object
 *                properties:
 *                  firstName:
 *                    type: string
 *                  lastName:
 *                    type: string
 *                  id:
 *                    type: string
 *                  photoURL:
 *                    type: string
 *                  associatedColor:
 *                    type: string
 *              organizationDetails:
 *                type: object
 *                properties:
 *                  id:
 *                    type: string
 *                  name:
 *                    type: string
 *              isSignedUp:
 *                type: boolean
 *              paymentStatus:
 *                type: string
 *        message:
 *          type: string
 *          example: "Success"
 */

/**
 * @openapi
 * /search:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Feed]
 *      summary: search feeds
 *      parameters:
 *        - in: query
 *          name: searchValue
 *          schema:
 *            type: string
 *          required: true
 *          description: The term to search for in the feeds
 *        - in: query
 *          name: childId
 *          schema:
 *            type: string
 *          description: The term to search for particular child feeds
 *
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successSearchFeedsList'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Unexpected error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /guest-signup:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: query
 *          name: referenceId
 *          schema:
 *            type: string
 *          description: Reference Id
 *          required: true
 *        - in: query
 *          name: isFundraiser
 *          schema:
 *            type: boolean
 *          description: Is Fundraiser
 *          required: false
 *        - in: header
 *          name: reqtoken
 *          schema:
 *            type: string
 *          description: Request token
 *          required: true
 *      tags: [Feed]
 *      summary: Get Feed Details for Guest Signup
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successFeedDetails'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          404:
 *              description: Not Found
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/notFound'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */
