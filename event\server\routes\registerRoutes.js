/**
 * This file contains routes used for register.
 * Created by Growexx on 09/11/2023.
 * @name registerRoutes
 */
const router = require('express').Router();

const RegisterController = require('../services/register/registerController');
const GuestRecaptchaOrAuthMiddleware = require('../middleware/guestRecaptchaOrAuth');

router.post('/webhook', RegisterController.stripeWebhook);
router.post('/', GuestRecaptchaOrAuthMiddleware, RegisterController.registerUserAndPayment);

module.exports = router;
