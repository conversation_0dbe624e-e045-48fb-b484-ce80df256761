/**
 * This file contains routes used for register.
 * Created by Growexx on 09/11/2023.
 * @name registerRoutes
 */
const router = require('express').Router();

const RegisterController = require('../services/register/registerController');
const AuthMiddleware = require('../middleware/auth');

router.post('/webhook', RegisterController.stripeWebhook);
router.post('/', AuthMiddleware, RegisterController.registerUserAndPayment);

module.exports = router;
