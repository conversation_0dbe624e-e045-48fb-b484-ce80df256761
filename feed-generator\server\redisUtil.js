class RedisUtil {
    /**
     * Adds an event reference to a sorted set
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {String} key - The key
     * @param {Number} initialScore - The initial score
     * @param {String} value - The value
     */
    static async addEventReferenceToSortedSet (redis, key, initialScore, value) {
        if (process.env.NODE_ENV !== 'testing') {
            let score = initialScore;
            while ((await this.getCountOfElementsInSortedSetForScore(redis, key, score)) > 0) {
                score++;
            }

            await redis.zadd(key, score, value);
        } else {
            return;
        }
    }

    /**
     * Adds a post reference to a sorted set
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {String} key - The key
     * @param {Number} initialScore - The initial score
     * @param {String} value - The value
     */
    static async addPostReferenceToSortedSet (redis, key, initialScore, value) {
        if (process.env.NODE_ENV !== 'testing') {
            let score = initialScore;
            while ((await this.getCountOfElementsInSortedSetForScore(redis, key, score)) > 0) {
                score++;
            }

            await redis.zadd(key, score, value);
        } else {
            return;
        }
    }

    /**
     * Adds event details to a hash
     * <AUTHOR>
     * @param {String} key - The key
     * @param {Redis} pipeline - The Redis pipeline
     * @param {Object} event - The event object
     */
    static addEventDetailsToHash (key, pipeline, event) {
        if (process.env.NODE_ENV !== 'testing') {
            pipeline.hset(key, 'details', JSON.stringify(event));
        } else {
            return;
        }
    }

    /**
     * Adds post details to a hash
     * <AUTHOR>
     * @param {String} key - The key
     * @param {Redis} pipeline - The Redis pipeline
     * @param {Object} post - The post object
     */
    static addPostDetailsToHash (key, pipeline, post) {
        if (process.env.NODE_ENV !== 'testing') {
            pipeline.hset(key, 'details', JSON.stringify(post));
        } else {
            return;
        }
    }

    /**
     * Adds fundraiser details to a hash
     * <AUTHOR>
     * @param {String} key - The key
     * @param {Redis} pipeline - The Redis pipeline
     * @param {Object} fundraiser - The fundraiser object
     */
    static addFundraiserDetailsToHash (key, pipeline, fundraiser) {
        if (process.env.NODE_ENV !== 'testing') {
            pipeline.hset(key, 'details', JSON.stringify(fundraiser));
        } else {
            return;
        }
    }

    /**
     * Gets a hash value
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {String} key - The key
     * @param {String} field - The field
     */
    static async getHashValue (redis, key, field) {
        if (process.env.NODE_ENV !== 'testing') {
            return await redis.hget(key, field);
        } else {
            return Promise.resolve();
        }
    }

    /**
     * Sets a hash value
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {String} key - The key
     * @param {String} field - The field
     * @param {Object} value - The value
     */
    static async setHashValue (redis, key, field, value) {
        if (process.env.NODE_ENV !== 'testing') {
            await redis.hset(key, field, JSON.stringify(value));
        } else {
            return;
        }
    }

    /**
     * Removes a member from a hash set
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {String} key - The key
     * @param {String} field - The field
     */
    static async removeMemberFromHashSet (redis, key, field) {
        if (process.env.NODE_ENV !== 'testing') {
            await redis.hdel(key, field);
        } else {
            return;
        }
    }

    /**
    * Gets elements of a sorted set by score
    * <AUTHOR>
    * @param {Redis} redis - The Redis client
    * @param {String} key - The key
    * @param {Number} start - The start
    * @param {Number} stop - The stop
    */
    static async getElementsOfSortedSetByScore (redis, key, start, stop) {
        if (process.env.NODE_ENV !== 'testing') {
            return await redis.zrangebyscore(key, start, stop);
        } else {
            return Promise.resolve();
        }
    }

    /**
     * Gets the count of elements in a sorted set for a score
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {String} key - The key
     * @param {Number} score - The score
     */
    static async getCountOfElementsInSortedSetForScore (redis, key, score) {
        if (process.env.NODE_ENV !== 'testing') {
            return await redis.zcount(key, score, score);
        } else {
            return Promise.resolve();
        }
    }

    static async getElementsOfSortedSetWithScores (redis, key, start, stop) {
        if (process.env.NODE_ENV !== 'testing') {
            return await redis.zrangebyscore(key, start, stop, 'WITHSCORES');
        } else {
            return Promise.resolve();
        }
    }

    /**
     * Gets the score of a member
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {String} key - The key
     * @param {String} member - The member
     */
    static async getScoreOfMember (redis, key, member) {
        if (process.env.NODE_ENV !== 'testing') {
            return await redis.zscore(key, member);
        } else {
            return Promise.resolve();
        }
    }

    /**
     * Removes a member from a sorted set
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {String} key - The key
     * @param {String} member - The member
     */
    static async removeMemberFromSortedSet (redis, key, member) {
        if (process.env.NODE_ENV !== 'testing') {
            await redis.zrem(key, member);
        } else {
            Promise.resolve();
        }
    }

    /**
     * Adds a member with a score to a sorted set
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {String} key - The key
     * @param {Number} score - The score
     * @param {String} member - The member
     */
    static async addMemberWithScore (redis, key, score, member) {
        if (process.env.NODE_ENV !== 'testing') {
            await redis.zadd(key, score, member);
        } else {
            Promise.resolve();
        }
    }

    /**
     * Gets the keys for a pattern
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {String} pattern - The pattern
     */
    static async getKeysForPattern (redis, pattern) {
        if (process.env.NODE_ENV !== 'testing') {
            return await redis.keys(pattern);
        } else {
            return Promise.resolve([]);
        }
    }

    /**
     * Deletes a key
     * <AUTHOR>
     * @param {Redis} pipeline - The Redis pipeline
     * @param {String} key - The key
     */
    static deleteKey (pipeline, key) {
        if (process.env.NODE_ENV !== 'testing') {
            pipeline.del(key);
        } else {
            return;
        }
    }

    /**
     * Deletes a key if it exists
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {String} key - The key
     */
    static async deleteKeyIfExists (redis, key) {
        if (process.env.NODE_ENV !== 'testing') {
            await redis.del(key);
        } else {
            return;
        }
    }
}

module.exports = RedisUtil;
