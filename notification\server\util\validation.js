const GeneralError = require('../util/GeneralError');
const REQUIRED = 'FIELD_REQUIRED';
const INVALID = 'FIELD_NOT_VALID';
const INVALID_NAME = 'NAME_NOT_VALID';

/**
 * Created by Growexx on 04/06/2020
 * @name validator
 */
class Validator {
    constructor (locale) {
        this.NOT_VALID = INVALID;
        this.REQUIRED = REQUIRED;
        this.INVALID_NAME = INVALID_NAME;

        if (locale) {
            this.__ = locale;
        }
    }

    /**
     * @desc This function is being used to validate if field exists
     * <AUTHOR>
     * @param {Number|String} value field value
     * @param {String} name field name
     * @since 04/10/2023
     */
    field (value, name) {
        if (!value) {
            throw new GeneralError(this.__(REQUIRED, name), 400);
        }
    }

    /**
     * @desc This function is being used to validate if field is array and not empty
     * <AUTHOR>
     * @param {Array} value field value
     * @param {String} name field name
     * @since 21/02/2024
     */
    arrayField (value, name) {
        if (!value || !value.length) {
            throw new GeneralError(this.__(REQUIRED, name), 400);
        }
    }
}

module.exports = Validator;
