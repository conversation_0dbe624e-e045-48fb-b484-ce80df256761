const { expect } = require('chai');
const { getReactionsForMessage } = require('../messageReactionsHandler');
const sinon = require('sinon');
const MessageReactionsModel = require('../../../../models/messageReactions.model');

describe('Get reactions for message', () => {
    try {
        it('should return empty object if messageIds array is empty', async () => {
            const result = await getReactionsForMessage({});
            expect(result).to.deep.equal({});
        });

        it('should return empty object if there is some error while fetching reactions for messages', async () => {
            sinon.stub(MessageReactionsModel, 'query').throws('Error');

            const result = await getReactionsForMessage({ messageIds: ['messageId1', 'messageId2'] });
            expect(result).to.deep.equal({});

            MessageReactionsModel.query.restore();
        });

        it('should return reactions for messages in the format { \'messageId\': { \'userId\': \'reaction\' } }', async () => {
            sinon.stub(MessageReactionsModel, 'query').returns({
                eq: sinon.stub().returns({
                    exec: sinon.stub().returns([
                        { messageId: 'messageId1', userId: 'userId1', reaction: 'reaction1' },
                        { messageId: 'messageId1', userId: 'userId2', reaction: 'reaction2' },
                        { messageId: 'messageId2', userId: 'userId1', reaction: 'reaction1' }
                    ])
                })
            });

            const result = await getReactionsForMessage({ messageIds: ['messageId1', 'messageId2'] });
            expect(result).to.deep.equal({
                messageId1: {
                    userId1: { messageId: 'messageId1', userId: 'userId1', reaction: 'reaction1' },
                    userId2: { messageId: 'messageId1', userId: 'userId2', reaction: 'reaction2' }
                },
                messageId2: {
                    userId1: { messageId: 'messageId2', userId: 'userId1', reaction: 'reaction1' }
                }
            });

            MessageReactionsModel.query.restore();
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
