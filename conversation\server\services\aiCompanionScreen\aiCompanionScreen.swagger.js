/**
 * Swagger schema for AI Companion Screen.
 */

/**
 * @openapi
 * components:
 *      schemas:
 *          addUpdateAiCompanionScreenFeedback:
 *              type: object
 *              properties:
 *                  aiResponseMessageId:
 *                      type: string
 *                      description: AI Response Message Id
 *                  userQueryMessageId:
 *                      type: string
 *                      description: User Query Message Id
 *                  userQueryMessage:
 *                      type: string
 *                      description: User Query Message
 *                  aiResponseMessage:
 *                      type: string
 *                      description: AI Response Message
 *                  aiResponseMessageContexts:
 *                      type: string
 *                      description: AI Response Message Contexts
 *                  feedback:
 *                      type: string
 *                      description: Feedback
 *              example:
 *                  aiResponseMessageId: uuid
 *                  userQueryMessageId: uuid
 *                  userQueryMessage: string
 *                  aiResponseMessage: string
 *                  aiResponseMessageContexts: string
 *                  feedback: positive
 *
 *          successAddUpdateAiCompanionScreenFeedback:
 *              type: object
 *              properties:
 *                  status:
 *                      $ref: '#/components/messageDefinition/properties/status'
 *                  message:
 *                      $ref: '#/components/messageDefinition/properties/message'
 *                  data:
 *                      type: object
 *                      properties:
 *                          aiResponseMessageId:
 *                              type: string
 *                              description: AI Response Message Id
 *                          userQueryMessageId:
 *                              type: string
 *                              description: User Query Message Id
 *                          userQueryMessage:
 *                              type: string
 *                              description: User Query Message
 *                          aiResponseMessage:
 *                              type: string
 *                              description: AI Response Message
 *                          aiResponseMessageContexts:
 *                              type: string
 *                              description: AI Response Message Contexts
 *                          feedback:
 *                              type: string
 *                              description: Feedback
 *                          createdAt:
 *                              type: string
 *                              description: Created At
 *                          updatedAt:
 *                              type: string
 *                              description: Updated At
 *              example:
 *                  status: 1
 *                  message: Success
 *                  data:
 *                      aiResponseMessageId: uuid
 *                      userQueryMessageId: uuid
 *                      userQueryMessage: string
 *                      aiResponseMessage: string
 *                      aiResponseMessageContexts: string
 *                      feedback: positive
 *                      createdAt: timestamp
 *                      updatedAt: timestamp
 *
 *          successGetAiCompanionScreenFeedbackList:
 *              type: object
 *              properties:
 *                  status:
 *                      $ref: '#/components/messageDefinition/properties/status'
 *                  message:
 *                      $ref: '#/components/messageDefinition/properties/message'
 *                  data:
 *                      type: object
 *                      properties:
 *                          startAiResponseMessageId:
 *                              type: string
 *                              description: Start AI Response Message Id
 *                          startUserId:
 *                              type: string
 *                              description: Start User Id
 *                          feedbackList:
 *                              type: array
 *                              description: Feedback List
 *                              items:
 *                                  type: object
 *                                  properties:
 *                                      userQueryMessage:
 *                                          type: object
 *                                          properties:
 *                                              id:
 *                                                  type: string
 *                                                  description: User Query Message Id
 *                                              senderId:
 *                                                  type: string
 *                                                  description: Sender Id
 *                                              message:
 *                                                  type: string
 *                                                  description: Message
 *                                      aiResponseMessage:
 *                                          type: object
 *                                          properties:
 *                                              id:
 *                                                  type: string
 *                                                  description: AI Response Message Id
 *                                              senderId:
 *                                                  type: string
 *                                                  description: Sender Id
 *                                              message:
 *                                                  type: string
 *                                                  description: Message
 *                                              contexts:
 *                                                  type: string
 *                                                  description: Contexts
 *                                              userQueryMessageId:
 *                                                  type: string
 *                                                  description: User Query Message Id
 *                                      feedback:
 *                                          type: string
 *                                          description: Feedback
 *              example:
 *                  status: 1
 *                  message: Success
 *                  data:
 *                      startAiResponseMessageId: uuid
 *                      feedbackList:
 *                          -
 *                              userQueryMessage:
 *                                  id: uuid
 *                                  senderId: uuid
 *                                  message: string
 *                              aiResponseMessage:
 *                                  id: uuid
 *                                  senderId: uuid
 *                                  message: string
 *                                  contexts: string
 *                                  userQueryMessageId: uuid
 *                              feedback: positive
*/

/**
 * @openapi
 * /conversation/ai-companion-screen/feedback-list:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: query
 *          name: pageSize
 *          schema:
 *            type: number
 *          description: Page Size
 *          required: false
 *        - in: query
 *          name: startAiResponseMessageId
 *          schema:
 *            type: string
 *          description: Start AI Response Message Id
 *          required: false
 *        - in: header
 *          name: reqtoken
 *          schema:
 *            type: string
 *          description: Request token
 *          required: true
 *      tags: [AI Companion Screen]
 *      summary: Get AI Companion Screen Feedback List
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successGetAiCompanionScreenFeedbackList'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /conversation/ai-companion-screen/add-update-feedback:
 *  post:
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: header
 *          name: reqtoken
 *          schema:
 *            type: string
 *          description: Request token
 *          required: true
 *      tags: [AI Companion Screen]
 *      summary: Add/Update AI Companion Screen Feedback
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/addUpdateAiCompanionScreenFeedback'
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successAddUpdateAiCompanionScreenFeedback'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */
