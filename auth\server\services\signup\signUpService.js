const SignUpValidator = require('./signUpValidator');
const Utils = require('../../util/utilFunctions');
const Cognito = require('../../util/cognito');
const User = require('../../models/user.model');
const Crypt = require('../../util/crypt');
const PendingPartnerInvite = require('../../models/pendingPartnerInvite.model');

/**
 * Class represents services for Sign-up.
 */
class SignUpService {
    /**
     * @desc This function is being used to signUp parent user
     * <AUTHOR>
     * @since 16/10/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {String} req.body.firstName firstName
     * @param {String} req.body.lastName lastName
     * @param {String} req.body.phoneNumber phoneNumber
     * @param {String} req.body.countryCode countryCode
     * @param {String} req.body.email email
     */
    static async signUp (req, locale) {
        const Validator = new SignUpValidator(req.body, locale);
        Validator.validate();
        req.body.email = req.body.email.toLowerCase();
        const otp = Utils.generateOtp();
        const otpExpiryDate = MOMENT().add(CONSTANTS.OTP_EXPIRY_DURATION, 'minutes').utc().toDate();
        const { role, email, password } = req.body;
        const user = await Cognito.signup(req.body);
        if (!user) {
            throw {
                message: MESSAGES.ALREADY_REGISTER,
                statusCode: 422
            };
        }
        await Cognito.updateConfirmStatus(email);
        const cognitoUser = await Cognito.login({ email, password });
        try {
            await SignUpService.saveOrUpdateRegistrationUser(req.body, otp, otpExpiryDate, role,
                user.userSub, cognitoUser.AuthenticationResult.IdToken, cognitoUser.AuthenticationResult.RefreshToken);
        } catch (error) {
            await Cognito.deleteUser(email);
            throw error;
        }
        // eslint-disable-next-line max-len
        const smsMessage = `Enter ${otp} to confirm your registration in Vaalee(valid for 30 minutes). Please ensure you complete the registration process promptly.`;
        await Utils.sendOtpToMail(otp, req.body, smsMessage,
            'verificationOtpMail.html', `Registration OTP for ${CONSTANTS.APP_NAME}`);
    }

    /**
     * @desc This function is being used to check email already exists for sign-up of user
     * <AUTHOR>
     * @since 16/10/2023
     * @param {Object} reqObj reqObj
     */
    static async isUserAlreadyRegister (reqObj) {
        return await User.query('email').eq(reqObj.email)
            .where('isGuestUser').not().eq(true)
            .exec();
    }

    /**
     * @desc This function is being used to save or update user details
     * <AUTHOR>
     * @since 16/10/2023
     * @param {Object} reqObj Request
     * @param {Object} reqObj.body RequestBody
     * @param {Object} reqObj reqObj
     * @param {Number} otp otp
     * @param {Date} otpExpiryDate otp expiry date
     * @param {Integer} userType role
     */
    static async saveOrUpdateRegistrationUser (reqObj, otp, otpExpiryDate, userType, id, token, refreshToken) {
        const obj = {
            firstName: reqObj.firstName.trim(),
            lastName: reqObj.lastName.trim(),
            email: reqObj.email.trim().toLowerCase(),
            isFeedsGenerated: false,
            id,
            otpExpiryDate,
            otp,
            token,
            refreshToken
        };
        if (reqObj.phoneNumber) {
            obj.phoneNumber = reqObj.phoneNumber.trim();
            obj.countryCode = reqObj.countryCode.trim();
        }
        if (userType) {
            obj.role = userType;
        }
        const { token: newToken } = await Crypt.getUserToken(obj);
        obj.token = newToken;
        const user = new User(obj);
        await user.save();
    }

    /**
     * @desc This function is being used to verify user account using otp
     * <AUTHOR>
     * @since 16/10/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {Object} req.body.email email
     * @param {Object} req.body.fcmToken fcmToken
     * @param {Object} req.body.otp otp
     */
    static async verifyAccount (req, locale) {
        const Validator = new SignUpValidator(req.body, locale);
        Validator.otpValidate();
        req.body.email = req.body.email.toLowerCase();
        const compareDate = MOMENT().utc().unix();
        const user = await SignUpService.isUserAlreadyRegister(req.body);
        if (user.count) {
            const { otp, id, otpExpiryDate, email, phoneNumber } = user.toJSON()[0];
            if (otp === req.body.otp && compareDate <= MOMENT(otpExpiryDate).utc().unix()) {
                const { token } = await Crypt.getUserToken(
                    { ...user.toJSON()[0], isVerified: CONSTANTS.VERIFIED.ACTIVE, status: CONSTANTS.STATUS.ACTIVE }
                );
                const invitedPartner = await PendingPartnerInvite.query('invitedPartner').eq(email).using('invitedPartner-index').exec();
                const pendingInviteMap = [];
                let isPendingInvite = false;
                let query = {
                    isVerified: CONSTANTS.VERIFIED.ACTIVE, status: CONSTANTS.STATUS.ACTIVE,
                    fcmToken: req.body.fcmToken, '$REMOVE': ['otp', 'otpExpiryDate'], token
                };
                if (invitedPartner.length) {
                    for (const item of invitedPartner) {
                        const { inviterPartnerId, inviterPartnerEmail, children } = item;
                        pendingInviteMap.push({
                            inviterPartnerId,
                            inviterPartnerEmail,
                            children
                        });
                    }
                    isPendingInvite = true;
                    query = { ...query, '$SET': { 'partnerInvites': pendingInviteMap } };
                }
                const updatedUser = await User.update({ id, email }, query);

                await Cognito.updateUserToActive(email, phoneNumber);
                for (const partner of invitedPartner) {
                    await PendingPartnerInvite.delete({ id: partner.id });
                }
                return { ...Utils.returnUser(updatedUser), isPendingInvite };
            } else {
                throw {
                    message: MESSAGES.INVALID_OTP,
                    statusCode: 400
                };
            }
        }
        throw {
            message: MESSAGES.USER_NOT_FOUND,
            statusCode: 400
        };
    }

    /**
     * @desc This function is being used to resend OTP in case of email not received
     * <AUTHOR>
     * @since 16/10/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {Object} req.body.email email
     */
    static async resendOTP (req, locale) {
        const { otpType } = req.body;
        const Validator = new SignUpValidator(req.body, locale);
        Validator.email(req.body.email);
        Validator.otpTypeValidate(otpType, 'OTP type');
        req.body.email = req.body.email.toLowerCase();
        const user = await SignUpService.isUserAlreadyRegister(req.body);
        if (user.count) {
            const { id, firstName, lastName, email } = user.toJSON()[0];
            const otp = Utils.generateOtp();
            const otpExpiryDate = MOMENT().add(CONSTANTS.OTP_EXPIRY_DURATION, 'minutes').utc().toDate();
            await User.update({ id, email }, { otpExpiryDate, otp });
            const smsMessage = otpType === CONSTANTS.OTP_TYPE[0] ?
                // eslint-disable-next-line max-len
                `Enter ${otp} to confirm your registration in Vaalee (valid for 30 minutes). Please ensure you complete the registration process promptly.` :
                // eslint-disable-next-line max-len
                `Enter ${otp} to reset your password (valid for 30 minutes). Please ensure you complete the password reset process promptly.`;

            await Utils.sendOtpToMail(otp, {
                firstName,
                lastName,
                email
            }, smsMessage, otpType === CONSTANTS.OTP_TYPE[0] ? CONSTANTS.EMAIL_TEMPLATE.REGISTER : CONSTANTS.EMAIL_TEMPLATE.FORGOTPASSWORD,
            otpType === CONSTANTS.OTP_TYPE[0] ? `Registration OTP for ${CONSTANTS.APP_NAME}` : 'Password Reset Request');
            return;
        } else {
            throw {
                message: MESSAGES.USER_NOT_FOUND,
                statusCode: 404
            };
        }
    }
}

module.exports = SignUpService;
