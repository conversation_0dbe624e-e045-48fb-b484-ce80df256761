{"SUCCESS": "Success", "REGISTER_SUCCESS": "User registration successful", "EVENT_REGISTER_SUCCESS": "Your order has been placed successfully!", "BOOSTER_CHILD_DETAILS_PATCHED": "Child details patched successfully.", "ALREADY_REGISTER": "This user is already registered with us.", "INACTIVE_USER": "Please activate your user by verify email that has sent earlier", "INVALID_OTP": "The otp that has entered is incorrect", "USER_VERIFY_SUCCESS": "Email is verified successfully", "USER_NOT_FOUND": "Invalid user request", "INVALID_REQUEST": "Request is invalid", "RESEND_OTP_SUCCESS": "Email resend successful", "LOGIN_SUCCESS": "User successfully logged in", "LOGIN_FAILED": "Invalid user credentials.", "PARAM_REQUIRED": "%s is required", "FIELD_REQUIRED": "%s can't be blank", "FIELD_NOT_VALID": "%s is not valid.", "PORTAL_EXISTS": "Jira portal is already exists", "ERROR_MSG": "Something went wrong. please try again.", "ACCESS_DENIED": "You are not authorized to access this resource.", "DEACTIVATE_ACCOUNT_BY_ADMIN": "You account has been deactivate.", "PHOTO_DELETE_SUCCESS": "Your profile picture has been deleted successfully.", "INVALID_JIRA_CREDENTIALS": "The entered email and token are not correct. Please verify it.", "SELECT_EMPLOYEE": "Select the employee first", "INVALID_PORTAL_ID": "Jira portal id is invalid.", "PASSWORD_NOT_MATCH": "The passwords do not match", "CHANGE_PASSWORD_SUCCESS": "Password changed successfully", "FILE_NOT_FOUND": "File not found", "TEMPLATE_NAME_REQUIRED": "Template name is required", "TEMPLATE_SUBJECT_REQUIRED": "Template subject is required", "FORGOT_PASSWORD_LINK_SENT_SUCCESS": "An email has been sent. Please follow instructions on it.", "LINK_IS_VALID": "<PERSON> validated successfully.", "RESET_PASSWORD_SUCCESS": "Your password has been reset successfully.", "SIGNIN_SUCCESS": "User successfully logged in.", "ADD_EVENT_SUCCESS": "Fundraiser added successfully.", "MAIL_SENT_SUCCESS_PTO_ONBOARD": "You are not onboarded on stripe so an email has been sent successfully for the stripe onboarding.", "PTO_REONBOARD": "You cannot create fundraiser as your stripe account is inactive. Please contact admin to get reonboard.", "INVALID_FILE_FORMAT": "Invalid file format", "EVENT_PUBLISH_SUCCESS": "Fundraiser status changed to published.", "EVENT_NOT_FOUND": "Fundraiser doesn't exists.", "EVENT_ALREADY_PUBLISHED": "Fundraiser is already published.", "EVENT_CANT_BE_DELETED": "Cannot delete published fundraiser.", "EVENT_UPDATE_SUCCESS": "Fundraiser updated successfully", "EVENT_DELETE_SUCCESS": "Fundraiser deleted successfully.", "PAYMENT_STATUS_UPDATE_SUCCESS": "Fundraiser payment status updated successfully.", "EVENT_ALREADY_REGISTERED": "You are already registered for this fundraiser!", "PAYMENT_PENDING_CASH_CHEQUE": "You are already registered for this fundraiser. Please wait till admin approve your request!", "PAYMENT_SUCCESS_CASH_CHEQUE": "Your order has been placed! After admin approval, your purchase will be visible in My Feeds, and you'll be notified.", "EVENT_REGISTRATION_LIMIT": "Fundraiser registration limit reached. Unable to register at this time.", "EVENT_FEE_INVALID": "Fundraiser fee should be greater than 0 for paid event.", "EVENT_START_DATE_ERROR": "Fundraiser start date & time cannot be before the current time.", "EVENT_END_DATE_BEFORE_START_DATE": "Fundraiser end date cannot be before the fundraiser start date.", "INVALID_EVENT_STATUS": "Fundraiser status can be published or draft", "PARTICIPANT_LIMIT_INVALID": "Participant limit should be greater than 0", "INVALID_ORGANIZATION": "Invalid organization id passed", "INVALID_EVENT_TYPE": "Invalid fundraiser type passed", "UNAUTHORIZED_ACTION_FOR_ORGANIZATION": "You are not associated with given organization", "EVENT_IS_FREE_ERROR": "Cannot add fee for a free fundraiser", "EVENT_STATUS_CANT_CHANGE": "Published fundraiser status cannot be changed from published to unpublished.", "DATE_FORMAT_ERROR": "%s must be in MM/DD/YYYY format", "TIME_FORMAT_ERROR": "%s must be in HH:mm format", "CANT_REGISTER_TO_PAST_EVENT": "You can't register for past fundraiser.", "%s must be in MM/DD/YYYY format": "%s must be in MM/DD/YYYY format", "%s must be in HH:mm format": "%s must be in HH:mm format", "QUANTITY_INSTRUCTION_LENGTH": "Quantity instruction length should be between 4 to 255 characters.", "INVALID_ENUM_VALUE": "%s field can contains value from %s", "VENMO_URL_REQUIRED": "Please provide venmo URL before creating a paid fundraiser", "PAYMENT_INSTRUCTIONS_REQUIRED": "Please add payment instructions before creating an fundraiser.", "INVALID_PAYMENT_METHOD": "This fundraiser is not paid by cash, check or venmo!", "STRIPE_NOT_ACTIVE": "This organization is not accepting online payments at the moment. Please try different payment method.", "MEMBERSHIP_BENEFIT_NOT_SET": "Membership benefit is not set for this organization.", "ALREADY_HAVE_FAMILY_MEMBERSHIP": "You already have a family membership for this organization.", "INVALID_CHILD_ID": "Invalid child id passed.", "ALREADY_HAVE_CHILD_MEMBERSHIP": "The child already has a membership for this organization.", "FUNDRAISER_ONLY_FOR_MEMBERS": "This fundraiser is only for members.", "FUNDRAISER_SIGNUP_APPROVAL_REQUIRED": "Fundraiser signup should be approved first!", "FUNDRAISER_SIGNUP_NOT_FOUND": "Fundraiser signup not found!", "FUNDRAISER_SIGNUP_ALREADY_FULFILLED": "Fundraiser signup is already fulfilled!", "CANNOT_REGISTER_FOR_GUEST_SIGNUP": "You cannot register for this fundraiser as a guest.", "INVALID_RECAPTCHA_ACTION": "Invalid reCAPTCHA action", "INVALID_RECAPTCHA_TOKEN": "Invalid reCAPTCHA token", "NO_RECAPTCHA_TOKEN_OR_ACTION_PROVIDED": "No reCAPTCHA token or action provided", "RECAPTCHA_SCORE_TOO_LOW": "reCAPTCHA score too low", "RECAPTCHA_VERIFICATION_FAILED": "reCAPTCHA verification failed"}