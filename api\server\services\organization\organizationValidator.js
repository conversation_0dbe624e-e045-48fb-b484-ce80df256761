const GeneralError = require('../../util/GeneralError');
const validation = require('../../util/validation');
const REQUIRED = 'FIELD_REQUIRED';
const INVALID = 'FIELD_NOT_VALID';
const INVALID_ADDRESS = 'INVALID_ADDRESS';
const INVALID_ORGANIZATION_NAME = 'INVALID_ORGANIZATION_NAME';
const INVALID_ZIP_CODE = 'INVALID_ZIP_CODE';

/**
 * Class represents validations for organization.
 */
class OrganizationValidator extends validation {
    constructor (body, locale, query) {
        super(locale);
        this.NOT_VALID = INVALID;
        this.REQUIRED = REQUIRED;
        this.INVALID_ADDRESS = INVALID_ADDRESS;
        this.INVALID_ZIP_CODE = INVALID_ZIP_CODE;
        this.INVALID_ORGANIZATION_NAME = INVALID_ORGANIZATION_NAME;
        this.body = body;
        this.query = query;
    }

    /**
   * @desc This function is being used to validate request for add ORGANIZATION
   * <AUTHOR>
   * @since 7/11/2023
   */
    validate () {
        this.verifyOrganizationName();
        super.name(this.body.adminFirstName, 'Admin first name');
        super.name(this.body.adminLastName, 'Admin last name');
        super.field(this.body.category, 'Category');
        this.verifyCategory();
        super.field(this.body.address, 'Address');
        this.verifyAddress();
        super.field(this.body.country, 'Country');
        super.field(this.body.state, 'State');
        super.field(this.body.city, 'City');
        super.field(this.body.zipCode, 'Zip Code');
        this.verifyZipCode();
        this.body.phoneNumber &&
      super.mobile(this.body.phoneNumber, 'Phone number');
        this.body.phoneNumber && super.field(this.body.countryCode, 'Country Code');
        super.email(this.body.email);
        this.body.category !== CONSTANTS.CATEGORIES.SCHOOL &&
      this.body.category !== CONSTANTS.CATEGORIES.CLUB &&
      this.body.category !== CONSTANTS.CATEGORIES.BUSINESS &&
      super.field(this.body.parentOrganization, 'Parent Organization Id');
        this.body.category !== CONSTANTS.CATEGORIES.SCHOOL &&
      this.body.category !== CONSTANTS.CATEGORIES.CLUB &&
      this.body.category !== CONSTANTS.CATEGORIES.BUSINESS &&
      super.uuid(this.body.parentOrganization, 'Parent Organization Id');
    }

    /**
   * @desc This function is being used to validate event image fileType
   * <AUTHOR>
   * @since 19/12/2023
   * @param {String} mimeType mimeType
   */
    fileType (file) {
        if (
            !file.mimetype ||
      CONSTANTS.PROFILE_PICTURE.ALLOWED_TYPE.indexOf(file.mimetype) === -1
        ) {
            throw {
                message: MESSAGES.INVALID_FILE_FORMAT,
                statusCode: 400
            };
        }
    }

    /**
   * @desc This function is being used to validate request for update ORGANIZATION details
   * <AUTHOR>
   * @since 15/11/2023
   */
    validateUpdateOrganizationDetails () {
        super.param(this.body.orgId, 'Organization Id');
        super.uuid(this.body.orgId, 'Organization Id');
        this.verifyOrganizationName();
        super.field(this.body.address, 'Address');
        this.verifyAddress();
        super.field(this.body.country, 'Country');
        super.field(this.body.state, 'State');
        super.field(this.body.city, 'City');
        super.field(this.body.zipCode, 'Zip Code');
        this.verifyZipCode();
        super.number(this.body.platformFee, 'Platform Fee');
        super.number(this.body.minPlatformFeeAmount, 'Minimum Platform Fee Amount');
        this.body.allowedPaymentType.cash &&
      super.field(
          this.body.paymentInstructions.cashInstruction,
          'Cash instruction'
      );
        this.body.allowedPaymentType.cheque &&
      super.field(
          this.body.paymentInstructions.chequeInstruction,
          'Cheque instruction'
      );
        this.body.allowedPaymentType.venmo &&
      super.field(
          this.body.paymentInstructions.venmoInstruction,
          'Venmo payment instruction'
      );
        this.body.allowedPaymentType.venmo &&
      super.field(
          this.body.paymentDetails.venmoPaymentURL,
          'Venmo payment URL'
      );
        if (
            !this.body.allowedPaymentType.cash &&
            !this.body.allowedPaymentType.stripe &&
            !this.body.allowedPaymentType.cheque &&
            !this.body.allowedPaymentType.venmo
        ) {
            throw new GeneralError(MESSAGES.PAYMENT_TYPE_REQUIRED, 400);
        }
        if (this.file) {
            this.fileType(this.file);
        }
    }

    /**
   * @desc This function is being used to validate request for update Organization status
   * <AUTHOR>
   * @since 10/11/2023
   */
    validateUpdateOrganizationStatus () {
        super.param(this.body.orgId, 'Organization Id');
        super.uuid(this.body.orgId, 'Organization Id');
        super.param(this.body.status, 'Status');
        super.status(this.body.status);
    }

    /**
   * @desc This function is being used to validate if orgId is sent in params
   * <AUTHOR>
   * @since 10/11/2023
   */
    validateOrganizationId () {
        super.param(this.query.orgId, 'Organization Id');
        super.uuid(this.query.orgId, 'Organization Id');
    }

    verifyOrganizationName () {
        const { orgName } = this.body;
        if (!orgName) {
            throw new GeneralError(this.__(REQUIRED, 'Organization name'), 400);
        }
        if (orgName.length > 200) {
            throw new GeneralError(
                this.__(INVALID_ORGANIZATION_NAME, 'Organization name'),
                400
            );
        }
    }

    verifyAddress () {
        const { address } = this.body;
        if (address.length > 200) {
            throw new GeneralError(this.__(INVALID_ADDRESS, 'Address'), 400);
        }
    }

    verifyCategory () {
        const { category } = this.body;
        if (!CONSTANTS.CATEGORY_TYPE.includes(category)) {
            throw new GeneralError(this.__(INVALID, 'Category'), 400);
        }
    }

    verifyCategoryParams () {
        const { category } = this.query;
        if (!CONSTANTS.CATEGORY_TYPE.includes(category)) {
            throw new GeneralError(this.__(INVALID, 'Category'), 400);
        }
    }

    verifyZipCode () {
        const { zipCode } = this.body;
        if (!CONSTANTS.REGEX.ZIP.test(zipCode)) {
            throw new GeneralError(this.__(INVALID_ZIP_CODE, 'Zip Code'), 400);
        }
    }

    validateAddOrganizationUser () {
        const {
            orgId,
            firstName,
            lastName,
            email,
            role,
            phoneNumber,
            countryCode,
            password
        } = this.body;
        super.field(orgId, 'Organization Id');
        super.uuid(orgId.trim(), 'Organization Id');
        super.name(firstName, 'First Name');
        super.name(lastName, 'Last Name');
        super.email(email);
        super.field(role, 'Organization Role');
        if (CONSTANTS.ORG_ROLES.indexOf(role.toLowerCase().trim()) === -1) {
            throw new GeneralError(this.__(INVALID, 'Organization role'), 400);
        }
        phoneNumber && super.mobile(phoneNumber, 'Phone number');
        phoneNumber && super.field(countryCode, 'Country Code');
        super.password(password, 'Password');
    }

    validateUpdateOrganizationUser () {
        const { orgId, userId, role, status, password, email } = this.body;
        super.field(orgId, 'Organization Id');
        super.uuid(orgId.trim(), 'Organization Id');
        super.field(userId, 'User Id');
        super.field(role, 'Organization Role');
        if (CONSTANTS.ORG_ROLES.indexOf(role.toLowerCase().trim()) === -1) {
            throw new GeneralError(this.__(INVALID, 'Organization role'), 400);
        }
        super.field(status, 'Status');
        if (CONSTANTS.ORG_STATUS.indexOf(status.toLowerCase().trim()) === -1) {
            throw new GeneralError(this.__(INVALID, 'Organization status'), 400);
        }
        if (password) {
            super.password(password, 'Password');
            super.email(email, 'Email');
        }
    }

    validateDeleteOrganizationUser () {
        const { orgId, userId } = this.body;
        super.field(orgId, 'Organization Id');
        super.uuid(orgId.trim(), 'Organization Id');
        super.field(userId, 'User Id');
    }

    async validateChildrenToOrganization () {
        const { orgId, addedChildIds, removedChildIds } = this.body;
        super.field(orgId, 'Organization Id');
        super.uuid(orgId.trim(), 'Organization Id');
        super.field(addedChildIds, 'Added Child Ids');
        super.field(removedChildIds, 'Removed Child Ids');
    }
}

module.exports = OrganizationValidator;
