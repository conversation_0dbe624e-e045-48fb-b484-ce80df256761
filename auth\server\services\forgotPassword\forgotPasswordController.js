const ForgotPasswordService = require('./forgotPasswordService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for forgot password Details.
 */
class ForgotPasswordController {
    /**
     * @desc This function is being used to generate otp to reset password
     * <AUTHOR>
     * @since 17/10/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {Object} req.body.email email of user to reset password
     * @param {function} res Response
     */
    static async forgotPassword (req, res) {
        try {
            const data = await ForgotPasswordService.forgotPassword(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.FORGOT_PASSWORD_LINK_SENT_SUCCESS);
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to verify otp
     * <AUTHOR>
     * @since 17/10/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     */
    static async verifyOtp (req, res) {
        try {
            const data = await ForgotPasswordService.verifyOtp(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to reset password
     * <AUTHOR>
     * @since 18/10/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {function} res Response
     */
    static async resetPassword (req, res) {
        try {
            const data = await ForgotPasswordService.resetPassword(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.RESET_PASSWORD_SUCCESS);
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = ForgotPasswordController;

