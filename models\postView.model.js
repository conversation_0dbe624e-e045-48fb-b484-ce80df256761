const dynamoose = require('dynamoose');
const { v4: uuidv4 } = require('uuid');

const postViewsSchema = new dynamoose.Schema({
    id: {
        hashKey: true,
        type: String,
        default: () => uuidv4()
    },
    childId: {
        type: String,
        required: true
    },
    postId: {
        type: String,
        required: true
    },
    createdBy: {
        type: String
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('PostViews', postViewsSchema);
