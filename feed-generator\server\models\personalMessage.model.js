const dynamoose = require('dynamoose');

const messageSchema = new dynamoose.Schema({
    messageId: {
        hashKey: true,
        type: String,
        required: true
    },
    conversationId: {
        type: String,
        required: true,
        index: {
            name: 'conversationId-createdAt-index',
            global: true,
            project: true,
            rangeKey: 'createdAt'
        }
    },
    senderId: {
        type: String,
        required: true
    },
    message: {
        type: String
    },
    mediaName: {
        type: String
    },
    mediaType: {
        type: String
    },
    mediaDisplayName: {
        type: String
    },
    mediaThumbnailName: {
        type: String
    },
    replyMessage: {
        type: Object,
        required: false,
        schema: {
            messageId: {
                type: String,
                required: true
            },
            senderId: {
                type: String,
                required: true
            },
            message: {
                type: String
            },
            mediaName: {
                type: String
            },
            mediaType: {
                type: String
            },
            mediaDisplayName: {
                type: String
            }
        },
        default: null
    },
    isEdited: {
        type: Boolean,
        default: false
    },
    isDeleted: {
        type: Boolean,
        default: false
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('PersonalMessage', messageSchema);
