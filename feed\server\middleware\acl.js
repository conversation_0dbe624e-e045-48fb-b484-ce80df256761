const Utils = require('../util/utilFunctions');
const HTTPStatus = require('../util/http-status');

module.exports = function (req, res, next) {
    const accessList = {
        'app': [
            { method: 'GET', path: '/feed/list' },
            { method: 'GET', path: '/feed/registered' },
            { method: 'GET', path: '/feed/events' },
            { method: 'GET', path: '/feed/registered-events' },
            { method: 'GET', path: '/feed/details' },
            { method: 'POST', path: '/feed/comment' },
            { method: 'GET', path: '/feed/comment/list' },
            { method: 'DELETE', path: '/feed/comment' },
            { method: 'GET', path: '/feed/calendar-events' }
        ]
    };
    const accessLevel = res.locals.user.accessLevel;
    const isAllowed = _.find(accessList[accessLevel], { method: req.method, path: req.originalUrl.split('?')[0] });
    if (isAllowed) {
        next();
    } else {
        const responseObject = Utils.errorResponse();
        responseObject.message = res.__('ACCESS_DENIED');
        res.status(HTTPStatus.NOT_ACCEPTABLE).send(responseObject);
        return;
    }
};
