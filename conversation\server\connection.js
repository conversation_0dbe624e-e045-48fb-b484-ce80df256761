const {
    DynamoDBClient,
    ListTablesCommand
} = require('@aws-sdk/client-dynamodb');
const CONSOLE_LOGGER = require('./util/logger');

class DynamoDBConnection {
    static async connectToDB () {
        const dynamoDBConfig = {
            region: process.env.AWS_DB_REGION
        };
        return new Promise(async (resolve) => {
            try {
                const client = new DynamoDBClient(dynamoDBConfig);
                const command = new ListTablesCommand({});
                const response = await client.send(command);
                const tableNames = response.TableNames;
                CONSOLE_LOGGER.info('Connected to DynamoDB');
                resolve(tableNames);
            } catch (error) {
                CONSOLE_LOGGER.info('Error connecting to DynamoDB:', JSON.stringify(error));
                CONSOLE_LOGGER.error(
                    'DynamoDB connection unsuccessful, retry after 0.5 seconds.'
                );
                setTimeout(DynamoDBConnection.connectToDB, 500);
            }
        });
    }
}

module.exports = DynamoDBConnection;
