
const SignInService = require('./signInService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for signin.
 */
class SignInController {
    /**
     * @desc This function is being used to login user
     * <AUTHOR>
     * @since 17/10/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {Object} req.body.email email
     * @param {Object} req.body.password password
     * @param {function} res Response
     */
    static async login (req, res) {
        try {
            CONSOLE_LOGGER.time('login');
            const data = await SignInService.signIn(req, res.__);
            CONSOLE_LOGGER.timeEnd('login');
            Utils.sendResponse(null, data, res, MESSAGES.LOGIN_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in login: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    static async sociallogin (req, res) {
        try {
            CONSOLE_LOGGER.time('login');
            const data = await SignInService.socialSignIn(req, res.__);
            CONSOLE_LOGGER.timeEnd('login');
            Utils.sendResponse(null, data, res, MESSAGES.LOGIN_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in login', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = SignInController;
