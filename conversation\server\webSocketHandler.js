const handleConnect = require('./services/webSocketHandlers/handleConnect');
const handleDisconnect = require('./services/webSocketHandlers/handleDisconnect');
const handleSendGroupMessage = require('./services/webSocketHandlers/handleSendGroupMessage');
const handleGetGroupMessage = require('./services/webSocketHandlers/handleGetGroupMessage');
const handleSendPersonalMessage = require('./services/webSocketHandlers/handleSendPersonalMessage');
const handleGetPersonalMessage = require('./services/webSocketHandlers/handleGetPersonalMessage');
const handleCompanionMessage = require('./services/webSocketHandlers/handleCompanionMessage');
const SendMessageService = require('./services/sendSocketMessageService');
const jwt = require('jsonwebtoken');
const CONSOLE_LOGGER = require('./util/logger');

const websocketRoutes = async (event) => {
    CONSOLE_LOGGER.info('Setting up WebSocket Handler');
    const routeKey = event.requestContext?.routeKey ?? event.action ?? event.body?.action;
    switch (routeKey) {
        case '$connect': {
            const processedEvent = processEventForConnect(event);
            return await handleConnect(processedEvent);
        }

        case '$disconnect':
            return await handleDisconnect(event);

        case 'sendMessage':{
            const response = await handleSendGroupMessage(event);
            await handleResponse(response, event);
            return response;
        }

        case 'getMessage':{
            const response = await handleGetGroupMessage(event);
            await handleResponse(response, event);
            return response;
        }

        case 'sendPersonalMessage':{
            const response = await handleSendPersonalMessage(event);
            await handleResponse(response, event);
            return response;
        }

        case 'getPersonalMessage':{
            const response = await handleGetPersonalMessage(event);
            await handleResponse(response, event);
            return response;
        }

        case 'sendCompanionMessage':{
            const response = await handleCompanionMessage(event);
            await handleResponse(response, event);
            return response;
        }

        default: {
            const response = { statusCode: 400, message: 'Invalid WebSocket route', data: {} };
            await handleResponse(response, event);
            return response;
        }
    }
};

const handleResponse = async (response, event) => {
    if (response && event.requestContext.connectionId) {
        await SendMessageService.sendMessageToConnection({
            connectionId: event.requestContext.connectionId,
            messageData: response,
            statusCode: response.statusCode
        });
    } else {
        CONSOLE_LOGGER.error('Error: Response or ConnectionId is not present');
    }
};

const processEventForConnect = (event) => {
    const processedEvent = { ...event };
    const userId = getUserId(processedEvent);
    if (!userId) {
        CONSOLE_LOGGER.error('User Id is not present in the token');
        return { statusCode: 401, message: 'You are not authorized to access this resource.', data: {} };
    }
    processedEvent.headers = { userId };
    return processedEvent;
};

const getUserId = (event) => {
    const token = getToken(event);
    if (!token) {
        return null;
    }
    try {
        const decodedJwt = jwt.decode(token, { complete: true });
        const decodedJwtPayload = decodedJwt.payload;
        return decodedJwtPayload.id;
    } catch (error) {
        return null;
    }
};

const getToken = (event) => {
    const token =
        event.headers.Authorization?.split(' ')[1] ??
        event.queryStringParameters.token;
    if (event.headers.Authorization) {
        return token.replace(/"/g, '');
    }
    return token;
};

module.exports = websocketRoutes;
