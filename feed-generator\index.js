const AWS = require('aws-sdk');
const { unmarshall } = require('@aws-sdk/util-dynamodb');
const CONSOLE_LOGGER = require('./server/logger');
const DynamoDBConnection = require('./server/connection');
const Redis = require('ioredis');
const FeedService = require('./server/feedService');
const { Client } = require('@opensearch-project/opensearch');
const dotenv = require('dotenv');
const { childSchema, eventSchema, postSchema, fundraiserSchema } = require('./server/opensearchDB');
const CONSTANTS = require('./server/constants');
const ConversationGroupService = require('./server/ConversationGroupService');
const ChildHelperService = require('./server/ChildHelperService');
const env = process.env.NODE_ENV || 'local';
dotenv.config({ path: env + '.env' });
const BoosterService = require('./server/BoosterService');
const UserHelperService = require('./server/UserHelperService');
const ConversationService = require('./server/ConversationService');
const DBModelHelperService = require('./server/DBModelHelperService');

AWS.config.update({
    region: process.env.AWS_DB_REGION,
    endpoint: process.env.DB_HOST,
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});
let openSearchClient;
if (process.env.NODE_ENV !== 'testing') {
    openSearchClient = new Client({
        node: process.env.OPENSEARCH_ENDPOINT,
        auth: {
            username: process.env.OPENSEARCH_USERNAME,
            password: process.env.OPENSEARCH_PASSWORD
        },
        ssl: { rejectUnauthorized: false }
    });
}

const tablesToNotLogDetails = ['Groups', 'GroupMembers', 'PersonalConversation', 'Message', 'PersonalMessage'];

exports.handler = async (event) => {
    try {
        const schemaMapping = {
            ['events']: eventSchema,
            ['children']: childSchema,
            ['posts']: postSchema,
            ['fundraisers']: fundraiserSchema
        };

        const createOpenSearchIndicesIfNotExists = async () => {
            const allOpenSearchTables = Object.keys(CONSTANTS.INDICES);
            for (let i = 0; i < allOpenSearchTables.length; i++) {
                const value = allOpenSearchTables[i];
                const indexExists = await openSearchClient.indices.exists({ index: value });
                if (indexExists.statusCode !== 200) {
                    CONSOLE_LOGGER.info(value, 'index created');
                    await openSearchClient.indices.create(schemaMapping[value]);
                }
            }
        };

        if (process.env.NODE_ENV !== 'testing') {
            createOpenSearchIndicesIfNotExists();
        }
        for (const record of event.Records) {
            if (record.eventName === 'INSERT') {
                if (!tablesToNotLogDetails.includes(record.eventSourceARN.split('/')[1])) {
                    CONSOLE_LOGGER.info('Inserting event ', JSON.stringify(record));
                }
                if (process.env.NODE_ENV !== 'testing') {
                    await DynamoDBConnection.connectToDB();
                }
                switch (record.eventSourceARN.split('/')[1]) {
                    case 'Events': {
                        const eventDetails = unmarshall(record.dynamodb.NewImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis: ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    CONSOLE_LOGGER.info('Connected to Redis');
                                    const result = await FeedService.generateFeedAndStoreToCache(redis, eventDetails);
                                    const createdGroup = await ConversationGroupService.createEventGroupWithAdmins(eventDetails);
                                    resolve(await Promise.all([result, createdGroup]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error generating feed and storing to cache: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'Posts': {
                        const postDetails = unmarshall(record.dynamodb.NewImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis:  ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    CONSOLE_LOGGER.info('Connected to Redis');
                                    const result = await FeedService.generatePostFeedAndStoreToCache(redis, postDetails);
                                    resolve(await Promise.all([result]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error generating feed and storing to cache: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'Fundraisers': {
                        const fundraiserDetails = unmarshall(record.dynamodb.NewImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis:', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    CONSOLE_LOGGER.info('Connected to Redis');
                                    const result = await FeedService.generateFundraiserFeedAndStoreToCache(redis, fundraiserDetails);
                                    resolve(await Promise.all([result]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error generating feed and storing to cache: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'Child': {
                        const childDetails = unmarshall(record.dynamodb.NewImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    CONSOLE_LOGGER.info('Connected to Redis');
                                    if (childDetails.isGuestChild) {
                                        resolve();
                                    } else {
                                        const modifyData = await FeedService.generateFeedForChild(redis, childDetails);
                                        const modifyPostData = await FeedService.generatePostFeedForChild(redis, childDetails);
                                        const modifyFundraiserData = await FeedService.generateFundraiserFeedForChild(redis, childDetails);
                                        const user = await ChildHelperService.updateUserIsFeedGenerated(childDetails.createdBy);
                                        const addUserToGroup = await ConversationGroupService.addUserToOrganizationGroup(childDetails);
                                        const result = await Promise.all(
                                            [
                                                modifyData, modifyPostData, modifyFundraiserData,
                                                user, addUserToGroup
                                            ]
                                        );
                                        resolve(await Promise.all([result]));
                                    }
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error generating feed for child: ', error);
                                    await ChildHelperService.updateUserIsFeedGenerated(childDetails.createdBy);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'EventSignups': {
                        const eventSignup = unmarshall(record.dynamodb.NewImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    CONSOLE_LOGGER.info('Connected to Redis');
                                    if (eventSignup.isGuestSignup) {
                                        resolve();
                                    } else {
                                        const result = await FeedService.addToRegisteredEvents(redis, eventSignup);
                                        const addUserToEventGroup = await ConversationGroupService.addGuardiansToEventGroup(eventSignup);
                                        resolve(await Promise.all([result, addUserToEventGroup]));
                                    }
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error adding registered events: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'FundraiserSignup': {
                        const fundraiserSignup = unmarshall(record.dynamodb.NewImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    CONSOLE_LOGGER.info('Connected to Redis');
                                    if (fundraiserSignup.isGuestSignup) {
                                        resolve();
                                    } else {
                                        const isBoosterFundraiser = await BoosterService.isBoosterFundraiser(fundraiserSignup.eventId);
                                        const updatedBooster = isBoosterFundraiser
                                            ? await BoosterService.updateSignupsLeaderboard(fundraiserSignup)
                                            : Promise.resolve();
                                        const feedUpdate = await FeedService.addToFundraiserSignupFeed(redis, fundraiserSignup);
                                        resolve(await Promise.all([feedUpdate, updatedBooster]));
                                    }
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error adding to fundraiser feed: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'OrgManagedFundraiserBoosterDonation': {
                        const orgManagedFundraiserBoosterDonation = unmarshall(record.dynamodb.NewImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    CONSOLE_LOGGER.info('Connected to Redis');
                                    const boosterUpdate = await BoosterService.isSuccessfulDonation(orgManagedFundraiserBoosterDonation)
                                        ? BoosterService.updateDonationsAmountRaisedAndLeaderboard(orgManagedFundraiserBoosterDonation)
                                        : Promise.resolve();
                                    const feedUpdate = FeedService.updateDonationAmountRaisedAndSendNotification(
                                        redis, orgManagedFundraiserBoosterDonation
                                    );
                                    resolve(await Promise.all([boosterUpdate, feedUpdate]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error adding to org managed fundraiser booster donation feed: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'Organization': {
                        const organizationDetails = unmarshall(record.dynamodb.NewImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    CONSOLE_LOGGER.info('Creating organization group with admins');
                                    const cacheOrgDetails = await FeedService
                                        .addOrUpdateOrganizationDetailsToCache(redis, organizationDetails);

                                    const createdGroup = await ConversationGroupService
                                        .createOrganizationGroupWithAdmins(organizationDetails);

                                    resolve(await Promise.all([cacheOrgDetails, createdGroup]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error creating organization group with admins: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'User': {
                        const userDetails = unmarshall(record.dynamodb.NewImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });

                            redis.on('connect', async () => {
                                try {
                                    CONSOLE_LOGGER.info('Connected to Redis');
                                    if (userDetails.isGuestUser) {
                                        resolve();
                                    } else {
                                        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
                                        const result = await UserHelperService.addOrUpdateUserDetails(redis, userDetails, versionPrefix);
                                        resolve(await Promise.all([result]));
                                    }
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error updating user details: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'Groups': {
                        const groupDetails = unmarshall(record.dynamodb.NewImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
                                    const result = await ConversationService.addOrUpdateGroupDetailsToHashSet(
                                        redis, groupDetails, versionPrefix
                                    );
                                    resolve(await Promise.all([result]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error updating group details: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'GroupMembers': {
                        const groupMemberDetails = unmarshall(record.dynamodb.NewImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
                                    const result =
                                        await ConversationService.addOrUpdateGroupMemberToHashSet(
                                            redis,
                                            groupMemberDetails,
                                            versionPrefix
                                        );
                                    resolve(await Promise.all([result]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error updating group member details: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'PersonalConversation': {
                        const personalConversationDetails = unmarshall(record.dynamodb.NewImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
                                    const result =
                                        await ConversationService.addOrUpdatePersonalConversationDetailsToHashSet(
                                            redis,
                                            personalConversationDetails,
                                            versionPrefix
                                        );
                                    resolve(await Promise.all([result]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error updating personal conversation details: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'Message': {
                        const messageDetails = unmarshall(record.dynamodb.NewImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
                                    const result =
                                        await ConversationService.addGroupMessageToCache({
                                            redis,
                                            messageDetails,
                                            versionPrefix
                                        });
                                    resolve(await Promise.all([result]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error updating message details: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'PersonalMessage': {
                        const personalMessageDetails = unmarshall(record.dynamodb.NewImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
                                    const result = await ConversationService.addPersonalMessageToCache({
                                        redis,
                                        personalMessageDetails,
                                        versionPrefix
                                    });
                                    resolve(await Promise.all([result]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error updating message details: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    default:
                        CONSOLE_LOGGER.info('Ignoring event ', JSON.stringify(record));
                        break;
                }
            } else if (record.eventName === 'MODIFY') {
                if (!tablesToNotLogDetails.includes(record.eventSourceARN.split('/')[1])) {
                    CONSOLE_LOGGER.info('Modifying event ', JSON.stringify(record));
                }
                if (process.env.NODE_ENV !== 'testing') {
                    await DynamoDBConnection.connectToDB();
                }
                switch (record.eventSourceARN.split('/')[1]) {
                    case 'Events': {
                        const newEvent = unmarshall(record.dynamodb.NewImage);
                        const oldEvent = unmarshall(record.dynamodb.OldImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    CONSOLE_LOGGER.info('Connected to Redis');
                                    CONSOLE_LOGGER.info('Updating event');
                                    const result = await FeedService.updateFeedAndStoreToCache(redis, oldEvent, newEvent);
                                    const updateEventGroupMetaData =
                                        await ConversationGroupService.updateEventGroupMetaData(oldEvent, newEvent);
                                    resolve(await Promise.all([result, updateEventGroupMetaData]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error updating feed and storing to cache: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'Fundraisers': {
                        const newFundraiser = unmarshall(record.dynamodb.NewImage);
                        const oldFundraiser = unmarshall(record.dynamodb.OldImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    CONSOLE_LOGGER.info('Connected to Redis');
                                    const result = await FeedService.updateFundraiserFeedAndStoreToCache(
                                        redis, oldFundraiser, newFundraiser
                                    );
                                    resolve(await Promise.all([result]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error updating feed and storing to cache: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'Posts': {
                        const newPost = unmarshall(record.dynamodb.NewImage);
                        const oldPost = unmarshall(record.dynamodb.OldImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    CONSOLE_LOGGER.info('Connected to Redis');
                                    CONSOLE_LOGGER.info('Updating post');
                                    const result = await FeedService.updatePostFeedAndStoreToCache(redis, oldPost, newPost);
                                    resolve(await Promise.all([result]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error updating feed and storing to cache: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'EventSignups': {
                        const newEventSignup = unmarshall(record.dynamodb.NewImage);
                        const oldEventSignup = unmarshall(record.dynamodb.OldImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    CONSOLE_LOGGER.info('Connected to Redis');
                                    if (newEventSignup.isGuestSignup) {
                                        resolve();
                                    } else {
                                        const result = await FeedService.updateRegisteredEvents(redis, oldEventSignup, newEventSignup);
                                        const addGuardiansToEventGroup =
                                            await ConversationGroupService.addGuardiansToEventGroupAfterUpdate(
                                                oldEventSignup, newEventSignup
                                            );
                                        resolve(await Promise.all([result, addGuardiansToEventGroup]));
                                    }
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error updating registered events: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'FundraiserSignup': {
                        const newFundraiserSignup = unmarshall(record.dynamodb.NewImage);
                        const oldFundraiserSignup = unmarshall(record.dynamodb.OldImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    CONSOLE_LOGGER.info('Connected to Redis');
                                    if (newFundraiserSignup.isGuestSignup) {
                                        resolve();
                                    } else {
                                        const result = await FeedService.updateFundraiserSignupFeed(
                                            redis, oldFundraiserSignup, newFundraiserSignup
                                        );
                                        resolve(await Promise.all([result]));
                                    }
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error updating fundraiser feed: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'Child': {
                        const newChild = unmarshall(record.dynamodb.NewImage);
                        const oldChild = unmarshall(record.dynamodb.OldImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    CONSOLE_LOGGER.info('Connected to Redis');
                                    if (newChild.isGuestChild) {
                                        resolve();
                                    } else {
                                        const result = await FeedService.updateFeedForChild(redis, oldChild, newChild);
                                        const updateChildGuardiansGroups =
                                            await ConversationGroupService.updateChildGuardiansGroups(oldChild, newChild);
                                        resolve(await Promise.all([result, updateChildGuardiansGroups]));
                                    }
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error updating feed for child: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'Organization': {
                        const newOrganization = unmarshall(record.dynamodb.NewImage);
                        const oldOrganization = unmarshall(record.dynamodb.OldImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    CONSOLE_LOGGER.info('Connected to Redis');
                                    const cacheOrgDetails = await FeedService.addOrUpdateOrganizationDetailsToCache(redis, newOrganization);

                                    const updateOrganizationGroupMetaData = await ConversationGroupService.updateOrganizationGroupMetaData(
                                        oldOrganization, newOrganization
                                    );
                                    resolve(await Promise.all([cacheOrgDetails, updateOrganizationGroupMetaData]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error updating organization group with admins: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'OrganizationMembers': {
                        const newOrganizationMember = unmarshall(record.dynamodb.NewImage);
                        const oldOrganizationMember = unmarshall(record.dynamodb.OldImage);
                        try {
                            const result = await ConversationGroupService.updateOrganizationGroupMembers(
                                oldOrganizationMember,
                                newOrganizationMember
                            );
                            await Promise.all([result]);
                        } catch (error) {
                            CONSOLE_LOGGER.error('Error updating organization group members: ', error);
                        }
                        break;
                    }
                    case 'User': {
                        const newUser = unmarshall(record.dynamodb.NewImage);
                        const oldUser = unmarshall(record.dynamodb.OldImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    CONSOLE_LOGGER.info('Connected to Redis');
                                    if (newUser.isGuestUser) {
                                        resolve();
                                    } else {
                                        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
                                        const result = await UserHelperService.handleUserDetailsUpdate(
                                            redis, oldUser, newUser, versionPrefix
                                        );
                                        resolve(await Promise.all([result]));
                                    }
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error updating user details: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'Groups': {
                        const oldGroup = unmarshall(record.dynamodb.OldImage);
                        const newGroup = unmarshall(record.dynamodb.NewImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
                                    const result =
                                        await ConversationService.handleGroupDetailsUpdate(
                                            redis,
                                            oldGroup,
                                            newGroup,
                                            versionPrefix
                                        );
                                    resolve(await Promise.all([result]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error updating group details: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'GroupMembers': {
                        const newGroupMember = unmarshall(record.dynamodb.NewImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
                                    const result =
                                        await ConversationService.addOrUpdateGroupMemberToHashSet(
                                            redis,
                                            newGroupMember,
                                            versionPrefix
                                        );
                                    resolve(await Promise.all([result]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error updating group members: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'PersonalConversation': {
                        const newPersonalConversation = unmarshall(record.dynamodb.NewImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
                                    const result =
                                        await ConversationService.addOrUpdatePersonalConversationDetailsToHashSet(
                                            redis,
                                            newPersonalConversation,
                                            versionPrefix
                                        );
                                    resolve(await Promise.all([result]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error updating personal conversation details: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'Message': {
                        const newMessage = unmarshall(record.dynamodb.NewImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
                                    const result = await ConversationService.updateGroupMessageDetailsInCache(
                                        redis,
                                        newMessage,
                                        versionPrefix
                                    );
                                    resolve(await Promise.all([result]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error updating message details: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'PersonalMessage': {
                        const newPersonalMessage = unmarshall(record.dynamodb.NewImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
                                    const result = await ConversationService.updatePersonalMessageDetailsInCache(
                                        redis,
                                        newPersonalMessage,
                                        versionPrefix
                                    );
                                    resolve(await Promise.all([result]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error updating personal message details: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    default:
                        CONSOLE_LOGGER.info('Ignoring event ', JSON.stringify(record));
                        break;
                }
            } else if (record.eventName === 'REMOVE') {
                if (!tablesToNotLogDetails.includes(record.eventSourceARN.split('/')[1])) {
                    CONSOLE_LOGGER.info('Removing event ', JSON.stringify(record));
                }
                if (process.env.NODE_ENV !== 'testing') {
                    await DynamoDBConnection.connectToDB();
                }
                switch (record.eventSourceARN.split('/')[1]) {
                    case 'EventSignups': {
                        const oldEventSignup = unmarshall(record.dynamodb.OldImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    CONSOLE_LOGGER.info('Connected to Redis');
                                    if (oldEventSignup.isGuestSignup) {
                                        resolve();
                                    } else {
                                        const result = await FeedService.removeRegisteredEvents(redis, oldEventSignup);
                                        const removeGuardiansFromEventGroup =
                                            await ConversationGroupService.removeGuardiansFromEventGroup(oldEventSignup);
                                        resolve(await Promise.all([result, removeGuardiansFromEventGroup]));
                                    }
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error removing registered events: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'FundraiserSignup': {
                        const oldFundraiserSignup = unmarshall(record.dynamodb.OldImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    CONSOLE_LOGGER.info('Connected to Redis');
                                    if (oldFundraiserSignup.isGuestSignup) {
                                        resolve();
                                    } else {
                                        const result = await FeedService.removeFundraiserSignupFeed(redis, oldFundraiserSignup);
                                        resolve(await Promise.all([result]));
                                    }
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error removing fundraiser feed: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'User': {
                        const oldUser = unmarshall(record.dynamodb.OldImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    CONSOLE_LOGGER.info('Connected to Redis');
                                    if (oldUser.isGuestUser) {
                                        resolve();
                                    } else {
                                        const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
                                        const result = await UserHelperService.removeUserDetails(redis, oldUser, versionPrefix);
                                        resolve(await Promise.all([result]));
                                    }
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error removing user details: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'GroupMembers': {
                        const oldGroupMember = unmarshall(record.dynamodb.OldImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
                                    const result =
                                        await ConversationService.removeGroupMemberFromHashSet(
                                            redis,
                                            oldGroupMember,
                                            versionPrefix
                                        );
                                    resolve(await Promise.all([result]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error removing group member: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    case 'PersonalConversation': {
                        const oldPersonalConversation = unmarshall(record.dynamodb.OldImage);
                        await new Promise((resolve, reject) => {
                            const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
                            redis.on('error', (err) => {
                                CONSOLE_LOGGER.error('Error connecting to redis ', err);
                                reject(err);
                            });
                            redis.on('connect', async () => {
                                try {
                                    const versionPrefix = await DBModelHelperService.getVersionPrefixForRedisKeys();
                                    const result =
                                        await ConversationService.removePersonalConversationDetailsFromHashSet(
                                            redis,
                                            oldPersonalConversation,
                                            versionPrefix
                                        );
                                    resolve(await Promise.all([result]));
                                } catch (error) {
                                    CONSOLE_LOGGER.error('Error removing personal conversation details: ', error);
                                    reject(error);
                                }
                            });
                        });
                        break;
                    }
                    default:
                        CONSOLE_LOGGER.info('Ignoring event ', JSON.stringify(record));
                        break;
                }
            }
        }
    } catch (error) {
        CONSOLE_LOGGER.error('Error processing event ', error);
    }
};
