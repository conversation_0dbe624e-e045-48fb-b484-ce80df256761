const validation = require('../../util/validation');

/**
 * Class represents validations for feed.
 */
class FeedValidator extends validation {
    constructor (query, locale) {
        super(locale);
        this.query = query;
    }

    /**
     * @desc This function is being used to validate request for add comment
     * <AUTHOR>
     * @since 27/11/2023
     */
    validateQueryParams () {
        const { eventId } = this.query;
        super.uuid(eventId, 'Event Id');
    }
}


module.exports = FeedValidator;
