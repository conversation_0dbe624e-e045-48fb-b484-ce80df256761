const validation = require('../../util/validation');

/**
 * Class represents validations for feed.
 */
class FeedValidator extends validation {
    constructor (query, locale) {
        super(locale);
        this.query = query;
    }

    /**
     * @desc This function is being used to validate request for add comment
     * <AUTHOR>
     * @since 27/11/2023
     */
    validateQueryParams () {
        const { eventId } = this.query;
        super.uuid(eventId, 'Event Id');
    }

    validateGuestSignupFeedDetails () {
        const { referenceId, isFundraiser } = this.query;
        super.field(referenceId, 'Reference Id');
        super.uuid(referenceId, 'Reference Id');
        this.query.isFundraiser = isFundraiser?.toString()?.toLowerCase() === 'true';
    }
}


module.exports = FeedValidator;
