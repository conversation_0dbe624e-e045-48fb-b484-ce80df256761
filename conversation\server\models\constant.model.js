const dynamoose = require('dynamoose');

const constantSchema = new dynamoose.Schema({
    name: {
        type: String,
        hashKey: true,
        required: true
    },
    value: {
        type: String,
        required: true
    },
    description: {
        type: String,
        required: false
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('Constants', constantSchema);
