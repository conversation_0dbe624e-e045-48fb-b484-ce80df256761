const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const sinon = require('sinon');
const jwt = require('jsonwebtoken');
const Utils = require('../../../../util/utilFunctions');
const User = require('../../../../models/user.model');
const GroupMembersModel = require('../../../../models/groupMembers.model');
const GroupsModel = require('../../../../models/groups.model');
const CONSTANTS = require('../../../../util/constants');
const testcaseConversation = require('./testcaseConversation');

const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
/* User Token */
const user = {
    id: '95285616-3769-4b2e-b36b-898597b8146e',
    sub: '95285616-3769-4b2e-b36b-898597b8146e',
    email: '<EMAIL>',
    isVerified: 1,
    role: 1,
    status: 'active',
    isDeleted: 0
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'testToken', tokenOptionalInfo)
};

const queryParams = {
    messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
    groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
    organizationId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
    isThumbnail: 'false',
    isGroupMessage: 'true'
};

const queryParamsWithConversationId = {
    messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
    isThumbnail: 'false',
    isGroupMessage: 'false',
    conversationId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
};

Utils.addCommonReqTokenForHMac(request);

describe('Conversation Service', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should handle error if token is not passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .get('/conversation/group-list')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and correct token is passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .get('/conversation/group-list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and is verified', (done) => {
            getStub.resolves({ status: 'active', isVerified: 0, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP });
            request(process.env.BASE_URL)
                .get('/conversation/group-list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 423);
                    done();
                });
        });

        it('should return an empty array if no groups are found', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, id: 'userId1' });

            sinon.stub(GroupMembersModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().returns([])
                            })
                        })
                    })
                })
            });

            request(process.env.BASE_URL)
                .get('/conversation/group-list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    GroupMembersModel.query.restore();
                    done();
                });
        });

        it('should return group conversation list', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, id: 'userId1' });

            sinon.stub(GroupsModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().returns([
                                    {
                                        groupId: 'groupId1',
                                        status: 'active'
                                    },
                                    {
                                        groupId: 'groupId2',
                                        status: 'active'
                                    }
                                ])
                            })
                        })
                    })
                })
            });

            sinon.stub(GroupMembersModel, 'query').returns({
                eq: sinon.stub().returns({
                    where: sinon.stub().returns({
                        in: sinon.stub().returns({
                            where: sinon.stub().returns({
                                in: sinon.stub().returns({
                                    exec: sinon.stub().returns([
                                        {
                                            groupId: 'groupId1',
                                            isAdmin: true,
                                            muteConversation: false,
                                            childrenIds: ['childId1', 'childId2']
                                        },
                                        {
                                            groupId: 'groupId2',
                                            isAdmin: false,
                                            muteConversation: false,
                                            childrenIds: []
                                        },
                                        {
                                            groupId: 'groupId3',
                                            isAdmin: false,
                                            muteConversation: false
                                        },
                                        {
                                            groupId: 'groupId4',
                                            isAdmin: false,
                                            muteConversation: true,
                                            childrenIds: ['childId1', 'childId2']
                                        }
                                    ])
                                })
                            })
                        })
                    }),
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            in: sinon.stub().returns({
                                count: sinon.stub().returns({
                                    exec: sinon.stub().resolves(2)
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(GroupsModel, 'batchGet').resolves([
                {
                    groupId: 'groupId1',
                    status: 'active'
                },
                {
                    groupId: 'groupId2',
                    status: 'active',
                    groupType: CONSTANTS.GROUP_TYPES.ORGANIZATION,
                    organizationMetaData: {
                        name: 'organization1',
                        logo: 'logo1'
                    }
                },
                {
                    groupId: 'groupId4',
                    status: 'active',
                    groupType: CONSTANTS.GROUP_TYPES.EVENT,
                    eventMetaData: {
                        title: 'event1',
                        image: 'image1'
                    }
                }
            ]);

            request(process.env.BASE_URL)
                .get('/conversation/group-list?organizationId=organizationId1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    GroupMembersModel.query.restore();
                    GroupsModel.batchGet.restore();
                    GroupsModel.query.restore();
                    done();
                });
        });

        it('should return empty group conversation list', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, id: 'userId1' });

            sinon.stub(GroupsModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().returns([])
                            })
                        })
                    })
                })
            });

            sinon.stub(GroupMembersModel, 'query').returns({
                eq: sinon.stub().returns({
                    where: sinon.stub().returns({
                        in: sinon.stub().returns({
                            where: sinon.stub().returns({
                                in: sinon.stub().returns({
                                    exec: sinon.stub().returns([])
                                })
                            })
                        })
                    })
                })
            });

            request(process.env.BASE_URL)
                .get('/conversation/group-list?organizationId=organizationId1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    GroupMembersModel.query.restore();
                    GroupsModel.query.restore();
                    done();
                });
        });

        it('Should handle error in getting group list', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, id: 'userId1' });
            sinon.stub(GroupMembersModel, 'query').rejects(new Error('Error in getting group list'));
            sinon.stub(GroupsModel, 'query').rejects(new Error('Error in getting group list'));
            request(process.env.BASE_URL)
                .get('/conversation/group-list?organizationId=organizationId1')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    GroupMembersModel.query.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Group Members Listing', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('should throw error is group id is not passed', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, id: 'userId1' });
            request(process.env.BASE_URL)
                .get('/conversation/group-members')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('Should throw error if group id is not valid', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, id: 'userId1' });
            request(process.env.BASE_URL)
                .get('/conversation/group-members?groupId=invalidGroupId')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('Should throw error if group not found', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, id: 'userId1' });
            sinon.stub(GroupMembersModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().returns([])
                            })
                        })
                    })
                })
            });
            request(process.env.BASE_URL)
                .get('/conversation/group-members?groupId=d32cc08d-db7a-4d21-9fd8-22f0d0a3ca64')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    GroupMembersModel.query.restore();
                    done();
                });
        });

        it('Should throw error if I am not a member of the group', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, id: 'userId1' });
            sinon.stub(GroupMembersModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().returns([{
                                    groupId: 'groupId1',
                                    userId: 'userId2',
                                    organizationId: 'organizationId1',
                                    role: 'admin'
                                }])
                            })
                        })
                    })
                })
            });
            request(process.env.BASE_URL)
                .get('/conversation/group-members?groupId=d32cc08d-db7a-4d21-9fd8-22f0d0a3ca64')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    GroupMembersModel.query.restore();
                    done();
                });
        });

        it('should return group members if I am a member of the group', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, id: 'userId1' });
            sinon.stub(GroupMembersModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            in: sinon.stub().returns({
                                exec: sinon.stub().returns([
                                    {
                                        groupId: 'groupId1',
                                        userId: 'userId1',
                                        organizationId: 'organizationId1',
                                        role: 'super admin'
                                    },
                                    {
                                        groupId: 'groupId1',
                                        userId: 'userId2',
                                        organizationId: 'organizationId2'
                                    },
                                    {
                                        groupId: 'groupId1',
                                        userId: 'userId3',
                                        organizationId: 'organizationId3',
                                        role: 'admin'
                                    }
                                ])
                            })
                        })
                    })
                })
            });
            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub()
                            .onFirstCall().resolves([{
                                id: 'userId1',
                                firstName: 'John',
                                lastName: 'Doe',
                                associatedOrganizations: [{
                                    id: 'organizationId1',
                                    role: 'admin'
                                }]
                            }])
                            .onSecondCall().resolves([{
                                id: 'userId2',
                                firstName: 'Jane',
                                lastName: 'Doe',
                                associatedOrganizations: [{
                                    id: 'organizationId2'
                                }]
                            }])
                            .onThirdCall().resolves([])
                    })
                })
            });

            request(process.env.BASE_URL)
                .get('/conversation/group-members?groupId=d32cc08d-db7a-4d21-9fd8-22f0d0a3ca64')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Generate Presigned Url', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        testcaseConversation.generatePresignedUrl.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                    accessLevel: CONSTANTS.ACCESS_LEVEL.APP
                });

                request(process.env.BASE_URL)
                    .get('/conversation/generate-presigned-url')
                    .set({ Authorization: requestPayloadUser.token })
                    .query(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        done();
                    });
            });
        });

        it('should return presigned url', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP
            });

            request(process.env.BASE_URL)
                .get('/conversation/generate-presigned-url')
                .set({ Authorization: requestPayloadUser.token })
                .query(queryParams)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

        it('should return presigned url with conversation id', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP
            });

            request(process.env.BASE_URL)
                .get('/conversation/generate-presigned-url')
                .set({ Authorization: requestPayloadUser.token })
                .query(queryParamsWithConversationId)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

        it('should return presigned url for thumbnail', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP
            });

            request(process.env.BASE_URL)
                .get('/conversation/generate-presigned-url')
                .set({ Authorization: requestPayloadUser.token })
                .query({ ...queryParamsWithConversationId, isThumbnail: 'true' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
