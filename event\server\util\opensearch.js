/* eslint-disable max-len */
const { Client } = require('@opensearch-project/opensearch');

let client;

if (process.env.NODE_ENV !== 'testing') {
    client = new Client({
        node: process.env.OPENSEARCH_ENDPOINT,
        auth: {
            username: process.env.OPENSEARCH_USERNAME,
            password: process.env.OPENSEARCH_PASSWORD
        },
        ssl: { rejectUnauthorized: false }
    });
}

/**
 * Class represents Utilities function for AWS Opensearch.
 */
class AwsOpenSearchService {
    /**
     * Function to create a new document in AWS Opensearch.
     * @param {String} collectionName Name of the collection.
     * @param {String} id Unique id of the document.
     * @param {Object} body Body of the document.
     * @returns {Object} Response from AWS Opensearch.
     */
    static async create (collectionName, id, body) {
        if (process.env.NODE_ENV !== 'testing') {
            try {
                return await client.index({
                    index: collectionName,
                    id,
                    body
                });
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error creating index', collectionName, id, body);
                return error;
            }
        } else {
            return '';
        }
    }

    /**
     * Function to get a document from AWS Opensearch.
     * @param {String} collectionName Name of the collection.
     * @param {String} id Unique id of the document.
     * @returns {Object} Response from AWS Opensearch.
     */
    static async updateField (collectionName, searchId, fieldUpdates) {
        if (process.env.NODE_ENV !== 'testing') {

            try {
                return await client.update({
                    index: collectionName,
                    id: searchId,
                    body: {
                        doc: fieldUpdates,
                        doc_as_upsert: true
                    }
                });
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error updating index', collectionName, searchId, fieldUpdates);
                return error;
            }
        } else {
            return '';
        }
    }

    /**
     * Function to delete a document from AWS OpenSearch.
     * @param {String} collectionName Name of the collection.
     * @param {String} id Unique id of the document.
     * @returns {Object} Response from AWS OpenSearch.
     */
    static async delete (collectionName, id) {
        if (process.env.NODE_ENV !== 'testing') {

            try {
                return await client.delete({
                    index: collectionName,
                    id
                });
            } catch (error) {
                return error;
            }
        } else {
            return '';
        }
    }

    static async registerEventInChildEvents (collectionName, childId, eventId) {
        if (process.env.NODE_ENV !== 'testing') {

            try {
                return await client.update({
                    index: collectionName,
                    id: childId,
                    body: {
                        script: {
                            source: 'if (!ctx._source.containsKey("childEvents")) { ctx._source.childEvents = new ArrayList(); } if (!ctx._source.childEvents.contains(params.tag)) { ctx._source.childEvents.add(params.tag); }',
                            lang: 'painless',
                            params: {
                                tag: eventId
                            }
                        }
                    }
                });
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error updating index', collectionName, childId, eventId);
                return error;
            }
        } else {
            return '';
        }
    }

    static async registerMultipleEvents (collectionName, childId, eventIds) {
        if (process.env.NODE_ENV !== 'testing') {
            try {
                return await client.update({
                    index: collectionName,
                    id: childId,
                    body: {
                        script: {
                            source: `
                                if (!ctx._source.containsKey("childCalendarEvents")) { 
                                    ctx._source.childCalendarEvents = new ArrayList(); 
                                } 
                                for (def eventId : params.tags) {
                                    if (!ctx._source.childCalendarEvents.contains(eventId)) { 
                                        ctx._source.childCalendarEvents.add(eventId); 
                                    }
                                }
                            `,
                            lang: 'painless',
                            params: {
                                tags: eventIds
                            }
                        }
                    }
                });
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error registering multiple events', collectionName, childId, eventIds);
                return error;
            }
        } else {
            return '';
        }
    }

    static async registerEventInChildFeeds (collectionName, childId, eventId) {
        if (process.env.NODE_ENV !== 'testing') {

            try {
                return await client.update({
                    index: collectionName,
                    id: childId,
                    body: {
                        script: {
                            source: 'if (!ctx._source.containsKey("childFeeds")) { ctx._source.childFeeds = new ArrayList(); } if (!ctx._source.childFeeds.contains(params.tag)) { ctx._source.childFeeds.add(params.tag); }',
                            lang: 'painless',
                            params: {
                                tag: eventId
                            }
                        }
                    }
                });
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error updating index', collectionName, childId, eventId);
                return error;
            }
        } else {
            return '';
        }
    }

    /**
     * Function to get a document from AWS Opensearch.
     * @param {String} index Name of the collection.
     * @param {String} body body to search
     * @returns {Object} Response from AWS Opensearch.
     */
    static async getAllEvents (index, body) {
        if (process.env.NODE_ENV !== 'testing') {

            const { searchText, eventIds } = body;
            const boolQuery = {
                bool: {
                    must: []
                }
            };

            const { academicYearStart, academicYearEnd } = this.getAcademicYear(new Date());
            boolQuery.bool.must.push({
                range: {
                    'details.startDateTime': {
                        gte: academicYearStart.format(),
                        lte: academicYearEnd.format()
                    }
                }
            });

            if (searchText) {
                const lowerCaseSearchText = searchText.toLowerCase();
                boolQuery.bool.must.push({
                    bool: {
                        should: [
                            {
                                wildcard: {
                                    title: {
                                        value: `*${lowerCaseSearchText}*`
                                    }
                                }
                            },
                            {
                                wildcard: {
                                    'details.details': {
                                        value: `*${lowerCaseSearchText}*`
                                    }
                                }
                            },
                            {
                                match_phrase: {
                                    title: lowerCaseSearchText
                                }
                            },
                            {
                                match_phrase: {
                                    'details.details': lowerCaseSearchText
                                }
                            }
                        ]
                    }
                });
            }
            if (eventIds) {
                boolQuery.bool.must.push({
                    terms: {
                        id: eventIds
                    }
                });
            }

            try {
                const size = 10;
                let from = 0;
                let continueFetching = true;
                const allRecords = [];

                while (continueFetching) {
                    const response = await client.search({
                        index,
                        size,
                        from,
                        body: {
                            query: boolQuery
                        }
                    });

                    allRecords.push(...response.body.hits.hits);
                    if (response.body.hits.hits.length < size) {
                        continueFetching = false;
                    } else {
                        from += size;
                    }
                }
                return allRecords;
            } catch (error) {
                CONSOLE_LOGGER.error(`Error in getAllEvents ${error}`);
                return error;
            }
        } else {
            return [];
        }
    }

    /**
     * Get the start and end of the academic year for a given date.
     *
     * @param {Date} date - The date for which to get the academic year.
     * @returns {Object} An object containing the start and end of the academic year.
    */
    static getAcademicYear (date) {
        const eventDate = MOMENT(date);

        const academicYearStart = MOMENT(eventDate).subtract(3, 'months').startOf('month');
        const academicYearEnd = MOMENT(eventDate).add(12, 'months').endOf('month');

        return { academicYearStart, academicYearEnd };
    }

    static async getAllChildEvents (index, body) {
        if (process.env.NODE_ENV !== 'testing') {
            const { childrenIds } = body;
            try {
                const size = 10;
                let from = 0;
                let continueFetching = true;
                const allRecords = [];

                while (continueFetching) {
                    const response = await client.search({
                        index,
                        size,
                        from,
                        body: {
                            query: {
                                terms: {
                                    _id: childrenIds
                                }
                            }
                        }
                    });

                    allRecords.push(...response.body.hits.hits);
                    if (response.body.hits.hits.length < size) {
                        continueFetching = false;
                    } else {
                        from += size;
                    }
                }
                return allRecords;
            } catch (error) {
                CONSOLE_LOGGER.error(`Error in getAllChildEvents ${error}`);
                return error;
            }
        } else {
            return [];
        }
    }

    static async removeEventFromChildFeeds (index, childId, eventId) {
        if (process.env.NODE_ENV !== 'testing') {
            try {
                return await client.update({
                    index,
                    id: childId,
                    body: {
                        script: {
                            source: 'ctx._source.childFeeds.remove(ctx._source.childFeeds.indexOf(params.tag))',
                            lang: 'painless',
                            params: {
                                tag: eventId
                            }
                        }
                    }
                });
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error removing event from child feeds', index, childId, eventId);
                return error;
            }
        } else {
            return '';
        }
    }

    static async removeEventFromChildEvents (index, childId, eventId) {
        if (process.env.NODE_ENV !== 'testing') {
            try {
                return await client.update({
                    index,
                    id: childId,
                    body: {
                        script: {
                            source: 'ctx._source.childEvents.remove(ctx._source.childEvents.indexOf(params.tag))',
                            lang: 'painless',
                            params: {
                                tag: eventId
                            }
                        }
                    }
                });
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error removing event from child events', index, childId, eventId);
                return error;
            }
        } else {
            return '';
        }
    }

    static async removeEventFromChildPendingEvents (index, childId, eventId) {
        if (process.env.NODE_ENV !== 'testing') {
            try {
                return await client.update({
                    index,
                    id: childId,
                    body: {
                        script: {
                            source: 'ctx._source.childPendingEvents.remove(ctx._source.childPendingEvents.indexOf(params.tag))',
                            lang: 'painless',
                            params: {
                                tag: eventId
                            }
                        }
                    }
                });
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error removing event from child pending events', index, childId, eventId);
                return error;
            }
        } else {
            return '';
        }
    }

    static async registerInChildPendingEvents (index, childId, eventId) {
        if (process.env.NODE_ENV !== 'testing') {
            try {
                return await client.update({
                    index,
                    id: childId,
                    body: {
                        script: {
                            source: 'if (!ctx._source.containsKey("childPendingEvents")) { ctx._source.childPendingEvents = new ArrayList(); } if (!ctx._source.childPendingEvents.contains(params.tag)) { ctx._source.childPendingEvents.add(params.tag); }',
                            lang: 'painless',
                            params: {
                                tag: eventId
                            }
                        }
                    }
                });
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error registering in child pending events', index, childId, eventId);
                return error;
            }
        } else {
            return '';
        }
    }
}
module.exports = AwsOpenSearchService;
