const LogoutService = require('./logoutService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for logout user
 */
class LogoutController {
    /**
     * @desc This function is being used to logout user
     * <AUTHOR>
     * @since 19/10/2023
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async logout (req, res) {
        try {
            const data = await LogoutService.logout(res.locals.user);
            Utils.sendResponse(null, data, res, MESSAGES.LOGOUT_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in logout', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = LogoutController;
