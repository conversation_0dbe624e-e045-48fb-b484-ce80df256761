const UploadService = require('../util/uploadService');

class EnrichmentService {
    static async enrichMessageWithMediaUrls (message) {
        const messageData = { ...message };
        const mediaUrlPromises = [];
        if (messageData.mediaName) {
            const promise = UploadService.getSignedUrlForAttachments(messageData.mediaName)
                .then((url) => {
                    messageData.mediaUrl = url;
                    messageData.mediaName = undefined;
                });
            mediaUrlPromises.push(promise);
        }
        if (messageData.mediaThumbnailName) {
            const promise = UploadService.getSignedUrlForAttachments(messageData.mediaThumbnailName)
                .then((url) => {
                    messageData.mediaThumbnailUrl = url;
                    messageData.mediaThumbnailName = undefined;
                });
            mediaUrlPromises.push(promise);
        }
        if (messageData.replyMessage?.mediaName) {
            const promise = UploadService.getSignedUrlForAttachments(messageData.replyMessage.mediaName)
                .then((url) => {
                    messageData.replyMessage.mediaUrl = url;
                    messageData.replyMessage.mediaName = undefined;
                });
            mediaUrlPromises.push(promise);
        }
        await Promise.all(mediaUrlPromises);
        return messageData;
    }
}

module.exports = EnrichmentService;
