/**
 *  routes and schema for Event routes
 */

/**
 * @openapi
 * components:
 *  schemas:
 *      updateEvent:
 *          type: object
 *          required:
 *              - eventId
 *              - title
 *              - startDate
 *              - startTime
 *              - endDate
 *              - endTime
 *              - venue
 *              - isPaid
 *              - volunteerRequired
 *              - eventDescription
 *              - isRecurring
 *              - eventStatus
 *          properties:
 *              eventId:
 *                  type: string
 *                  description: uuid of the event
 *              eventType:
 *                  type: string
 *                  description: Type of the event [event, post, calendar]
 *              title:
 *                  type: string
 *                  description: Title of the event
 *              startDate:
 *                  type: string
 *                  description: Start Date of Event in MM/DD/YYYY format
 *              startTime:
 *                  type: string
 *                  description: Start Time of Event in HH:mm format
 *              endDate:
 *                  type: string
 *                  description: End Date of Event in MM/DD/YYYY format
 *              endTime:
 *                  type: string
 *                  description: End Time of Event in HH:mm format
 *              venue:
 *                  type: string
 *                  description: Venue of event
 *              quantityType:
 *                  type: string
 *                  description: quantityType of event can be People/Items
 *              quantityInstruction:
 *                  type: string
 *                  description: quantityInstruction of event
 *              isPaid:
 *                  type: string
 *                  description: Whether event is paid true/false
 *              fee:
 *                  type: string
 *                  description: Fee for registration of event
 *              volunteerRequired:
 *                  type: string
 *                  description: Whether event is volunteer required true/false
 *              volunteerSignupURL:
 *                  type: string
 *                  description: Google sheet url where volunteer can fill their availability
 *              eventStatus:
 *                  type: string
 *                  description: Status of event either published / draft
 *              participantsLimit:
 *                  type: string
 *                  description: Maximum number of participants
 *              eventDescription:
 *                  type: string
 *                  description: Description of event
 *              isRecurring:
 *                  type: string
 *                  description: Whether event is recurring true/false
 *              recurringFrequency:
 *                  type: string
 *                  description: Recurring frequency of event day/week/month
 *              documentURLs:
 *                  description: String of Array of document URls
 *                  type: string
 *              image:
 *                  type: string
 *                  format: binary
 *              documents:
 *                  type: array
 *                  items:
 *                      type: string
 *                      format: binary
 *      updatePaymentStatus:
 *          type: object
 *          required:
 *              - id
 *              - status
 *          properties:
 *              id:
 *                  type: string
 *                  description: uuid of the event signup
 *              status:
 *                  type: string
 *                  description: Status of payment
 *          example:
 *              id: uuid of the event signup
 *              status: approved
 *
 *      addEvent:
 *          type: object
 *          required:
 *              - title
 *              - startDate
 *              - startTime
 *              - endDate
 *              - endTime
 *              - venue
 *              - isPaid
 *              - volunteerRequired
 *              - organizationId
 *              - eventDescription
 *              - isRecurring
 *              - eventStatus
 *          properties:
 *              title:
 *                  type: string
 *                  description: Title of the event
 *              eventType:
 *                  type: string
 *                  description: Type of the event [event, post, calendar]
 *              startDate:
 *                  type: string
 *                  description: Start Date of Event in MM/DD/YYYY format
 *              startTime:
 *                  type: string
 *                  description: Start Time of Event in HH:mm format
 *              endDate:
 *                  type: string
 *                  description: End Date of Event in MM/DD/YYYY format
 *              endTime:
 *                  type: string
 *                  description: End Time of Event in HH:mm format
 *              quantityType:
 *                  type: string
 *                  description: quantityType of event can be People/Items
 *              quantityInstruction:
 *                  type: string
 *                  description: quantityInstruction of event
 *              venue:
 *                  type: string
 *                  description: Venue of event
 *              isPaid:
 *                  type: string
 *                  description: Whether event is paid true/false
 *              fee:
 *                  type: string
 *                  description: Fee for registration of event
 *              volunteerRequired:
 *                  type: string
 *                  description: Whether event is volunteer required true/false
 *              volunteerSignupURL:
 *                  type: string
 *                  description: Google sheet url where volunteer can fill their availability
 *              eventStatus:
 *                  type: string
 *                  description: Status of event either published / draft
 *              participantsLimit:
 *                  type: string
 *                  description: Maximum number of participants
 *              organizationId:
 *                  type: string
 *                  description: Organization Id with which event is associated
 *              eventDescription:
 *                  type: string
 *                  description: Description of event
 *              isRecurring:
 *                  type: string
 *                  description: Whether event is recurring true/false
 *              recurringFrequency:
 *                  type: string
 *                  description: Recurring frequency of event day/week/month
 *              image:
 *                  type: string
 *                  format: binary
 *              documents:
 *                  type: array
 *                  items:
 *                      type: string
 *                      format: binary
 *
 *
 *      successAddEvent:
 *          type: object
 *          properties:
 *              status:
 *                  type: number
 *                  description: status if data exists
 *              data:
 *                  type: string
 *                  description: school list data
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Event added successfully
 *      successUpdateEvent:
 *          type: object
 *          properties:
 *              status:
 *                  type: number
 *                  description: status if data exists
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Event updated successfully
 *
 *      successEventList:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: array
 *                  description: array of Event Object
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Success
 *              data:
 *                  - id: uuid
 *                    title: Test Event
 *                    eventType: event
 *                    eventScope: public
 *                    status: draft
 *                    fee: fee
 *                    venue: Venue
 *                    startDate: startDate
 *                    startTime: startTime
 *                    endDate: endDate
 *                    endTime: endTime
 *                    isRecurring: true
 *                    participantsCount: 1
 *                    quantityType: People
 *                    quantityInstruction: Quantity Instruction
 *                    recurringFrequency: week
 *                    details: Event Details
 *
 *      successEventDetails:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: array
 *                  description: array of Event Object
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Success
 *              data:
 *                    id: uuid
 *                    title: Test Event
 *                    eventType: event
 *                    eventScope: public
 *                    status: draft
 *                    fee: fee
 *                    venue: Venue
 *                    participantsLimit: 10
 *                    participantsCount: 1
 *                    quantityType: People
 *                    quantityInstruction: Quantity Instruction
 *                    startDate: startDate
 *                    startTime: startTime
 *                    endDate: endDate
 *                    endTime: endTime
 *                    isRecurring: true
 *                    recurringFrequency: week
 *                    details: Event Details
 *                    canBeDeleted: true
 *
 *      successParticipantEventList:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: array
 *                  description: id, title of event and array of Event signup objects
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Success
 *              data:
 *                    title: Event title
 *                    fee: fee
 *                    signups:
 *                       - id: uuid
 *                         childName: Child name
 *                         paymentStatus: approved
 *                         paymentType: cash
 *
 *
 *      successEventPublish:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Event status changed to published.
 *
 *
 *      successPaymentStatusParticipant:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Event payment status updated successfully.
 *
 *      successDeleteEvent:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Event deleted successfully.
 *
 *      addMultiEvents:
 *          type: array
 *          items:
 *              type: object
 *              required:
 *                  - title
 *                  - startDate
 *                  - startTime
 *                  - endDate
 *                  - endTime
 *                  - venue
 *                  - isPaid
 *                  - volunteerRequired
 *                  - organizationId
 *                  - eventDescription
 *                  - isRecurring
 *                  - eventStatus
 *              properties:
 *                  title:
 *                      type: string
 *                      description: Title of the event
 *                  eventType:
 *                      type: string
 *                      description: Type of the event [event, post, calendar]
 *                  startDate:
 *                      type: string
 *                      description: Start Date of Event in MM/DD/YYYY format
 *                  startTime:
 *                      type: string
 *                      description: Start Time of Event in HH:mm format
 *                  endDate:
 *                      type: string
 *                      description: End Date of Event in MM/DD/YYYY format
 *                  endTime:
 *                      type: string
 *                      description: End Time of Event in HH:mm format
 *                  venue:
 *                      type: string
 *                      description: Venue of event
 *                  isPaid:
 *                      type: string
 *                      description: Whether event is paid true/false
 *                  fee:
 *                      type: string
 *                      description: Fee for registration of event
 *                  volunteerRequired:
 *                      type: string
 *                      description: Whether event is volunteer required true/false
 *                  volunteerSignupURL:
 *                      type: string
 *                      description: Google sheet url where volunteer can fill their availability
 *                  eventStatus:
 *                      type: string
 *                      description: Status of event either published / draft
 *                  participantsLimit:
 *                      type: string
 *                      description: Maximum number of participants
 *                  organizationId:
 *                      type: string
 *                      description: Organization Id with which event is associated
 *                  eventDescription:
 *                      type: string
 *                      description: Description of event
 *                  isRecurring:
 *                      type: string
 *                      description: Whether event is recurring true/false
 *                  recurringFrequency:
 *                      type: string
 *                      description: Recurring frequency of event day/week/month
 *
 *
 */

/**
 * @openapi
 * /event:
 *  post:
 *      security:
 *        - bearerAuth: []
 *      tags: [Event]
 *      summary: add event
 *      requestBody:
 *          content:
 *              multipart/form-data:
 *                  schema:
 *                      $ref: '#/components/schemas/addEvent'
 *
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successAddEvent'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /event/multiple:
 *  post:
 *      security:
 *        - bearerAuth: []
 *      tags: [Event]
 *      summary: add event
 *      requestBody:
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/addMultiEvents'
 *
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successAddEvent'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /event:
 *  put:
 *      security:
 *        - bearerAuth: []
 *      tags: [Event]
 *      summary: update event
 *      requestBody:
 *          content:
 *              multipart/form-data:
 *                  schema:
 *                      $ref: '#/components/schemas/updateEvent'
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successUpdateEvent'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /event/list:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Event]
 *      summary: get events list
 *      parameters:
 *          - in: query
 *            name: status
 *            schema:
 *                type: string
 *            description: status of event on which you want to filter
 *          - in: query
 *            name: organizationId
 *            schema:
 *                type: string
 *            description: organizationId of organization for which you want event list
 *
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successEventList'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /event:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Event]
 *      summary: get event details
 *      parameters:
 *          - in: query
 *            name: eventId
 *            schema:
 *                type: string
 *            description: uuid of the event
 *
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successEventDetails'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/inValidFiledError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /event/publish:
 *  patch:
 *      security:
 *        - bearerAuth: []
 *      tags: [Event]
 *      summary: change event status to publish
 *      parameters:
 *          - in: query
 *            name: eventId
 *            schema:
 *                type: string
 *            description: uuid of the event
 *
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successEventPublish'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/inValidFiledError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /event:
 *  delete:
 *      security:
 *        - bearerAuth: []
 *      tags: [Event]
 *      summary: delete event
 *      parameters:
 *          - in: query
 *            name: eventId
 *            schema:
 *                type: string
 *            description: uuid of the event
 *
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successDeleteEvent'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/inValidFiledError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /event/participant:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Event]
 *      summary: get list of participants in that event
 *      parameters:
 *          - in: query
 *            name: eventId
 *            schema:
 *                type: string
 *            description: uuid of the event
 *
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successParticipantEventList'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/inValidFiledError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /event/signup-status:
 *  put:
 *      security:
 *        - bearerAuth: []
 *      tags: [Event]
 *      summary: change the payment status of participants in that event
 *      requestBody:
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/updatePaymentStatus'
 *
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successPaymentStatusParticipant'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/inValidFiledError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */
/**
 * @openapi
 * /event/search:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Event]
 *      summary: Search events
 *      parameters:
 *        - in: query
 *          name: query
 *          required: true
 *          schema:
 *            type: string
 *          description: The search query
 *        - in: query
 *          name: childId
 *          schema:
 *            type: string
 *          description: The child ID
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          type: object
 *                          properties:
 *                              status:
 *                                  type: integer
 *                                  example: 1
 *                              data:
 *                                  type: array
 *                                  items:
 *                                      type: object
 *                                      properties:
 *                                          id:
 *                                              type: string
 *                                          startDateTime:
 *                                              type: string
 *                                              format: date-time
 *                                          endDateTime:
 *                                              type: string
 *                                              format: date-time
 *                                          organizationName:
 *                                              type: string
 *                                          title:
 *                                              type: string
 *                                          eventType:
 *                                              type: string
 *                                          venue:
 *                                              type: string
 *                                          details:
 *                                              type: string
 *                                          isSignedUp:
 *                                              type: boolean
 *                                          paymentStatus:
 *                                              type: string
 *                                          associatedChild:
 *                                              type: object
 *                                              properties:
 *                                                  id:
 *                                                      type: string
 *                                                  firstName:
 *                                                      type: string
 *                                                  lastName:
 *                                                      type: string
 *                                                  associatedColor:
 *                                                      type: string
 *                                                  photoURL:
 *                                                      type: string
 *                                                      nullable: true
 *                              message:
 *                                  type: string
 *                                  example: "Success"
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */
