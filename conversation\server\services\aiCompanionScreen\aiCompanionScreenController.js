/**
 * This file contains controller for AI Companion Screen.
 * <AUTHOR>
 * @since 12/06/2025
 * @name aiCompanionScreenController
 */

const AiCompanionScreenService = require('./aiCompanionScreenService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for AI Companion Screen.
 */
class AiCompanionScreenController {
    /**
     * @desc This function is being used to add/update feedback for AI Companion Screen
     * <AUTHOR>
     * @since 12/06/2025
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async addUpdateAiCompanionScreenFeedback (req, res) {
        try {
            const data = await AiCompanionScreenService.addUpdateAiCompanionScreenFeedback(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in add/update feedback for AI Companion Screen', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to get feedback list for AI Companion Screen
     * <AUTHOR>
     * @since 12/06/2025
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async getAiCompanionScreenFeedbackList (req, res) {
        try {
            const data = await AiCompanionScreenService.getAiCompanionScreenFeedbackList(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get feedback list for AI Companion Screen', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = AiCompanionScreenController;
