const organizationSchema = {
    'index': 'organizations',
    'body': {
        'mappings': {
            'properties': {
                'id': {
                    'type': 'keyword',
                    'index': true
                },
                'name': {
                    'type': 'text'
                },
                'zipCode': {
                    'type': 'keyword'
                },
                'applicableAgeRange': {
                    'type': 'text'
                },
                'organizationType': {
                    'type': 'text'
                },
                'parentOrganization': {
                    'type': 'keyword'
                },
                'category': {
                    'type': 'keyword'
                },
                'address': {
                    'type': 'text'
                },
                'country': {
                    'type': 'keyword'
                },
                'state': {
                    'type': 'keyword'
                },
                'city': {
                    'type': 'keyword'
                },
                'logo': {
                    'type': 'text'
                },
                'isEnabled': {
                    'type': 'integer'
                },
                'isDeleted': {
                    'type': 'integer'
                }
            }
        }
    }
};

module.exports = organizationSchema;
