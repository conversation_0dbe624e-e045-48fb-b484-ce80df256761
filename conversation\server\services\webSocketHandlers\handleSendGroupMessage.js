// handleConnect.js
const Message = require('../../models/message.model');
const GroupMembers = require('../../models/groupMembers.model');
const SendMessageService = require('../sendSocketMessageService');
const SQSPushNotificationService = require('../sqsPushNotificationService');
const EnrichmentService = require('../../services/enrichmentService');
const { handleMuteGroupConversation } = require('./handlers/muteConversationHandler');
const { handleAddOrUpdateReaction, handleRemoveReaction } = require('./handlers/messageReactionsHandler');
const CONSTANTS = require('../../util/constants');

module.exports = async (event) => {
    const eventBody = event.body;
    switch (eventBody.actionType) {
        case CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.NEW_GROUP_MESSAGE:
            return await sendMessage(eventBody);
        case CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.EDIT_GROUP_MESSAGE:
            return await editGroupMessage(eventBody);
        case CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.DELETE_GROUP_MESSAGE:
            return await deleteGroupMessage(eventBody);
        case CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.ADD_REACTION:
            return await handleAddOrUpdateReaction({ eventBody, isGroupMessage: true, isAddReaction: true });
        case CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.UPDATE_REACTION:
            return await handleAddOrUpdateReaction({ eventBody, isGroupMessage: true });
        case CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.REMOVE_REACTION:
            return await handleRemoveReaction({ eventBody, isGroupMessage: true });
        case CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.READ_GROUP_MESSAGE:
            return await readGroupMessage(eventBody);
        case CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.MUTE_GROUP_CONVERSATION:
            return await handleMuteGroupConversation(eventBody);
        default:
            return {
                statusCode: 400,
                body: 'Invalid event'
            };
    }
};

const getUserGroupDetails = async (userId, groupId) => {
    return await GroupMembers.query('userId')
        .eq(userId)
        .using('userId-index')
        .where('groupId')
        .eq(groupId)
        .using('groupId-index')
        .exec();
};

const getActiveGroupMemberIds = async (groupId) => {
    return (
        await GroupMembers.query('groupId')
            .eq(groupId)
            .using('groupId-index')
            .attributes(['userId', 'status', 'muteConversation'])
            .where('status')
            .in([CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE, CONSTANTS.GROUP_MEMBER_STATUS.DISABLED])
            .exec()
    );
};

const getGroupMemberUserIds = (groupMembers) => {
    return groupMembers.map((member) => member.userId);
};

const sendMessage = async (eventBody) => {
    try {
        const {
            messageId, senderId, groupId, message, actionType,
            mediaName, mediaType, replyMessage, mediaDisplayName, mediaThumbnailName
        } = eventBody;

        const userGroupDetails = await getUserGroupDetails(senderId, groupId);
        if (!userGroupDetails || userGroupDetails.length === 0) {
            CONSOLE_LOGGER.error('--> Group member not found!');
            return {
                statusCode: 404,
                message: 'Group member not found!',
                actionType: 'NEW_GROUP_MESSAGE',
                action: 'sendMessage',
                data: { messageId, senderId, groupId, message }
            };
        }
        if (replyMessage?.messageId) {
            const replyToMessage = await Message.get(
                replyMessage.messageId,
                { attributes: ['message', 'mediaName', 'mediaType', 'mediaDisplayName', 'mediaThumbnailName'] }
            );
            if (replyToMessage) {
                replyMessage.message = replyToMessage.message;
                replyMessage.mediaName = replyToMessage.mediaThumbnailName || replyToMessage.mediaName;
                replyMessage.mediaType = replyToMessage.mediaType;
                replyMessage.mediaDisplayName = replyToMessage.mediaDisplayName;
            }
        }
        const savedMessage = await Message
            .create({
                id: messageId, senderId, groupId, message, mediaName,
                mediaType, replyMessage, mediaDisplayName, mediaThumbnailName
            });

        userGroupDetails[0].lastReadMessage = { messageId, createdAt: savedMessage.createdAt };
        await userGroupDetails[0].save();
        const messageData = await EnrichmentService.enrichMessageWithMediaUrls(savedMessage);
        const activeGroupMemberIds = await getActiveGroupMemberIds(groupId);
        const groupMemberUserIds = getGroupMemberUserIds(activeGroupMemberIds);

        await SendMessageService.sendMessagesToUsers(groupMemberUserIds, { data: messageData, actionType });
        await handleSendNotificationToGroupMembers({ senderId, groupId, message, activeGroupMemberIds });
        return {
            statusCode: 200,
            message: 'Message Sent Successfully!',
            actionType: 'NEW_GROUP_MESSAGE',
            action: 'sendMessage',
            data: messageData
        };
    } catch (error) {
        CONSOLE_LOGGER.error('--> Error while sending group message --> ', error);
        return {
            statusCode: 500,
            message: 'Failed to send message!',
            actionType: 'NEW_GROUP_MESSAGE',
            action: 'sendMessage',
            data: { messageId: eventBody.messageId }
        };
    }
};

const handleSendNotificationToGroupMembers = async ({ senderId, groupId, message, activeGroupMemberIds }) => {
    const groupMembersWithUnmutedConversations = activeGroupMemberIds
        .filter((member) => !member.muteConversation)
        .map((member) => member.userId);
    await SQSPushNotificationService.sendGroupMessage(senderId, groupId, message, groupMembersWithUnmutedConversations);
};

const editGroupMessage = async (eventBody) => {
    const { groupId, messageId, message } = eventBody;
    try {
        const updatedMessage = await Message.update({ id: messageId }, { message, isEdited: true });
        const messageData = { ...updatedMessage };
        const activeGroupMemberIds = await getActiveGroupMemberIds(groupId);
        const groupMemberUserIds = getGroupMemberUserIds(activeGroupMemberIds);

        await SendMessageService.sendMessagesToUsers(groupMemberUserIds, { data: messageData, actionType: 'EDIT_GROUP_MESSAGE' });
        return {
            statusCode: 200,
            message: 'Message edited successfully!',
            actionType: 'EDIT_GROUP_MESSAGE',
            action: 'sendMessage',
            data: messageData
        };
    } catch (error) {
        CONSOLE_LOGGER.error('--> Error while editing group message --> ', error);
        return {
            statusCode: 500,
            message: 'Failed to edit group message!',
            actionType: 'EDIT_GROUP_MESSAGE',
            action: 'sendMessage',
            data: { messageId }
        };
    }
};

const deleteGroupMessage = async (eventBody) => {
    const { groupId, messageId } = eventBody;
    try {
        const deletedMessage = await Message.update({ id: messageId }, { isDeleted: true });
        const activeGroupMemberIds = await getActiveGroupMemberIds(groupId);
        const groupMemberUserIds = getGroupMemberUserIds(activeGroupMemberIds);

        const dataToSend = { groupId, messageId, isFlagged: deletedMessage.isFlagged, actionType: 'DELETE_GROUP_MESSAGE' };
        const data = { data: dataToSend, actionType: 'DELETE_GROUP_MESSAGE' };
        await SendMessageService.sendMessagesToUsers(groupMemberUserIds, data);
        return {
            statusCode: 200,
            message: 'Message deleted successfully!',
            actionType: 'DELETE_GROUP_MESSAGE',
            action: 'sendMessage',
            data: dataToSend
        };
    } catch (error) {
        CONSOLE_LOGGER.error('Error while deleting group message --> ', error);
        return {
            statusCode: 500,
            message: 'Failed to delete group message!',
            actionType: 'DELETE_GROUP_MESSAGE',
            action: 'sendMessage',
            data: { messageId }
        };
    }
};

const readGroupMessage = async (eventBody) => {
    const { groupId, userId, messageId, createdAt } = eventBody;
    try {
        const groupMember = await getUserGroupDetails(userId, groupId);
        if (!groupMember || groupMember.length === 0) {
            return {
                statusCode: 404,
                message: 'Group member not found!',
                actionType: 'READ_GROUP_MESSAGE',
                action: 'sendMessage',
                data: { messageId }
            };
        }
        groupMember[0].lastReadMessage = { messageId, createdAt };
        await groupMember[0].save();
        return {
            statusCode: 200,
            message: 'Message read successfully!',
            actionType: 'READ_GROUP_MESSAGE',
            action: 'sendMessage',
            data: { messageId }
        };
    } catch (error) {
        CONSOLE_LOGGER.error('--> Error while reading group message --> ', error);
        return {
            statusCode: 500,
            message: 'Failed to read group message!',
            actionType: 'READ_GROUP_MESSAGE',
            action: 'sendMessage',
            data: { messageId }
        };
    }
};
