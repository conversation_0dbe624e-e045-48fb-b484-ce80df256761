const User = require('../../models/user.model');

class UserRepository {
    /**
     * @desc This function returns array of users with ROOT access level
     * <AUTHOR>
     * @since 27/08/2024
     */
    static async getRootUsers () {
        return await User.query('accessLevel').eq(CONSTANTS.ACCESS_LEVEL.ROOT).using('accessLevel-index').exec();
    }

    /**
     * @desc This function returns array of users with given email
     * <AUTHOR>
     * @since 27/08/2024
     * @param {string} email email
     */
    static async userExists (email) {
        return await User.query('email').eq(email).exec();
    }
}

module.exports = UserRepository;
