const Utils = require('../util/utilFunctions');
const HTTPStatus = require('../util/http-status');

module.exports = function (req, res, next) {
    const accessList = {
        'org_app': [
            { method: 'POST', path: '/post' },
            { method: 'GET', path: '/post' },
            { method: 'PUT', path: '/post' },
            { method: 'DELETE', path: '/post' },
            { method: 'GET', path: '/post/list' },
            { method: 'PATCH', path: '/post/publish' }
        ],
        'root': [
            { method: 'POST', path: '/post' },
            { method: 'GET', path: '/post' },
            { method: 'PUT', path: '/post' },
            { method: 'DELETE', path: '/post' },
            { method: 'GET', path: '/post/list' },
            { method: 'PATCH', path: '/post/publish' }
        ]
    };
    const accessLevel = res.locals.user.accessLevel;
    const isAllowed = _.find(accessList[accessLevel], { method: req.method, path: req.originalUrl.split('?')[0] });
    if (isAllowed) {
        next();
    } else {
        const responseObject = Utils.errorResponse();
        responseObject.message = res.__('ACCESS_DENIED');
        res.status(HTTPStatus.NOT_ACCEPTABLE).send(responseObject);
        return;
    }
};
