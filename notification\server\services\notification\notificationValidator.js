const validation = require('../../util/validation');

/**
 * Class represents validations for notification.
 */
class NotificationValidator extends validation {
    constructor (body, locale, query) {
        super(locale);
        this.body = body;
        this.query = query;
    }

    /**
     * @desc This function is being used to validate request for add notification
     * <AUTHOR>
     * @since 31/01/2024
     */
    validateCreateNotification () {
        const { title, description, associatedChildId } = this.body;
        super.field(title, 'Title');
        super.field(description, 'Description');
        super.field(associatedChildId, 'Associated Child Id');
    }

    /**
     * @desc This function is being used to validate request for update notification read status
     * <AUTHOR>
     * @since 31/01/2024
     */
    validateUpdateNotification () {
        const { notificationId } = this.query;
        super.field(notificationId, 'Notification Id');
    }

    /**
     * @desc This function is being used to validate request for send notification to user or group of users
     * <AUTHOR>
     * @since 21/02/2024
     */
    validateSendNotification () {
        const { description, children, allParticipants, allOrgChildren, eventId, organizationId } = this.body;
        super.field(organizationId, 'Organization Id');
        super.field(description, 'Notification description');
        if (!allOrgChildren) {
            if (!allParticipants) {
                super.arrayField(children, 'Children array');
            } else {
                super.field(eventId, 'Event Id');
            }
        }
    }
}


module.exports = NotificationValidator;
