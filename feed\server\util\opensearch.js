/* eslint-disable max-len */
const { Client } = require('@opensearch-project/opensearch');
const UploadService = require('./uploadService');

let client;

if (process.env.NODE_ENV !== 'testing') {
    client = new Client({
        node: process.env.OPENSEARCH_ENDPOINT,
        auth: {
            username: process.env.OPENSEARCH_USERNAME,
            password: process.env.OPENSEARCH_PASSWORD
        },
        ssl: { rejectUnauthorized: false }
    });
}

/**
 * Class represents Utilities function for AWS Opensearch.
 */
class AwsOpenSearchService {
    static async getChild (index, childId) {
        if (process.env.NODE_ENV !== 'testing') {
            try {
                return await client.get({
                    index,
                    id: childId
                });
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error getting child', index, childId);
                // return error;
                return '';
            }
        } else {
            return '';
        }
    }

    /**
     * Function to get a document from AWS Opensearch.
     * @param {String} index Name of the collection.
     * @param {String} body body to search
     * @returns {Object} Response from AWS Opensearch.
     */
    static async getAllEvents (index, eventIds, child, searchText, isSignedUp, paymentStatus) {
        if (process.env.NODE_ENV !== 'testing') {
            const boolQuery = {
                bool: {
                    must: []
                }
            };

            if (searchText) {
                const lowerCaseSearchText = searchText.toLowerCase();
                boolQuery.bool.must.push({
                    bool: {
                        should: [
                            {
                                wildcard: {
                                    title: {
                                        value: `*${lowerCaseSearchText}*`
                                    }
                                }
                            },
                            {
                                wildcard: {
                                    'details.details': {
                                        value: `*${lowerCaseSearchText}*`
                                    }
                                }
                            },
                            {
                                match_phrase: {
                                    title: lowerCaseSearchText
                                }
                            },
                            {
                                match_phrase: {
                                    'details.details': lowerCaseSearchText
                                }
                            }
                        ]
                    }
                });
            }

            if (eventIds) {
                boolQuery.bool.must.push({
                    terms: {
                        id: eventIds
                    }
                });
            }

            try {
                const response = await client.search({
                    index,

                    body: {
                        query: boolQuery
                    }
                });

                const hits = response.body.hits.hits;
                return await Promise.all(hits.map(async hit => {
                    const eventData = hit._source;
                    eventData.photoURL = await UploadService.getSignedUrl(eventData.photoURL);
                    const childData = {
                        firstName: child.firstName,
                        lastName: child.lastName,
                        id: child.id,
                        photoURL: child?.photoURL || null,
                        associatedColor: child.associatedColor
                    };
                    eventData.associatedChild = childData;
                    if (child?.photoURL) {
                        eventData.associatedChild.photoURL = await UploadService.getSignedUrl(child.photoURL);
                    }
                    const organizationDetails = await client.get({
                        index: 'organizations',
                        id: eventData.organizationId
                    });
                    eventData.organizationDetails = {};
                    eventData.organizationDetails.id = organizationDetails.body._id;
                    eventData.organizationDetails.name = organizationDetails.body._source.name;
                    eventData.isSignedUp = isSignedUp;
                    eventData.paymentStatus = paymentStatus;
                    return eventData;
                })).then(res => res);
            } catch (error) {
                CONSOLE_LOGGER.error(`Error in getAllEvents ${error}`);
                return error;
            }
        } else {
            return [];
        }
    }

    /**
     * Function to get a document from AWS Opensearch.
     * @param {String} index Name of the collection.
     * @param {String} body body to search
     * @returns {Object} Response from AWS Opensearch.
     */
    static async getAllFundraisers (index, fundraiserIds, child, searchText, isSignedUp, paymentStatus) {
        if (process.env.NODE_ENV !== 'testing') {
            const boolQuery = {
                bool: {
                    must: []
                }
            };

            if (searchText) {
                const lowerCaseSearchText = searchText.toLowerCase();
                boolQuery.bool.must.push({
                    bool: {
                        should: [
                            {
                                wildcard: {
                                    title: {
                                        value: `*${lowerCaseSearchText}*`
                                    }
                                }
                            },
                            {
                                wildcard: {
                                    'description': {
                                        value: `*${lowerCaseSearchText}*`
                                    }
                                }
                            },
                            {
                                match_phrase: {
                                    title: lowerCaseSearchText
                                }
                            },
                            {
                                match_phrase: {
                                    'description': lowerCaseSearchText
                                }
                            }
                        ]
                    }
                });
            }

            if (fundraiserIds) {
                boolQuery.bool.must.push({
                    terms: {
                        id: fundraiserIds
                    }
                });
            }

            try {
                const response = await client.search({
                    index,
                    body: {
                        query: boolQuery
                    }
                });

                const hits = response.body.hits.hits;
                return await Promise.all(hits.map(async hit => {
                    const fundraiserData = hit._source;
                    if (fundraiserData.imageURL) {
                        fundraiserData.photoURL = await UploadService.getSignedUrl(fundraiserData.imageURL);
                    }
                    const childData = {
                        firstName: child.firstName,
                        lastName: child.lastName,
                        id: child.id,
                        photoURL: child?.photoURL || null,
                        associatedColor: child.associatedColor
                    };
                    fundraiserData.associatedChild = childData;
                    if (child?.photoURL) {
                        fundraiserData.associatedChild.photoURL = await UploadService.getSignedUrl(child.photoURL);
                    }
                    const organizationDetails = await client.get({
                        index: 'organizations',
                        id: fundraiserData.organizationId
                    });
                    fundraiserData.organizationDetails = {};
                    fundraiserData.organizationDetails.id = organizationDetails.body._id;
                    fundraiserData.organizationDetails.name = organizationDetails.body._source.name;
                    fundraiserData.isSignedUp = isSignedUp;
                    fundraiserData.paymentStatus = paymentStatus;
                    fundraiserData.isFundraiser = true;
                    return fundraiserData;
                })).then(res => res);
            } catch (error) {
                CONSOLE_LOGGER.error(`Error in getAllFundraisers ${error}`);
                return error;
            }
        } else {
            return [];
        }
    }


    /**
     * Function to get a document from AWS Opensearch.
     * @param {String} index Name of the collection.
     * @param {String} body body to search
     * @returns {Object} Response from AWS Opensearch.
     */
    static async getAllPosts (index, postIds, child, searchText) {
        if (process.env.NODE_ENV !== 'testing') {
            const boolQuery = {
                bool: {
                    must: []
                }
            };

            if (searchText) {
                const lowerCaseSearchText = searchText.toLowerCase();
                boolQuery.bool.must.push({
                    bool: {
                        should: [
                            {
                                wildcard: {
                                    title: {
                                        value: `*${lowerCaseSearchText}*`
                                    }
                                }
                            },
                            {
                                wildcard: {
                                    subTitle: {
                                        value: `*${lowerCaseSearchText}*`
                                    }
                                }
                            },
                            {
                                match_phrase: {
                                    title: lowerCaseSearchText
                                }
                            },
                            {
                                match_phrase: {
                                    subTitle: lowerCaseSearchText
                                }
                            }
                        ]
                    }
                });
            }

            if (postIds) {
                boolQuery.bool.must.push({
                    terms: {
                        id: postIds
                    }
                });
            }

            try {
                const response = await client.search({
                    index,

                    body: {
                        query: boolQuery
                    }
                });

                const hits = response.body.hits.hits;
                return await Promise.all(hits.map(async hit => {
                    const eventData = hit._source;
                    eventData.photoURL = eventData.photoURL ? await UploadService.getSignedUrl(eventData?.photoURL) : null;
                    const childData = {
                        firstName: child.firstName,
                        lastName: child.lastName,
                        id: child.id,
                        photoURL: child?.photoURL || null,
                        associatedColor: child.associatedColor
                    };
                    eventData.associatedChild = childData;
                    if (child?.photoURL) {
                        eventData.associatedChild.photoURL = await UploadService.getSignedUrl(child.photoURL);
                    }
                    if (eventData.organizationId) {
                        const organizationDetails = await client.get({
                            index: 'organizations',
                            id: eventData.organizationId
                        });
                        eventData.organizationDetails = {};
                        eventData.organizationDetails.id = organizationDetails.body._id;
                        eventData.organizationDetails.name = organizationDetails.body._source.name;
                        eventData.organizationLogo = organizationDetails.body._source.logo ? await UploadService.getSignedUrl(organizationDetails.body._source.logo) : null;
                    }
                    eventData.isPost = true;
                    return eventData;
                })).then(res => res.filter(eventData => eventData.status === CONSTANTS.STATUS.PUBLISHED));
            } catch (error) {
                CONSOLE_LOGGER.error(`Error in getAllPosts ${error}`);
                return error;
            }
        } else {
            return [];
        }
    }

    /**
     * Function to get a document from AWS Opensearch.
     * @param {String} index Name of the collection.
     * @param {String} body body to search
     * @returns {Object} Response from AWS Opensearch.
     */
    static async getAllEventsWithChild (childArray, searchText) {
        return Promise.all(childArray.map(async childId => {
            const requiredChild = await this.getChild(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, childId);
            if (requiredChild) {
                const child = requiredChild.body._source;
                const childFeeds = child.childFeeds || [];
                const childEvents = child.childEvents || [];
                const childPendingEvents = child.childPendingEvents || [];
                const childFundraiserFeeds = child.childFundraiserFeeds || [];
                const childFundraiserSignups = child.childFundraiserSignups || [];
                const childPendingFundraiserSignups = child.childPendingFundraiserSignups || [];
                const childPosts = child.childPosts || [];
                const childEventsArray = [];
                if (childFeeds) {
                    let allFeeds = await this.getAllEvents(CONSTANTS.OPEN_SEARCH.COLLECTION.EVENT, childFeeds, child, searchText, false);
                    allFeeds = allFeeds.filter(eventData => eventData.status === CONSTANTS.STATUS.PUBLISHED);
                    if (allFeeds.length > 0) {
                        childEventsArray.push(...allFeeds);
                    }
                }
                if (childEvents) {
                    const allEvents = await this.getAllEvents(CONSTANTS.OPEN_SEARCH.COLLECTION.EVENT, childEvents, child, searchText, true, CONSTANTS.PAYMENT_STATUS.APPROVED);
                    if (allEvents.length > 0) {
                        childEventsArray.push(...allEvents);
                    }
                }
                if (childPendingEvents) {
                    const allPendingEvents = await this.getAllEvents(CONSTANTS.OPEN_SEARCH.COLLECTION.EVENT, childPendingEvents, child, searchText, true, CONSTANTS.PAYMENT_STATUS.PENDING);
                    if (allPendingEvents.length > 0) {
                        childEventsArray.push(...allPendingEvents);
                    }
                }
                if (childFundraiserFeeds) {
                    let allFundraiserFeeds = await this.getAllFundraisers(CONSTANTS.OPEN_SEARCH.COLLECTION.FUNDRAISER, childFundraiserFeeds, child, searchText, false);
                    allFundraiserFeeds = allFundraiserFeeds.filter(eventData => eventData.status === CONSTANTS.STATUS.PUBLISHED);
                    if (allFundraiserFeeds.length > 0) {
                        childEventsArray.push(...allFundraiserFeeds);
                    }
                }
                if (childFundraiserSignups) {
                    const childFundraiserSignupsArray = childFundraiserSignups.map(fundraiserSignup => fundraiserSignup.fundraiserId);
                    const allFundraiserSignups = await this.getAllFundraisers(CONSTANTS.OPEN_SEARCH.COLLECTION.FUNDRAISER, childFundraiserSignupsArray, child, searchText, true, CONSTANTS.PAYMENT_STATUS.APPROVED);
                    if (allFundraiserSignups.length > 0) {
                        for (const childFundraiserSignup of childFundraiserSignups) {
                            const fundraiserSignup = allFundraiserSignups.find(fundraiser => fundraiser.id === childFundraiserSignup.fundraiserId);
                            if (fundraiserSignup) {
                                const fundraiserSignupClone = { ...fundraiserSignup };
                                fundraiserSignupClone.purchasedProducts = childFundraiserSignup.purchasedProducts;
                                fundraiserSignupClone.fundraiserSignupId = childFundraiserSignup.fundraiserSignupId;
                                childEventsArray.push(fundraiserSignupClone);
                            }
                        }
                    }
                }
                if (childPendingFundraiserSignups) {
                    const childPendingFundraiserSignupsArray = childPendingFundraiserSignups.map(fundraiserSignup => fundraiserSignup.fundraiserId);
                    const allPendingFundraiserSignups = await this.getAllFundraisers(CONSTANTS.OPEN_SEARCH.COLLECTION.FUNDRAISER, childPendingFundraiserSignupsArray, child, searchText, true, CONSTANTS.PAYMENT_STATUS.PENDING);
                    if (allPendingFundraiserSignups.length > 0) {
                        for (const childFundraiserSignup of childPendingFundraiserSignups) {
                            const pendingFundraiserSignup = allPendingFundraiserSignups.find(fundraiser => fundraiser.id === childFundraiserSignup.fundraiserId);
                            if (pendingFundraiserSignup) {
                                const fundraiserSignupClone = { ...pendingFundraiserSignup };
                                fundraiserSignupClone.purchasedProducts = childFundraiserSignup.purchasedProducts;
                                fundraiserSignupClone.fundraiserSignupId = childFundraiserSignup.fundraiserSignupId;
                                childEventsArray.push(fundraiserSignupClone);
                            }
                        }
                    }
                }
                if (childPosts) {
                    const allPosts = await this.getAllPosts(CONSTANTS.OPEN_SEARCH.COLLECTION.POST, childPosts, child, searchText, false);
                    if (allPosts.length > 0) {
                        childEventsArray.push(...allPosts);
                    }
                }
                return childEventsArray;
            } else {
                return [];
            }
        })).then(eventList => {
            return eventList.flat().filter(Boolean);
        });
    }
}
module.exports = AwsOpenSearchService;
