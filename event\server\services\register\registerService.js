/* eslint-disable max-len */
const Stripe = require('../../util/Stripe');
const Organization = require('../../models/organization.model');
const EventSignup = require('../../models/eventSignup.model');
const Event = require('../../models/event.model');
const User = require('../../models/user.model');
const lodash = require('lodash');
const EmailService = require('../../util/sendEmail');
const Child = require('../../models/child.model');
const PaymentValidator = require('./registerValidator');
const AwsOpenSearchService = require('../../util/opensearch');

const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

/**
 * Class represents services for stripe
 */
class RegisterService {
    /**
   * @desc This function is being used to create a stripe checkout session
   * <AUTHOR>
   * @since 15/11/2023
   * @param {Object} loggedInUser loggedInUser
   */
    static async createCheckoutSession (loggedInUser) {
        const user = await User.get({ id: loggedInUser.id, email: loggedInUser.email });
        let customerId = lodash.get(user, 'stripeCustomerId');
        if (!customerId) {
            const customer = await Stripe.createCustomer(
                user.email,
                `${user.firstName} ${user.lastName}`,
                stripe
            );
            customerId = customer.id;
            await User.update({ id: loggedInUser.id, email: user.email }, { stripeCustomerId: customerId });
        }
        return customerId;
    }

    /**
     * @desc This function is being used to create a payment intent and register user
     * <AUTHOR>
     * @since 15/11/2023
     * @param {Object} req req
     * @param {Object} loggedInUser loggedInUser
     * @param {Object} locale locale
     */
    static async registerUserAndPayment (req, loggedInUser, locale) {
        CONSOLE_LOGGER.info('Registering user and payment', { body: req.body });
        const {
            eventId,
            childId,
            paymentType,
            isCoveredByUser,
            purchasedProducts
        } = req.body;
        const Validator = new PaymentValidator(req.body, locale);
        Validator.validate();

        let { donationAmount } = req.body;
        donationAmount = donationAmount !== undefined ? (Number(donationAmount.trim()) || 0) : 0;
        let { quantity } = req.body;
        quantity = quantity || 1;
        const event = await Event.get({ id: eventId });
        const currentDateTime = MOMENT();
        const endDateTime = MOMENT(event.details.endDateTime, 'MM/DD/YYYY HH:mm');
        const isFree = CONSTANTS.PAYMENT_TYPES.FREE;
        const isStripe = CONSTANTS.PAYMENT_TYPES.STRIPE;
        const isCheck = CONSTANTS.PAYMENT_TYPES.CHECK;
        const isCash = CONSTANTS.PAYMENT_TYPES.CASH;
        const isVenmo = CONSTANTS.PAYMENT_TYPES.VENMO;
        const isPaymentInitiated = CONSTANTS.PAYMENT_TYPES.PAYMENT_INITIATED;
        const isPending = CONSTANTS.PAYMENT_STATUS.PENDING;
        const isApproved = CONSTANTS.PAYMENT_STATUS.APPROVED;
        if (endDateTime.isBefore(currentDateTime)) {
            throw {
                message: MESSAGES.CANT_REGISTER_TO_PAST_EVENT,
                statusCode: 400
            };
        }

        const isExistsChild = await EventSignup.query('eventId')
            .eq(eventId).where('childId').eq(childId).exec();
        const paymentStatus = lodash.get(isExistsChild.toJSON()[0], 'paymentDetails.paymentStatus');
        const eventSignupPaymentType = lodash.get(isExistsChild.toJSON()[0], 'paymentDetails.paymentType');

        if (isExistsChild.count && paymentStatus === isApproved) {
            throw {
                message: MESSAGES.EVENT_ALREADY_REGISTERED,
                statusCode: 403
            };
        }
        const existingPaymentTypeStripe = eventSignupPaymentType && eventSignupPaymentType === isStripe;
        const isCashChequeVenmo = paymentType === isCash || paymentType === isCheck || paymentType === isVenmo;

        const { organizationId, fee, participantsCount, membershipBenefitDetails, products } = event;
        const membershipBenefitDetailsClone = structuredClone(membershipBenefitDetails);
        const membershipBenefitDetailsParsed = membershipBenefitDetailsClone ?? {};
        membershipBenefitDetailsParsed.benefitDiscount = membershipBenefitDetailsParsed.benefitDiscount ? JSON.parse(membershipBenefitDetails.benefitDiscount) : [];

        const child = await Child.get(childId);
        const membershipsEnabledForChild = this.getMembershipsOfChild(
            event.organizationId,
            child,
            MOMENT(event.details.startDateTime)
        );
        if (membershipBenefitDetailsParsed.isOnlyForMembers) {
            if (!membershipsEnabledForChild) {
                throw {
                    message: MESSAGES.EVENT_ONLY_FOR_MEMBERS,
                    statusCode: 403
                };
            }
        }

        let membershipBenefitAmount = 0;
        if (membershipsEnabledForChild && membershipBenefitDetailsParsed) {
            const purchasedMembership =
            membershipBenefitDetailsParsed?.benefitDiscount?.filter(
                (membership) =>
                    `${membership.id}` ===
                membershipsEnabledForChild.membershipId
            );
            membershipBenefitAmount = purchasedMembership.length > 0 ? purchasedMembership[0].value : 0;
        }
        let preorderItemFees = 0;
        if (products && products.length > 0) {
            const optionPriceMap = new Map();
            products.forEach(product => {
                product.optionPrices?.forEach(optionPrice => {
                    optionPriceMap.set(optionPrice.id, optionPrice.itemCost);
                });
            });

            preorderItemFees = purchasedProducts.reduce((acc, purchasedProduct) => {
                if (optionPriceMap.has(purchasedProduct.id)) {
                    const itemCost = parseFloat(optionPriceMap.get(purchasedProduct.id));
                    acc += itemCost * purchasedProduct.quantity;
                }
                return acc;
            }, 0);
        }

        let childMembershipDiscount = 0;
        if (membershipsEnabledForChild && membershipBenefitDetails?.benefitDiscount && !isNaN(membershipBenefitAmount)) {
            childMembershipDiscount = Math.min(fee, membershipBenefitAmount);
        }

        const eventFeeAfterDiscount =
          (fee * quantity) + preorderItemFees - childMembershipDiscount;

        // current user is already pending for the event with payment type stripe and now paying with payment type cash or cheque
        if (isExistsChild.count && paymentStatus === isPaymentInitiated && existingPaymentTypeStripe && isCashChequeVenmo) {
            CONSOLE_LOGGER.info('User first paid with stripe now trying to pay with cash/check/venmo');
            const eventSignup = await EventSignup.get(isExistsChild.toJSON()[0].id);
            eventSignup.paymentDetails.stripeCustomerId = '';
            eventSignup.paymentDetails.stripeConnectAccountId = '';
            eventSignup.paymentDetails.transactionFee = 0;
            eventSignup.paymentDetails.platformFeeCoveredBy = undefined;
            eventSignup.paymentDetails.paymentType = paymentType;
            eventSignup.paymentDetails.paymentStatus = isPending;
            eventSignup.paymentDetails.membershipDiscount = childMembershipDiscount;
            eventSignup.quantityCount = quantity;
            eventSignup.donationAmount = donationAmount;
            eventSignup.purchasedProducts = purchasedProducts ?? undefined;
            eventSignup.stripePaymentIntentId = undefined;
            await eventSignup.save();
            throw {
                message: MESSAGES.PAYMENT_SUCCESS_CASH_CHEQUE,
                statusCode: 200
            };
        }

        if ((event.participantsLimit && participantsCount + quantity <= event.participantsLimit) || (!event.participantsLimit)) {
            if (paymentType === isFree) {
                await EventSignup.create({
                    eventId,
                    organizationId,
                    childId,
                    parentId: loggedInUser.id,
                    createdBy: loggedInUser.id,
                    quantityCount: quantity,
                    paymentDetails: { paymentStatus: isApproved, paymentType: isFree },
                    purchasedProducts: purchasedProducts ?? undefined
                });
                const userEmail = loggedInUser.email;
                const childName = `${child.firstName} ${child.lastName}`;
                const eventDetails = event;
                const eventName = eventDetails.title;
                const eventStartTime = MOMENT(eventDetails.details.startDateTime).format('MMMM Do YYYY, h:mm:ss a');
                const eventEndTime = MOMENT(eventDetails.details.endDateTime).format('MMMM Do YYYY, h:mm:ss a');
                const eventLocation = eventDetails.details.venue;
                event.participantsCount += quantity;
                await event.save();
                await this.sendRegisterMail({
                    childName, eventName, eventStartTime, eventEndTime, eventLocation, userEmail
                }, 'eventRegistration.html', `Event Registration for ${eventName}`);
                CONSOLE_LOGGER.time('Registering in free events');
                await this.updateFreeEventChildFeeds(childId, eventId);
                CONSOLE_LOGGER.timeEnd('Registering in free events');
                return {
                    message: MESSAGES.EVENT_REGISTER_SUCCESS
                };
            } else if (paymentType === isCash || paymentType === isCheck || paymentType === isVenmo) {
                await EventSignup.create({
                    eventId,
                    organizationId,
                    childId,
                    donationAmount,
                    parentId: loggedInUser.id,
                    createdBy: loggedInUser.id,
                    quantityCount: quantity,
                    paymentDetails: { paymentType, paymentStatus: isPending, membershipDiscount: childMembershipDiscount },
                    purchasedProducts: purchasedProducts ?? undefined
                });
                CONSOLE_LOGGER.time('Registering in pending events');
                await this.updateChildFeedsAndPendingEvents(childId, eventId);
                CONSOLE_LOGGER.timeEnd('Registering in pending events');
                throw {
                    message: MESSAGES.PAYMENT_SUCCESS_CASH_CHEQUE,
                    statusCode: 200
                };
            } else if (isExistsChild.count && paymentStatus === isPaymentInitiated && existingPaymentTypeStripe && paymentType === isStripe) {
                CONSOLE_LOGGER.info('User first paid with stripe now again trying to pay with stripe');
                const org = await Organization.get({ id: organizationId });
                const ptoId = lodash.get(org, 'paymentDetails.stripeConnectAccountId');
                const ptoStripeStatus = lodash.get(org, 'paymentDetails.stripeOnboardingStatus');
                if (ptoStripeStatus !== CONSTANTS.STATUS.ACTIVE || !ptoId) {
                    throw {
                        message: MESSAGES.STRIPE_NOT_ACTIVE,
                        statusCode: 403
                    };
                }
                let isOptionalStripeFee = lodash.get(org, 'platformFeeCoveredBy');
                if (paymentType === isStripe && isOptionalStripeFee === 'optional') {
                    const Validator = new PaymentValidator(req.body, locale);
                    Validator.validateOptionalFee();
                    isOptionalStripeFee = isCoveredByUser ? 'parent' : 'organization';
                }

                const customerId = await this.createCheckoutSession(loggedInUser);
                const { paymentIntent, stripeFee } = await Stripe.createPaymentIntent(stripe, eventFeeAfterDiscount, ptoId,
                    customerId, org, isCoveredByUser, donationAmount);
                const newStripeFee = isOptionalStripeFee === 'organization' ? 0 : stripeFee;
                const eventSignup = await EventSignup.get(isExistsChild.toJSON()[0].id);
                eventSignup.quantityCount = quantity;
                eventSignup.stripePaymentIntentId = paymentIntent.id;
                eventSignup.paymentDetails.stripeCustomerId = customerId;
                eventSignup.paymentDetails.stripeConnectAccountId = ptoId;
                eventSignup.paymentDetails.paymentType = isStripe;
                eventSignup.paymentDetails.transactionFee = newStripeFee / 100;
                eventSignup.paymentDetails.platformFeeCoveredBy = isOptionalStripeFee;
                eventSignup.paymentDetails.membershipDiscount = childMembershipDiscount;
                eventSignup.donationAmount = donationAmount;
                eventSignup.purchasedProducts = purchasedProducts ?? undefined;
                await eventSignup.save();
                return {
                    clientSecret: paymentIntent.client_secret,
                    publicableKey: process.env.PUBLISHABLE_SECRET_KEY
                };
            } else {
                CONSOLE_LOGGER.info('User trying to pay with stripe, payment-initiated');

                const org = await Organization.get({ id: organizationId });
                const ptoId = lodash.get(org, 'paymentDetails.stripeConnectAccountId');
                const ptoStripeStatus = lodash.get(org, 'paymentDetails.stripeOnboardingStatus');

                if (ptoStripeStatus !== CONSTANTS.STATUS.ACTIVE || !ptoId) {
                    throw {
                        message: MESSAGES.STRIPE_NOT_ACTIVE,
                        statusCode: 403
                    };
                }
                let isOptionalStripeFee = lodash.get(org, 'platformFeeCoveredBy');
                if (paymentType === isStripe && isOptionalStripeFee === 'optional') {
                    const Validator = new PaymentValidator(req.body, locale);
                    Validator.validateOptionalFee();
                    isOptionalStripeFee = isCoveredByUser ? 'parent' : 'organization';
                }
                CONSOLE_LOGGER.info('Creating payment intent');

                const customerId = await this.createCheckoutSession(loggedInUser);

                const { paymentIntent, stripeFee } = await Stripe.createPaymentIntent(stripe, eventFeeAfterDiscount,
                    ptoId, customerId, org, isCoveredByUser, donationAmount);
                CONSOLE_LOGGER.info('Payment intent created successfully', { paymentIntent, stripeFee });
                const newStripeFee = isOptionalStripeFee === 'organization' ? 0 : stripeFee;
                await EventSignup.create({
                    eventId,
                    organizationId,
                    childId,
                    donationAmount,
                    parentId: loggedInUser.id,
                    createdBy: loggedInUser.id,
                    stripePaymentIntentId: paymentIntent.id,
                    quantityCount: quantity,
                    paymentDetails: {
                        stripeCustomerId: customerId,
                        stripeConnectAccountId: ptoId,
                        paymentType: isStripe,
                        paymentStatus: isPaymentInitiated,
                        transactionFee: (newStripeFee / 100),
                        platformFeeCoveredBy: isOptionalStripeFee,
                        membershipDiscount: childMembershipDiscount
                    },
                    purchasedProducts: purchasedProducts ?? undefined
                });

                return {
                    clientSecret: paymentIntent.client_secret,
                    publicableKey: process.env.PUBLISHABLE_SECRET_KEY
                };
            }
        } else {
            throw {
                message: MESSAGES.EVENT_REGISTRATION_LIMIT,
                statusCode: 403
            };
        }
    }

    static async updateFreeEventChildFeeds (childId, eventId) {
        await AwsOpenSearchService.registerEventInChildEvents(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, childId, eventId);
        await AwsOpenSearchService.removeEventFromChildFeeds(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, childId, eventId);
    }

    static async updateChildFeedsAndPendingEvents (childId, eventId) {
        await AwsOpenSearchService.removeEventFromChildFeeds(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, childId, eventId);
        await AwsOpenSearchService.registerInChildPendingEvents(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, childId, eventId);
    }

    /**
    * Checks if the child has a membership with the specified organization.
    * <AUTHOR>
    * @since 29/07/2024
    * @param {string} organizationId - The ID of the organization.
    * @param {Object} child - The child object.
    * @returns {Array} - Returns membership, if the child has a membership with the specified organization, empty otherwise.
    */
    static getMembershipsOfChild (organizationId, child, eventStartDate) {
        const activeMembershipsForOrg = child.membershipsPurchased?.filter(
            (membership) =>
                membership.organizationId === organizationId &&
            MOMENT(membership.endDate).isAfter(MOMENT().utc()) &&
            MOMENT(membership.startDate).isBefore(eventStartDate)
        );
        if (activeMembershipsForOrg?.length) {
            return activeMembershipsForOrg[0];
        } else {
            return null;
        }
    }


    /**
     * @desc This function is being used to handle Webhook
     * <AUTHOR>
     * @since 16/11/2023
     * @param {Object} req req
     * @param {Object} res res
     */
    static async stripeWebhook (req, res) {
        const signature = req.headers['stripe-signature'];
        let event;
        try {
            event = await Stripe.constructWebhookEvent(req.rawBody, signature, stripe);
        } catch (err) {
            CONSOLE_LOGGER.error('Webhook Error:', err);
            throw {
                message: `Webhook Error: ${err.message}`,
                statusCode: 400
            };
        }
        if (event.type === 'payment_intent.canceled') {
            const payment = event.data.object;
            const eventSignup = await EventSignup.query('stripePaymentIntentId').eq(payment.id).exec();

            if (eventSignup.count) {
                const eventInSignup = await EventSignup.get(eventSignup.toJSON()[0].id);
                await eventInSignup.delete();
            }

            CONSOLE_LOGGER.info('payment intent: canceled ', payment);
        }
        if (event.type === 'payment_intent.succeeded') {
            // Extract payment details from the event
            const payment = event.data.object;

            // Fetch the event signup details
            const eventSignup = await EventSignup.query('stripePaymentIntentId').eq(payment.id).exec();
            const { id: signupId, parentId, childId, eventId, quantityCount } = eventSignup.toJSON()[0];

            // Update the payment status
            const eventInSignup = await EventSignup.get(signupId);
            eventInSignup.paymentDetails.paymentStatus = CONSTANTS.PAYMENT_STATUS.APPROVED;
            await eventInSignup.save();

            // Fetch user and child details
            const user = await User.query('id').eq(parentId).exec();
            const { email: userEmail } = user.toJSON()[0];
            const child = await Child.get({ id: childId });
            const childName = `${child.firstName} ${child.lastName}`;

            // Fetch event details
            const eventDetails = await Event.get({ id: eventId });
            const { title: eventName, details: { startDateTime, endDateTime, venue: eventLocation } } = eventDetails;

            // Format event start and end times
            const eventStartTime = MOMENT(startDateTime).format('MMMM Do YYYY, h:mm:ss a');
            const eventEndTime = MOMENT(endDateTime).format('MMMM Do YYYY, h:mm:ss a');

            // Update the event participants count
            eventDetails.participantsCount += quantityCount;
            await eventDetails.save();

            // Send registration mail
            await this.sendRegisterMail({
                childName, eventName, eventStartTime, eventEndTime, eventLocation, userEmail
            }, 'eventRegistration.html', `Event Registration for ${eventName}`);

            await AwsOpenSearchService.registerEventInChildEvents(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, childId, eventId);
            await AwsOpenSearchService.removeEventFromChildFeeds(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, childId, eventId);
            CONSOLE_LOGGER.info('payment intent: success', payment);
        }
        if (event.type === 'payment_intent.payment_failed') {
            const payment = event.data.object;
            const eventSignup = await EventSignup.query('stripePaymentIntentId').eq(payment.id).exec();

            if (eventSignup.count) {
                const eventInSignup = await EventSignup.get(eventSignup.toJSON()[0].id);
                await eventInSignup.delete();
            }

            CONSOLE_LOGGER.info('payment intent: payment_failed ', payment);
        } else {
            CONSOLE_LOGGER.info(`Unhandled event type: ${event.type}`);
        }
        CONSOLE_LOGGER.info(JSON.stringify(event), 'all events');
        res.send();
    }

    /**
     * @desc This function is being used to send event registration to email
     * <AUTHOR>
     * @since 16/11/2023
     * @param {Object} reqObj reqObj
     */
    static async sendRegisterMail (reqObj, templateFile, subject) {
        const { childName, eventName, eventStartTime, eventEndTime, eventLocation, userEmail: email } = reqObj;
        const template = `emailTemplates/${templateFile}`;
        const currentDate = new Date();
        const year = currentDate.getFullYear();
        const templateVariables = {
            year,
            eventName, eventStartTime, eventEndTime, eventLocation,
            email: CONSTANTS.CLIENT_INFO.HELP_EMAIL,
            appname: CONSTANTS.APP_NAME,
            username: `${childName}`
        };
        await EmailService.prepareAndSendEmail([email], subject, template, templateVariables);
    }
}

module.exports = RegisterService;
