const jwt = require('jsonwebtoken');
const Utils = require('../util/utilFunctions');
const User = require('../models/user.model');
const HTTPStatus = require('../util/http-status');

/**
 * @desc This function is being used to authenticate each private request
 * <AUTHOR>
 * @since 19/10/2023
 * @param {Object} req Request req.headers RequestBody req.headers.accessToken accessToken
 * @param {Object} res Response
 * @param {function} next exceptionHandler Calls exceptionHandler
 */
const checkUser = (me, res, next) => {
    User.get({ id: me.sub, email: me.email }).then((userObj) => {
        const responseObject = Utils.errorResponse();
        if (!userObj) {
            responseObject.message = res.__('ACCESS_DENIED');
            res.status(HTTPStatus.UNAUTHORIZED).send(responseObject);
            return;
        } else if (userObj.status !== CONSTANTS.STATUS.ACTIVE || !userObj.isVerified) {
            responseObject.data = {
                status: userObj.status,
                message: res.__('DEACTIVATE_ACCOUNT_BY_ADMIN')
            };
            res.status(HTTPStatus.ACCOUNT_SUSPENDED).send(responseObject);
            return;
        } else {
            // Do nothing...
        }
        res.locals.user = userObj;
        next();
    }).catch(next);
};

module.exports = function (req, res, next) {
    try {
        const token = req.headers.authorization.split(' ')[1];
        const decodedJwt = jwt.decode(token, { complete: true });
        const decodedJwtPayload = decodedJwt.payload;
        checkUser(decodedJwtPayload, res, next);
    } catch (err) {
        const responseObject = Utils.errorResponse();
        responseObject.message = res.__('ACCESS_DENIED');
        res.status(HTTPStatus.UNAUTHORIZED).send(responseObject);
    }
};
