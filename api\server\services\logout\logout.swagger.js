/**
 * @openapi
 * components:
 *  schemas:
 *      successLogout:
 *          type: object
 *          properties:
 *              status:
 *                  type: number
 *                  description: status if data exists
 *              data:
 *                  type: string
 *                  description: success data
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: User logged out successfully.
 */
/**

 * @openapi
 *  /user/signout:
 *      post:
 *          security:
 *              - bearerAuth: []
 *          tags: [User]
 *          summary: logout user
 *          responses:
 *              200:
 *                  description: success details
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#/components/schemas/successLogout'
 *              401:
 *                  description: Unauthorised Access
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#/components/schemas/unauthorisedAccessUser'
 *              500:
 *                  description: internal server error
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#/components/schemas/unexpectedError'
 *
 *
 */
