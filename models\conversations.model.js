const dynamoose = require('dynamoose');
const { v4: uuidv4 } = require('uuid');

const conversationsSchema = new dynamoose.Schema({
    conversationId: {
        hashKey: true,
        type: String,
        default: () => uuidv4()
    },
    groupId: {
        type: String,
        required: true
    },
    text: {
        type: String,
        required: true
    },
    sentBy: {
        type: String,
        required: true
    },
    attachments: {
        type: Array,
        schema: [String],
        default: []
    },
    forwardedMessageId: {
        type: String
    },
    replyToMessageId: {
        type: String
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('Conversations', conversationsSchema);
