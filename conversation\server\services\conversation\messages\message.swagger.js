/**
 * Swagger schema for message routes
 */

/**
 * @openapi
 * components:
 *      schemas:
 *          sendPersonalMessageWithMedia:
 *              type: object
 *              properties:
 *                  messageId:
 *                      type: string
 *                  senderId:
 *                      type: string
 *                  actionType:
 *                      type: string
 *                  message:
 *                      type: string
 *                  mediaName:
 *                      type: string
 *                  mediaType:
 *                      type: string
 *                  mediaDisplayName:
 *                      type: string
 *                  mediaThumbnailName:
 *                      type: string
 *                  replyMessage:
 *                      type: object
 *                  conversationId:
 *                      type: string
 *                  files:
 *                      type: array
 *                      items:
 *                          type: string
 *                          format: binary
 *              example:
 *                  messageId: 1
 *                  senderId: 1
 *                  actionType: 1
 *                  message: Hello
 *                  mediaName: media.jpg
 *                  mediaType: image/jpeg
 *                  mediaDisplayName: media.jpg
 *                  mediaThumbnailName: media.jpg
 *                  replyMessage: {}
 *                  conversationId: 1
 *
 *          successSendPersonalMessageWithMedia:
 *              type: object
 *              properties:
 *                  status:
 *                      $ref: '#/components/messageDefinition/properties/status'
 *                  message:
 *                      $ref: '#/components/messageDefinition/properties/message'
 *                  data:
 *                      type: object
 *                      properties:
 *                          messageId:
 *                              type: string
 *                          senderId:
 *                              type: string
 *                          actionType:
 *                              type: string
 *                          message:
 *                              type: string
 *                          mediaName:
 *                              type: string
 *                          mediaType:
 *                              type: string
 *                          mediaDisplayName:
 *                              type: string
 *                          mediaThumbnailName:
 *                              type: string
 *              example:
 *                  status: 1
 *                  message: Success
 *                  data:
 *                      messageId: 1
 *                      senderId: 1
 *                      actionType: 1
 *                      message: Hello
 *                      mediaName: media.jpg
 *                      mediaType: image/jpeg
 *                      mediaDisplayName: media.jpg
 *                      mediaThumbnailName: media.jpg
 *
 *          successGetFlagMessageReasons:
 *              type: object
 *              properties:
 *                  status:
 *                      $ref: '#/components/messageDefinition/properties/status'
 *                  message:
 *                      $ref: '#/components/messageDefinition/properties/message'
 *                  data:
 *                      type: object
 *                      properties:
 *                          flaggedMessageReasons:
 *                              type: object
 *                              properties:
 *                                  id:
 *                                      type: string
 *                                  reasons:
 *                                      type: array
 *                                      items:
 *                                          type: string
 *                                  status:
 *                                      type: string
 *                                  reporter:
 *                                      type: object
 *                                      properties:
 *                                          id:
 *                                          type: string
 *                                      firstName:
 *                                          type: string
 *                                      lastName:
 *                                          type: string
 *                      flaggedMessageDetails:
 *                          type: object
 *                          properties:
 *                              id:
 *                                  type: string
 *                              groupId:
 *                                  type: string
 *                              mediaDisplayName:
 *                                  type: string
 *                              mediaUrl:
 *                                  type: string
 *                              mediaType:
 *                                  type: string
 *                              mediaThumbnailUrl:
 *                                  type: string
 *                              message:
 *                                  type: string
 *                              replyMessage:
 *                                  type: object
 *                                  properties:
 *                                      messageId:
 *                                          type: string
 *                                      senderId:
 *                                          type: string
 *                                      message:
 *                                          type: string
 *                                      mediaUrl:
 *                                          type: string
 *                                      mediaType:
 *                                          type: string
 *                                      mediaDisplayName:
 *                                          type: string
 *                              senderId:
 *                                  type: object
 *                                  properties:
 *                                      id:
 *                                          type: string
 *                                      firstName:
 *                                          type: string
 *                                      lastName:
 *                                          type: string
 *                              organizationId:
 *                                  type: string
 *                              groupName:
 *                                  type: string
 *              example:
 *                  status: 1
 *                  message: Success
 *                  data:
 *                      flaggedMessageDetails:
 *                          id: "flaggedMessageId"
 *                          groupId: "groupId"
 *                          mediaDisplayName: "mediaDisplayName"
 *                          mediaUrl: "mediaUrl"
 *                          mediaType: "mediaType"
 *                          mediaThumbnailUrl: "mediaThumbnailUrl"
 *                          message: "message"
 *                          replyMessage: {
 *                              messageId: "messageId",
 *                              senderId: "senderId",
 *                              message: "message",
 *                              mediaUrl: "mediaUrl",
 *                              mediaType: "mediaType",
 *                              mediaDisplayName: "mediaDisplayName"
 *                          }
 *                          sender: {
 *                              id: "senderId",
 *                              firstName: "senderFirstName",
 *                              lastName: "senderLastName"
 *                          }
 *                          groupName: "groupName"
 *                          organizationId: "organizationId"
 *                      flaggedMessageReasons:
 *                          - id: "flaggedMessageId"
 *                            reasons: ["reason1", "reason2"]
 *                            status: "status"
 *                            reporter: { id: "reporterId", firstName: "reporterFirstName", lastName: "reporterLastName" }
 *
 *          successGetDisabledCommenterList:
 *              type: object
 *              properties:
 *                  status:
 *                      $ref: '#/components/messageDefinition/properties/status'
 *                  message:
 *                      $ref: '#/components/messageDefinition/properties/message'
 *                  data:
 *                      type: array
 *                      items:
 *                          type: object
 *                          properties:
 *                              groupName:
 *                                  type: string
 *                              users:
 *                                  type: array
 *                                  items:
 *                                      type: object
 *                                      properties:
 *                                          id:
 *                                              type: string
 *                                          firstName:
 *                                              type: string
 *                                          lastName:
 *                                              type: string
 *              example:
 *                  status: 1
 *                  message: Success
 *                  data:
 *                      - groupName: "groupName"
 *                        users:
 *                          - id: "userId"
 *                            firstName: "firstName"
 *                            lastName: "lastName"
 */

/**
 * @openapi
 * /conversation/send-personal-message-with-media:
 *  post:
 *      security:
 *        - bearerAuth: []
 *      tags:
 *          - Conversation
 *      summary: Send personal message with media
 *      description: Send personal message with media
 *      requestBody:
 *          content:
 *              multipart/form-data:
 *                  schema:
 *                      $ref: '#/components/schemas/sendPersonalMessageWithMedia'
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successSendPersonalMessageWithMedia'
 *          400:
 *              description: Bad Request
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorized
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /conversation/flag-message-reasons:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags:
 *          - Conversation
 *      summary: Get flag message reasons
 *      description: Get flag message reasons
 *      parameters:
 *          - in: query
 *            name: messageId
 *            required: true
 *            description: Message Id
 *            schema:
 *              type: string
 *          - in: query
 *            name: organizationId
 *            required: true
 *            description: Organization Id
 *            schema:
 *              type: string
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successGetFlagMessageReasons'
 *          400:
 *              description: Bad Request
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorized
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /conversation/disabled-commenter-list:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags:
 *          - Conversation
 *      summary: Get disabled commenter list
 *      description: Get disabled commenter list
 *      parameters:
 *          - in: query
 *            name: organizationId
 *            required: true
 *            description: Organization Id
 *            schema:
 *              type: string
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successGetDisabledCommenterList'
 *          400:
 *              description: Bad Request
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorized
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */
