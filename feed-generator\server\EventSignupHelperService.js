const MOMENT = require('moment');
const RedisUtil = require('./redisUtil');
const CONSTANTS = require('./constants');
const NotificationHelperService = require('./NotificationHelperService');
const OpensearchHelperService = require('./OpensearchHelperService');
const Utils = require('./Utils');

class EventSignupHelperService {
    /**
     * @description Adds or updates the registered events for a child
     * <AUTHOR>
     * @param {Redis} redis
     * @param {Object} eventSignUp
     * @param {Object} feed
     * @param {string} childKey
     * @param {string} childRegisteredKey
     * @param {Object} childDetails
     * @param {String} versionPrefix
     * @param {boolean} isPopulatingCache
     * @param {Object} feedReferenceForPopulatingCache
     * @returns {number}
     */
    static async addOrUpdateRegisteredEvents ({
        redis,
        eventSignUp,
        feed,
        childKey,
        childRegisteredKey,
        childDetails,
        versionPrefix,
        isPopulatingCache = false,
        feedReferenceForPopulatingCache = null
    }) {
        const timestamp = MOMENT(feed.startDateTime).toDate().getTime();
        const { guardians = [] } = childDetails;
        const keys = this.getKeys({
            childKey,
            childRegisteredKey,
            guardians,
            versionPrefix,
            shouldOnlyGenerateRegisterKey: childKey === childRegisteredKey
        });

        await this.updateEventSignUpStatus({
            redis,
            eventSignUp,
            timestamp,
            childKey,
            keys,
            isPopulatingCache,
            feedReferenceForPopulatingCache
        });

        return keys;
    }

    /**
     * @description Updates the event sign up status
     * <AUTHOR>
     * @param {Redis} redis
     * @param {Object} eventSignUp
     * @param {number} timestamp
     * @param {string} childKey
     * @param {Array} keys
     * @param {boolean} isPopulatingCache
     * @param {Object} feedReferenceForPopulatingCache
     * @returns {string}
     */
    static async updateEventSignUpStatus ({
        redis,
        eventSignUp,
        timestamp,
        childKey,
        keys,
        isPopulatingCache,
        feedReferenceForPopulatingCache
    }) {
        const childEvents = await RedisUtil.getElementsOfSortedSetByScore(
            redis,
            childKey,
            timestamp,
            CONSTANTS.PLUS_INF
        );
        const eventToUpdate = childEvents.find((event) => {
            const { eventId } = JSON.parse(event);
            return eventId === eventSignUp.eventId;
        });
        if (eventToUpdate || isPopulatingCache) {
            for (const key of keys) {
                const feedReference = (isPopulatingCache && !eventToUpdate)
                    ? feedReferenceForPopulatingCache
                    : JSON.parse(eventToUpdate);

                await RedisUtil.removeMemberFromSortedSet(redis, key.key, eventToUpdate);
                await RedisUtil.addEventReferenceToSortedSet(
                    redis,
                    key.keyRegistered,
                    timestamp,
                    JSON.stringify({
                        ...feedReference,
                        isSignedUp: true,
                        purchasedProducts: eventSignUp.purchasedProducts,
                        donationAmount: eventSignUp.donationAmount,
                        status: eventSignUp.paymentDetails.paymentStatus,
                        quantityCount: eventSignUp.quantityCount,
                        transactionFee: eventSignUp.paymentDetails.transactionFee,
                        platformFeeCoveredBy: eventSignUp.paymentDetails.platformFeeCoveredBy,
                        paymentType: eventSignUp.paymentDetails.paymentType,
                        membershipDiscount: eventSignUp.paymentDetails.membershipDiscount
                    })
                );
            }
        }
        return childKey;
    }

    /**
     * @description Updates the event participants
     * <AUTHOR>
     * @param {Redis} redis
     * @param {Object} eventSignUp
     * @param {Object} feed
     * @param {Object} childDetails
     * @param {String} versionPrefix
     * @param {boolean} isPopulatingCache
     * @returns {string}
     */
    static async updateEventParticipants ({ redis, eventSignUp, feed, childDetails, versionPrefix, isPopulatingCache = false }) {
        if (!feed.participants) {
            feed.participants = [];
        }
        feed.participants.push({
            parentId: eventSignUp.parentId,
            childId: eventSignUp.childId
        });

        if (!isPopulatingCache) {
            await NotificationHelperService.sendEventNotification(eventSignUp, feed, childDetails);
        }

        await RedisUtil.setHashValue(
            redis,
            Utils.getEventDetailsKey({ versionPrefix, eventId: eventSignUp.eventId }),
            'details',
            feed
        );

        if (!isPopulatingCache) {
            await OpensearchHelperService.addParticipantsInOpensearch(
                eventSignUp.eventId,
                feed.participants
            );
        }

        return feed.participants;
    }

    /**
     * @description Removes the registered event references
     * <AUTHOR>
     * @param {Redis} redis
     * @param {Object} eventSignUp
     * @param {Object} childDetails
     * @param {String} versionPrefix
     * @returns {string}
     */
    static async removeRegisteredEventReferences ({ redis, eventSignUp, childDetails, versionPrefix }) {
        const childRegisteredKey = Utils.getRegisteredChildKey({ versionPrefix, childId: eventSignUp.childId });
        const childKey = Utils.getChildKey({ versionPrefix, childId: eventSignUp.childId });

        const { guardians = [] } = childDetails;
        const keys = this.getKeys({ childKey, childRegisteredKey, guardians, versionPrefix });

        const feed = await this.handleUpdateEventParticipants({ redis, eventSignUp, versionPrefix });

        await OpensearchHelperService.addParticipantsInOpensearch(
            eventSignUp.eventId,
            feed.participants
        );

        const timestamp = MOMENT(feed.startDateTime).toDate().getTime();

        const childRegisteredEvents = await RedisUtil.getElementsOfSortedSetByScore(
            redis,
            childRegisteredKey,
            timestamp,
            CONSTANTS.PLUS_INF
        );

        const eventToRemove = childRegisteredEvents.find((event) => {
            const { eventId } = JSON.parse(event);
            return eventId === eventSignUp.eventId;
        });

        if (eventToRemove) {
            for (const key of keys) {
                await RedisUtil.removeMemberFromSortedSet(
                    redis,
                    key.registeredKey,
                    eventToRemove
                );

                const eventToAdd = JSON.parse(eventToRemove);
                delete eventToAdd.isSignedUp;
                delete eventToAdd.paymentType;
                delete eventToAdd.status;
                delete eventToAdd.quantityCount;
                delete eventToAdd.membershipDiscount;

                await RedisUtil.addEventReferenceToSortedSet(
                    redis,
                    key.key,
                    timestamp,
                    JSON.stringify(eventToAdd)
                );
            }
        }

        return eventSignUp.childId;
    }

    /**
     * @description Handles the update event participants
     * <AUTHOR>
     * @param {Redis} redis
     * @param {Object} eventSignUp
     * @param {String} versionPrefix
     * @returns {void}
     */
    static async handleUpdateEventParticipants ({ redis, eventSignUp, versionPrefix }) {
        const feed = JSON.parse(
            await RedisUtil.getHashValue(
                redis,
                Utils.getEventDetailsKey({ versionPrefix, eventId: eventSignUp.eventId }),
                'details'
            )
        );

        feed.participants = feed.participants.filter(
            (participant) => participant.childId !== eventSignUp.childId
        );
        await RedisUtil.setHashValue(
            redis,
            Utils.getEventDetailsKey({ versionPrefix, eventId: eventSignUp.eventId }),
            'details',
            feed
        );

        return feed;
    }

    /**
     * @description Gets the keys
     * <AUTHOR>
     * @param {string} childKey
     * @param {string} childRegisteredKey
     * @param {Array} guardians
     * @param {String} versionPrefix
     * @param {boolean} shouldOnlyGenerateRegisterKey
     * @returns {Array}
     */
    static getKeys ({ childKey, childRegisteredKey, guardians, versionPrefix, shouldOnlyGenerateRegisterKey = false }) {
        const keys = [{ key: childKey, keyRegistered: childRegisteredKey }];
        guardians.forEach((guardian) => {
            const registeredUserKey = Utils.getRegisteredUserKey({ versionPrefix, userId: guardian });
            keys.push({
                key: shouldOnlyGenerateRegisterKey
                    ? registeredUserKey
                    : Utils.getUserKey({ versionPrefix, userId: guardian }),
                keyRegistered: registeredUserKey
            });
        });

        return keys;
    }
}

module.exports = EventSignupHelperService;
