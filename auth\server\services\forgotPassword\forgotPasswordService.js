/* eslint-disable max-len */
const User = require('../../models/user.model');
const validation = require('../../util/validation');
const UtilFunctions = require('../../util/utilFunctions');
const SignUpService = require('../signup/signUpService');
const Cognito = require('../../util/cognito');

/**
 * Class represents services fo forgot/reset password .
 */
class ForgotPasswordService {

    /**
     * @desc This function is being used to generate otp to reset password
     * <AUTHOR>
     * @since 17/10/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {Object} req.body.email email
     */
    static async forgotPassword (req, locale) {
        let { email } = req.body;
        const Validator = new validation(locale);
        Validator.email(email);
        email = email.toLowerCase();
        const validUser = await SignUpService.isUserAlreadyRegister({ email });
        if (validUser.count) {
            const { id, status, isDeleted } = validUser.toJSON()[0];
            if (isDeleted) {
                throw {
                    message: MESSAGES.INVALID_EMAIL_PHONE,
                    statusCode: 403
                };
            }
            if (status !== CONSTANTS.STATUS.ACTIVE) {
                throw {
                    message: MESSAGES.INACTIVE_USER,
                    statusCode: 403
                };
            }
            const otp = UtilFunctions.generateOtp();
            const otpExpiryDate = MOMENT().add(CONSTANTS.OTP_EXPIRY_DURATION, 'minutes').utc().toDate();
            await User.update({ id, email }, { otp, otpExpiryDate });
            const smsMessage = `Enter ${otp} to reset your password(valid for 30 minutes). Please ensure you complete the reset password process promptly.`;
            await UtilFunctions.sendOtpToMail(otp, validUser.toJSON()[0], smsMessage, 'forgotPassword.html', 'Password Reset Request');
            return;
        }
        throw {
            message: MESSAGES.NEW_USER,
            statusCode: 404
        };
    }

    /**
     * @desc This function is being used to verify user account using otp
     * <AUTHOR>
     * @since 17/10/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {Object} req.body.email email
     * @param {Object} req.body.otp otp
     */
    static async verifyOtp (req, locale) {
        const Validator = new validation(locale);
        Validator.email(req.body.email);
        Validator.otp(req.body.otp);
        req.body.email = req.body.email.toLowerCase();
        const compareDate = MOMENT().utc().unix();
        const user = await SignUpService.isUserAlreadyRegister(req.body);
        if (user.count) {
            const { otp, otpExpiryDate } = user.toJSON()[0];
            if (otp === req.body.otp && compareDate <= MOMENT(otpExpiryDate).utc().unix()) {
                return;
            } else {
                throw {
                    message: MESSAGES.INVALID_VERIFY_OTP,
                    statusCode: 400
                };
            }
        }
        throw {
            message: MESSAGES.NEW_USER,
            statusCode: 404
        };
    }

    /**
     * @desc This function is being used to reset password
     * <AUTHOR>
     * @since 18/10/2023
     * @param {Object} req Request req.body RequestBody
     * @param {Object} locale Locale passed from request
     */
    static async resetPassword (req, locale) {
        const Validator = new validation(locale);
        Validator.email(req.body.email);
        Validator.password(req.body.password);
        Validator.otp(req.body.otp);

        req.body.email = req.body.email.toLowerCase();
        const user = await SignUpService.isUserAlreadyRegister({ email: req.body.email });
        if (user.count) {
            const { id, email, otp, provider } = user.toJSON()[0];
            if (otp === req.body.otp) {
                if (provider === CONSTANTS.PROVIDER.GOOGLE || provider === CONSTANTS.PROVIDER.APPLE) {
                    await Cognito.signup({ email, password: req.body.password });
                    await Cognito.updateConfirmStatus(email);
                    await Cognito.updateUserToActive(email);
                    await User.update({ id, email }, { provider: CONSTANTS.PROVIDER.EMAIL });
                } else {
                    await Cognito.setCognitoUserPassword(req.body.email, req.body.password);
                    const query = {
                        provider: CONSTANTS.PROVIDER.EMAIL, isVerified: CONSTANTS.VERIFIED.ACTIVE,
                        status: CONSTANTS.STATUS.ACTIVE, '$REMOVE': ['otp', 'otpExpiryDate']
                    };
                    await User.update({ id, email }, query);
                }
                return;
            } else {
                throw {
                    message: MESSAGES.INVALID_VERIFY_OTP,
                    statusCode: 400
                };
            }
        }
        throw {
            message: MESSAGES.NEW_USER,
            statusCode: 404
        };
    }
}

module.exports = ForgotPasswordService;
