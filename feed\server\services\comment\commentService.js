const CommentValidator = require('./commentValidator');
const Event = require('../../models/event.model');
const Validation = require('../../util/validation');
const RedisUtil = require('../../util/redisUtil');

/**
 * Class represents services for comment.
 */
class CommentService {
    /**
     * @desc This function is being used to add comment
     * <AUTHOR>
     * @since 27/11/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {Object} user User
     * @param {Object} res Response
     */
    static async addComment (req, user, locale) {
        const Validator = new CommentValidator(req.body, locale);
        Validator.commentValidate();
        const { eventId, message, childId } = req.body;
        const event = await Event.get({ id: eventId });
        if (!event || event.isDeleted) {
            throw {
                message: MESSAGES.EVENT_NOT_FOUND,
                statusCode: 404
            };
        }
        if (!event.comments) {
            event.comments = [];
        }
        const commentObj = {
            childId,
            message,
            parentId: user.id
        };
        event.comments.push(commentObj);
        await event.save();
    }

    /**
     * @desc This function is being used to list comment
     * <AUTHOR>
     * @since 29/11/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {Object} user User
     * @param {Object} res Response
     */
    static async getCommentList (req, user, locale) {
        const { eventId, nextIndex } = req.query;
        const Validator = new Validation(locale);
        Validator.uuid(eventId, 'Event Id');
        const cursor = Number(nextIndex) || '-inf';

        const data = await RedisUtil.getMembersByScore(`event-comments:${eventId}`, cursor, CONSTANTS.DEFAULT_PAGE_SIZE + 1);
        const comments = [];
        for (let i = 0; i < data.length; i += 2) {
            comments.push(JSON.parse(data[i]));
        }

        let nextCursor;
        if ((data.length / 2) > CONSTANTS.DEFAULT_PAGE_SIZE) {
            comments.pop();
            nextCursor = Number(data[data.length - 1]);
        }
        return { comments, nextIndex: nextCursor || null };
    }

    /**
     * @desc This function is being used to delete comment
     * <AUTHOR>
     * @since 29/11/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {Object} user User
     * @param {Object} res Response
     */
    static async deleteComment (req, user, locale) {
        const { eventId, commentId, childId } = req.query;
        const Validator = new Validation(locale);
        Validator.uuid(eventId, 'Event Id');
        Validator.uuid(commentId, 'Comment Id');
        Validator.uuid(childId, 'Child Id');
        const event = await Event.get({ id: eventId });
        if (!event || event.isDeleted) {
            throw {
                message: MESSAGES.EVENT_NOT_FOUND,
                statusCode: 404
            };
        }
        const comment = event.comments.find(comment => comment.id === commentId);
        if (!comment || comment.isDeleted) {
            throw {
                message: MESSAGES.COMMENT_NOT_FOUND,
                statusCode: 404
            };
        }
        if (comment.parentId !== user.id || comment.childId !== childId) {
            throw {
                message: MESSAGES.COMMENT_NOT_AUTHORIZED,
                statusCode: 403
            };
        }
        comment.isDeleted = 1;
        await event.save();
    }
}

module.exports = CommentService;
