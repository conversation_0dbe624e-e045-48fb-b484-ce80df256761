const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const request = require('supertest');
chai.use(chaiHttp);
const sinon = require('sinon');
const assert = sinon.assert;
const jwt = require('jsonwebtoken');
const Utils = require('../../../util/utilFunctions');
const User = require('../../../models/user.model');
const CONSTANTS = require('../../../util/constants');
const testcaseAiCompanionScreen = require('./testcaseAiCompanionScreen');
const AiCompanionScreenFeedbackModel = require('../../../models/aiCompanionScreenFeedback.model');

const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
/* User Token */
const user = {
    id: '95285616-3769-4b2e-b36b-898597b8146e',
    sub: '95285616-3769-4b2e-b36b-898597b8146e',
    email: '<EMAIL>',
    isVerified: 1,
    role: 1,
    status: 'active',
    isDeleted: 0
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'testToken', tokenOptionalInfo)
};

Utils.addCommonReqTokenForHMac(request);

const payload = {
    aiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
    userQueryMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c608',
    userQueryMessage: 'Hello',
    aiResponseMessage: 'Hello, how can I assist you today?',
    aiResponseMessageContexts: '[{"source": {"url": "https://test.source.com"}}]',
    feedback: 'positive'
};

const payloadGetFeedbackList = {
    startAiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
};

describe('Add/Update Feedback', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        testcaseAiCompanionScreen.addUpdateAiCompanionScreenFeedback.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                    accessLevel: CONSTANTS.ACCESS_LEVEL.APP
                });

                request(process.env.BASE_URL)
                    .post('/conversation/ai-companion-screen/add-update-feedback')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        done();
                    });
            });
        });

        it('should update the feedback if feedback with same ai response message id and user id is already present', (done) => {
            const saveStub = sinon.stub();
            saveStub.resolves({
                aiResponseMessageId: payload.aiResponseMessageId,
                userId: 1,
                userQueryMessageId: payload.userQueryMessageId,
                feedback: 'positive',
                aiResponseMessage: payload.aiResponseMessage,
                aiResponseMessageContexts: payload.aiResponseMessageContexts,
                userQueryMessage: payload.userQueryMessage
            });

            const aiScreenFeedbackStub = sinon.stub(AiCompanionScreenFeedbackModel, 'get').resolves({
                id: payload.aiResponseMessageId,
                userId: 1,
                userQueryMessageId: payload.userQueryMessageId,
                senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
                feedback: 'positive',
                save: saveStub
            });

            request(process.env.BASE_URL)
                .post('/conversation/ai-companion-screen/add-update-feedback')
                .set({ Authorization: requestPayloadUser.token })
                .send(payload)
                .end((err, res) => {
                    assert.calledOnceWithMatch(aiScreenFeedbackStub, {
                        aiResponseMessageId: payload.aiResponseMessageId,
                        userId: 1
                    });
                    assert.calledOnce(saveStub);

                    expect(res.body.status).to.be.equal(1);
                    expect(res.statusCode).to.be.equal(200);
                    expect(res.body.message).to.be.equal('Success');

                    assert.match(res.body.data, {
                        aiResponseMessageId: payload.aiResponseMessageId,
                        userId: 1,
                        userQueryMessageId: payload.userQueryMessageId,
                        feedback: 'positive',
                        aiResponseMessage: payload.aiResponseMessage,
                        aiResponseMessageContexts: payload.aiResponseMessageContexts,
                        userQueryMessage: payload.userQueryMessage
                    });

                    aiScreenFeedbackStub.restore();
                    done();
                });
        });

        it('should create the feedback if feedback with same ai response message id and user id is not present', (done) => {
            const aiScreenFeedbackStub = sinon.stub(AiCompanionScreenFeedbackModel, 'get').resolves(null);
            const aiScreenFeedbackCreateStub = sinon.stub(AiCompanionScreenFeedbackModel, 'create').resolves({
                aiResponseMessageId: payload.aiResponseMessageId,
                userId: 1,
                userQueryMessageId: payload.userQueryMessageId,
                feedback: 'positive',
                aiResponseMessage: payload.aiResponseMessage,
                aiResponseMessageContexts: payload.aiResponseMessageContexts,
                userQueryMessage: payload.userQueryMessage
            });

            request(process.env.BASE_URL)
                .post('/conversation/ai-companion-screen/add-update-feedback')
                .set({ Authorization: requestPayloadUser.token })
                .send(payload)
                .end((err, res) => {
                    assert.calledOnceWithMatch(aiScreenFeedbackStub, {
                        aiResponseMessageId: payload.aiResponseMessageId,
                        userId: 1
                    });
                    assert.calledOnceWithMatch(aiScreenFeedbackCreateStub, {
                        aiResponseMessageId: payload.aiResponseMessageId,
                        userId: 1,
                        userQueryMessageId: payload.userQueryMessageId,
                        feedback: 'positive',
                        aiResponseMessage: payload.aiResponseMessage,
                        aiResponseMessageContexts: payload.aiResponseMessageContexts,
                        userQueryMessage: payload.userQueryMessage
                    });

                    expect(res.body.status).to.be.equal(1);
                    expect(res.statusCode).to.be.equal(200);
                    expect(res.body.message).to.be.equal('Success');

                    assert.match(res.body.data, {
                        aiResponseMessageId: payload.aiResponseMessageId,
                        userId: 1,
                        userQueryMessageId: payload.userQueryMessageId,
                        feedback: 'positive',
                        aiResponseMessage: payload.aiResponseMessage,
                        aiResponseMessageContexts: payload.aiResponseMessageContexts,
                        userQueryMessage: payload.userQueryMessage
                    });

                    aiScreenFeedbackStub.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Get Feedback List', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        testcaseAiCompanionScreen.getAiCompanionScreenFeedbackList.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                    accessLevel: CONSTANTS.ACCESS_LEVEL.APP
                });

                request(process.env.BASE_URL)
                    .get('/conversation/ai-companion-screen/feedback-list')
                    .set({ Authorization: requestPayloadUser.token })
                    .query(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        done();
                    });
            });
        });

        it('should return empty array if no feedback is present', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP
            });

            const aiScreenFeedbackStub = sinon.stub(AiCompanionScreenFeedbackModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        limit: sinon.stub().returns({
                            exec: sinon.stub().resolves([])
                        })
                    })
                })
            });

            request(process.env.BASE_URL)
                .get('/conversation/ai-companion-screen/feedback-list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    assert.calledOnce(aiScreenFeedbackStub);

                    expect(res.body.status).to.be.equal(1);
                    expect(res.statusCode).to.be.equal(200);
                    expect(res.body.message).to.be.equal('Success');

                    assert.match(res.body.data, {
                        startAiResponseMessageId: null,
                        feedbackList: []
                    });

                    aiScreenFeedbackStub.restore();
                    done();
                });
        });

        it('should return feedback list if feedback is present with last key', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP
            });

            const response = [
                {
                    id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    userId: 1,
                    userQueryMessageId: payload.userQueryMessageId,
                    feedback: 'positive',
                    aiResponseMessage: payload.aiResponseMessage,
                    aiResponseMessageContexts: payload.aiResponseMessageContexts,
                    userQueryMessage: payload.userQueryMessage
                },
                {
                    id: '38325e0b-fed4-42a9-8d45-36f9a445c608',
                    userId: 1,
                    userQueryMessageId: payload.userQueryMessageId,
                    feedback: 'negative',
                    aiResponseMessage: payload.aiResponseMessage,
                    aiResponseMessageContexts: payload.aiResponseMessageContexts,
                    userQueryMessage: payload.userQueryMessage
                }
            ];

            response.lastKey = {
                aiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c608',
                userId: 1
            };

            const execStub = sinon.stub().resolves(response);

            const aiScreenFeedbackStub = sinon.stub(AiCompanionScreenFeedbackModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        limit: sinon.stub().returns({
                            startAt: sinon.stub().returns({
                                exec: execStub
                            }),
                            exec: execStub
                        })
                    })
                })
            });

            request(process.env.BASE_URL)
                .get('/conversation/ai-companion-screen/feedback-list')
                .set({ Authorization: requestPayloadUser.token })
                .query(payloadGetFeedbackList)
                .end((err, res) => {
                    assert.calledOnce(aiScreenFeedbackStub);

                    expect(res.body.status).to.be.equal(1);
                    expect(res.statusCode).to.be.equal(200);
                    expect(res.body.message).to.be.equal('Success');

                    assert.match(res.body.data, {
                        startAiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c608',
                        feedbackList: [
                            {
                                userQueryMessage: {
                                    id: '38325e0b-fed4-42a9-8d45-36f9a445c608',
                                    senderId: 1,
                                    message: 'Hello'
                                },
                                aiResponseMessage: {
                                    message: 'Hello, how can I assist you today?',
                                    contexts: '[{"source": {"url": "https://test.source.com"}}]',
                                    userQueryMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c608'
                                },
                                feedback: 'positive'
                            },
                            {
                                userQueryMessage: {
                                    id: '38325e0b-fed4-42a9-8d45-36f9a445c608',
                                    senderId: 1,
                                    message: 'Hello'
                                },
                                aiResponseMessage: {
                                    message: 'Hello, how can I assist you today?',
                                    contexts: '[{"source": {"url": "https://test.source.com"}}]',
                                    userQueryMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c608'
                                },
                                feedback: 'negative'
                            }
                        ]
                    });

                    aiScreenFeedbackStub.restore();
                    done();
                });
        });

        it('should return feedback list if feedback is present without last key', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP
            });

            const response = [
                {
                    id: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    userId: 1,
                    userQueryMessageId: payload.userQueryMessageId,
                    feedback: 'positive',
                    aiResponseMessage: payload.aiResponseMessage,
                    aiResponseMessageContexts: payload.aiResponseMessageContexts,
                    userQueryMessage: payload.userQueryMessage
                }
            ];

            const execStub = sinon.stub().resolves(response);

            const aiScreenFeedbackStub = sinon.stub(AiCompanionScreenFeedbackModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        limit: sinon.stub().returns({
                            startAt: sinon.stub().returns({
                                exec: execStub
                            }),
                            exec: execStub
                        })
                    })
                })
            });

            request(process.env.BASE_URL)
                .get('/conversation/ai-companion-screen/feedback-list')
                .set({ Authorization: requestPayloadUser.token })
                .query({ pageSize: 1 })
                .end((err, res) => {
                    assert.calledOnce(aiScreenFeedbackStub);

                    expect(res.body.status).to.be.equal(1);
                    expect(res.statusCode).to.be.equal(200);
                    expect(res.body.message).to.be.equal('Success');

                    assert.match(res.body.data, {
                        startAiResponseMessageId: null,
                        feedbackList: [
                            {
                                userQueryMessage: {
                                    id: '38325e0b-fed4-42a9-8d45-36f9a445c608',
                                    senderId: 1,
                                    message: 'Hello'
                                },
                                aiResponseMessage: {
                                    message: 'Hello, how can I assist you today?',
                                    contexts: '[{"source": {"url": "https://test.source.com"}}]',
                                    userQueryMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c608'
                                },
                                feedback: 'positive'
                            }
                        ]
                    });

                    aiScreenFeedbackStub.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
