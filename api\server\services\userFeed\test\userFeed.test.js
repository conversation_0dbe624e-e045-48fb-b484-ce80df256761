const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
const User = require('../../../models/user.model');
const sinon = require('sinon');
chai.use(chaiHttp);
const jwt = require('jsonwebtoken');
const UserFeedService = require('../userFeedService');
const Utils = require('../../../util/utilFunctions');
const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
/* User Token */
const user = {
    id: '95285616-3769-4b2e-b36b-898597b8146e',
    email: '<EMAIL>'
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'testingjwt', tokenOptionalInfo)
};
Utils.addCommonReqTokenForHMac(request);
describe('User feed', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP });
        });
        after(async () => {
            sinon.restore();
        });
        it('Should get user feeds', (done) => {
            request(process.env.BASE_URL)
                .get('/user/feeds')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });
        it('Should handle error in getFeeds', (done) => {
            const getUserDetailsStub = sinon.stub(UserFeedService, 'getUserFeeds');
            getUserDetailsStub.throws(new Error('Something went wrong'));
            request(process.env.BASE_URL)
                .get('/user/feeds')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    getUserDetailsStub.restore();
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
