const sinon = require('sinon');
const FeedService = require('../server/feedService');
const EventSignups = require('../server/models/eventSignup.model');
const { expect } = require('chai');
const { afterEach } = require('mocha');
const Users = require('../server/models/user.model');
const CONSOLE_LOGGER = require('../server/logger');
const Messages = require('../server/models/message.model');
const ConversationService = require('../server/ConversationService');
const groupMembersModel = require('../server/models/groupMembers.model');
const ConversationGroupService = require('../server/ConversationGroupService');

describe('Util functions', () => {
    afterEach(() => {
        sinon.restore();
    });

    it('should get signed in empty signed in users', async () => {
        const eventId = 'eventId';
        sinon.stub(EventSignups, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([])
                    })
                })
            })
        });

        const users = await FeedService.getSignedInUsers(eventId);
        expect(users).to.be.an.instanceOf(Array);
        expect(users).to.have.lengthOf(0);
    });

    it('should get signed in users', async () => {
        const eventId = 'eventId';

        sinon.stub(EventSignups, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ parentId: 'userId' }])
                    })
                })
            })
        });

        sinon.stub(Users, 'query').resolves([{ id: 'userId', fcmToken: 'fcmToken' }]);

        const users = await FeedService.getSignedInUsers(eventId);
        expect(users).to.be.an.instanceOf(Array);
        expect(users).to.have.lengthOf(1);
        expect(users[0][0]).to.have.property('fcmToken', 'fcmToken');
        expect(users).to.deep.equal([[{ id: 'userId', fcmToken: 'fcmToken' }]]);
    });

    it('should return user if found in allGroupMembersUserDetails', async () => {
        const userId = 'userId';
        const allGroupMembersUserDetails = [{ id: 'userId' }];

        const result = await ConversationGroupService.getUserDetails(allGroupMembersUserDetails, userId);
        expect(result).to.deep.equal({ id: 'userId' });
    });

    it('should fetch user details if user not found in allGroupMembersUserDetails', async () => {
        const userId = 'userId';
        const allGroupMembersUserDetails = [];

        sinon.stub(Users, 'query').returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().resolves([{ id: 'userId', firstName: 'firstName', lastName: 'lastName' }])
                })
            })
        });

        const result = await ConversationGroupService.getUserDetails(allGroupMembersUserDetails, userId);
        expect(result).to.deep.equal({ id: 'userId', firstName: 'firstName', lastName: 'lastName' });
    });

    it('should log error if user not found in getUserDetails', async () => {
        const userId = 'userId';
        const allGroupMembersUserDetails = [];

        sinon.stub(Users, 'query').returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().resolves([])
                })
            })
        });

        const errorLogStub = sinon.stub(CONSOLE_LOGGER, 'error');

        await ConversationGroupService.getUserDetails(allGroupMembersUserDetails, userId);
        sinon.assert.calledWith(errorLogStub, 'User not found for id:', userId);
    });

    it('should fetch messages for group', async () => {
        const groupId = 'groupId';

        const messagesArray = [
            { id: 'messageId', message: 'message' },
            {
                id: 'messageId',
                message: 'message',
                mediaName: 'mediaName',
                mediaThumbnailName: 'mediaThumbnailName',
                replyMessage: {
                    mediaName: 'mediaName'
                }
            }
        ];

        messagesArray.lastKey = 'lastKey';

        sinon.stub(Messages, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    sort: sinon.stub().returns({
                        limit: sinon.stub().returns({
                            exec: sinon.stub().resolves(messagesArray)
                        })
                    })
                })
            })
        });

        sinon.stub(ConversationService, 'getReactionsForMessage').resolves({
            messageId: {
                userId: {
                    messageId: 'messageId',
                    reaction: 'reaction',
                    userId: 'userId'
                }
            }
        });

        const messages = await ConversationGroupService.fetchMessages(groupId);
        expect(messages).to.be.an.instanceOf(Object);
        expect(messages.messages).to.be.an.instanceOf(Array);
        expect(messages.messages).to.have.lengthOf(2);
        expect(messages.messages).to.deep.equal([
            { id: 'messageId', message: 'message' },
            {
                id: 'messageId',
                message: 'message',
                mediaThumbnailUrl: undefined,
                mediaUrl: undefined,
                replyMessage: {
                    mediaUrl: undefined
                }
            }
        ]);

        expect(messages.reactions).to.be.an.instanceOf(Object);
        expect(messages.reactions).to.deep.equal({
            messageId: {
                userId: {
                    messageId: 'messageId',
                    reaction: 'reaction',
                    userId: 'userId'
                }
            }
        });

        expect(messages.lastEvaluatedKey).to.equal('lastKey');
    });

    it('should create group details', async () => {
        const membership = {
            id: 'membershipId',
            groupId: 'groupId',
            userId: 'userId',
            isAdmin: true,
            childrenIds: ['childrenId'],
            muteConversation: false
        };
        const groupDetails = {
            id: 'groupId',
            organizationMetaData: {
                logo: 'logo',
                name: 'name'
            },
            groupType: 'organization',
            organizationId: 'organizationId'
        };
        const isOrganizationGroup = true;
        const groupMembers = [{ userId: 'userId', firstName: 'firstName', lastName: 'lastName' }];
        const messagesData = {
            messages: [],
            lastEvaluatedKey: 'lastKey',
            reactions: {}
        };

        const result = await ConversationGroupService.createGroupDetails(
            membership, groupDetails, isOrganizationGroup, groupMembers, messagesData
        );
        expect(result).to.be.an.instanceOf(Object);

        expect(result).to.deep.equal({
            groupDetails: {
                groupId: membership.groupId,
                groupName: groupDetails.organizationMetaData.name,
                isAdmin: membership.isAdmin,
                childrenIds: membership.childrenIds,
                eventEndDate: undefined,
                eventId: undefined,
                eventStartDate: undefined,
                groupImage: undefined,
                groupType: 'organization',
                lastReadMessage: undefined,
                muteConversation: false,
                organizationId: groupDetails.organizationId
            },
            groupMembers: groupMembers,
            groupMessages: messagesData.messages,
            reactions: messagesData.reactions,
            lastEvaluatedKey: messagesData.lastEvaluatedKey
        });
    });

    it('should create group details for event group', async () => {
        const membership = {
            id: 'membershipId',
            groupId: 'groupId',
            userId: 'userId',
            isAdmin: true,
            childrenIds: ['childrenId'],
            muteConversation: false
        };
        const groupDetails = {
            id: 'groupId',
            eventMetaData: {
                title: 'name',
                startDateTime: 'eventStartDate',
                endDateTime: 'eventEndDate'
            },
            groupType: 'event',
            eventId: 'eventId'
        };
        const isOrganizationGroup = false;
        const groupMembers = [{ userId: 'userId1', firstName: 'firstName', lastName: 'lastName' }];
        const messagesData = {
            messages: [],
            lastEvaluatedKey: 'lastKey',
            reactions: {}
        };

        const result = await ConversationGroupService.createGroupDetails(
            membership, groupDetails, isOrganizationGroup, groupMembers, messagesData
        );
        expect(result).to.be.an.instanceOf(Object);

        expect(result).to.deep.equal({
            groupDetails: {
                groupId: membership.groupId,
                groupName: groupDetails.eventMetaData.title,
                isAdmin: membership.isAdmin,
                childrenIds: membership.childrenIds,
                eventEndDate: groupDetails.eventMetaData.endDateTime,
                eventId: groupDetails.eventId,
                eventStartDate: groupDetails.eventMetaData.startDateTime,
                groupImage: null,
                groupType: 'event',
                lastReadMessage: undefined,
                muteConversation: false,
                organizationId: undefined
            },
            groupMembers: [groupMembers[0], membership],
            groupMessages: messagesData.messages,
            reactions: messagesData.reactions,
            lastEvaluatedKey: messagesData.lastEvaluatedKey
        });
    });

    it('should handle handleGroupMemberStatus', async () => {
        const user = {
            id: 'userId'
        };
        const orgGroup = {
            groupId: 'orgGroupId'
        };
        const groupMember = {
            id: 'groupMemberId'
        };

        sinon.stub(groupMembersModel, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    exec: sinon.stub().resolves([
                        { id: 'groupMemberId', status: 'active', userId: 'userId' },
                        { id: 'groupMemberId1', status: 'active', userId: 'userId1' }
                    ])
                })
            })
        });

        sinon.stub(Users, 'query').returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub()
                        .onFirstCall().resolves([{ id: 'userId', firstName: 'firstName', lastName: 'lastName' }])
                        .onSecondCall().resolves([])
                        .onThirdCall().resolves([{ id: 'userId1', firstName: 'firstName1', lastName: 'lastName1' }])
                })
            })
        });

        sinon.stub(ConversationService, 'getActiveSocketConnections').resolves([]);
        sinon.stub(ConversationService, 'sendMessagesToConnections').resolves();
        sinon.stub(ConversationGroupService, 'fetchMessages').resolves({});
        sinon.stub(ConversationGroupService, 'createGroupDetails').resolves({});

        await ConversationGroupService.handleGroupMemberStatus(user, orgGroup, groupMember);

        sinon.assert.called(ConversationService.getActiveSocketConnections);
        sinon.assert.called(ConversationService.sendMessagesToConnections);
        sinon.assert.called(ConversationGroupService.fetchMessages);
    });

    it('should remove user from group', async () => {
        const saveStub = sinon.stub();

        const isUserInGroup = [{
            id: 'groupMemberId',
            userId: 'userId',
            childrenIds: ['childId'],
            isAdmin: true,
            groupId: 'orgGroupId',
            save: saveStub
        }];
        const child = {
            id: 'childId'
        };

        saveStub.resolves();
        sinon.stub(ConversationService, 'getGroupMemberIds').resolves([]);
        sinon.stub(ConversationService, 'sendMessagesToConnections').resolves();

        const result = await ConversationGroupService.removeUserFromGroup(isUserInGroup, child);

        sinon.assert.called(saveStub);
        sinon.assert.called(ConversationService.getGroupMemberIds);
        sinon.assert.called(ConversationService.sendMessagesToConnections);
        expect(result).to.be.true;
    });

    it('should add guardians to group', async () => {
        const guardians = ['userId1', 'userId2'];
        const groups = [
            {
                groupId: 'groupId1',
                groupType: 'organization',
                organizationMetaData: {
                    logo: 'groupLogo'
                }
            },
            {
                groupId: 'groupId2',
                groupType: 'event',
                eventMetaData: { title: 'eventName', image: 'eventImage' }
            },
            null
        ];
        const child = {
            id: 'childId'
        };

        sinon.stub(groupMembersModel, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    exec: sinon.stub().resolves([
                        {
                            id: 'groupMemberId',
                            userId: 'userId',
                            childrenIds: ['childId'],
                            isAdmin: true,
                            groupId: 'groupId1'
                        },
                        {
                            id: 'groupMemberId1',
                            userId: 'userId1',
                            childrenIds: ['childId1'],
                            isAdmin: false,
                            groupId: 'groupId1'
                        }
                    ])
                })
            })
        });

        sinon.stub(Users, 'query').returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().resolves([{ id: 'userId', firstName: 'firstName1', lastName: 'lastName1' }])
                })
            })
        });

        sinon.stub(groupMembersModel, 'batchPut').resolves();
        sinon.stub(ConversationService, 'getActiveSocketConnections').resolves([]);
        sinon.stub(ConversationService, 'sendMessagesToConnections').resolves();
        sinon.stub(ConversationGroupService, 'getUserDetails').resolves({});
        sinon.stub(ConversationGroupService, 'fetchMessages').resolves([]);

        const result = await ConversationGroupService.addGuardiansToGroups(guardians, groups, child);

        sinon.assert.called(groupMembersModel.query);
        sinon.assert.called(Users.query);
        sinon.assert.called(groupMembersModel.batchPut);
        sinon.assert.called(ConversationService.getActiveSocketConnections);
        sinon.assert.called(ConversationService.sendMessagesToConnections);
        sinon.assert.called(ConversationGroupService.getUserDetails);
        sinon.assert.called(ConversationGroupService.fetchMessages);
        expect(result).to.be.true;
    });

    it('should remove guardians from group', async () => {
        const guardians = ['userId1', 'userId2'];
        const groups = [
            { groupId: 'groupId1' },
            { groupId: 'groupId2' },
            null
        ];
        const child = {
            id: 'childId'
        };

        sinon.stub(groupMembersModel, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    exec: sinon.stub().resolves([
                        {
                            id: 'groupMemberId',
                            userId: 'userId',
                            childrenIds: ['childId'],
                            isAdmin: true,
                            groupId: 'groupId1'
                        },
                        {
                            id: 'groupMemberId1',
                            userId: 'userId1',
                            childrenIds: [],
                            isAdmin: false,
                            groupId: 'groupId1'
                        }
                    ])
                })
            })
        });

        sinon.stub(Users, 'query').returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().resolves([{ id: 'userId', firstName: 'firstName1', lastName: 'lastName1' }])
                })
            })
        });

        sinon.stub(groupMembersModel, 'batchPut').resolves();
        sinon.stub(ConversationService, 'getActiveSocketConnections').resolves([]);
        sinon.stub(ConversationService, 'sendMessagesToConnections').resolves();
        sinon.stub(ConversationGroupService, 'getUserDetails').resolves({});
        sinon.stub(ConversationGroupService, 'fetchMessages').resolves([]);

        await ConversationGroupService.removeGuardiansFromGroups(guardians, groups, child);

        sinon.assert.called(groupMembersModel.query);
        sinon.assert.called(groupMembersModel.batchPut);
    });

    it('add members to organization group', async () => {
        const members = [{ id: 'userId' }, { id: 'userId2' }];
        const orgGroups = [
            {
                groupId: 'groupId1',
                organizationMetaData: {
                    logo: 'logo'
                }
            },
            { groupId: 'groupId2' }
        ];

        sinon.stub(groupMembersModel, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    exec: sinon.stub().resolves([
                        {
                            id: 'groupMemberId',
                            userId: 'userId',
                            childrenIds: ['childId'],
                            isAdmin: true,
                            groupId: 'groupId1'
                        },
                        {
                            id: 'groupMemberId1',
                            userId: 'userId1',
                            childrenIds: [],
                            isAdmin: false,
                            groupId: 'groupId1'
                        }
                    ])
                })
            })
        });

        sinon.stub(Users, 'query').returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub()
                        .onFirstCall().resolves([{ id: 'userId', firstName: 'firstName1', lastName: 'lastName1' }])
                        .onSecondCall().resolves([])
                        .onThirdCall().resolves([{ id: 'userId1', firstName: 'firstName2', lastName: 'lastName2' }])
                        .onCall(4).resolves([
                            { id: 'userId2', firstName: 'firstName3', lastName: 'lastName3' },
                            { id: 'userId', firstName: 'firstName4', lastName: 'lastName4' }
                        ])
                        .onCall(5).resolves([
                            { id: 'userId2', firstName: 'firstName5', lastName: 'lastName5' },
                            { id: 'userId', firstName: 'firstName6', lastName: 'lastName6' }
                        ])
                })
            })
        });

        sinon.stub(groupMembersModel, 'batchPut').resolves();

        sinon.stub(ConversationService, 'getActiveSocketConnections').resolves([]);
        sinon.stub(ConversationService, 'sendMessagesToConnections').resolves([]);
        sinon.stub(ConversationGroupService, 'fetchMessages').resolves([]);
        sinon.stub(ConversationGroupService, 'createGroupDetails').resolves({});

        await ConversationGroupService.addMembersToOrganizationGroups(members, orgGroups);

        sinon.assert.called(groupMembersModel.query);
        sinon.assert.called(Users.query);
        sinon.assert.called(groupMembersModel.batchPut);
        sinon.assert.called(ConversationService.getActiveSocketConnections);
        sinon.assert.called(ConversationService.sendMessagesToConnections);
        sinon.assert.called(ConversationGroupService.fetchMessages);
        sinon.assert.called(ConversationGroupService.createGroupDetails);
    });
});
