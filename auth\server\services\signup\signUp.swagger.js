/**
 *  routes and schema for SignUp
 */

/**
 * @openapi
 * components:
 *  schemas:
 *      userSignUp:
 *          type: object
 *          required:
 *              - email
 *              - password
 *              - firstName
 *              - lastName
 *          properties:
 *              email:
 *                  $ref: '#/components/messageDefinition/properties/email'
 *              password:
 *                  $ref: '#/components/messageDefinition/properties/password'
 *              firstName:
 *                  type: string
 *                  description: first name of the user
 *              lastName:
 *                  type: string
 *                  description: last name of the user
 *              phoneNumber:
 *                  type: string
 *                  description: mobile number of the user
 *              countryCode:
 *                  type: string
 *                  description: country code of the user
 *              role:
 *                  type: string
 *                  description: role of the user
 *
 *          example:
 *                   email: <EMAIL>
 *                   password: Password@123
 *                   firstName: Sam
 *                   lastName: Jones
 *                   phoneNumber: '8978546945'
 *                   countryCode: '+91'
 *                   role: 1
 *
 */

/**
 * @openapi
 * /auth/signup:
 *  post:
 *      tags: [Authentication]
 *      summary: user signup
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/userSignUp'
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successUserRegister'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/errorBadRequest'
 *          422:
 *              description: User duplicate
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/errorUserRegister'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /auth/verify-account:
 *  post:
 *      tags: [Authentication]
 *      summary: User name verification
 *      requestBody:
 *          required: true
 *          description: Body parameter
 *          content:
 *              application/json:
 *                  schema:
 *                      allOf:
 *                          - $ref: '#/components/schemas/userVerify'
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successVerifyUser'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/errorBadRequest'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /auth/resend-otp:
 *  post:
 *      tags: [Authentication]
 *      summary: To resend verification code in case of email is not received in the first attempt
 *      requestBody:
 *          required: true
 *          description: Body parameter
 *          content:
 *              application/json:
 *                  schema:
 *                      allOf:
 *                          - $ref: '#/components/schemas/resendOTPEmail'
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successResendOTP'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/errorBadRequest'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */
