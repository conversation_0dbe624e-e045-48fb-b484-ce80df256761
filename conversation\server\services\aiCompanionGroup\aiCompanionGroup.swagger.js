/**
 * Swagger schema for AI Companion Group.
 */

/**
 * @openapi
 * components:
 *      schemas:
 *          addUpdateAiCompanionGroupFeedback:
 *              type: object
 *              properties:
 *                  aiResponseMessageId:
 *                      type: string
 *                      description: AI Response Message Id
 *                  userQueryMessageId:
 *                      type: string
 *                      description: User Query Message Id
 *                  groupId:
 *                      type: string
 *                      description: Group Id
 *                  feedback:
 *                      type: string
 *                      description: Feedback
 *              example:
 *                  aiResponseMessageId: uuid
 *                  userQueryMessageId: uuid
 *                  groupId: uuid
 *                  feedback: positive
 *
 *          successAddUpdateAiCompanionGroupFeedback:
 *              type: object
 *              properties:
 *                  status:
 *                      $ref: '#/components/messageDefinition/properties/status'
 *                  message:
 *                      $ref: '#/components/messageDefinition/properties/message'
 *                  data:
 *                      type: object
 *                      properties:
 *                          aiResponseMessageId:
 *                              type: string
 *                              description: AI Response Message Id
 *                          userQueryMessageId:
 *                              type: string
 *                              description: User Query Message Id
 *                          groupId:
 *                              type: string
 *                              description: Group Id
 *                          feedback:
 *                              type: string
 *                              description: Feedback
 *                          createdAt:
 *                              type: string
 *                              description: Created At
 *                          updatedAt:
 *                              type: string
 *                              description: Updated At
 *              example:
 *                  status: 1
 *                  message: Success
 *                  data:
 *                      aiResponseMessageId: uuid
 *                      userQueryMessageId: uuid
 *                      groupId: uuid
 *                      feedback: positive
 *                      createdAt: timestamp
 *                      updatedAt: timestamp
 *
 *          successGetAiCompanionGroupFeedbackList:
 *              type: object
 *              properties:
 *                  status:
 *                      $ref: '#/components/messageDefinition/properties/status'
 *                  message:
 *                      $ref: '#/components/messageDefinition/properties/message'
 *                  data:
 *                      type: object
 *                      properties:
 *                          startAiResponseMessageId:
 *                              type: string
 *                              description: Start AI Response Message Id
 *                          startUserQueryMessageId:
 *                              type: string
 *                              description: Start User Query Message Id
 *                          feedbackList:
 *                              type: array
 *                              description: Feedback List
 *                              items:
 *                                  type: object
 *                                  properties:
 *                                      userQueryMessage:
 *                                          type: object
 *                                          properties:
 *                                              id:
 *                                                  type: string
 *                                                  description: User Query Message Id
 *                                              senderId:
 *                                                  type: string
 *                                                  description: Sender Id
 *                                              message:
 *                                                  type: string
 *                                                  description: Message
 *                                              createdAt:
 *                                                  type: string
 *                                                  description: Created At
 *                                              updatedAt:
 *                                                  type: string
 *                                                  description: Updated At
 *                                      aiResponseMessage:
 *                                          type: object
 *                                          properties:
 *                                              id:
 *                                                  type: string
 *                                                  description: AI Response Message Id
 *                                              senderId:
 *                                                  type: string
 *                                                  description: Sender Id
 *                                              message:
 *                                                  type: string
 *                                                  description: Message
 *                                              createdAt:
 *                                                  type: string
 *                                                  description: Created At
 *                                              updatedAt:
 *                                                  type: string
 *                                                  description: Updated At
 *                                              contexts:
 *                                                  type: string
 *                                                  description: Contexts
 *                                              userQueryMessageId:
 *                                                  type: string
 *                                                  description: User Query Message Id
 *                                      feedback:
 *                                          type: string
 *                                          description: Feedback
 *              example:
 *                  status: 1
 *                  message: Success
 *                  data:
 *                      startAiResponseMessageId: uuid
 *                      startUserQueryMessageId: uuid
 *                      feedbackList:
 *                          -
 *                              userQueryMessage:
 *                                  id: uuid
 *                                  senderId: uuid
 *                                  message: string
 *                                  createdAt: timestamp
 *                                  updatedAt: timestamp
 *                              aiResponseMessage:
 *                                  id: uuid
 *                                  senderId: uuid
 *                                  message: string
 *                                  createdAt: timestamp
 *                                  updatedAt: timestamp
 *                                  contexts: string
 *                                  userQueryMessageId: uuid
 *                              feedback: positive
*/

/**
 * @openapi
 * /conversation/ai-companion-group/feedback-list:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: query
 *          name: groupId
 *          schema:
 *            type: string
 *          description: Group Id
 *          required: true
 *        - in: query
 *          name: pageSize
 *          schema:
 *            type: number
 *          description: Page Size
 *          required: false
 *        - in: query
 *          name: startAiResponseMessageId
 *          schema:
 *            type: string
 *          description: Start AI Response Message Id
 *          required: false
 *        - in: query
 *          name: startUserId
 *          schema:
 *            type: string
 *          description: Start User Id
 *          required: false
 *        - in: header
 *          name: reqtoken
 *          schema:
 *            type: string
 *          description: Request token
 *          required: true
 *      tags: [AI Companion Group]
 *      summary: Get AI Companion Group Feedback List
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successGetAiCompanionGroupFeedbackList'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /conversation/ai-companion-group/add-update-feedback:
 *  post:
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: header
 *          name: reqtoken
 *          schema:
 *            type: string
 *          description: Request token
 *          required: true
 *      tags: [AI Companion Group]
 *      summary: Add/Update AI Companion Group Feedback
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/addUpdateAiCompanionGroupFeedback'
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successAddUpdateAiCompanionGroupFeedback'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */
