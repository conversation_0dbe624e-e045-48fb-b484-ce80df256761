const sinon = require('sinon');
const ConversationService = require('../server/ConversationService');
const GroupMembers = require('../server/models/groupMembers.model');
const { expect } = require('chai');
const { afterEach } = require('mocha');
const SocketConnections = require('../server/models/socketConnections.model');
const { ApiGatewayManagementApiClient } = require('@aws-sdk/client-apigatewaymanagementapi');
const MessageReactions = require('../server/models/messageReactions.model');

describe('ConversationService', () => {
    afterEach(() => {
        sinon.restore();
    });

    it('should get group member ids', async () => {
        const groupId = 'groupId1';
        sinon.stub(GroupMembers, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().returns([{ userId: 'userId1' }, { userId: 'userId2' }])
                    })
                })
            })
        });

        const memberIds = await ConversationService.getGroupMemberIds(groupId);
        expect(memberIds).to.be.an.instanceOf(Array);
        expect(memberIds).to.have.lengthOf(2);
        expect(memberIds).to.deep.equal(['userId1', 'userId2']);
    });

    it('should get active socket connections', async () => {
        const groupMemberIds = ['userId1', 'userId2'];
        sinon.stub(SocketConnections, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    exec: sinon.stub()
                        .onFirstCall().resolves([
                            { userId: 'userId1', id: 'socketConnectionId1' },
                            { userId: 'userId1', id: 'socketConnectionId2' }
                        ])
                        .onSecondCall().resolves([
                            { userId: 'userId2', id: 'socketConnectionId3' }
                        ])
                })
            })
        });

        const activeSocketConnections = await ConversationService.getActiveSocketConnections(groupMemberIds);
        expect(activeSocketConnections).to.be.an.instanceOf(Array);
        expect(activeSocketConnections).to.have.lengthOf(3);
        expect(activeSocketConnections).to.deep.equal([
            { userId: 'userId1', id: 'socketConnectionId1' },
            { userId: 'userId1', id: 'socketConnectionId2' },
            { userId: 'userId2', id: 'socketConnectionId3' }
        ]);
    });

    it('should send message to socket connections', async () => {
        const socketConnections = [
            { userId: 'userId1', id: 'socketConnectionId1' },
            { userId: 'userId1', id: 'socketConnectionId2' },
            { userId: 'userId2', id: 'socketConnectionId3' }
        ];
        const message = { messageId: 'messageId1', message: 'test message' };
        const sendStub = sinon.stub(ApiGatewayManagementApiClient.prototype, 'send');

        sendStub.onFirstCall().resolves();
        sendStub.onSecondCall().rejects({ statusCode: 410, message: 'Test error' });
        sendStub.onThirdCall().rejects({ statusCode: 500, message: 'Test error' });

        const deleteStub = sinon.stub(SocketConnections, 'delete').resolves();

        await ConversationService.sendMessagesToConnections(socketConnections, message);
        sinon.assert.callCount(sendStub, 3);
        sinon.assert.calledOnce(deleteStub);
    });

    it('should get reactions for message if message ids are not provided', async () => {
        const reactions = await ConversationService.getReactionsForMessage({});

        expect(reactions).to.be.an.instanceOf(Object);
        expect(reactions).to.deep.equal({});
    });

    it('should get reactions for message if message ids are provided', async () => {
        const messageIds = ['messageId1', 'messageId2'];

        sinon.stub(MessageReactions, 'query').returns({
            eq: sinon.stub().returns({
                exec: sinon.stub().resolves(
                    [
                        { messageId: 'messageId1', reaction: 'reaction1', userId: 'userId1' },
                        { messageId: 'messageId2', reaction: 'reaction2', userId: 'userId2' }
                    ]
                )
            })
        });

        const reactions = await ConversationService.getReactionsForMessage({ messageIds });

        expect(reactions).to.be.an.instanceOf(Object);
        expect(reactions).to.deep.equal({
            messageId1: {
                userId1: {
                    reaction: 'reaction1',
                    userId: 'userId1',
                    messageId: 'messageId1'
                }
            },
            messageId2: {
                userId2: {
                    reaction: 'reaction2',
                    userId: 'userId2',
                    messageId: 'messageId2'
                }
            }
        });
    });

    it('should handle error when getting reactions for message', async () => {
        const messageIds = ['messageId1', 'messageId2'];
        sinon.stub(MessageReactions, 'query').throws(new Error('Test error'));

        const reactions = await ConversationService.getReactionsForMessage({ messageIds });
        expect(reactions).to.be.an.instanceOf(Object);
        expect(reactions).to.deep.equal({});
    });
});
