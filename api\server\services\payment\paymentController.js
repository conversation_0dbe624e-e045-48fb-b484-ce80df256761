const PaymentService = require('./paymentService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for payment.
 */
class PaymentController {
    /**
     * @desc This function is being used for payment to resend account link to org.
     * <AUTHOR>
     * @since 09/11/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async resendAccountLink (req, res) {
        try {
            const data = await PaymentService.resendAccountLink(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.MAIL_SENT_SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in resendAccountLink', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used for payment webhooks for stripe connect account creation and updation.
     * <AUTHOR>
     * @since 09/11/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async stripeWebhookConnect (req, res) {
        try {
            await PaymentService.stripeWebhookConnect(req, res, req.locale);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in stripeWebhookConnect', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = PaymentController;
