const eventSchema = {
    'index': 'events',
    'body': {
        'mappings': {
            'properties': {
                'id': {
                    'type': 'keyword',
                    'index': true
                },
                'title': {
                    'type': 'text',
                    'fields': {
                        'keyword': {
                            'type': 'keyword',
                            'ignore_above': 256
                        }
                    }
                },
                'details': {
                    'properties': {
                        'details': {
                            'type': 'text'
                        },
                        'startDateTime': {
                            'type': 'date',
                            'format': 'strict_date_optional_time||epoch_millis'
                        },
                        'endDateTime': {
                            'type': 'date',
                            'format': 'strict_date_optional_time||epoch_millis'
                        },
                        'venue': {
                            'type': 'text'
                        }
                    }
                },
                'photoURL': {
                    'type': 'keyword'
                },
                'eventType': {
                    'type': 'keyword'
                },
                'eventScope': {
                    'type': 'keyword'
                },
                'organizationId': {
                    'type': 'keyword'
                },
                'isPaid': {
                    'type': 'boolean'
                },
                'isDonatable': {
                    'type': 'boolean'
                },
                'fee': {
                    'type': 'integer'
                },
                'membershipBenefitDetails': {
                    'properties': {
                        'benefitDiscount': {
                            'type': 'string'
                        },
                        'isOnlyForMembers': {
                            'type': 'boolean'
                        }
                    }
                },
                'status': {
                    'type': 'keyword'
                },
                'participants': {
                    'type': 'keyword'
                }
            }
        }
    }
};

module.exports = eventSchema;
