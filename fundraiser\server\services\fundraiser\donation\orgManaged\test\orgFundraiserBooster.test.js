const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const sinon = require('sinon');
const User = require('../../../../../models/user.model');
const jwt = require('jsonwebtoken');
const Utils = require('../../../../../util/utilFunctions');
const TestCase = require('./testcaseFundraiserBooster');
const fundraiserModel = require('../../../../../models/fundraiser.model');
const moment = require('moment');
const organizationModel = require('../../../../../models/organization.model');
const orgManagedFundraiserBoosterDonationModel = require('../../../../../models/orgManagedFundraiserBoosterDonation.model');
const fundraiserSignupModel = require('../../../../../models/fundraiserSignup.model');
const childModel = require('../../../../../models/child.model');

const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
/* User Token */
const user = {
    id: '95285616-3769-4b2e-b36b-898597b8146e',
    email: '<EMAIL>'
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'testingjwt', tokenOptionalInfo)
};

Utils.addCommonReqTokenForHMac(request);

describe('Donation', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should handle error if token is not passed', (done) => {
            request(process.env.BASE_URL)
                .post('/fundraiser/orgBooster/webhook')
                .set({ 'stripe-signature': 'test_signature' })
                .send({})
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        TestCase.getBoosterDetails.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                    associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
                });
                request(process.env.BASE_URL)
                    .get('/fundraiser/orgBooster/details')
                    .query(data.options)
                    .set({ Authorization: requestPayloadUser.token })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should not get booster details if fundraiser is expired', (done) => {
            const fundraiserId = 'cf972fee-97a9-401c-a84a-6d2da501d21e';
            const childId = 'childId1';

            sinon.stub(fundraiserModel, 'get').resolves({
                organizationId: 'organizationId',
                title: 'title',
                description: 'description',
                boosterGoal: 'boosterGoal',
                startDate: 'startDate',
                endDate: 'endDate',
                imageURL: 'imageURL',
                status: 'expired'
            });

            request(process.env.BASE_URL)
                .get('/fundraiser/orgBooster/details')
                .query({ fundraiserId, childId })
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    fundraiserModel.get.restore();
                    done();
                });
        });

        it('As a user I should not get booster details if fundraiser end date is in the past', (done) => {
            const fundraiserId = 'cf972fee-97a9-401c-a84a-6d2da501d21e';
            const childId = 'childId1';

            sinon.stub(fundraiserModel, 'get').resolves({
                organizationId: 'organizationId',
                title: 'title',
                description: 'description',
                boosterGoal: 'boosterGoal',
                startDate: moment().subtract(10, 'minutes').toDate(),
                endDate: moment().subtract(5, 'minutes').toDate(),
                imageURL: 'imageURL',
                status: 'published'
            });

            request(process.env.BASE_URL)
                .get('/fundraiser/orgBooster/details')
                .query({ fundraiserId, childId })
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    fundraiserModel.get.restore();
                    done();
                });
        });

        it('As a user I should get booster details if fundraiser end date is in the future', (done) => {
            const fundraiserId = 'cf972fee-97a9-401c-a84a-6d2da501d21e';
            const childId = 'childId1';

            sinon.stub(fundraiserModel, 'get').resolves({
                organizationId: 'organizationId',
                title: 'title',
                description: 'description',
                boosterGoal: 'boosterGoal',
                startDate: moment().add(5, 'minutes').toDate(),
                endDate: moment().add(10, 'minutes').toDate(),
                imageURL: 'imageURL',
                status: 'published',
                homeroomStats: {
                    donations: [
                        {
                            homeRoomId: 'homeRoomId',
                            donationsAmount: 100
                        }
                    ],
                    registrations: [
                        {
                            homeRoomId: 'homeRoomId',
                            registrationsAmount: 1
                        }
                    ]
                },
                raisedDonationsAmountForOrg: 100,
                childLevelStats: {
                    donations: [
                        {
                            childId: 'childId',
                            donationsAmount: 100
                        },
                        {
                            childId: 'childId1',
                            donationsAmount: 200
                        }
                    ]
                }
            });

            const organizationStub = {
                get: sinon.stub(organizationModel, 'get').resolves({
                    id: 'organizationId',
                    name: 'name',
                    logo: 'logo',
                    address: 'address',
                    city: 'city',
                    state: 'state',
                    country: 'country',
                    platformFeeCoveredBy: 'platformFeeCoveredBy',
                    minPlatformFeeAmount: 'minPlatformFeeAmount',
                    platformFee: 'platformFee'
                }),
                batchGet: sinon.stub(organizationModel, 'batchGet').resolves([
                    {
                        id: 'homeRoom',
                        name: 'homeRoom'
                    }
                ])
            };

            sinon.stub(orgManagedFundraiserBoosterDonationModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            limit: sinon.stub().returns({
                                startAt: sinon.stub().returns({
                                    attributes: sinon.stub().returns({
                                        exec: sinon.stub().resolves([
                                            {
                                                amount: 10,
                                                childId: 'childId',
                                                childName: 'childName',
                                                donorName: 'donorName',
                                                donorMessage: 'donorMessage',
                                                createdAt: 'createdAt',
                                                paymentType: 'stripe'
                                            },
                                            {
                                                amount: 20,
                                                childId: 'childId1',
                                                childName: 'childName1',
                                                donorName: 'donorName1',
                                                donorMessage: 'donorMessage1',
                                                createdAt: 'createdAt1',
                                                paymentType: 'cash'
                                            }
                                        ])
                                    })
                                })
                            })
                        })
                    })
                })
            });

            const signupQueryStub = sinon.stub(fundraiserSignupModel, 'query');

            const childSignups = [
                {
                    childId: 'childId',
                    boosterGoalForChild: 100,
                    boosterMessageForChild: 'boosterMessageForChild'
                }
            ];

            signupQueryStub.returns({
                eq: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            exec: sinon.stub().resolves(childSignups)
                        })
                    })
                })
            });

            const childModelStubs = {
                get: sinon.stub(childModel, 'get').resolves({
                    id: 'childId',
                    photoURL: 'photoURL'
                }),
                batchGet: sinon.stub(childModel, 'batchGet').resolves([
                    {
                        id: 'childId',
                        firstName: 'firstName',
                        lastName: 'lastName'
                    }
                ])
            };




            request(process.env.BASE_URL)
                .get('/fundraiser/orgBooster/details')
                .query({ fundraiserId, childId })
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    fundraiserModel.get.restore();
                    organizationStub.get.restore();
                    organizationStub.batchGet.restore();
                    orgManagedFundraiserBoosterDonationModel.query.restore();
                    fundraiserSignupModel.query.restore();
                    childModelStubs.get.restore();
                    childModelStubs.batchGet.restore();
                    done();
                });
        });

        it('As a user I should not be able to create stripe session if organization doesnt have stripe account id', (done) => {
            const childName = 'childName';
            const donorName = 'donorName';
            const donorMessage = 'donorMessage';
            const fundraiserBoosterId = 'fundraiserBoosterId';
            const fundraiserName = 'fundraiserName';
            const fundraiserDescription = 'fundraiserDescription';
            const fundraiserImageURL = 'fundraiserImageURL';
            const donationAmount = 100;
            const childId = 'childId';
            const expectDonorMatch = true;
            const organizationId = 'organizationId';

            const requestPayload = {
                childName,
                donorName,
                donorMessage,
                fundraiserBoosterId,
                fundraiserName,
                fundraiserDescription,
                fundraiserImageURL,
                donationAmount,
                childId,
                expectDonorMatch,
                organizationId
            };

            sinon.stub(organizationModel, 'get').resolves({
                paymentDetails: {
                    stripeOnboardingStatus: 'active'
                },
                platformFeeCoveredBy: 'platformFeeCoveredBy'
            });

            request(process.env.BASE_URL)
                .post('/fundraiser/orgBooster/stripeSession')
                .set({ Authorization: requestPayloadUser.token })
                .send(requestPayload)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    organizationModel.get.restore();
                    done();
                });
        });

        it('As a user I should be able to create stripe session if organization has stripe account id', (done) => {
            const childName = 'childName';
            const donorName = 'donorName';
            const donorMessage = 'donorMessage';
            const fundraiserBoosterId = 'fundraiserBoosterId';
            const fundraiserName = 'fundraiserName';
            const fundraiserDescription = 'fundraiserDescription';
            const fundraiserImageURL = 'fundraiserImageURL';
            const donationAmount = 100;
            const childId = 'childId';
            const expectDonorMatch = true;
            const organizationId = 'organizationId';

            const requestPayload = {
                childName,
                donorName,
                donorMessage,
                fundraiserBoosterId,
                fundraiserName,
                fundraiserDescription,
                fundraiserImageURL,
                donationAmount,
                childId,
                expectDonorMatch,
                organizationId
            };

            sinon.stub(organizationModel, 'get').resolves({
                paymentDetails: {
                    stripeOnboardingStatus: 'active',
                    stripeConnectAccountId: 'stripeAccountId'
                },
                platformFeeCoveredBy: 'parent'
            });

            request(process.env.BASE_URL)
                .post('/fundraiser/orgBooster/stripeSession')
                .set({ Authorization: requestPayloadUser.token })
                .send(requestPayload)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    organizationModel.get.restore();
                    done();
                });
        });

        it('As a user I should be able to get the donations for a fundraiser', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const fundraiserBoosterId = 'fundraiserId';

            sinon.stub(orgManagedFundraiserBoosterDonationModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        sort: sinon.stub().returns({
                            exec: sinon.stub().resolves([
                                { amount: 100 },
                                { amount: 200, childId: 'childId2' }
                            ])
                        })
                    })
                })
            });

            sinon.stub(childModel, 'batchGet').resolves([
                {
                    id: 'childId',
                    firstName: 'firstName',
                    lastName: 'lastName'
                },
                {
                    id: 'childId2',
                    firstName: 'firstName2',
                    lastName: 'lastName2',
                    homeRoom: 'homeRoom'
                }
            ]);

            sinon.stub(organizationModel, 'batchGet').resolves([
                {
                    id: 'organizationId',
                    name: 'organizationName'
                }
            ]);

            request(process.env.BASE_URL)
                .get('/fundraiser/orgBooster/donations')
                .query({ fundraiserBoosterId })
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgManagedFundraiserBoosterDonationModel.query.restore();
                    childModel.batchGet.restore();
                    organizationModel.batchGet.restore();
                    done();
                });
        });

        it('should handle error in getting the donations for a fundraiser', (done) => {
            const fundraiserBoosterId = 'fundraiserId';

            sinon.stub(orgManagedFundraiserBoosterDonationModel, 'query').throws(new Error('Error'));

            request(process.env.BASE_URL)
                .get('/fundraiser/orgBooster/donations')
                .query({ fundraiserBoosterId })
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgManagedFundraiserBoosterDonationModel.query.restore();
                    done();
                });
        });

        it('As a user I should get the session details for a fundraiser', (done) => {
            const sessionId = 'sessionId';

            request(process.env.BASE_URL)
                .get('/fundraiser/orgBooster/stripeSession')
                .query({ sessionId })
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error('Error in add fundraiser', error);
    }
});

describe('Manual donations', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        TestCase.addManualDonations.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                    associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
                });
                request(process.env.BASE_URL)
                    .post('/fundraiser/orgBooster/donations')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should be able to add manual donations for a fundraiser', (done) => {
            const donations = [
                {
                    childName: 'childName',
                    donorName: 'donorName',
                    donorMessage: 'donorMessage',
                    amount: 100,
                    expectDonorMatch: true
                }
            ];
            const fundraiserBoosterId = 'cf972fee-97a9-401c-a84a-6d2da501d21e';
            const requestPayload = {
                donations,
                fundraiserBoosterId
            };

            sinon.stub(orgManagedFundraiserBoosterDonationModel, 'batchPut').resolves();

            request(process.env.BASE_URL)
                .post('/fundraiser/orgBooster/donations')
                .set({ Authorization: requestPayloadUser.token })
                .send(requestPayload)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    orgManagedFundraiserBoosterDonationModel.batchPut.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error('Error in add fundraiser', error);
    }
});
