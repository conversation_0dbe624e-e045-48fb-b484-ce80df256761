// handleConnect.js
const SocketConnections = require('../../models/socketConnections.model');

module.exports = async (event) => {
    const connectionId = event.requestContext.connectionId;
    const userId = event.headers.userId;
    try {
        await SocketConnections.create({ id: connectionId, userId });
        return {
            statusCode: 200,
            body: 'Connected'
        };
    } catch (error) {
        CONSOLE_LOGGER.error('Error creating connection --> %s', error);
        return {
            statusCode: 500,
            body: 'Error creating connection'
        };
    }
};
