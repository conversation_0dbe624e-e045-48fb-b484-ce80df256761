const Fundraiser = require('./models/fundraiser.model');
const Organization = require('./models/organization.model');
const MOMENT = require('moment');
const EmailService = require('./sendEmail');

class EmailHelperService {
    /**
     * @desc This function is being used to send email receipt to user
     * <AUTHOR>
     * @since 03/10/2024
     * @param {Object} paymentIntent Payment intent
     * @param {Object} donation Donation
     */
    static async sendEmailReceiptToUser (paymentIntent, donation) {
        const { fundraiserBoosterId, amount: donationAmount } = donation;
        const { payment_method: paymentMethod, created, status: paymentStatus } = paymentIntent;
        const { billing_details: billingDetails } = paymentMethod;
        const { email } = billingDetails;
        if (paymentStatus === 'succeeded' && email) {
            const fundraiser = await Fundraiser.get({ id: fundraiserBoosterId }, { attributes: ['title', 'organizationId'] });
            if (fundraiser) {
                const organization =
                    await Organization.get({ id: fundraiser.organizationId },
                        { attributes: ['name', 'address', 'city', 'state', 'zipCode'] });

                const { name: schoolName, address: schoolAddress, city: schoolCity, state: schoolState, zipCode: schoolZip } = organization;
                const fundraiserName = fundraiser.title;
                // Convert to milliseconds
                const donationDate = MOMENT(created * 1000).utc().format('MM/DD/YYYY HH:mm');
                const subject = `Thank you for your donation to ${fundraiserName}`;
                const template = 'emailTemplates/boosterFundraiserReceipt.html';

                const templateVariables = {
                    schoolName,
                    donationAmount,
                    fundraiserName,
                    donationDate,
                    schoolAddress,
                    schoolCity,
                    schoolState,
                    schoolZip
                };
                await EmailService.prepareAndSendEmail(
                    [email],
                    subject,
                    template,
                    templateVariables
                );
            }
        }
        return donation.childId;
    }
}

module.exports = EmailHelperService;
