const CONSTANTS = require('../util/constants');
const Groups = require('../models/groups.model');
const MOMENT = require('moment');

class ConversationService {
    static async updateConversationGroups () {
        try {
            const groups = await Groups.query('groupType').eq(CONSTANTS.GROUP_CONVERSATION_TYPES.EVENT).using('groupType-index')
                .where('status').eq(CONSTANTS.GROUP_CONVERSATION_STATUS.ACTIVE).exec();
            const expiredEventGroups = groups.filter(
                group =>
                    MOMENT(group.eventMetaData?.endDateTime)
                        .add(CONSTANTS.GROUP_CONVERSATION_EXPIRATION_DAYS, 'days').isBefore(MOMENT())
            );

            CONSOLE_LOGGER.info('Expired event groups length', expiredEventGroups.length);
            CONSOLE_LOGGER.info('Expired event groups ids', expiredEventGroups.map(group => group.groupId));

            expiredEventGroups.forEach(group => {
                group.status = CONSTANTS.GROUP_CONVERSATION_STATUS.EXPIRED;
            });

            if (expiredEventGroups.length > 0) {
                await Groups.batchPut(expiredEventGroups);
            }
        } catch (error) {
            CONSOLE_LOGGER.error('Error updating conversation groups', error);
        }
    }
}

module.exports = ConversationService;
