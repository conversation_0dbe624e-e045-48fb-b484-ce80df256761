const dynamoose = require('dynamoose');

const organizationMembersSchema = new dynamoose.Schema({
    organizationId: {
        hashKey: true,
        type: String,
        required: true
    },
    users: {
        type: Array,
        schema: [{
            type: Object,
            schema: {
                id: {
                    type: String,
                    required: true
                },
                email: {
                    type: String,
                    required: true
                },
                firstName: {
                    type: String,
                    required: true
                },
                lastName: {
                    type: String
                },
                associatedOrgRole: {
                    type: String,
                    required: true
                },
                status: {
                    type: String,
                    enum: ['active', 'inactive', 'deleted'],
                    default: 'active'
                }
            }
        }],
        required: true
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('OrganizationMembers', organizationMembersSchema);
