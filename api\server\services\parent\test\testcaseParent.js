module.exports = {
    addChild: [{
        it: 'As a user I should validate if firstName is not passed',
        options: {
            firstName: '',
            lastName: 'User'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if firstName is invalid',
        options: {
            firstName: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam at purus quis odio tristique rhoncus.'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if lastName is not passed',
        options: {
            firstName: 'Test',
            lastName: ''
        },
        status: 0
    },
    {
        it: 'As a user I should validate if lastName is invalid',
        options: {
            firstName: 'Test',
            lastName: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. <PERSON>ullam at purus quis odio tristique rhoncus.'
        },
        status: 0
    },
    {
        it: 'As a user I should validate if schoolId is not passed',
        options: {
            firstName: 'Test',
            lastName: 'User',
            dob: '19/10/2005',
            gender: 'male',
            schoolId: ''
        },
        status: 0
    },
    {
        it: 'As a user I should validate if zipCode is not passed',
        options: {
            firstName: 'Test',
            lastName: 'User',
            dob: '19/10/2005',
            gender: 'male',
            schoolId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
            zipCode: ''
        },
        status: 0
    },
    {
        it: 'As a user I should validate if associatedColor is not passed',
        options: {
            firstName: 'Test',
            lastName: 'User',
            dob: '19/10/2005',
            gender: 'male',
            schoolId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
            zipCode: '12345',
            associatedColor: ''
        },
        status: 0
    },
    {
        it: 'As a user I should validate if homeroom is not passed',
        options: {
            firstName: 'Test',
            lastName: 'User',
            dob: '19/10/2005',
            gender: 'male',
            schoolId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
            zipCode: '12345',
            associatedColor: 'FACD01',
            homeroomId: ''
        },
        status: 0
    }]
};
