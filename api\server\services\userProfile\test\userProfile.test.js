const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
const sinon = require('sinon');
chai.use(chaiHttp);
const jwt = require('jsonwebtoken');
const User = require('../../../models/user.model');
const Child = require('../../../models/child.model');
const ChildOrganizationMapping = require('../../../models/childOrganizationMapping.model');
const Organization = require('../../../models/organization.model');
const OrganizationMember = require('../../../models/organizationMember.model');
const PendingPushNotification = require('../../../models/pendingPushNotification.model');
const Notification = require('../../../models/notification.model');
const Event = require('../../../models/event.model');
const EventSignup = require('../../../models/eventSignup.model');
const PendingPartnerInvite = require('../../../models/pendingPartnerInvite.model');
const dynamoose = require('dynamoose');
const TestCase = require('./testcaseUserProfile');
const UserProfileService = require('../userProfileService');
const Cognito = require('../../../util/cognito');
const Utils = require('../../../util/utilFunctions');
const fundraiserSignupModel = require('../../../models/fundraiserSignup.model');
const GroupMember = require('../../../models/groupMembers.model');
const ConversationService = require('../ConversationService');
const ConstantModel = require('../../../models/constant.model');
const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
// Inactive
const inactiveUser = {
    id: '69d3a470-6fbe-11ec-9e5a-afdbd34c7759',
    email: '<EMAIL>'
};
const requestPayloadInactive = {
    token: 'Bearer ' + jwt.sign(inactiveUser, 'jwtTest', tokenOptionalInfo)
};
// User Token
const user = {
    id: '95285616-3769-4b2e-b36b-898597b8146e',
    email: '<EMAIL>'
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'jwtTest', tokenOptionalInfo)
};

Utils.addCommonReqTokenForHMac(request);
describe('User Profile get', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            sinon.restore();
        });
        it('Get inactive user details', (done) => {
            getStub.resolves({ status: 'inactive', isVerified: 1 });
            request(process.env.BASE_URL)
                .get('/user/details')
                .set({ Authorization: requestPayloadInactive.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 423);
                    done();
                });
        });
        it('Should get user details with photo url and organization details', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                children: ['123'], associatedOrganizations: [{ organizationId: '222', role: 3 }]
            });
            const childStub = sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test',
                photoURL: 'https://test.com/test.jpg'
            }]);
            const orgStub = sinon.stub(Organization, 'get').resolves({
                isDeleted: 0,
                address: 'test',
                country: 'test',
                name: 'test',
                state: 'test',
                city: 'test',
                category: 'test',
                id: '222',
                zipCode: 'test'
            });
            request(process.env.BASE_URL)
                .get('/user/details')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childStub.restore();
                    orgStub.restore();
                    done();
                });
        });
        it('Should get user details without photo url', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: ['123'], associatedOrganizations: [{ organizationId: '222', role: 3 }]
            });
            const childStub = sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test'
            }]);
            const orgStub = sinon.stub(Organization, 'get').resolves({
                isDeleted: 1,
                address: 'test',
                country: 'test',
                name: 'test',
                state: 'test',
                city: 'test',
                category: 'test',
                id: '222',
                zipCode: 'test'
            });
            request(process.env.BASE_URL)
                .get('/user/details')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    childStub.restore();
                    orgStub.restore();
                    done();
                });
        });
        it('Should get user details with empty array if there is no children and associatedOrganizations', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP, children: [], associatedOrganizations: []
            });
            request(process.env.BASE_URL)
                .get('/user/details')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });
        it('Should get user details if admin is passing the userId', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT, populate: () => { } });
            const userScanStub = sinon.stub(User, 'query');
            userScanStub.returns({
                eq: sinon.stub().returns({
                    exec: sinon.stub().returns({ status: 'active', isVerified: 1, role: 4, populate: () => { } })
                })
            });
            request(process.env.BASE_URL)
                .get('/user/details?userId=95285616-3769-4b2e-b36b-898597b8146e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });
        it('Should handle error in getUserDetails', (done) => {
            const getUserDetailsStub = sinon.stub(UserProfileService, 'getUserDetails');
            getUserDetailsStub.throws(new Error('Something went wrong'));
            request(process.env.BASE_URL)
                .get('/user/details')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    getUserDetailsStub.restore();
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});

describe('User Profile change password', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            sinon.restore();
        });

        TestCase.changePassword.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({ status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP });
                request(process.env.BASE_URL)
                    .put('/user/change-password')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.statusCode, 400);
                        done();
                    });
            });
        });

        it('As a user I should check if my old password is correct', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP });

            sinon.stub(Cognito, 'login').rejects({ code: 'NotAuthorizedException' });

            request(process.env.BASE_URL)
                .put('/user/change-password')
                .set({ Authorization: requestPayloadUser.token })
                .send({ oldPassword: 'Test@123', newPassword: 'Test@1234' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Cognito.login.restore();
                    done();
                });
        });

        it('As a user I should handle error in login', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP });

            sinon.stub(Cognito, 'login').rejects({ code: 'NetworkError' });

            request(process.env.BASE_URL)
                .put('/user/change-password')
                .set({ Authorization: requestPayloadUser.token })
                .send({ oldPassword: 'Test@123', newPassword: 'Test@1234' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 500);
                    Cognito.login.restore();
                    done();
                });
        });

        it('As a user I should change my password', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP });

            sinon.stub(Cognito, 'login').resolves({
                AuthenticationResult: {
                    idToken: 'test'
                }
            });

            request(process.env.BASE_URL)
                .put('/user/change-password')
                .set({ Authorization: requestPayloadUser.token })
                .send({ oldPassword: 'Test@123', newPassword: 'Test@1234' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Cognito.login.restore();
                    done();
                });
        });

        it('As a user I should handle error in change password', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP });

            sinon.stub(Cognito, 'login').resolves(null);

            request(process.env.BASE_URL)
                .put('/user/change-password')
                .set({ Authorization: requestPayloadUser.token })
                .send({ oldPassword: 'Test@123', newPassword: 'Test@1234' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Cognito.login.restore();
                    done();
                });
        });

    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});

describe('Update User Profile', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            sinon.restore();
        });
        it('should update user profile without org member', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                firstName: 'Test', lastName: 'User', email: '<EMAIL>', associatedOrganizations: [{ organizationId: 'orgId1' }]
            });

            const updatedUser = {
                id: 'userId',
                email: '<EMAIL>',
                firstName: 'NewFirstName',
                lastName: 'NewLastName',
                associatedOrganizations: []
            };

            const organizationMember = [{
                users: [
                    { id: 'userId', firstName: 'OldFirstName', lastName: 'OldLastName' },
                    { id: 'anotherUserId', firstName: 'AnotherFirstName', lastName: 'AnotherLastName' }
                ],
                save: () => { }
            }];

            sinon.stub(User, 'update').resolves(updatedUser);
            sinon.stub(Cognito, 'updateUserFirstNameAndLastName').resolves();
            sinon.stub(OrganizationMember, 'batchGet').resolves(organizationMember);
            sinon.stub(GroupMember, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([])
                    })
                })
            });
            sinon.stub(ConversationService, 'getGroupMemberIds').resolves([]);
            sinon.stub(ConversationService, 'getActiveSocketConnections').resolves([]);
            sinon.stub(ConversationService, 'sendMessagesToConnections').resolves();

            request(process.env.BASE_URL)
                .patch('/user/update-user')
                .set({ Authorization: requestPayloadUser.token })
                .send({ firstName: 'New', lastName: 'Name' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    User.update.restore();
                    Cognito.updateUserFirstNameAndLastName.restore();
                    OrganizationMember.batchGet.restore();
                    GroupMember.query.restore();
                    ConversationService.getGroupMemberIds.restore();
                    ConversationService.getActiveSocketConnections.restore();
                    ConversationService.sendMessagesToConnections.restore();
                    done();
                });
        });
        it('should update user profile with org member', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                firstName: 'Test', lastName: 'User', email: '<EMAIL>', associatedOrganizations: [{ organizationId: 'orgId1' }]
            });

            const updatedUser = {
                id: 'userId',
                email: '<EMAIL>',
                firstName: 'NewFirstName',
                lastName: 'NewLastName',
                associatedOrganizations: [
                    { organizationId: 'orgId1' },
                    { organizationId: 'orgId2' }
                ]
            };

            const organizationMember = [{
                users: [
                    { id: 'userId', firstName: 'OldFirstName', lastName: 'OldLastName' },
                    { id: 'anotherUserId', firstName: 'AnotherFirstName', lastName: 'AnotherLastName' }
                ],
                save: () => { }
            }];

            sinon.stub(User, 'update').resolves(updatedUser);
            sinon.stub(Cognito, 'updateUserFirstNameAndLastName').resolves();
            sinon.stub(OrganizationMember, 'batchGet').resolves(organizationMember);
            sinon.stub(GroupMember, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([])
                    })
                })
            });
            sinon.stub(ConversationService, 'getGroupMemberIds').resolves([]);
            sinon.stub(ConversationService, 'getActiveSocketConnections').resolves([]);
            sinon.stub(ConversationService, 'sendMessagesToConnections').resolves();

            request(process.env.BASE_URL)
                .patch('/user/update-user')
                .set({ Authorization: requestPayloadUser.token })
                .send({ firstName: 'New', lastName: 'Name' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    User.update.restore();
                    Cognito.updateUserFirstNameAndLastName.restore();
                    OrganizationMember.batchGet.restore();
                    GroupMember.query.restore();
                    ConversationService.getGroupMemberIds.restore();
                    ConversationService.getActiveSocketConnections.restore();
                    ConversationService.sendMessagesToConnections.restore();
                    done();
                });

        });
        it('should throw error from cognito when updating the first and lastname of the user', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                firstName: 'Test', lastName: 'User', email: '<EMAIL>',
                associatedOrganizations: [{ organizationId: 'orgId1' }],
                provider: 'email'
            });

            const updatedUser = {
                id: 'userId',
                email: '<EMAIL>',
                firstName: 'NewFirstName',
                lastName: 'NewLastName',
                associatedOrganizations: [
                    { organizationId: 'orgId1' },
                    { organizationId: 'orgId2' }
                ],
                provider: 'email'
            };

            const organizationMember = [{
                users: [
                    { id: 'userId', firstName: 'OldFirstName', lastName: 'OldLastName' },
                    { id: 'anotherUserId', firstName: 'AnotherFirstName', lastName: 'AnotherLastName' }
                ],
                save: () => { }
            }];

            sinon.stub(User, 'update').resolves(updatedUser);
            sinon.stub(Cognito, 'updateUserFirstNameAndLastName').rejects();
            sinon.stub(OrganizationMember, 'batchGet').resolves(organizationMember);
            sinon.stub(GroupMember, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([])
                    })
                })
            });
            sinon.stub(ConversationService, 'getGroupMemberIds').resolves([]);
            sinon.stub(ConversationService, 'getActiveSocketConnections').resolves([]);
            sinon.stub(ConversationService, 'sendMessagesToConnections').resolves();

            request(process.env.BASE_URL)
                .patch('/user/update-user')
                .set({ Authorization: requestPayloadUser.token })
                .send({ firstName: 'New', lastName: 'Name' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    User.update.restore();
                    Cognito.updateUserFirstNameAndLastName.restore();
                    OrganizationMember.batchGet.restore();
                    GroupMember.query.restore();
                    ConversationService.getGroupMemberIds.restore();
                    ConversationService.getActiveSocketConnections.restore();
                    ConversationService.sendMessagesToConnections.restore();
                    assert.equal(res.statusCode, 400);
                    done();
                });

        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});

describe('Get Connection List', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            sinon.restore();
        });

        it('Should get connection list', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                firstName: 'Test', lastName: 'User', children: ['123']
            });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test',
                photoURL: 'https://test.com/test.jpg',
                connections: [{ childId: '123', status: 'connected' }, { childId: '456', status: 'connected' }],
                school: '222'
            },
            {
                id: '123',
                firstName: 'test',
                lastName: 'test',
                photoURL: 'https://test.com/test.jpg',
                connections: [{ childId: 'xyz85616-3769-4b2e-b36b-898597b8146e', status: 'connected' }],
                school: '222'
            },
            {
                id: '456',
                firstName: 'test',
                lastName: 'test',
                school: '222',
                connections: []
            }]);

            const organization = {
                id: '222',
                name: 'test',
                address: 'test',
                country: 'test',
                state: 'test',
                city: 'test',
                category: 'test',
                zipCode: 'test'
            };

            sinon.stub(Organization, 'batchGet').resolves([organization]);

            request(process.env.BASE_URL)
                .get('/user/connections')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.batchGet.restore();
                    Organization.batchGet.restore();
                    done();
                });
        });

        it('should return empty array if there is no children', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                firstName: 'Test', lastName: 'User', children: []
            });

            request(process.env.BASE_URL)
                .get('/user/connections')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

        it('should return empty array if there is no connections', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                firstName: 'Test', lastName: 'User', children: ['123']
            });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test',
                photoURL: 'https://test.com/test.jpg',
                connections: [],
                school: '222'
            }]);

            request(process.env.BASE_URL)
                .get('/user/connections')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.batchGet.restore();
                    done();
                });
        });

        it('should handle error in getConnectionsList', (done) => {
            const getConnectionsListStub = sinon.stub(UserProfileService, 'getConnectionsList');
            getConnectionsListStub.throws(new Error('Something went wrong'));
            request(process.env.BASE_URL)
                .get('/user/connections')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    getConnectionsListStub.restore();
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});

describe('Delete User Profile', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            sinon.restore();
        });

        it('Should throw error if user is associated with organizations', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                firstName: 'Test', lastName: 'User', email: '<EMAIL>',
                associatedOrganizations: [{ organizationId: '123' }]
            });

            request(process.env.BASE_URL)
                .delete('/user/')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('Should delete user', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                firstName: 'Test', lastName: 'User', email: '<EMAIL>',
                associatedOrganizations: [],
                children: ['childId-1'],
                id: 'userId-1',
                sendInvites: [{
                    invitedPartnerEmail: '<EMAIL>', children: [
                        { childId: 'childId-1', status: 'accepted' },
                        { childId: 'childId-2', status: 'pending' }
                    ]
                }],
                partnerInvites: []
            });

            sinon.stub(User, 'update').resolves();
            sinon.stub(Child, 'batchGet').resolves([{
                id: 'childId-1', createdBy: 'userId-1', associatedOrganizations: ['orgId-1'],
                connections: [],
                photoURL: 'https://test.com/test.jpg'
            }]);
            sinon.stub(PendingPushNotification, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ id: 'pendingPushNotificationId-1' }])
                    })
                })
            });
            sinon.stub(Notification, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        attributes: sinon.stub().returns({
                            exec: sinon.stub().resolves([{ id: 'notificationId-1' }])
                        })
                    })
                })
            });
            sinon.stub(EventSignup, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        attributes: sinon.stub().returns({
                            exec: sinon.stub().resolves([{ id: 'eventSignupId-1' }])
                        })
                    })
                })
            });
            sinon.stub(fundraiserSignupModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        attributes: sinon.stub().returns({
                            exec: sinon.stub().resolves([{ id: 'fundraiserSignupId-1' }])
                        })
                    })
                })
            });
            sinon.stub(Event, 'get').resolves({ id: 'eventId-1', createdBy: 'userId-1', participantsCount: 1, save: () => { } });

            sinon.stub(ChildOrganizationMapping, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                attributes: sinon.stub().returns({
                                    exec: sinon.stub().resolves([{ id: 'childOrganizationMappingId-1' }])
                                })
                            })
                        })
                    })
                })
            });
            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([
                            {
                                id: 'userId-2', children: ['childId-1'],
                                partnerInvites: [
                                    {
                                        inviterPartnerId: 'userId-1',
                                        inviterPartnerEmail: '<EMAIL>',
                                        children: [
                                            { childId: 'childId-1', invitedAt: Date.now() },
                                            { childId: 'childId-2', invitedAt: Date.now() }
                                        ]
                                    }
                                ]
                            }
                        ])
                    })
                })
            });
            sinon.stub(PendingPartnerInvite, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ id: 'pendingPartnerInviteId-1' }])
                    })
                })
            });
            sinon.stub(GroupMember, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ id: 'groupMemberId-1' }])
                    })
                })
            });
            sinon.stub(dynamoose, 'transaction').resolves();
            sinon.stub(ConversationService, 'getGroupMemberIds').resolves([]);
            sinon.stub(ConversationService, 'getActiveSocketConnections').resolves([]);
            sinon.stub(ConversationService, 'sendMessagesToConnections').resolves();
            sinon.stub(ConstantModel, 'get').resolves({ value: 'v1' });

            request(process.env.BASE_URL)
                .delete('/user/')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});

describe('Delete User Profile exceptions', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            sinon.restore();
        });

        it('Should delete user with no children', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                firstName: 'Test', lastName: 'User', email: '<EMAIL>',
                associatedOrganizations: [],
                children: [],
                id: 'userId-1',
                sendInvites: [],
                partnerInvites: []
            });

            sinon.stub(User, 'update').resolves();
            sinon.stub(Child, 'batchGet').resolves([]);
            sinon.stub(PendingPushNotification, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ id: 'pendingPushNotificationId-1' }])
                    })
                })
            });
            sinon.stub(Notification, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        attributes: sinon.stub().returns({
                            exec: sinon.stub().resolves([{ id: 'notificationId-1' }])
                        })
                    })
                })
            });
            sinon.stub(EventSignup, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        attributes: sinon.stub().returns({
                            exec: sinon.stub().resolves([])
                        })
                    })
                })
            });
            sinon.stub(ChildOrganizationMapping, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                attributes: sinon.stub().returns({
                                    exec: sinon.stub().resolves([])
                                })
                            })
                        })
                    })
                })
            });
            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([])
                    })
                })
            });
            sinon.stub(PendingPartnerInvite, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([])
                    })
                })
            });
            sinon.stub(GroupMember, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([])
                    })
                })
            });

            sinon.stub(dynamoose, 'transaction').resolves();
            sinon.stub(ConstantModel, 'get').resolves({ value: 'v1' });

            request(process.env.BASE_URL)
                .delete('/user/')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});

describe('Check user feed status', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            sinon.restore();
        });

        it('Should return feed status if isFeedGenerated attribute doesnt exist', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                firstName: 'Test', lastName: 'User', email: '<EMAIL>',
                associatedOrganizations: [],
                children: [],
                id: 'userId-1',
                sendInvites: [],
                partnerInvites: []
            });

            request(process.env.BASE_URL)
                .get('/user/check-feed-status')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });

        });

        it('Should return feed status if isFeedGenerated attribute exist', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                firstName: 'Test', lastName: 'User', email: '<EMAIL>',
                associatedOrganizations: [],
                children: [],
                id: 'userId-1',
                sendInvites: [],
                partnerInvites: [],
                isFeedGenerated: false
            });

            request(process.env.BASE_URL)
                .get('/user/check-feed-status')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

        it('Should handle error in checkIsFeedgenerated', (done) => {
            const checkIsFeedGeneratedStub = sinon.stub(UserProfileService, 'checkIsFeedGenerated');
            checkIsFeedGeneratedStub.throws(new Error('Something went wrong'));
            request(process.env.BASE_URL)
                .get('/user/check-feed-status')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    checkIsFeedGeneratedStub.restore();
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});

describe('Get purchased membership list', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            sinon.restore();
        });

        it('Should return empty list if user haven\'t purchased any membership', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                firstName: 'Test', lastName: 'User', email: '<EMAIL>',
                associatedOrganizations: [],
                children: ['childId1'],
                id: 'userId-1',
                sendInvites: [],
                partnerInvites: []
            });

            sinon.stub(Child, 'batchGet').resolves([
                {
                    id: 'childId1',
                    firstName: 'Test',
                    lastName: 'User'
                }
            ]);

            request(process.env.BASE_URL)
                .get('/user/membership-list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.batchGet.restore();
                    done();
                });
        });

        it('Should return empty list if user haven\'t purchased any membership and child and organization id is empty', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                firstName: 'Test', lastName: 'User', email: '<EMAIL>',
                associatedOrganizations: [],
                id: 'userId-1',
                sendInvites: [],
                partnerInvites: [],
                membershipsPurchased: [
                    {
                        membershipType: 'child'
                    }
                ]
            });

            request(process.env.BASE_URL)
                .get('/user/membership-list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    done();
                });
        });

        it('Should return list of purchased memberships with organization and child details', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                firstName: 'Test', lastName: 'User', email: '<EMAIL>',
                associatedOrganizations: [],
                children: ['childId1', 'childId2'],
                id: 'userId-1',
                sendInvites: [],
                partnerInvites: [],
                membershipsPurchased: [
                    {
                        fundraiserSignupId: 'fundraiserSignupId1',
                        organizationId: 'orgId1',
                        membershipType: 'family'
                    }
                ]
            });

            sinon.stub(Child, 'batchGet').resolves([
                {
                    id: 'childId1',
                    firstName: 'Test',
                    lastName: 'User',
                    membershipsPurchased: [
                        {
                            fundraiserSignupId: 'fundraiserSignupId2',
                            organizationId: 'orgId1',
                            membershipType: 'child'
                        },
                        {
                            fundraiserSignupId: 'fundraiserSignupId1',
                            organizationId: 'orgId1',
                            membershipType: 'family'
                        }
                    ]
                },
                {
                    id: 'childId2',
                    firstName: 'Test',
                    lastName: 'User',
                    photoURL: 'test-image',
                    membershipsPurchased: [
                        {
                            fundraiserSignupId: 'fundraiserSignupId3',
                            organizationId: 'orgId2',
                            membershipType: 'child'
                        },
                        {
                            fundraiserSignupId: 'fundraiserSignupId4',
                            organizationId: 'orgId3',
                            membershipType: 'child'
                        },
                        {
                            fundraiserSignupId: 'fundraiserSignupId1',
                            organizationId: 'orgId1',
                            membershipType: 'family'
                        }
                    ]
                }
            ]);

            sinon.stub(Organization, 'batchGet').resolves([
                {
                    id: 'orgId1'
                },
                {
                    id: 'orgId2'
                },
                {
                    id: 'orgId3'
                }
            ]);

            request(process.env.BASE_URL)
                .get('/user/membership-list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Child.batchGet.restore();
                    Organization.batchGet.restore();
                    done();
                });
        });

        it('should handle error in membership listing', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                firstName: 'Test', lastName: 'User', email: '<EMAIL>',
                associatedOrganizations: [],
                children: [],
                id: 'userId-1',
                sendInvites: [],
                partnerInvites: [],
                membershipsPurchased: [
                    {
                        organizationId: 'orgId1',
                        childId: 'childId1',
                        membershipType: 'child'
                    }
                ]
            });

            sinon.stub(UserProfileService, 'getPurchasedMembershipsList').rejects('Something went wrong');

            request(process.env.BASE_URL)
                .get('/user/membership-list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    UserProfileService.getPurchasedMembershipsList.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});

describe('Update chat guidelines read status', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            sinon.restore();
        });

        it('Should update chat guidelines read status', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                firstName: 'Test', lastName: 'User', email: '<EMAIL>',
                associatedOrganizations: [],
                children: [],
                id: 'userId-1',
                sendInvites: [],
                partnerInvites: [],
                chatGuidelinesRead: false
            });

            sinon.stub(User, 'update').resolves();

            request(process.env.BASE_URL)
                .patch('/user/chat-guidelines-read')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    User.update.restore();
                    done();
                });
        }

        );

        it('Should handle error in updating chat guidelines read status', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, accessLevel: CONSTANTS.ACCESS_LEVEL.APP,
                firstName: 'Test', lastName: 'User', email: '<EMAIL>',
                associatedOrganizations: [],
                children: [],
                id: 'userId-1',
                sendInvites: [],
                partnerInvites: [],
                chatGuidelinesRead: false
            });

            sinon.stub(User, 'update').rejects('Something went wrong');

            request(process.env.BASE_URL)
                .patch('/user/chat-guidelines-read')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    User.update.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
