const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
const sinon = require('sinon');
chai.use(chaiHttp);
const jwt = require('jsonwebtoken');
const User = require('../../../models/user.model');
const Organization = require('../../../models/organization.model');
const PaymentService = require('../paymentService');
const Stripe = require('../../../util/Stripe');
const OrganizationMembers = require('../../../models/organizationMember.model');
const Utils = require('../../../util/utilFunctions');
const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
/* User Token */
const user = {
    id: '********-3769-4b2e-b36b-898597b8146e',
    email: '<EMAIL>'
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'testToken', tokenOptionalInfo)
};
Utils.addCommonReqTokenForHMac(request);
describe('Stripe create account', () => {
    try {
        let createAccountStub;
        let createOnboardingLinkStub;
        let getOrganizationStub;

        before(async () => {
            createAccountStub = sinon.stub(Stripe, 'createAccount');
            createOnboardingLinkStub = sinon.stub(Stripe, 'createOnboardingLink');
            getOrganizationStub = sinon.stub(Organization, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('Should be able to create a Stripe account and receive an onboarding link', async () => {
            const mockedAccount = { id: 'mocked_account_id' };
            const mockedAccountLink = { url: 'mocked_onboarding_link_url' };
            createAccountStub.resolves(mockedAccount);
            createOnboardingLinkStub.resolves(mockedAccountLink);
            getOrganizationStub.withArgs('test-id').resolves({
                id: 'test-id',
                paymentDetails: {
                    stripeConnectAccountId: 'mocked_account_id'
                },
                save: sinon.stub().resolves()
            });

            const reqObj = { id: 'test-id' };
            const result = await PaymentService.createAccount(reqObj, { url: 'testurl' });
            assert.deepEqual(result, { url: mockedAccountLink.url });
            expect(createAccountStub.calledOnce).to.be.true;
            expect(createOnboardingLinkStub.calledOnce).to.be.true;
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
describe('Stripe payment service', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            getStub.restore();
            sinon.restore();
        });
        it('As a super admin I should resend onboarding link to org if stripeConnectAccountId exists', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });
            const scanStub = sinon.stub();

            scanStub.returns({ paymentDetails: { stripeConnectAccountId: '123' } });
            sinon.replace(Organization, 'get', scanStub);
            const createOnboardingLinkStub = sinon.stub(Stripe, 'createOnboardingLink');
            createOnboardingLinkStub.resolves({ url: 'mocked_onboarding_link_url' });
            const orgGetStub = sinon.stub(OrganizationMembers, 'get');
            orgGetStub.resolves({
                users: [{ associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, email: '<EMAIL>' }]
            });
            request(process.env.BASE_URL)
                .post('/organization/resend-onboarding?orgId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: 'test-id' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    sinon.restore();
                    createOnboardingLinkStub.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
describe('Resend account link', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            getStub.restore();
            sinon.restore();
        });
        it('As a super admin, should send resend onboarding link and create account on stripe if not exists', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });

            const scanStub = sinon.stub(Organization, 'scan');
            scanStub.returns({
                eq: sinon.stub().returns({
                    exec: sinon.stub().resolves({ toJSON: () => [{ id: 'testid' }] })
                })
            });

            const createAccountStub = sinon.stub(Stripe, 'createAccount');
            createAccountStub.resolves({ id: 'mocked_account_id' });

            const updateOrganizationStub = sinon.stub(Organization, 'update');
            updateOrganizationStub.resolves();

            const orgGetStub = sinon.stub(Organization, 'get');
            orgGetStub.resolves({ save: sinon.stub(), paymentDetails: {} });

            const createOnboardingLinkStub = sinon.stub(Stripe, 'createOnboardingLink');
            createOnboardingLinkStub.resolves({ url: 'mocked_onboarding_link_url' });
            const orgMemGetStub = sinon.stub(OrganizationMembers, 'get');
            orgMemGetStub.resolves({
                users: [{ associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN, email: '<EMAIL>' }]
            });
            request(process.env.BASE_URL)
                .post('/organization/resend-onboarding?orgId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: 'test-id' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    sinon.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
describe('Resend account link', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            getStub.restore();
            sinon.restore();
        });
        it('As a super admin, should give error on resend link', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });

            const scanStub = sinon.stub(Organization, 'get');
            scanStub.rejects({});

            request(process.env.BASE_URL)
                .post('/organization/resend-onboarding?orgId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .send({ id: 'test-id' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    sinon.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
describe('Stripe connect webhooks', () => {
    try {
        let stripeConstructWebhookEventStub;
        let organizationGetStub;

        before(async () => {
            stripeConstructWebhookEventStub = sinon.stub(Stripe, 'constructWebhookEvent');
            organizationGetStub = sinon.stub(Organization, 'get');
        });

        after(async () => {
            stripeConstructWebhookEventStub.restore();
            sinon.restore();
        });

        it('As a super admin, should watch for account.updated and update status if it gets active', async () => {

            const scanStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            scanStub.returns({ eq: eqStub });
            eqStub.returns({ exec: execStub });
            execStub.resolves({ toJSON: () => [{ id: 'org_id' }] });
            sinon.replace(Organization, 'scan', scanStub);

            organizationGetStub.withArgs('org_id').resolves({
                id: 'org_id',
                paymentDetails: {
                    stripeConnectAccountId: 'mocked_account_id'
                },
                allowedPaymentType: { stripe: true },
                save: sinon.stub().resolves()
            });

            stripeConstructWebhookEventStub.returns({
                type: 'account.updated',
                data: {
                    id: 'account_1',
                    object: {
                        capabilities: { transfers: 'active' }
                    }
                }
            });
            const res = await request(process.env.BASE_URL)
                .post('/payment/webhook/connect')
                .send();
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 200);
        });

        it('As a super admin, should watch for account.updated and update status if it gets disabled by the admin from dashboard',
            async () => {

                stripeConstructWebhookEventStub.returns({
                    type: 'account.updated',
                    data: {
                        id: 'account_1',
                        object: {
                            requirements: { disabled_reason: 'platform_paused' }
                        }
                    }
                });
                const res = await request(process.env.BASE_URL)
                    .post('/payment/webhook/connect')
                    .send();
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
            });

        it('As a super admin, should watch for account.updated and update status if it gets rejected.other by the admin from dashboard',
            async () => {

                stripeConstructWebhookEventStub.returns({
                    type: 'account.updated',
                    data: {
                        id: 'account_1',
                        object: {
                            requirements: { disabled_reason: 'rejected.other' }
                        }
                    }
                });
                const res = await request(process.env.BASE_URL)
                    .post('/payment/webhook/connect')
                    .send();
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
            });


        it('As a super admin, should watch for account.updated and got to else case',
            async () => {
                stripeConstructWebhookEventStub.returns({
                    type: 'account.updated',
                    data: {
                        id: 'account_1',
                        object: {
                            requirements: { disabled_reason: '' }
                        }
                    }
                });
                const res = await request(process.env.BASE_URL)
                    .post('/payment/webhook/connect')
                    .send();
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
            });

        it('As a super admin, should watch for account.updated and update status if it gets rejected by the admin from dashboard',
            async () => {

                stripeConstructWebhookEventStub.returns({
                    type: 'account.updated',
                    data: {
                        id: 'account_1',
                        object: {
                            requirements: { disabled_reason: 'rejected.fraud' }
                        }
                    }
                });
                const res = await request(process.env.BASE_URL)
                    .post('/payment/webhook/connect')
                    .send();
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
            });

        it('As a super admin, should watch unhandled event', async () => {
            stripeConstructWebhookEventStub.returns({
                type: 'account.authorized',
                data: {
                    id: 'account_1',
                    object: {
                        capabilities: { transfers: 'active' }
                    }
                }
            });
            const res = await request(process.env.BASE_URL)
                .post('/payment/webhook/connect')
                .send();
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 200);
        });

        it('Invalid stripe signature', async () => {
            stripeConstructWebhookEventStub.throws(new Error('error'));

            const res = await request(process.env.BASE_URL)
                .post('/payment/webhook/connect')
                .send();
            expect(res.body.status).to.be.status;
            assert.equal(res.body.status, 0);
            assert.equal(res.statusCode, 400);
        });

    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
