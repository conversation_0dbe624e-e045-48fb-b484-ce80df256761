/* eslint-disable max-len */
const Stripe = require('../../util/Stripe');
const Organization = require('../../models/organization.model');
const FundraiserSignup = require('../../models/fundraiserSignup.model');
const Fundraiser = require('../../models/fundraiser.model');
const User = require('../../models/user.model');
const lodash = require('lodash');
const EmailService = require('../../util/sendEmail');
const Child = require('../../models/child.model');
const PaymentValidator = require('./fundraiserRegisterValidator');
const AwsOpenSearchService = require('../../util/opensearch');
const Utils = require('../../util/utilFunctions');

const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

/**
 * Class represents services for stripe
 */
class FundraiserRegisterService {
    /**
* @desc This function is being used to create a stripe checkout session
* <AUTHOR>
* @since 15/11/2023
* @param {Object} loggedInUser loggedInUser
*/
    static async createCheckoutSession (loggedInUser) {
        const user = await User.get({
            id: loggedInUser.id,
            email: loggedInUser.email
        });
        let customerId = lodash.get(user, 'stripeCustomerId');
        if (!customerId) {
            const customer = await Stripe.createCustomer(
                user.email,
                `${user.firstName} ${user.lastName}`,
                stripe
            );
            customerId = customer.id;
            await User.update(
                { id: loggedInUser.id, email: user.email },
                { stripeCustomerId: customerId }
            );
        }
        return customerId;
    }

    /**
* @desc This function is being used to create a payment intent and register user
* <AUTHOR>
* @since 15/11/2023
* @param {Object} req req
* @param {Object} loggedInUser loggedInUser
* @param {Object} locale locale
*/
    // eslint-disable-next-line consistent-return
    static async registerUserAndPayment (req, loggedInUser, locale) {
        const Validator = new PaymentValidator(req.body, locale);
        Validator.validate();
        const {
            eventId,
            childId,
            purchasedProducts,
            paymentType,
            isCoveredByUser
        } = req.body;
        const event = await Fundraiser.get({ id: eventId });
        const currentDateTime = MOMENT();
        const endDateTime = MOMENT(event.endDate, 'MM/DD/YYYY hh:mm:ss');
        const isFree = CONSTANTS.PAYMENT_TYPES.FREE;
        const isStripe = CONSTANTS.PAYMENT_TYPES.STRIPE;
        const isCheck = CONSTANTS.PAYMENT_TYPES.CHECK;
        const isCash = CONSTANTS.PAYMENT_TYPES.CASH;
        const isVenmo = CONSTANTS.PAYMENT_TYPES.VENMO;
        const isPaymentInitiated = CONSTANTS.PAYMENT_TYPES.PAYMENT_INITIATED;
        const isPending = CONSTANTS.PAYMENT_STATUS.PENDING;
        const isApproved = CONSTANTS.PAYMENT_STATUS.APPROVED;
        if (endDateTime.isBefore(currentDateTime)) {
            throw {
                message: MESSAGES.CANT_REGISTER_TO_PAST_EVENT,
                statusCode: 400
            };
        }

        const { organizationId, products, membershipBenefitDetails, fundraiserType } = event;
        let { boosterGoalForChild, boosterMessageForChild } = req.body;

        if (fundraiserType === 'booster') {
            if (!boosterGoalForChild) {
                boosterGoalForChild = event.boosterGoalForChild;
            }
            if (!boosterMessageForChild) {
                boosterMessageForChild = event.boosterMessageForChild;
            }
        }

        const child = await Child.get(childId);
        let membershipType;
        let membershipId;

        if (fundraiserType === CONSTANTS.FUNDRAISER_TYPES.MEMBERSHIP) {
            membershipType = purchasedProducts[0].membershipType;
            Validator.validateMembershipType(membershipType);
            membershipId = purchasedProducts[0].itemId;
            await this.checkExistingMembership(loggedInUser, child, organizationId, membershipType);
        }

        const isExistsChild = await FundraiserSignup.query('eventId')
            .eq(eventId)
            .where('childId')
            .eq(childId)
            .exec();
        const paymentStatus = lodash.get(
            isExistsChild.toJSON()[0],
            'paymentDetails.paymentStatus'
        );
        const eventSignupPaymentType = lodash.get(
            isExistsChild.toJSON()[0],
            'paymentDetails.paymentType'
        );

        const existingPaymentTypeStripe =
            eventSignupPaymentType && eventSignupPaymentType === isStripe;
        const isCashChequeVenmo =
            paymentType === isCash ||
            paymentType === isCheck ||
            paymentType === isVenmo;

        const membershipBenefitDetailsParsed = membershipBenefitDetails ?? {};
        membershipBenefitDetailsParsed.benefitDiscount = membershipBenefitDetailsParsed.benefitDiscount ? JSON.parse(membershipBenefitDetails.benefitDiscount) : [];
        if (membershipBenefitDetailsParsed.isOnlyForMembers) {
            const membershipsEnabledForChild = Utils.getMembershipsOfChild(
                event.organizationId,
                child,
                MOMENT(event.startDate)
            );
            if (!membershipsEnabledForChild) {
                throw {
                    message: MESSAGES.FUNDRAISER_ONLY_FOR_MEMBERS,
                    statusCode: 403
                };
            }
        }

        const parsedProducts = JSON.parse(products);

        const optionPriceMap = new Map();
        parsedProducts.forEach(product => {
            product.optionPrices?.forEach(optionPrice => {
                optionPriceMap.set(optionPrice.id, optionPrice.itemCost);
            });
        });

        let membershipBenefitAmount = 0;
        const membershipsEnabledForChild = Utils.getMembershipsOfChild(
            organizationId,
            child,
            MOMENT(event.startDate)
        );
        if (membershipsEnabledForChild && membershipBenefitDetails?.benefitDiscount) {
            const purchasedMembershipMapping =
                membershipBenefitDetailsParsed?.benefitDiscount?.filter(
                    (membership) =>
                        `${membership.id}` ===
                        membershipsEnabledForChild.membershipId
                );
            membershipBenefitAmount = purchasedMembershipMapping.length ? purchasedMembershipMapping[0].value : 0;
        }

        let childMembershipDiscount = 0;
        const fee = purchasedProducts.reduce((acc, purchasedProduct) => {
            if (optionPriceMap.has(purchasedProduct.id)) {
                const itemCost = parseFloat(optionPriceMap.get(purchasedProduct.id));
                acc += itemCost * purchasedProduct.quantity;
            }
            return acc;
        }, 0);

        if (membershipsEnabledForChild && membershipBenefitDetails?.benefitDiscount && !isNaN(membershipBenefitAmount)) {
            const totalDiscount = Math.min(membershipBenefitAmount, fee);
            childMembershipDiscount = totalDiscount;
        }

        const finalFeeWithDiscount = fee - childMembershipDiscount;

        const purchasedProductsStr = JSON.stringify(purchasedProducts);

        // current user is already pending for the event with payment type stripe and now paying with payment type cash or cheque
        if (
            isExistsChild.count &&
      paymentStatus === isPaymentInitiated &&
      existingPaymentTypeStripe &&
      isCashChequeVenmo
        ) {
            CONSOLE_LOGGER.info(
                'User first paid with stripe now trying to pay with cash/check/venmo'
            );
            const eventSignup = await FundraiserSignup.get(
                isExistsChild.toJSON()[0].id
            );
            eventSignup.paymentDetails.stripeCustomerId = '';
            eventSignup.paymentDetails.stripeConnectAccountId = '';
            eventSignup.paymentDetails.transactionFee = 0;
            eventSignup.paymentDetails.platformFeeCoveredBy = undefined;
            eventSignup.paymentDetails.paymentType = paymentType;
            eventSignup.paymentDetails.paymentStatus = isPending;
            eventSignup.paymentDetails.membershipDiscount = childMembershipDiscount;
            eventSignup.purchasedProducts = purchasedProductsStr;
            eventSignup.stripePaymentIntentId = undefined;

            await eventSignup.save();
            throw {
                message: MESSAGES.PAYMENT_SUCCESS_CASH_CHEQUE,
                statusCode: 200
            };
        }
        if (paymentType === isFree && finalFeeWithDiscount === 0) {
            const fundraiserSignup = await FundraiserSignup.create({
                eventId,
                organizationId,
                childId,
                parentId: loggedInUser.id,
                createdBy: loggedInUser.id,
                purchasedProducts: purchasedProductsStr,
                paymentDetails: { paymentStatus: isApproved, paymentType: isFree },
                boosterGoalForChild,
                boosterMessageForChild
            });
            await this.updateChildMembershipStatus(event, loggedInUser, organizationId, membershipType, child, fundraiserSignup.id, membershipId);

            await this.updateFreeEventChildFeeds(childId, eventId, fundraiserSignup.id, purchasedProductsStr);
            return {
                fundraiserSignupId: fundraiserSignup.id
            };
        } else if (
            paymentType === isCash ||
      paymentType === isCheck ||
      paymentType === isVenmo
        ) {
            const fundraiserSignup = await FundraiserSignup.create({
                eventId,
                organizationId,
                childId,
                parentId: loggedInUser.id,
                createdBy: loggedInUser.id,
                purchasedProducts: purchasedProductsStr,
                paymentDetails: { paymentType, paymentStatus: isPending, membershipDiscount: childMembershipDiscount },
                boosterGoalForChild,
                boosterMessageForChild
            });
            CONSOLE_LOGGER.time('Registering in pending events');
            await this.updateChildFeedsAndPendingEvents(childId, eventId, fundraiserSignup.id, purchasedProductsStr);
            CONSOLE_LOGGER.timeEnd('Registering in pending events');
            throw {
                message: MESSAGES.PAYMENT_SUCCESS_CASH_CHEQUE,
                statusCode: 200
            };
        } else if (
            isExistsChild.count &&
      paymentStatus === isPaymentInitiated &&
      existingPaymentTypeStripe &&
      paymentType === isStripe
        ) {
            CONSOLE_LOGGER.info(
                'User first paid with stripe now again trying to pay with stripe and total fee is ', fee
            );
            const org = await Organization.get({ id: organizationId });
            const orgId = lodash.get(org, 'paymentDetails.stripeConnectAccountId');
            const orgStripeStatus = lodash.get(
                org,
                'paymentDetails.stripeOnboardingStatus'
            );
            if (orgStripeStatus !== CONSTANTS.STATUS.ACTIVE || !orgId) {
                throw {
                    message: MESSAGES.STRIPE_NOT_ACTIVE,
                    statusCode: 403
                };
            }
            let isOptionalStripeFee = lodash.get(org, 'platformFeeCoveredBy');
            if (paymentType === isStripe && isOptionalStripeFee === 'optional') {
                const Validator = new PaymentValidator(req.body, locale);
                Validator.validateOptionalFee();
                isOptionalStripeFee = isCoveredByUser ? 'parent' : 'organization';
            }

            const customerId = await this.createCheckoutSession(loggedInUser);
            const { paymentIntent, stripeFee } = await Stripe.createPaymentIntent(
                stripe,
                finalFeeWithDiscount,
                orgId,
                customerId,
                org,
                isCoveredByUser
            );
            const eventSignup = await FundraiserSignup.get(
                isExistsChild.toJSON()[0].id
            );
            eventSignup.purchasedProducts = purchasedProductsStr;
            eventSignup.stripePaymentIntentId = paymentIntent.id;
            eventSignup.paymentDetails.stripeCustomerId = customerId;
            eventSignup.paymentDetails.stripeConnectAccountId = orgId;
            eventSignup.paymentDetails.paymentType = isStripe;
            eventSignup.paymentDetails.transactionFee = stripeFee / 100;
            eventSignup.paymentDetails.platformFeeCoveredBy = isOptionalStripeFee;
            eventSignup.paymentDetails.membershipDiscount = childMembershipDiscount;

            await eventSignup.save();
            return {
                clientSecret: paymentIntent.client_secret,
                publicableKey: process.env.PUBLISHABLE_SECRET_KEY
            };
        } else {
            CONSOLE_LOGGER.info('User trying to pay with stripe, payment-initiated');

            const org = await Organization.get({ id: organizationId });
            const orgId = lodash.get(org, 'paymentDetails.stripeConnectAccountId');
            const orgStripeStatus = lodash.get(
                org,
                'paymentDetails.stripeOnboardingStatus'
            );

            if (orgStripeStatus !== CONSTANTS.STATUS.ACTIVE || !orgId) {
                throw {
                    message: MESSAGES.STRIPE_NOT_ACTIVE,
                    statusCode: 403
                };
            }
            let isOptionalStripeFee = lodash.get(org, 'platformFeeCoveredBy');
            if (paymentType === isStripe && isOptionalStripeFee === 'optional') {
                const Validator = new PaymentValidator(req.body, locale);
                Validator.validateOptionalFee();
                isOptionalStripeFee = isCoveredByUser ? 'parent' : 'organization';
            }

            const customerId = await this.createCheckoutSession(loggedInUser);
            const { paymentIntent, stripeFee } = await Stripe.createPaymentIntent(
                stripe,
                finalFeeWithDiscount,
                orgId,
                customerId,
                org,
                isCoveredByUser
            );
            await FundraiserSignup.create({
                eventId,
                organizationId,
                childId,
                parentId: loggedInUser.id,
                createdBy: loggedInUser.id,
                stripePaymentIntentId: paymentIntent.id,
                purchasedProducts: purchasedProductsStr,
                paymentDetails: {
                    stripeCustomerId: customerId,
                    stripeConnectAccountId: orgId,
                    paymentType: isStripe,
                    paymentStatus: isPaymentInitiated,
                    transactionFee: stripeFee / 100,
                    platformFeeCoveredBy: isOptionalStripeFee,
                    membershipDiscount: childMembershipDiscount
                },
                boosterGoalForChild,
                boosterMessageForChild
            });

            return {
                clientSecret: paymentIntent.client_secret,
                publicableKey: process.env.PUBLISHABLE_SECRET_KEY
            };
        }
    }

    static async updateFreeEventChildFeeds (childId, eventId, fundraiserSignupId, purchasedProducts) {
        await AwsOpenSearchService.registerEventInChildEvents(
            CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD,
            childId,
            {
                fundraiserId: eventId,
                fundraiserSignupId,
                purchasedProducts
            }
        );
    }

    static async updateChildFeedsAndPendingEvents (childId, eventId, fundraiserSignupId, purchasedProducts) {
        await AwsOpenSearchService.registerInChildPendingEvents(
            CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD,
            childId,
            {
                fundraiserId: eventId,
                fundraiserSignupId,
                purchasedProducts
            }
        );
    }

    /**
* @desc This function is being used to handle Webhook
* <AUTHOR>
* @since 16/11/2023
* @param {Object} req req
* @param {Object} res res
*/
    static async stripeWebhook (req, res) {
        const signature = req.headers['stripe-signature'];
        let event;
        try {
            event = await Stripe.constructWebhookEvent(
                req.rawBody,
                signature,
                stripe
            );
        } catch (err) {
            CONSOLE_LOGGER.error('Webhook Error:', err);
            throw {
                message: `Webhook Error: ${err.message}`,
                statusCode: 400
            };
        }
        if (event.type === 'payment_intent.canceled') {
            const payment = event.data.object;
            const eventSignup = await FundraiserSignup.query('stripePaymentIntentId')
                .eq(payment.id)
                .exec();

            if (eventSignup.count) {
                const eventInSignup = await FundraiserSignup.get(
                    eventSignup.toJSON()[0].id
                );
                await eventInSignup.delete();
            }

            CONSOLE_LOGGER.info('payment intent: canceled ', payment);
        }
        if (event.type === 'payment_intent.succeeded') {
        // Extract payment details from the event
            const payment = event.data.object;

            // Fetch the event signup details
            const eventSignup = await FundraiserSignup.query('stripePaymentIntentId')
                .eq(payment.id)
                .exec();
            const {
                id: signupId,
                parentId,
                childId,
                eventId,
                purchasedProducts
            } = eventSignup.toJSON()[0];

            // Update the payment status
            const eventInSignup = await FundraiserSignup.get(signupId);
            eventInSignup.paymentDetails.paymentStatus = CONSTANTS.PAYMENT_STATUS.APPROVED;
            await eventInSignup.save();

            // Fetch user and child details
            const user = await User.query('id').eq(parentId).exec();
            const child = await Child.get({ id: childId });

            // Fetch event details
            const eventDetails = await Fundraiser.get({ id: eventId });
            let membershipType;
            let membershipId;

            if (eventDetails.fundraiserType === CONSTANTS.FUNDRAISER_TYPES.MEMBERSHIP) {
                const parsedPurchasedProducts = JSON.parse(purchasedProducts);
                membershipType = parsedPurchasedProducts[0].membershipType;
                membershipId = parsedPurchasedProducts[0].itemId;
            }

            await this.updateChildMembershipStatus(eventDetails, user[0], eventDetails.organizationId, membershipType, child, eventInSignup.id, membershipId);
            await eventDetails.save();

            await AwsOpenSearchService.registerEventInChildEvents(
                CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD,
                childId,
                {
                    fundraiserId: eventId,
                    fundraiserSignupId: signupId,
                    purchasedProducts: eventInSignup.purchasedProducts
                }
            );
            CONSOLE_LOGGER.info('payment intent: success', payment);
        }
        if (event.type === 'payment_intent.payment_failed') {
            const payment = event.data.object;
            const eventSignup = await FundraiserSignup.query('stripePaymentIntentId')
                .eq(payment.id)
                .exec();

            if (eventSignup.count) {
                const eventInSignup = await FundraiserSignup.get(
                    eventSignup.toJSON()[0].id
                );
                await eventInSignup.delete();
            }

            CONSOLE_LOGGER.info('payment intent: payment_failed ', payment);
        } else {
            CONSOLE_LOGGER.info(`Unhandled event type: ${event.type}`);
        }
        CONSOLE_LOGGER.info(JSON.stringify(event), 'all events');
        res.send();
    }

    static async checkExistingMembership (user, child, organizationId, membershipType) {
        const hasFamilyMembership = user.membershipsPurchased?.some(
            membership => membership.organizationId === organizationId && MOMENT(membership.endDate).isAfter(MOMENT().utc())
        );

        if (hasFamilyMembership) {
            throw {
                message: MESSAGES.ALREADY_HAVE_FAMILY_MEMBERSHIP,
                statusCode: 400
            };
        }

        if (membershipType === CONSTANTS.MEMBERSHIP_TYPES.CHILD) {
            if (!child) {
                throw {
                    message: MESSAGES.INVALID_CHILD_ID,
                    statusCode: 400
                };
            }

            const hasChildMembership = child.membershipsPurchased?.some(
                membership => membership.organizationId === organizationId && MOMENT(membership.endDate).isAfter(MOMENT().utc())
            );

            if (hasChildMembership) {
                throw {
                    message: MESSAGES.ALREADY_HAVE_CHILD_MEMBERSHIP,
                    statusCode: 400
                };
            }
        }
    }

    static async updateChildMembershipStatus (fundraiser, parentUser, organizationId, membershipType, child, fundraiserSignupId, membershipId) {
        if (fundraiser.fundraiserType === CONSTANTS.FUNDRAISER_TYPES.MEMBERSHIP) {
            const startDate = MOMENT(fundraiser.startDate).toDate();
            const endDate = MOMENT(fundraiser.endDate).toDate();
            const membershipIdStr = `${membershipId}`;
            if (membershipType === CONSTANTS.MEMBERSHIP_TYPES.FAMILY) {
                if (!parentUser.membershipsPurchased) {
                    parentUser.membershipsPurchased = [];
                }
                parentUser.membershipsPurchased.push({
                    fundraiserSignupId,
                    organizationId,
                    startDate,
                    endDate,
                    membershipId: membershipIdStr,
                    membershipType: CONSTANTS.MEMBERSHIP_TYPES.FAMILY
                });

                const childrenIds = [...new Set(parentUser.children)].filter(Boolean);

                if (childrenIds.length > 0) {
                    const children = await Child.batchGet(childrenIds);

                    for (const childItem of children) {
                        if (childItem.associatedOrganizations.includes(organizationId)) {
                            if (!childItem.membershipsPurchased) {
                                childItem.membershipsPurchased = [];
                            }
                            childItem.membershipsPurchased.push({
                                fundraiserSignupId,
                                organizationId,
                                startDate,
                                endDate,
                                membershipId: membershipIdStr,
                                membershipType: CONSTANTS.MEMBERSHIP_TYPES.FAMILY
                            });

                            await childItem.save();
                        }
                    }
                }

                await parentUser.save();
            } else if (membershipType === CONSTANTS.MEMBERSHIP_TYPES.CHILD) {
                if (!child.membershipsPurchased) {
                    child.membershipsPurchased = [];
                }
                child.membershipsPurchased.push({
                    fundraiserSignupId,
                    organizationId,
                    startDate,
                    endDate,
                    membershipId: membershipIdStr,
                    membershipType: CONSTANTS.MEMBERSHIP_TYPES.CHILD
                });

                await child.save();
            }
        }
    }

    /**
* @desc This function is being used to send event registration to email
* <AUTHOR>
* @since 16/11/2023
* @param {Object} reqObj reqObj
*/
    static async sendRegisterMail (reqObj, templateFile, subject) {
        const {
            childName,
            eventName,
            eventStartTime,
            eventEndTime,
            eventLocation,
            userEmail: email
        } = reqObj;
        const template = `emailTemplates/${templateFile}`;
        const currentDate = new Date();
        const year = currentDate.getFullYear();
        const templateVariables = {
            year,
            eventName,
            eventStartTime,
            eventEndTime,
            eventLocation,
            email: CONSTANTS.CLIENT_INFO.HELP_EMAIL,
            appname: CONSTANTS.APP_NAME,
            username: `${childName}`
        };
        await EmailService.prepareAndSendEmail(
            [email],
            subject,
            template,
            templateVariables
        );
    }

    static async patchChildBoosterDetails (req, locale) {
        const Validator = new PaymentValidator(req.body, locale);
        Validator.validateChildBoosterDetails();
        const { fundraiserSignupId, boosterGoalForChild, boosterMessageForChild } = req.body;
        const fundraiserSignup = await FundraiserSignup.get({ id: fundraiserSignupId });

        if (!fundraiserSignup) {
            throw {
                message: 'Fundraiser Sign up not found',
                statusCode: 400
            };
        }
        if (
            fundraiserSignup.boosterGoalForChild !== boosterGoalForChild ||
            fundraiserSignup.boosterMessageForChild !== boosterMessageForChild
        ) {
            await FundraiserSignup.update(
                { id: fundraiserSignupId },
                { boosterGoalForChild, boosterMessageForChild }
            );
        }
    }
}

module.exports = FundraiserRegisterService;
