const dynamoose = require('dynamoose');
const { v4: uuidv4 } = require('uuid');

const childSchema = new dynamoose.Schema({
    id: {
        hashKey: true,
        type: String,
        default: () => uuidv4()
    },
    firstName: {
        type: String,
        required: true
    },
    lastName: {
        type: String,
        required: true
    },
    dob: {
        type: Date
    },
    zipCode: {
        type: String,
        required: true
    },
    gender: {
        type: String,
        enum: ['boy', 'girl']
    },
    associatedColor: {
        type: String,
        enum: ['#2772ED', '#FACD01', '#90C33A', '#FF82A9', '#CB66D9', '#FF8F7C'],
        required: true
    },
    photoURL: {
        type: String
    },
    associatedOrganizations: {
        type: Array,
        schema: [String],
        default: []
    },
    isDeleted: {
        type: Number,
        enum: [0, 1],
        default: 0
    },
    homeRoom: {
        type: String
    },
    school: {
        type: String,
        index: {
            name: 'school-index',
            global: true,
            project: true
        }
    },
    childEvents: {
        type: Set,
        schema: [String]
    },
    guardians: {
        type: Array,
        schema: [String],
        default: []
    },
    followers: {
        type: Array,
        schema: [{
            type: Object,
            schema: {
                childId: {
                    type: String,
                    required: true
                },
                status: {
                    type: String,
                    enum: ['requested', 'accepted', 'rejected'],
                    default: 'requested'
                }
            }
        }]
    },
    followings: {
        type: Array,
        schema: [{
            type: Object,
            schema: {
                childId: {
                    type: String,
                    required: true
                },
                status: {
                    type: String,
                    enum: ['requested', 'accepted', 'rejected'],
                    default: 'requested'
                }
            }
        }]
    },
    connections: {
        type: Array,
        schema: [{
            type: Object,
            schema: {
                childId: {
                    type: String,
                    required: true
                },
                status: {
                    type: String,
                    enum: ['requestedTo', 'requestedBy', 'connected'],
                    required: true
                },
                notificationIds: {
                    type: Array,
                    schema: [String],
                    default: []
                }
            }
        }],
        default: []
    },
    membershipsPurchased: {
        type: Array,
        schema: [{
            type: Object,
            schema: {
                organizationId: {
                    type: String,
                    required: true
                },
                fundraiserSignupId: {
                    type: String,
                    required: true
                },
                membershipType: {
                    type: String,
                    required: true,
                    enum: ['child', 'family']
                },
                startDate: {
                    type: Date,
                    required: true
                },
                membershipId: {
                    type: String,
                    required: true
                },
                endDate: {
                    type: Date,
                    required: true
                }
            }
        }],
        default: []
    },
    createdBy: {
        type: String
    },
    updatedBy: {
        type: String
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('Child', childSchema);
