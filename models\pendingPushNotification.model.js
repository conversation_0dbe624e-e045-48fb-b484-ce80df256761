const dynamoose = require('dynamoose');
const { v4: uuidv4 } = require('uuid');

const PendingPushNotification = new dynamoose.Schema({
    id: {
        hashKey: true,
        type: String,
        default: () => uuidv4()
    },
    eventId: {
        type: String,
        index: {
            name: 'eventId-index',
            global: true
        }
    },
    pushNotificationTime: {
        type: Date,
        required: true
    },
    associatedChildId: {
        type: String,
        index: {
            name: 'associatedChildId-index',
            global: true
        }
    },
    notificationAction: {
        type: String
    },
    payload: {
        type: Object
    }
}, {
    saveUnknown: true
});

module.exports = dynamoose.model('PendingPushNotification', PendingPushNotification);
