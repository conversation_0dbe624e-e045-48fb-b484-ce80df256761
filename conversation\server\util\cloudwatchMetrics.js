const { CloudWatchClient, PutMetricDataCommand } = require('@aws-sdk/client-cloudwatch');
const CONSOLE_LOGGER = require('./logger');
const CONSTANTS = require('./constants');

const client = new CloudWatchClient({ region: process.env.AWS_REGION });

/**
 * Publishes a custom metric to CloudWatch.
 *
 * @param {number} value - The value for the metric.
 * @param {Object} [options] - Optional metadata.
 * @param {string} [options.metricName] - Name of the metric (required, enum: 'AICompanionQueryTime', 'AICompanionAccuracy').
 * @param {string} [options.namespace] - CloudWatch namespace (default: 'Vaalee/AICompanion').
 * @param {Array<Object>} [options.dimensions] - Array of dimension objects (default: []).
 * @param {string} [options.unit] - Unit for the metric (default: 'None').
 */
const publishCustomMetric = async (value, options = {}) => {
    const {
        metricName,
        namespace = CONSTANTS.CLOUDWATCH_METRIC_NAMESPACE,
        dimensions = [],
        unit = 'None'
    } = options;

    if (!metricName) {
        CONSOLE_LOGGER.error('Metric name is required');
        return;
    }

    const command = new PutMetricDataCommand({
        Namespace: namespace,
        MetricData: [
            {
                MetricName: metricName,
                Dimensions: dimensions,
                Unit: unit,
                Value: value
            }
        ]
    });

    try {
        await client.send(command);
    } catch (err) {
        CONSOLE_LOGGER.error('CloudWatch metric error:', err);
    }
};

module.exports = { publishCustomMetric };
