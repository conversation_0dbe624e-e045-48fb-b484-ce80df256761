const GeneralError = require('../util/GeneralError');
const REQUIRED = 'FIELD_REQUIRED';
const INVALID = 'FIELD_NOT_VALID';
const INVALID_NAME = 'NAME_NOT_VALID';

/**
 * Created by Growexx on 04/06/2020
 * @name validator
 */
class Validator {
    constructor (locale) {
        this.NOT_VALID = INVALID;
        this.REQUIRED = REQUIRED;
        this.INVALID_NAME = INVALID_NAME;

        if (locale) {
            this.__ = locale;
        }
    }

    /**
     * @desc This function is being used to validate first name and last name
     * <AUTHOR>
     * @param {string} name name
     * @param {string} field field name
     */
    name (name, field) {
        if (!name) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        }
        if (!CONSTANTS.REGEX.NAME.test(name)) {
            throw new GeneralError(this.__(INVALID_NAME, field), 400);
        }
    }

    /**
     * @desc This function is being used to validate email address
     * <AUTHOR>
     * @param {string} email Email
     */
    email (email) {
        if (!email) {
            throw new GeneralError(this.__(REQUIRED, 'Email'), 400);
        }
        if (!CONSTANTS.REGEX.EMAIL.test(email)) {
            throw new GeneralError(this.__(INVALID, 'Email'), 400);
        }
    }

    /**
     * @desc This function is being used to check mobile
     * <AUTHOR>
     * @param {string} mobile mobile
     */
    mobile (mobile, key = 'mobile') {
        if (!mobile) {
            throw new GeneralError(this.__(REQUIRED, key), 400);
        }
    }

    /**
     * @desc This function is being used to check password
     * <AUTHOR>
     * @param {string} password Password
     */
    password (password) {
        if (!password) {
            throw new GeneralError(this.__(REQUIRED, 'Password'), 400);
        }
        if (!CONSTANTS.REGEX.PASSWORD.test(password)) {
            throw new GeneralError(this.__(INVALID, 'Password'), 400);
        }
    }

    /**
     * @desc This function is being used to validate otp
     * <AUTHOR>
     * @param {string} id id
     * @since 21/09/2023
     */
    otp (otp, field = 'OTP') {
        if (!otp) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        }

        if (otp.toString().length !== CONSTANTS.OTPLENGTH) {
            throw new GeneralError(this.__(INVALID, field), 400);
        }
    }


    /**
     * @desc This function is being used to validate if field exists
     * <AUTHOR>
     * @param {Number|String} value field value
     * @param {String} name field name
     * @since 04/10/2023
     */
    field (value, name) {
        if (!value) {
            throw new GeneralError(this.__(REQUIRED, name), 400);
        }
    }
}

module.exports = Validator;
