const validation = require('../../util/validation');
const GeneralError = require('../../util/GeneralError');

/**
 * Class represents validations for parent.
 */
class FundraiserValidator extends validation {
    constructor (body, locale, files = [], query) {
        super(locale);
        this.body = body;
        this.files = files;
        this.query = query;
    }

    /**
     * @desc This function is being used to validate request for add event
     * <AUTHOR>
     * @since 08/11/2023
     */
    validate () {
        super.field(this.body.title, 'Event Title');
        super.field(this.body.startDate, 'Start Date');

        super.field(this.body.endDate, 'End Date');

        super.field(this.body.description, 'Event Description');

        if (this.body.fundraiserType && this.body.fundraiserType.trim()
            && !CONSTANTS.FUNDRAISER_TYPE.includes(this.body.fundraiserType.trim())) {
            throw new GeneralError(MESSAGES.INVALID_EVENT_TYPE, 400);
        }

        if (this.body.isMembershipEnabled?.trim() === 'true') {
            super.field(this.body.membershipBenefitAmount?.trim(), 'Membership benefit percentage');
            if (isNaN(Number(this.body.membershipBenefitAmount?.trim()))
                || Number(this.body.membershipBenefitAmount?.trim()) <= 0) {
                throw new GeneralError(MESSAGES.MEMBERSHIP_BENEFIT_INVALID, 400);
            }
        }

        if (this.body.status) {
            if (!CONSTANTS.ALLOWED_EVENT_STATUS.includes(this.body.status.trim())) {
                throw new GeneralError(MESSAGES.INVALID_EVENT_STATUS, 400);
            }
        }
        if (this.body.bannerImageName) {
            super.field(this.body.bannerImageName, 'Banner Image Name');
        }

        if (this.body.fundraiserType === 'booster') {
            super.field(this.body.boosterGoal, 'Booster Goal');
        }
    }
    validateMultiEvents () {
        super.field(this.body.title, 'Event Title');
        super.field(this.body.startDate, 'Start Date');
        super.field(this.body.endDate, 'End Date');
        super.field(this.body.description, 'Description');
    }



    /**
     * @desc This function is being used to validate request for add event
     * <AUTHOR>
     * @since 27/11/2023
     */
    validateAddFundraiser () {
        this.validate();
        super.field(this.body.organizationId, 'Organization Id');
    }

    /**
     * @desc This function is being used to validate request for update event
     * <AUTHOR>
     * @since 17/11/2023
     */
    validateUpdateFundraiser () {
        super.param(this.body.eventId, 'Event Id');
        super.uuid(this.body.eventId, 'Event Id');
        this.validate();
    }

    /**
     * @desc This function is being used to validate event image fileType
     * <AUTHOR>
     * @since 01/03/2021
     * @param {String} mimeType mimeType
     */
    fileType (files) {
        for (const file of files) {
            if (file.fieldname === 'image') {
                if (!file.mimetype || CONSTANTS.EVENT_IMAGE.ALLOWED_TYPE.indexOf(file.mimetype) === -1) {
                    throw {
                        message: MESSAGES.INVALID_FILE_FORMAT,
                        statusCode: 400
                    };
                }
            } else if (file.fieldname === 'documents' &&
                (!file.mimetype || CONSTANTS.USER_DOCUMENT_FILE.ALLOWED_TYPE.indexOf(file.mimetype) === -1)) {
                throw {
                    message: MESSAGES.INVALID_FILE_FORMAT,
                    statusCode: 400
                };
            } else {
                // Do nothing
            }
        }
    }

    /**
     * @desc This function is being used to validate if event id is sent in params
     * <AUTHOR>
     * @since 10/11/2023
     */
    validateEventId () {
        super.uuid(this.query.eventId, 'Event Id');
    }

    /**
     * @desc This function is being used to validate request for generate presigned url
     * <AUTHOR>
     * @since 18/12/2024
     */
    validateGeneratePresignedUrl () {
        super.field(this.query.fileName, 'File Name');
        super.field(this.query.organizationId, 'Organization Id');
    }

}


module.exports = FundraiserValidator;
