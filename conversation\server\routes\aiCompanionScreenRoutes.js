/**
 * This file contains routes used for AI Companion Screen.
 * <AUTHOR>
 * @since 12/06/2025
 * @name aiCompanionScreenRoutes
 */
const router = require('express').Router();

const AiCompanionScreenController = require('../services/aiCompanionScreen/aiCompanionScreenController');
const AuthMiddleware = require('../middleware/auth');

router.post('/add-update-feedback', AuthMiddleware, AiCompanionScreenController.addUpdateAiCompanionScreenFeedback);
router.get('/feedback-list', AuthMiddleware, AiCompanionScreenController.getAiCompanionScreenFeedbackList);

module.exports = router;
