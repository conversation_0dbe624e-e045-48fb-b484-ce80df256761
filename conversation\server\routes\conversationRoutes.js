/**
 * This file contains routes used for conversation.
 * Created by Growexx on 09/10/2024.
 * @name conversationRoutes
 */
const router = require('express').Router();
const UploadMiddleware = require('../middleware/upload');

const ConversationController = require('../services/conversation/groups/conversationController');
const MessageController = require('../services/conversation/messages/messageController');

const AuthMiddleware = require('../middleware/auth');
const AclMiddleware = require('../middleware/acl');

router.get('/group-list', AuthMiddleware, ConversationController.getGroupConversationList);
router.get('/group-members', AuthMiddleware, ConversationController.getGroupMembers);
router.post('/messageWithMedia', AuthMiddleware, UploadMiddleware.any(), MessageController.sendMessageWithMedia);
router.post('/personal-message-with-media', AuthMiddleware, UploadMiddleware.any(), MessageController.sendPersonalMessageWithMedia);
router.get('/generate-presigned-url', AuthMiddleware, ConversationController.generatePresignedUrl);
router.post('/flag-message', AuthMiddleware, MessageController.flagMessage);
router.get('/flag-message-list', AuthMiddleware, AclMiddleware, MessageController.getFlagMessageList);
router.patch('/update-flagged-message-status', AuthMiddleware, AclMiddleware, MessageController.updateFlagMessageStatus);
router.get('/flag-message-reasons', AuthMiddleware, MessageController.getFlagMessageReasons);
router.get('/get-children-list', AuthMiddleware, MessageController.getUserChildrenList);
router.get('/disabled-commenter-list', AuthMiddleware, AclMiddleware, MessageController.getDisabledCommenterList);
router.patch('/enable-commenter', AuthMiddleware, AclMiddleware, MessageController.enableCommenter);

module.exports = router;
