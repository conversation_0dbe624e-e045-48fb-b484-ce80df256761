const DBModelHelperService = require('./DBModelHelperService');
const RedisUtil = require('./redisUtil');
const CONSTANTS = require('./constants');

class Utils {
    /**
    * @description Upserts child and user events to a sorted set
    * <AUTHOR>
    * @param {Array} guardians - The guardians
    * @param {Redis} redis - The redis client
    * @param {String} keyForGuardian - The key for guardian
    * @param {Number} score - The score
    * @param {String} value - The value
    * @param {String} keyForChild - The key for child
    */
    static async upsertChildAndUserEventsToSortedSet ({ guardians, redis, keyForGuardian, score, value, keyForChild }) {
        await RedisUtil.addEventReferenceToSortedSet(
            redis,
            keyForChild,
            score,
            value
        );

        for (const guardian of guardians) {
            const guardianKey = `${keyForGuardian}:${guardian}`;
            await RedisUtil.addEventReferenceToSortedSet(
                redis,
                guardianKey,
                score,
                value
            );
        }
    }

    /**
     * @description Upserts child details to a hash set
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {String} key - The key
     * @param {String} field - The field
     * @param {Object} childDetails - The child details
     * @returns {Promise<void>}
     */
    static async upsertChildDetailsToHashSet ({ redis, key, field, childDetails }) {
        await RedisUtil.setHashValue(
            redis,
            key,
            field,
            childDetails
        );
    }

    /**
     * @description Upserts organization details to a hash set
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {String} key - The key
     * @param {String} field - The field
     * @param {Object} organizationDetails - The organization details
     */
    static async upsertOrganizationDetailsToHashSet ({ redis, key, field, organizationDetails }) {
        await RedisUtil.setHashValue(
            redis,
            key,
            field,
            organizationDetails
        );
    }

    /**
     * @description Gets a map of children details with the given attributes
     * <AUTHOR>
     * @param {Array} children - The children
     * @param {Array} attributes - The attributes
     * @returns {Promise<Object>} The children details map
     */
    static async getChildrenDetailsMapWithAttributes (children, attributes) {
        const uniqueChildrenIds = new Set(children.map((child) => child.childId));
        let childrenDetailsMap = {};
        if (uniqueChildrenIds.size > 0) {
            childrenDetailsMap = (
                await DBModelHelperService.batchGetChildDetailsWithAttributes(
                    Array.from(uniqueChildrenIds).filter(Boolean),
                    attributes
                )
            ).reduce((acc, child) => {
                acc[child.id] = child;
                return acc;
            }, {});
        }

        return childrenDetailsMap;
    }

    /**
     * @description Generates a prefix for a key
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} keyPrefix - The key prefix
     * @returns {String} The prefix
     */
    static generatePrefixForKey ({ versionPrefix, keyPrefix }) {
        return `${versionPrefix}:${keyPrefix}`;
    }

    /**
     * @description Generates a key
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} keyPrefix - The key prefix
     * @param {String} id - The id
     * @returns {String} The key
     */
    static generateKey ({ versionPrefix, keyPrefix, id }) {
        return `${this.generatePrefixForKey({ versionPrefix, keyPrefix })}:${id}`;
    }

    /**
     * @description Generates a key for a user
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} userId - The user id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getUserKey ({ versionPrefix, userId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_USER_EVENTS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_USER_EVENTS, id: userId });
    }

    /**
     * @description Generates a key for a registered user
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} userId - The user id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getRegisteredUserKey ({ versionPrefix, userId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_USER_REGISTERED_EVENTS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_USER_REGISTERED_EVENTS, id: userId });
    }

    /**
     * @description Generates a key for a calendar user
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} userId - The user id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getCalendarUserKey ({ versionPrefix, userId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_USER_CALENDAR_EVENTS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_USER_CALENDAR_EVENTS, id: userId });
    }

    /**
     * @description Generates a key for a child
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} childId - The child id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getChildKey ({ versionPrefix, childId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CHILD_EVENTS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CHILD_EVENTS, id: childId });
    }

    /**
     * @description Generates a key for a registered child
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} childId - The child id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getRegisteredChildKey ({ versionPrefix, childId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CHILD_REGISTERED_EVENTS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CHILD_REGISTERED_EVENTS, id: childId });
    }

    /**
     * @description Generates a key for a calendar child
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} childId - The child id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getCalendarChildKey ({ versionPrefix, childId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CHILD_CALENDAR_EVENTS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CHILD_CALENDAR_EVENTS, id: childId });
    }

    /**
     * @description Generates a key for a child details
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} childId - The child id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getChildDetailsKey ({ versionPrefix, childId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CHILD_DETAILS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CHILD_DETAILS, id: childId });
    }

    /**
     * @description Generates a key for a event details
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} eventId - The event id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getEventDetailsKey ({ versionPrefix, eventId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_EVENT_DETAILS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_EVENT_DETAILS, id: eventId });
    }

    /**
     * @description Generates a key for a fundraiser details
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} fundraiserId - The fundraiser id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getFundraiserDetailsKey ({ versionPrefix, fundraiserId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_FUNDRAISER_DETAILS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_FUNDRAISER_DETAILS, id: fundraiserId });
    }

    /**
     * @description Generates a key for a post details
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} postId - The post id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getPostDetailsKey ({ versionPrefix, postId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_POST_DETAILS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_POST_DETAILS, id: postId });
    }

    /**
     * @description Generates a key for a organization details
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} organizationId - The organization id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getOrganizationDetailsKey ({ versionPrefix, organizationId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_ORGANIZATION_DETAILS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_ORGANIZATION_DETAILS, id: organizationId });
    }

    /**
     * @description Generates a key for user details
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} userId - The user id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key prefix
     * @returns {String} The key
     */
    static getUserDetailsKey ({ versionPrefix, userId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_USER_DETAILS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_USER_DETAILS, id: userId });
    }

    /**
     * @description Generates a key for conversation members
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} groupId - The group id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key prefix
     * @returns {String} The key
    */
    static getConversationMembersKey ({ versionPrefix, groupId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CONVERSATION_MEMBERS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CONVERSATION_MEMBERS, id: groupId });
    }

    /**
     * @description Generates a key for user conversations
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} userId - The user id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key prefix
     * @returns {String} The key
    */
    static getUserConversationsKey ({ versionPrefix, userId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_USER_CONVERSATIONS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_USER_CONVERSATIONS, id: userId });
    }

    /**
     * @description Generates a key for conversation details
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} conversationId - The conversation id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key prefix
     * @returns {String} The key
    */
    static getConversationDetailsKey ({ versionPrefix, conversationId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CONVERSATION_DETAILS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CONVERSATION_DETAILS, id: conversationId });
    }

    /**
     * @description Generates a key for conversation messages
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} conversationId - The conversation id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key prefix
     * @returns {String} The key
    */
    static getConversationMessagesKey ({ versionPrefix, conversationId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CONVERSATION_MESSAGES })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CONVERSATION_MESSAGES, id: conversationId });
    }
}

module.exports = Utils;
