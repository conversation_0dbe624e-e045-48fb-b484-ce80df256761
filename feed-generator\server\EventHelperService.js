const CONSTANTS = require('./constants');
const RedisUtil = require('./redisUtil');
const MOMENT = require('moment');
const DBModelHelperService = require('./DBModelHelperService');
const FeedHelperService = require('./FeedHelperService');
const Utils = require('./Utils');

class EventHelperService {
    /**
     * @description Adds a reference to a user and children
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} event - The event object
     * @param {Number} timestamp - The timestamp
     * @param {Boolean} isCreated - Whether the event is created
     * @param {Object} oldEvent - The old event object
     * @param {String} versionPrefix - The version prefix
     */
    static async addReferenceToUserAndChildren ({
        redis,
        event,
        timestamp,
        isCreated,
        oldEvent,
        versionPrefix
    }) {
        if (event.status === CONSTANTS.EVENT_STATUS.PUBLISHED) {
            const children = await DBModelHelperService.getChildOrganizationMapping(
                event.organizationId
            );

            const childrenDetailsMap = await Utils.getChildrenDetailsMapWithAttributes(children);

            await this.handlePublishedEvent({
                children,
                childrenDetailsMap,
                event,
                redis,
                timestamp,
                versionPrefix
            });

            return true;
        } else if (
            !isCreated &&
            oldEvent.status === CONSTANTS.EVENT_STATUS.PUBLISHED
        ) {
            await FeedHelperService.deleteAllEventReferences({
                redis,
                versionPrefix,
                event: oldEvent,
                shouldDeleteRegisteredEvents: false,
                isCalendarEvent: event.eventType === CONSTANTS.EVENT_TYPE.CALENDAR
            });
            return false;
        } else {
            return true;
        }
    }

    /**
     * @description Handles a published event
     * <AUTHOR>
     * @param {Redis} redis - The redis client
     * @param {Array} children - The children
     * @param {Object} childrenDetailsMap - The children details map
     * @param {Object} event - The event
     * @param {Number} timestamp - The timestamp
     * @param {String} versionPrefix - The version prefix
     */
    static async handlePublishedEvent ({ children, childrenDetailsMap, event, redis, timestamp, versionPrefix }) {
        for (const child of children) {
            const { childId } = child;
            const { eventType, details, id: eventId } = event;
            const childDetails = childrenDetailsMap[childId] ?? {};

            const childKey = Utils.getChildKey({ versionPrefix, childId });
            const childRegisteredKey = Utils.getRegisteredChildKey({ versionPrefix, childId });
            const childCalendarKey = Utils.getCalendarChildKey({ versionPrefix, childId });

            const score = MOMENT(details.startDateTime).toDate().getTime();

            if (eventType === CONSTANTS.EVENT_TYPE.CALENDAR) {
                await this.handleCalendarEvent({
                    redis,
                    childCalendarKey,
                    score,
                    event,
                    childDetails,
                    versionPrefix
                });
            } else {
                await this.handleEvent({
                    redis,
                    childKey,
                    childRegisteredKey,
                    timestamp,
                    score,
                    eventId,
                    childDetails,
                    versionPrefix
                });
            }
        }

        return true;
    }

    /**
     * @description Handles a calendar event
     * <AUTHOR>
     * @param {Redis} redis - The redis client
     * @param {String} childCalendarKey - The child calendar key
     * @param {Number} score - The score
     * @param {Object} event - The event
     * @param {Object} childDetails - The child details
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async handleCalendarEvent ({ redis, childCalendarKey, score, event, childDetails, versionPrefix }) {
        const { id, guardians = [] } = childDetails;
        const { id: eventId } = event;
        const value = JSON.stringify({
            eventId,
            childId: id
        });

        await Utils.upsertChildAndUserEventsToSortedSet({
            guardians,
            redis,
            score,
            value,
            keyForChild: childCalendarKey,
            keyForGuardian: Utils.getCalendarUserKey({ versionPrefix, shouldGenerateKeyPrefix: true })
        });

        await Utils.upsertChildDetailsToHashSet({
            redis,
            childDetails,
            key: Utils.getChildDetailsKey({ versionPrefix, childId: id }),
            field: 'details'
        });

        return childDetails;
    }

    /**
     * @description Handles an event
     * <AUTHOR>
     * @param {Redis} redis - The redis client
     * @param {String} childKey - The child key
     * @param {String} childRegisteredKey - The child registered key
     * @param {Number} timestamp - The timestamp
     * @param {Number} score - The score
     * @param {String} eventId - The event id
     * @param {Object} childDetails - The child details
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async handleEvent ({ redis, childKey, childRegisteredKey, timestamp, score, eventId, childDetails, versionPrefix }) {
        const { id, guardians } = childDetails;

        const childEvents = await RedisUtil.getElementsOfSortedSetByScore(
            redis,
            childKey,
            timestamp,
            CONSTANTS.PLUS_INF
        );
        const childEvent = childEvents.find((childEvent) => {
            const feed = JSON.parse(childEvent);
            return eventId === feed.eventId;
        });

        if (childEvent) {
            await Utils.upsertChildAndUserEventsToSortedSet({
                guardians,
                redis,
                score,
                value: childEvent,
                keyForGuardian: Utils.getUserKey({ versionPrefix, shouldGenerateKeyPrefix: true }),
                keyForChild: childKey
            });
        } else {
            await this.handleEventNotInChildKey({
                redis,
                childKey,
                childRegisteredKey,
                timestamp,
                score,
                eventId,
                childDetails,
                versionPrefix
            });
        }

        await Utils.upsertChildDetailsToHashSet({
            redis,
            childDetails,
            key: Utils.getChildDetailsKey({ versionPrefix, childId: id }),
            field: 'details'
        });

        return childDetails;
    }

    /**
     * @description Handles an event not in the child key
     * <AUTHOR>
     * @param {Redis} redis - The redis client
     * @param {String} childKey - The child key
     * @param {String} childRegisteredKey - The child registered key
     * @param {Number} timestamp - The timestamp
     * @param {Number} score - The score
     * @param {String} eventId - The event id
     * @param {Object} childDetails - The child details
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async handleEventNotInChildKey ({
        redis,
        childKey,
        childRegisteredKey,
        timestamp,
        score,
        eventId,
        childDetails,
        versionPrefix
    }) {
        const { guardians = [] } = childDetails;

        const childRegisteredEvents =
            await RedisUtil.getElementsOfSortedSetByScore(
                redis,
                childRegisteredKey,
                timestamp,
                CONSTANTS.PLUS_INF
            );
        const childRegisteredEvent = childRegisteredEvents.find(
            (childEvent) => {
                const feed = JSON.parse(childEvent);
                return eventId === feed.eventId;
            }
        );

        if (childRegisteredEvent) {
            await Utils.upsertChildAndUserEventsToSortedSet({
                guardians,
                redis,
                score,
                value: childRegisteredEvent,
                keyForGuardian: Utils.getRegisteredUserKey({ versionPrefix, shouldGenerateKeyPrefix: true }),
                keyForChild: childRegisteredKey
            });
        } else {
            const value = JSON.stringify({
                eventId,
                childId: childDetails.id
            });

            await Utils.upsertChildAndUserEventsToSortedSet({
                guardians,
                redis,
                score,
                value,
                keyForGuardian: Utils.getUserKey({ versionPrefix, shouldGenerateKeyPrefix: true }),
                keyForChild: childKey
            });
        }

        return childDetails;
    }
}

module.exports = EventHelperService;
