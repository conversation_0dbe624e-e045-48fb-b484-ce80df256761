const PersonalConversation = require('../../../models/personalConversation.model');
const SendMessageService = require('../../sendSocketMessageService');

/**
 * @description Block or unblock personal conversation
 * <AUTHOR>
 * @since 09/01/2025
 * @param {Object} eventBody - The event body
 * @returns {Object} - The response object
 */
module.exports.handleBlockPersonalConversation = async (eventBody) => {
    const isBlocked = eventBody?.isBlocked;
    const action = isBlocked ? 'block' : 'unblock';
    try {
        const { userId, receiverId } = eventBody;
        const personalConversation = await getPersonalConversation(userId, receiverId);

        await PersonalConversation.update({ userAId: userId, userBId: receiverId }, { isBlocked });

        const data = {
            userAId: userId,
            userBId: receiverId,
            isBlocked,
            conversationId: personalConversation.conversationId
        };

        await SendMessageService.sendMessagesToUsers(
            [userId, receiverId],
            {
                data,
                actionType: 'BLOCK_PERSONAL_CONVERSATION'
            }
        );

        return {
            statusCode: 200,
            message: `Personal conversation ${action}ed successfully`,
            data: JSON.stringify(data),
            action: 'sendPersonalMessage',
            actionType: 'BLOCK_PERSONAL_CONVERSATION'
        };
    } catch (error) {
        if (error.statusCode) {
            return error;
        }

        return {
            statusCode: 500,
            message: `Failed to ${action} personal conversation`,
            action: 'sendPersonalMessage',
            actionType: 'BLOCK_PERSONAL_CONVERSATION'
        };
    }
};

/**
 * @description Get personal conversation
 * <AUTHOR>
 * @since 09/01/2025
 * @param {String} userId - The user ID
 * @param {String} receiverId - The receiver ID
 * @returns {Object} - The personal conversation object
 */
const getPersonalConversation = async (userId, receiverId) => {
    const personalConversation = await PersonalConversation.get({ userAId: userId, userBId: receiverId });
    if (!personalConversation) {
        throw {
            statusCode: 404,
            body: 'Personal conversation not found',
            action: 'sendPersonalMessage',
            actionType: 'BLOCK_PERSONAL_CONVERSATION'
        };
    }
    return personalConversation;
};
