const SignInValidator = require('./signInValidator');
const Utils = require('../../util/utilFunctions');
const User = require('../../models/user.model');
const Child = require('../../models/child.model');
const Organization = require('../../models/organization.model');
const Cognito = require('../../util/cognito');
const jwksClient = require('jwks-rsa');
const jwt = require('jsonwebtoken');
const Crypt = require('../../util/crypt');
const UploadService = require('../../util/uploadService');
const PendingPartnerInvite = require('../../models/pendingPartnerInvite.model');

/**
 * Class represents services for signin.
 */
class SignInService {
    /**
     * @desc This function is being used to sign in user
     * <AUTHOR>
     * @since 17/10/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {Object} req.body.email email
     * @param {Object} req.body.password password
     * @param {Object} locale Locale passed from request
     * @param {Object} res Response
     */
    static async signIn (req, locale) {
        const Validator = new SignInValidator(req.body, locale);
        Validator.validate();
        const email = req.body.email.toLowerCase();
        return await SignInService.userLogin(email, req.body.password, req.body.fcmToken, req.headers?.platform);
    }

    /**
     * @desc This function is being used to sign in user
     * <AUTHOR>
     * @since 07/03/2024
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {String} req.body.givenName firstName
     * @param {String} req.body.familyName lastName
     * @param {Object} locale Locale passed from request
     * @param {Object} res Response
     */
    static async socialSignIn (req) {
        // No validation needed. Check the token

        return await SignInService.doSocialSignIn(req.headers?.authorization, req.headers?.platform,
            req.body?.givenName, req.body?.familyName);

    }

    /**
     * @desc This function is being used to end user login
     * <AUTHOR>
     * @since 17/10/2023
     * @param {Object} email email
     * @param {Object} password password
     */
    static async userLogin (email, password, fcmToken, platform) {
        CONSOLE_LOGGER.time('login:cognitoLogin');
        const cognitoUser = await Cognito.login({ email, password });
        if (!cognitoUser) {
            throw {
                message: MESSAGES.LOGIN_FAILED,
                statusCode: 401
            };
        }
        CONSOLE_LOGGER.timeEnd('login:cognitoLogin');
        const decodedJwt = jwt.decode(cognitoUser.AuthenticationResult.IdToken, { complete: true });
        const decodedJwtPayload = decodedJwt.payload;
        CONSOLE_LOGGER.time('login:getUser');
        let user = await User.query('email').eq(decodedJwtPayload.email)
            .where('isGuestUser').not().eq(true)
            .exec();
        if (!user.length) {
            throw {
                message: MESSAGES.LOGIN_FAILED,
                statusCode: 401
            };
        }
        user = user[0];
        CONSOLE_LOGGER.timeEnd('login:getUser');
        if (user.isVerified && user.status === 'active' && !user.isDeleted) {
            const { id } = user;
            const { token } = await Crypt.getUserToken(user);
            const conversationToken = await Crypt.getConversationToken(id);
            const updatedUser = await User.update({ id, email }, {
                fcmToken,
                token
            });
            return await this.formatUserResponse({ ...updatedUser, conversationToken }, platform);
        } else {
            throw {
                message: MESSAGES.INACTIVE_USER,
                statusCode: 423
            };
        }
    }

    static async doSocialSignIn (tokenProvided, platform, givenName, familyName) {

        if (tokenProvided) {
            const token = tokenProvided.split(' ')[1];
            const decodedJwt = jwt.decode(token, { complete: true });
            let decodedJwtPayload = decodedJwt?.payload;

            if (decodedJwtPayload?.iss.includes('google')) {
                decodedJwtPayload = await this.verifyGoogleToken(token);
            } else if (decodedJwtPayload?.iss.includes('apple')) {
                decodedJwtPayload = await this.verifyAppleToken(token);
            } else {
                throw {
                    message: MESSAGES.INVALID_REQUEST,
                    statusCode: 400
                };
            }

            let user = await User.query('email').eq(decodedJwtPayload.email.trim())
                .where('isGuestUser').not().eq(true)
                .exec();

            if (!user.length) {
                const obj = {
                    firstName: decodedJwtPayload.given_name?.trim() || givenName?.trim() || decodedJwtPayload.email?.split('@')[0]?.trim(),
                    lastName: decodedJwtPayload.family_name?.trim() || familyName?.trim() || '',
                    email: decodedJwtPayload.email?.trim().toLowerCase(),
                    id: decodedJwtPayload.sub.trim(),
                    status: CONSTANTS.STATUS.ACTIVE,
                    provider: decodedJwtPayload.iss.includes(CONSTANTS.PROVIDER.GOOGLE)
                        ? CONSTANTS.PROVIDER.GOOGLE : CONSTANTS.PROVIDER.APPLE,
                    isVerified: 1,
                    isFeedsGenerated: false,
                    token
                };
                const { token: newToken } = await Crypt.getUserToken(obj);
                const conversationToken = await Crypt.getConversationToken(obj.id);
                obj.token = newToken;
                const invitedPartner = await PendingPartnerInvite.query('invitedPartner')
                    .eq(decodedJwtPayload.email.trim()).using('invitedPartner-index').exec();
                const pendingInviteMap = [];
                if (invitedPartner.length) {
                    for (const item of invitedPartner) {
                        const { inviterPartnerId, inviterPartnerEmail, children } = item;
                        pendingInviteMap.push({
                            inviterPartnerId,
                            inviterPartnerEmail,
                            children
                        });
                    }
                }
                obj.partnerInvites = pendingInviteMap;
                obj.sendInvites = [];
                user = new User(obj);
                const savedUser = await user.save();
                for (const partner of invitedPartner) {
                    PendingPartnerInvite.delete({ id: partner.id });
                }
                // get the updated user details
                user = [{ ...savedUser, conversationToken }];
            } else {
                // Add conversation token for existing users
                const conversationToken = await Crypt.getConversationToken(user[0].id);
                user = [{ ...user[0], conversationToken }];
            }

            return await this.formatUserResponse(user[0], platform);
        } else {
            throw {
                message: MESSAGES.INVALID_REQUEST,
                statusCode: 403
            };
        }

    }

    static async verifyAppleToken (tokenProvided) {

        return new Promise((resolve, reject) => {
            const decodedToken = jwt.decode(tokenProvided, { complete: true });
            const kid = decodedToken.header.kid;

            const APPLE_AUTH_URL = process.env.APPLE_AUTH_URL;

            const client = jwksClient({
                jwksUri: APPLE_AUTH_URL,
                timeout: 30000
            });
            client.getSigningKey(kid).then(key => {
                const publicKey = key.getPublicKey();
                resolve(jwt.verify(tokenProvided, publicKey));
            }).catch(err => {
                reject(err);
            });
        });

    }

    static async verifyGoogleToken (tokenProvided) {
        return new Promise((resolve, reject) => {
            try {
                const decodedToken = jwt.decode(tokenProvided, { complete: true });
                const payload = decodedToken.payload;

                const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;

                // Verify the issuer and audience
                if (!payload.iss?.includes('accounts.google.com')) {
                    reject(new Error('Invalid issuer'));
                } else if (payload.aud !== GOOGLE_CLIENT_ID) {
                    reject(new Error('Invalid audience'));
                } else {
                    resolve(payload);
                }
            } catch (error) {
                reject(error);
            }
        });
    }

    static async formatUserResponse (user, platform) {
        CONSOLE_LOGGER.time('User:formatUserResponse');
        const children = await this.fetchChildren(user, platform);
        const organizations = await this.fetchOrganizations(user, platform);
        const selectedUserAttributes = ['id', 'firstName', 'lastName', 'accessLevel',
            'email', 'countryCode', 'phoneNumber', 'status', 'stripeCustomerId', 'token', 'refreshToken',
            'provider', 'hasReadChatGuidelines', 'conversationToken'];
        const formattedUser = Utils.selectAttributes(selectedUserAttributes, user);
        CONSOLE_LOGGER.timeEnd('User:formatUserResponse');
        return { ...formattedUser,
            children: children.childrenWithSignedUrls ?? [],
            associatedOrganizations: organizations ?? [],
            connections: children.connections ?? [] };
    }


    static async fetchChildren (user, platform) {
        if (!platform || platform === 'mobile') {
            const children = user.children.length ? await Child.batchGet(user.children) : [];
            const childrenWithSignedUrls = children.length ?
                await Promise.all(children.map(async child => {
                    const photoURL = child.photoURL ? await UploadService.getSignedUrl(child.photoURL) : null;
                    const selectedChildAttributes = ['id', 'firstName', 'lastName', 'gender',
                        'associatedColor', 'school', 'homeRoom', 'dob', 'zipCode', 'associatedOrganizations'];
                    const formattedChild = Utils.selectAttributes(selectedChildAttributes, child);
                    return { ...formattedChild, photoURL };
                }))
                : [];
            const connections = await this.getConnectionsList(children);
            return { childrenWithSignedUrls, connections };
        }
        return { childrenWithSignedUrls: [], connections: [] };
    }

    static async fetchOrganizations (user, platform) {
        if (!platform || platform === 'web') {
            return user.associatedOrganizations.length &&
                (await Promise.all(user.associatedOrganizations.map(async organization => {
                    const attributesToSelect = ['address', 'country', 'name', 'state', 'city', 'category', 'id', 'zipCode'];
                    const org = await Organization.get({ id: organization.organizationId },
                        { attributes: attributesToSelect });

                    if (org.isDeleted === 1) {
                        return null;
                    }
                    const associatedOrgRole = user.associatedOrganizations.find(
                        (organization) => organization.organizationId === org.id
                    ).role;
                    return { ...org, associatedOrgRole };
                }))).filter(org => org !== null);
        }
        return [];
    }

    static async getConnectionsList (children) {
        if (!children.length) {
            return [];
        }

        const connectedChildIds = this.getConnectedChildIds(children);

        if (!connectedChildIds.length) {
            return [];
        }

        const connectedChildren = await this.getConnectedChildren(connectedChildIds);
        const organizationMap = await this.getOrganizationMap(connectedChildren);

        this.assignOrganizationNames(connectedChildren, organizationMap);

        const childMap = new Map(connectedChildren.map(child => [child.id, child]));

        return await this.buildResult(children, childMap);
    }

    static getConnectedChildIds (children) {
        return [...new Set(children.flatMap(child =>
            child.connections
                .filter(connection => connection.status === 'connected')
                .map(connection => connection.childId)
        ))];
    }

    static async getConnectedChildren (childIds) {
        return await Child.batchGet(childIds, {
            attributes: ['id', 'firstName', 'lastName', 'photoURL', 'school', 'homeRoom', 'associatedColor']
        });
    }

    static async getOrganizationMap (connectedChildren) {
        const schoolIds = [...new Set(connectedChildren.map(child => child.school))];
        const homeroomIds = [...new Set(connectedChildren.map(child => child.homeRoom).filter(Boolean))];
        const organizationIds = [...new Set([...schoolIds, ...homeroomIds])];
        const organizations = await Organization.batchGet(organizationIds, { attributes: ['id', 'name'] });

        return new Map(organizations.map(org => [org.id, org]));
    }

    static assignOrganizationNames (connectedChildren, organizationMap) {
        connectedChildren.forEach(child => {
            child.school = organizationMap.get(child.school).name;
            child.homeRoom = organizationMap.get(child.homeRoom)?.name;
        });
    }

    static async buildResult (children, childMap) {
        return await Promise.all(children.map(async child => ({
            childId: child.id,
            connections: await Promise.all(child.connections
                .filter(connection => connection.status === CONSTANTS.CONNECTION_STATUS.CONNECTED)
                .map(async connection => {
                    const connectedChild = childMap.get(connection.childId);
                    return {
                        ...connectedChild,
                        photoURL: connectedChild.photoURL ? await UploadService.getSignedUrl(connectedChild.photoURL) : null,
                        id: undefined,
                        childId: connectedChild.id
                    };
                }))
        })));
    }
}

module.exports = SignInService;
