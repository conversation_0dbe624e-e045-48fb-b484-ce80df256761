const dynamoose = require('dynamoose');
const { v4: uuidv4 } = require('uuid');
const CONSTANTS = require('../util/constants');

const flaggedMessageSchema = new dynamoose.Schema({
    id: {
        hashKey: true,
        type: String,
        default: () => uuidv4()
    },
    orgId: {
        type: String,
        required: true,
        index: {
            global: true,
            name: 'orgId-status-index',
            rangeKey: 'status'
        }
    },
    conversationId: {
        type: String,
        required: true
    },
    messageId: {
        type: String,
        required: true,
        index: {
            global: true,
            name: 'messageId-index'
        }
    },
    flaggedBy: {
        type: String,
        required: true
    },
    reasons: {
        type: Array,
        schema: [{
            type: String,
            enum: Object.values(CONSTANTS.FLAG_MESSAGE_REASON)
        }],
        required: true
    },
    status: {
        type: String,
        enum: Object.values(CONSTANTS.FLAG_MESSAGE_STATUS),
        default: CONSTANTS.FLAG_MESSAGE_STATUS.PENDING
    },
    reviewedBy: {
        type: String,
        required: false
    },
    reviewedAt: {
        type: Date,
        required: false
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('FlaggedMessage', flaggedMessageSchema);
