const FundraiserRegisterService = require('./fundraiserRegisterService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for Register.
 */
class FundRaiserRegisterController {
    /**
     * @desc This function is being used to register user and payment for event registration.
     * <AUTHOR>
     * @since 15/11/2023
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async registerUserAndPayment (req, res) {
        try {
            const data = await FundraiserRegisterService.registerUserAndPayment(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, res.__('EVENT_REGISTER_SUCCESS'));
        } catch (error) {
            CONSOLE_LOGGER.error('Error in registerUserAndPayment: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used for Register webhooks for stripe connect account creation and updation.
     * <AUTHOR>
     * @since 16/11/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async stripeWebhook (req, res) {
        try {
            await FundraiserRegisterService.stripeWebhook(req, res, req.locale);
        } catch (error) {
            CONSOLE_LOGGER.error('Error from stripe webhook at payment time: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    static async patchChildBoosterDetails (req, res) {
        try {
            const data = await FundraiserRegisterService.patchChildBoosterDetails(req, res.__);
            Utils.sendResponse(null, data, res, res.__('BOOSTER_CHILD_DETAILS_PATCHED'));
        } catch (error) {
            CONSOLE_LOGGER.error('Error in Patch child booster details: ', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = FundRaiserRegisterController;
