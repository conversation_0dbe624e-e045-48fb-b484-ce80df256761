module.exports = {
    // For AES, this is always 16
    IV_LENGTH: 16,
    LOG_LEVEL: 'debug',
    POST_EXPIRATION_DAYS: 'PostExpirationDays',
    PROFILE_PICTURE: {
        MIN_SIZE: 5120,
        MAX_SIZE: 5242880,
        ALLOWED_TYPE: ['image/jpg', 'image/jpeg', 'image/png']
    },
    USER_DOCUMENT_FILE: {
        MIN_SIZE: 10240,
        MAX_SIZE: 5242880,
        ALLOWED_TYPE: ['image/jpg', 'image/jpeg', 'image/png', 'application/pdf']
    },
    REGEX: {
        EMAIL: /^[A-Za-z0-9](\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\.([a-z]{1,6}\.)?[a-z]{2,6}$/,
        FIRSTNAME: /^[a-zA-Z0-9,'~._^ -]*$/,
        SURNAME: /^[a-zA-Z0-9,'~._^ -]*$/,
        ALPHA_ONLY: /^[a-zA-Z']*$/,
        ALPHA_SPECIAL_CHAR: /^[ A-Za-z0-9_@./#&+-]*$/,
        ALPHA_SPECIAL_CHAR_EXCEPT_NUMBER: /^[ A-Za-z_@./#&+-]*$/,
        FULL_ACCESS: /^[^<> ?//\\]+$/,
        ALPHA_NUMARIC: /^[\w@ ]+$/,
        templateName: /^[ A-Za-z0-9_@./#&+-]*$/,
        SUBJECT: /^[ A-Za-z0-9_@./#&+-]*$/,
        URL: /(http(s)?:\/\/www\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\.[a-z]{2,4}\b([-a-zA-Z0-9@:%+.~#?&//=]*)/,
        MOBILE: /^([+]\d{1,2})?\d{10}$/
    },
    OTPLENGTH: 6,
    STATUS: {
        INACTIVE: 'inactive',
        ACTIVE: 'active',
        SUSPENDED: 'suspended',
        FREEZED: 'freezed'
    },
    ENVIRONMENT: {
        TESTING: 'testing',
        LOCAL: 'local',
        DEV: 'dev',
        PRODUCTION: 'production'
    },
    EVENT_TYPES: {
        EVENT: 'event'
    },
    TRIGGER: {
        REQUESTED_BY: 'requestedBy',
        EVENT_SIGNUP: 'eventSignup',
        FUNDRAISER_SIGNUP: 'fundraiserSignup',
        EVENT_UPDATED: 'eventSignupUpdate',
        POST_CREATED: 'postCreated',
        BOOSTER_DONATION: 'boosterDonation',
        CONVERSATION: 'conversation',
        PERSONAL_CONVERSATION: 'personalConversation'
    },
    DEVELOPERS_EMAIL: '<EMAIL>',
    CONTACT_EMAIL: '<EMAIL>',
    SES_HOST: 'email-smtp.eu-west-1.amazonaws.com',
    ROLE: {
        USER: 1,
        ADMIN: 4
    },
    DEFAULT_PAGE_SIZE: 15,
    AWS_S3_PUBLIC_BUCKET: 'vaalee-assets',
    PAYMENT_STATUS: {
        PENDING: 'pending',
        APPROVED: 'approved',
        CANCELLED: 'cancelled',
        PAYMENT_INITIATED: 'payment-initiated'
    },
    DEFAULT_NOTIFICATION_TITLE: 'Alert',
    ORG_ROLE: {
        SUPER_ADMIN: 'super admin',
        ADMIN: 'admin'
    },
    NOTIFICATION_ROUTE: {
        EVENT_DETAILS: 'eventDetails'
    },
    APP_NAME: 'Vaalee',
    CONTACT_US_SUBJECT: 'New Contact Form Submission',
    GROUP_CONVERSATION_TYPES: {
        EVENT: 'event',
        ORGANIZATION: 'organization',
        ORGANIZATION_ADMIN: 'organization_admin'
    },
    GROUP_CONVERSATION_STATUS: {
        ACTIVE: 'active',
        EXPIRED: 'expired'
    },
    GROUP_CONVERSATION_EXPIRATION_DAYS: 2,
    KEY_FOR_USER_EVENTS: 'user-events',
    KEY_FOR_USER_REGISTERED_EVENTS: 'user-registered-events',
    KEY_FOR_USER_CALENDAR_EVENTS: 'user-calendar-events',
    KEY_FOR_CHILD_EVENTS: 'child-events',
    KEY_FOR_CHILD_REGISTERED_EVENTS: 'child-registered-events',
    KEY_FOR_CHILD_CALENDAR_EVENTS: 'child-calendar-events',
    KEY_FOR_CHILD_DETAILS: 'child-details',
    KEY_FOR_EVENT_DETAILS: 'event-details',
    KEY_FOR_FUNDRAISER_DETAILS: 'fundraiser-details',
    KEY_FOR_POST_DETAILS: 'post-details',
    KEY_FOR_ORGANIZATION_DETAILS: 'organization-details',
    FEED_VERSION_PREFIX: 'FeedVersionPrefix'
};
