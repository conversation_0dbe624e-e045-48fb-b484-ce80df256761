const sinon = require('sinon');
const AWS = require('aws-sdk');
const AWSMock = require('jest-aws-sdk-mock');
const Redis = require('ioredis');
const CONSOLE_LOGGER = require('../server/logger');
const RedisUtil = require('../server/redisUtil');
const Event = require('../server/models/event.model');
const Post = require('../server/models/post.model');
const Fundraiser = require('../server/models/fundraiser.model');
const ConstantModel = require('../server/models/constant.model');
const { beforeEach, afterEach } = require('mocha');
const CONSTANTS = require('../server/constants');
const EventSignups = require('../server/models/eventSignup.model');
const Child = require('../server/models/child.model');
const PopulateCacheService = require('../server/PopulateCacheService');
const Organization = require('../server/models/organization.model');
const MOMENT = require('moment');
const ChildOrganizationMappingModel = require('../server/models/childOrganizationMapping.model');
const FundraiserSignup = require('../server/models/fundraiserSignup.model');
const { expect } = require('chai');

const assert = sinon.assert;

const mockEvents = [
    {
        id: 'eventId1',
        organizationId: 'organizationId1',
        eventType: CONSTANTS.EVENT_TYPE.EVENT,
        status: CONSTANTS.EVENT_STATUS.PUBLISHED,
        details: {
            startDateTime: MOMENT().add(1, 'days').toDate(),
            endDateTime: MOMENT().add(2, 'days').toDate()
        }
    },
    {
        id: 'eventId2',
        organizationId: 'organizationId',
        eventType: CONSTANTS.EVENT_TYPE.EVENT,
        status: 'draft',
        details: {
            startDateTime: MOMENT().add(1, 'days').toDate(),
            endDateTime: MOMENT().add(2, 'days').toDate()
        }
    },
    {
        id: 'eventId3',
        organizationId: 'organizationId2',
        eventType: CONSTANTS.EVENT_TYPE.EVENT,
        status: CONSTANTS.EVENT_STATUS.PUBLISHED,
        details: {
            startDateTime: MOMENT().subtract(2, 'days').toDate(),
            endDateTime: MOMENT().subtract(1, 'days').toDate()
        }
    },
    {
        id: 'eventId4',
        organizationId: 'organizationId2',
        eventType: CONSTANTS.EVENT_TYPE.CALENDAR,
        status: 'published',
        details: {
            startDateTime: MOMENT().add(1, 'days').toDate(),
            endDateTime: MOMENT().add(2, 'days').toDate()
        }
    }
];

const mockFundraisers = [
    {
        id: 'fundraiserId1',
        organizationId: 'organizationId1',
        status: CONSTANTS.FUNDRAISER_STATUS.PUBLISHED,
        startDate: MOMENT().add(1, 'days').toDate(),
        endDate: MOMENT().add(2, 'days').toDate(),
        fundraiserType: CONSTANTS.FUNDRAISER_TYPES.SPIRITWEAR
    },
    {
        id: 'fundraiserId2',
        organizationId: 'organizationId2',
        status: 'draft',
        startDate: MOMENT().add(1, 'days').toDate(),
        endDate: MOMENT().add(2, 'days').toDate(),
        fundraiserType: CONSTANTS.FUNDRAISER_TYPES.MEMBERSHIP
    },
    {
        id: 'fundraiserId3',
        organizationId: 'organizationId3',
        status: CONSTANTS.FUNDRAISER_STATUS.PUBLISHED,
        startDate: MOMENT().subtract(2, 'days').toDate(),
        endDate: MOMENT().subtract(1, 'days').toDate(),
        fundraiserType: CONSTANTS.FUNDRAISER_TYPES.SPIRITWEAR
    },
    {
        id: 'fundraiserId4',
        organizationId: 'organizationId3',
        status: CONSTANTS.FUNDRAISER_STATUS.PUBLISHED,
        startDate: MOMENT().add(1, 'days').toDate(),
        endDate: MOMENT().add(2, 'days').toDate(),
        fundraiserType: CONSTANTS.FUNDRAISER_TYPES.BOOSTER
    }
];

const mockPosts = [
    {
        id: 'postId1',
        organizationId: 'organizationId1',
        status: CONSTANTS.POST_STATUS.PUBLISHED,
        publishedDate: MOMENT().add(1, 'days').toDate()
    },
    {
        id: 'postId2',
        organizationId: 'organizationId2',
        status: 'draft',
        publishedDate: MOMENT().add(1, 'days').toDate()
    },
    {
        id: 'postId3',
        organizationId: 'organizationId3',
        status: CONSTANTS.POST_STATUS.PUBLISHED,
        publishedDate: MOMENT().subtract(2, 'days').toDate()
    },
    {
        id: 'postId4',
        organizationId: 'organizationId2',
        status: CONSTANTS.POST_STATUS.PUBLISHED,
        publishedDate: MOMENT().add(16, 'days').toDate()
    },
    {
        id: 'postId5',
        organizationId: 'organizationId5',
        status: CONSTANTS.POST_STATUS.PUBLISHED,
        publishedDate: MOMENT().add(16, 'days').toDate()
    }
];

const mockOrganizations = [
    {
        id: 'organizationId1',
        name: 'organizationName1',
        type: 'school'
    },
    {
        id: 'organizationId2',
        name: 'organizationName2',
        type: 'pto'
    },
    {
        id: 'organizationId3',
        name: 'organizationName3',
        type: 'homeroom'
    },
    {
        id: 'organizationId4',
        name: 'organizationName4',
        type: 'pto'
    },
    {
        id: 'organizationId5',
        name: 'organizationName5',
        type: 'pto'
    }
];

const firstCallMockChildrenAssociatedWithOrganization = [
    {
        childId: 'childId1',
        organizationId: 'organizationId1'
    },
    {
        childId: 'childId2',
        organizationId: 'organizationId1'
    }
];

const thirdCallMockChildrenAssociatedWithOrganization = [
    {
        childId: 'childId3',
        organizationId: 'organizationId1'
    },
    {
        childId: 'childId4',
        organizationId: 'organizationId1'
    }
];

const mockChildren = [
    {
        id: 'childId1',
        firstName: 'childFirstName1',
        lastName: 'childLastName1',
        guardians: [
            'userId1',
            'userId2'
        ]
    },
    {
        id: 'childId2',
        firstName: 'childFirstName2',
        lastName: 'childLastName2'
    },
    {
        id: 'childId3',
        firstName: 'childFirstName3',
        lastName: 'childLastName3',
        guardians: [
            'userId3'
        ]
    },
    {
        id: 'childId4',
        firstName: 'childFirstName4',
        lastName: 'childLastName4'
    }
];

const mockEventSignups = [
    {
        id: 'eventSignupId1',
        eventId: 'eventId1',
        childId: 'childId1',
        paymentDetails: {
            paymentStatus: CONSTANTS.PAYMENT_STATUS.APPROVED
        }
    },
    {
        id: 'eventSignupId2',
        eventId: 'eventId2',
        childId: 'childId2',
        paymentDetails: {
            paymentStatus: CONSTANTS.PAYMENT_STATUS.PAYMENT_INITIATED
        }
    },
    {
        id: 'eventSignupId3',
        eventId: 'eventId3',
        childId: 'childId3',
        paymentDetails: {
            paymentStatus: CONSTANTS.PAYMENT_STATUS.SUCCESS
        }
    },
    {
        id: 'eventSignupId4',
        eventId: 'eventId4',
        childId: 'childId5',
        paymentDetails: {
            paymentStatus: CONSTANTS.PAYMENT_STATUS.APPROVED
        }
    }
];

const mockFundraiserSignups = [
    {
        id: 'fundraiserSignupId1',
        fundraiserId: 'fundraiserId1',
        childId: 'childId1',
        paymentDetails: {
            paymentStatus: CONSTANTS.PAYMENT_STATUS.APPROVED
        }
    },
    {
        id: 'fundraiserSignupId2',
        fundraiserId: 'fundraiserId2',
        childId: 'childId2',
        paymentDetails: {
            paymentStatus: CONSTANTS.PAYMENT_STATUS.PAYMENT_INITIATED
        }
    },
    {
        id: 'fundraiserSignupId3',
        fundraiserId: 'fundraiserId3',
        childId: 'childId3',
        paymentDetails: {
            paymentStatus: CONSTANTS.PAYMENT_STATUS.SUCCESS
        }
    },
    {
        id: 'fundraiserSignupId4',
        fundraiserId: 'fundraiserId4',
        childId: 'childId5',
        paymentDetails: {
            paymentStatus: CONSTANTS.PAYMENT_STATUS.APPROVED
        }
    }
];

const mockRedis = {
    pipeline: sinon.stub().returns({
        exec: sinon.stub().resolves()
    })
};

describe('Populate cache service', () => {
    let sandbox;
    let stubRedisConnect;
    let pipelineExecStub;
    let pipelineStub;
    let constantModelGetStub;

    before(() => {
        AWSMock.setSDKInstance(AWS);
        sandbox = sinon.createSandbox();
    });

    after(() => {
        AWSMock.restore();
        sinon.restore();
        sandbox.restore();
        pipelineStub.restore();
    });

    beforeEach(() => {
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
        pipelineExecStub = sinon.stub();
        pipelineStub = sinon.stub(Redis.prototype, 'pipeline').returns({ exec: pipelineExecStub });
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        constantModelGetStub = sinon.stub(ConstantModel, 'get');
        sinon.stub(ConstantModel, 'update').resolves();
    });

    afterEach(() => {
        sinon.restore();
    });

    try {
        it('should repopulate cache with the events, fundraisers, posts, event signups and fundraiser registrations', async () => {
            const scanEventsStub = sinon.stub(Event, 'scan').returns({
                where: sinon.stub().returns({
                    eq: sinon.stub().returns({
                        exec: sinon.stub().resolves(mockEvents)
                    })
                })
            });

            const scanFundraisersStub = sinon.stub(Fundraiser, 'scan').returns({
                where: sinon.stub().returns({
                    eq: sinon.stub().returns({
                        exec: sinon.stub().resolves(mockFundraisers)
                    })
                })
            });

            const scanPostsStub = sinon.stub(Post, 'scan').returns({
                where: sinon.stub().returns({
                    eq: sinon.stub().returns({
                        exec: sinon.stub().resolves(mockPosts)
                    })
                })
            });

            const scanOrganizationsStub = sinon.stub(Organization, 'scan').returns({
                where: sinon.stub().returns({
                    eq: sinon.stub().returns({
                        exec: sinon.stub().resolves(mockOrganizations)
                    })
                })
            });

            const getPostExpirationDays = constantModelGetStub.withArgs(CONSTANTS.POST_EXPIRATION_DAYS).resolves({ value: 15 });

            sinon.stub(ChildOrganizationMappingModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub()
                            .onFirstCall().resolves(firstCallMockChildrenAssociatedWithOrganization)
                            .onSecondCall().resolves([])
                            .resolves(thirdCallMockChildrenAssociatedWithOrganization)
                    })
                })
            });

            sinon.stub(Child, 'batchGet').resolves(mockChildren);

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([]);
            sinon.stub(RedisUtil, 'getHashValue').resolves(
                JSON.stringify({
                    id: 'eventId1', details: {
                        startDateTime: MOMENT().add(1, 'days').toDate()
                    }
                })
            );

            sinon.stub(EventSignups, 'scan').returns({
                exec: sinon.stub().resolves(mockEventSignups)
            });

            sinon.stub(FundraiserSignup, 'scan').returns({
                exec: sinon.stub().resolves(mockFundraiserSignups)
            });

            sinon.stub(Child, 'get').resolves();

            sinon.stub(RedisUtil, 'getKeysForPattern').resolves(['key1', 'key2']);

            await PopulateCacheService.populateCache(mockRedis, 'v1');

            assert.calledOnce(scanEventsStub);
            assert.calledOnce(scanFundraisersStub);
            assert.calledOnce(scanPostsStub);
            assert.calledOnce(getPostExpirationDays);
            assert.calledOnce(scanOrganizationsStub);
        });

        it('should return v1 if previous version was v2', async () => {
            const version = PopulateCacheService.getVersionPrefixAccToPrev('v2');
            expect(version).to.equal('v1');
        });

        it('should return v2 if previous version was v1', async () => {
            const version = PopulateCacheService.getVersionPrefixAccToPrev('v1');
            expect(version).to.equal('v2');
        });

        it('should update new version to db', async () => {
            await PopulateCacheService.updateVersionPrefixToDB('v2');
            assert.calledOnce(ConstantModel.update);
        });

        it('should clear redis cache for previous version', async () => {
            sinon.stub(RedisUtil, 'getKeysForPattern').resolves(['key1', 'key2']);

            await PopulateCacheService.clearRedisCacheForVersion(mockRedis, 'v1');
            assert.called(mockRedis.pipeline);
        });

        it('should clear redis cache for non versioned feed', async () => {
            sinon.stub(RedisUtil, 'getKeysForPattern').resolves(['key1', 'key2']);

            await PopulateCacheService.clearRedisCacheForNonVersionedKeys(mockRedis, 'v1');
            assert.called(mockRedis.pipeline);
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
