const ChildOrganizationMapping = require('./models/childOrganizationMapping.model');
const Organization = require('./models/organization.model');
const Child = require('./models/child.model');
const Event = require('./models/event.model');
const EventSignup = require('./models/eventSignup.model');
const FundraiserSignup = require('./models/fundraiserSignup.model');
const Post = require('./models/post.model');
const ConstantModel = require('./models/constant.model');
const Fundraiser = require('./models/fundraiser.model');
const OrgManagedFundraiserBoosterDonation = require('./models/orgManagedFundraiserBoosterDonation.model');
const CONSOLE_LOGGER = require('./logger');
const CONSTANTS = require('./constants');

class DBModelHelperService {
    /**
     * @description Gets the child organization mapping
     * <AUTHOR>
     * @param {String} organizationId - The organization id
     * @returns {Promise<Array>} The child organization mapping
     */
    static async getChildOrganizationMapping (organizationId) {
        return await ChildOrganizationMapping.query('organizationId')
            .eq(organizationId)
            .using('organizationId-index')
            .exec();
    }

    /**
     * @description Gets the organization name
     * <AUTHOR>
     * @param {String} organizationId - The organization id
     * @returns {Promise<String>} The organization name
     */
    static async getOrganizationName (organizationId) {
        const organization = await Organization.get(
            { id: organizationId },
            {
                attributes: ['name']
            }
        );
        return organization.name;
    }

    /**
     * @description Gets the child details with attributes
     * <AUTHOR>
     * @param {String} childId - The child id
     * @param {Array} attributes - The attributes
     * @returns {Promise<Object>} The child details
     */
    static async getChildDetailsWithAttributes (childId, attributes) {
        return await Child.get(childId, { attributes });
    }

    /**
     * @description Gets the child details with attributes in batch
     * <AUTHOR>
     * @param {Array} childIds - The child ids
     * @param {Array} attributes - The attributes
     * @returns {Promise<Array>} The child details
     */
    static async batchGetChildDetailsWithAttributes (childIds, attributes = []) {
        const chunkSize = 100;
        const childrenDetails = [];
        for (let i = 0; i < childIds.length; i += chunkSize) {
            const chunk = childIds.slice(i, i + chunkSize);
            let childrenChunk;
            if (attributes.length > 0) {
                childrenChunk = await Child.batchGet(chunk, { attributes });
            } else {
                childrenChunk = await Child.batchGet(chunk);
            }
            childrenDetails.push(...childrenChunk);
        }
        return childrenDetails;
    }

    /**
     * @description Gets the events in chunks
     * <AUTHOR>
     * @param {Array} eventIds - The event ids
     * @returns {Promise<Array>} The events
     */
    static async batchGetEventsWithAttributes (eventIds, attributes) {
        const chunkSize = 100;
        const events = [];
        for (let i = 0; i < eventIds.length; i += chunkSize) {
            const chunk = eventIds.slice(i, i + chunkSize);
            const eventsChunk = await Event.batchGet(chunk, { attributes });
            events.push(...eventsChunk);
        }
        return events;
    }

    /**
     * @description Gets the fundraisers in chunks
     * <AUTHOR>
     * @param {Array} fundraiserIds - The fundraiser ids
     * @returns {Promise<Array>} The fundraisers
     */
    static async batchGetFundraisersWithAttributes (fundraiserIds, attributes) {
        const chunkSize = 100;
        const fundraisers = [];
        for (let i = 0; i < fundraiserIds.length; i += chunkSize) {
            const chunk = fundraiserIds.slice(i, i + chunkSize);
            const fundraisersChunk = await Fundraiser.batchGet(chunk, { attributes });
            fundraisers.push(...fundraisersChunk);
        }
        return fundraisers;
    }

    /**
     * @description Gets the posts in chunks
     * <AUTHOR>
     * @param {Array} postIds - The post ids
     * @returns {Promise<Array>} The posts
     */
    static async batchGetPostsWithAttributes (postIds, attributes) {
        const chunkSize = 100;
        const posts = [];
        for (let i = 0; i < postIds.length; i += chunkSize) {
            const chunk = postIds.slice(i, i + chunkSize);
            const postsChunk = await Post.batchGet(chunk, { attributes });
            posts.push(...postsChunk);
        }
        return posts;
    }

    /**
     * @description Gets the events for organizations
     * <AUTHOR>
     * @param {Array} organizations - The organizations
     * @returns {Promise<Array>} The events
     */
    static async getEventsForOrganizations (organizations) {
        const queryPromises = organizations.map((organizationId) => {
            return Event.query('organizationId')
                .eq(organizationId)
                .using('organizationId-index')
                .where('isDeleted')
                .eq(0)
                .where('status')
                .eq('published')
                .attributes(['id', 'details', 'eventType'])
                .exec();
        });
        const eventsArrays = await Promise.all(queryPromises);
        return eventsArrays.flatMap((eventArray) => eventArray);
    }

    /**
     * @description Gets the posts for organizations
     * <AUTHOR>
     * @param {Array} organizations - The organizations
     * @returns {Promise<Array>} The posts
     */
    static async getPostsForOrganizations (organizations) {
        const queryPromises = organizations.map((organizationId) => {
            return Post.query('organizationId')
                .eq(organizationId)
                .using('organizationId-index')
                .where('isDeleted')
                .eq(0)
                .where('status')
                .eq('published')
                .attributes([
                    'id',
                    'title',
                    'subTitle',
                    'content',
                    'status',
                    'publishedDate'
                ])
                .exec();
        });
        const postsArrays = await Promise.all(queryPromises);
        return postsArrays.flatMap((postArray) => postArray);
    }

    /**
     * @description Gets the post expiration days
     * <AUTHOR>
     * @returns {Promise<Number>} The post expiration days
     */
    static async getPostExpirationDays () {
        try {
            const postExpirationConstant = await ConstantModel.get(
                CONSTANTS.POST_EXPIRATION_DAYS
            );
            const expirationDays = parseInt(postExpirationConstant?.value, 10);

            if (isNaN(expirationDays) || expirationDays <= 0) {
                throw new Error('Invalid expiration days value');
            }

            return expirationDays;
        } catch (error) {
            CONSOLE_LOGGER.info(
                'Error retrieving post expiration days constant:',
                error.message
            );
            return 15;
        }
    }

    /**
     * @description Gets the version prefix for redis keys
     * <AUTHOR>
     * @returns {Promise<String>} The version prefix
     */
    static async getVersionPrefixForRedisKeys () {
        const versionPrefixConstant = await ConstantModel.get(
            CONSTANTS.FEED_VERSION_PREFIX
        );
        return versionPrefixConstant?.value ?? '';
    }

    /**
     * @description Updates the version prefix for redis keys
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async updateVersionPrefixForRedisKeys (versionPrefix) {
        await ConstantModel.update({ name: CONSTANTS.FEED_VERSION_PREFIX }, { value: versionPrefix });
    }

    /**
     * @description Gets the fundraisers for organizations
     * <AUTHOR>
     * @param {Array} organizations - The organizations
     * @returns {Promise<Array>} The fundraisers
     */
    static async getFundraisersForOrganizations (organizations) {
        const queryPromises = organizations.map((organizationId) => {
            return Fundraiser.query('organizationId')
                .eq(organizationId)
                .using('organizationId-index')
                .where('isDeleted')
                .eq(0)
                .where('status')
                .eq('published')
                .attributes(['id', 'startDate', 'endDate'])
                .exec();
        });
        const fundraisersArrays = await Promise.all(queryPromises);
        return fundraisersArrays.flatMap((fundraiserArray) => fundraiserArray);
    }

    /**
     * @description Gets the org managed fundraiser booster donations for child
     * <AUTHOR>
     * @param {String} fundraiserBoosterId - The fundraiser booster id
     * @param {String} childId - The child id
     * @param {String} status - The status
     * @returns {Promise<Array>} The org managed fundraiser booster donations
     */
    static async getOrgManagedFundraiserBoosterDonationsForChild (fundraiserBoosterId, childId, status) {
        return await OrgManagedFundraiserBoosterDonation
            .query('fundraiserBoosterId')
            .eq(fundraiserBoosterId)
            .where('childId')
            .eq(childId)
            .where('status')
            .eq(status)
            .exec();
    }

    /**
     * @description Gets all events
     * <AUTHOR>
     * @returns {Promise<Array>} The events
     */
    static async getAllEvents () {
        return await Event.scan().where('isDeleted').eq(0).exec();
    }

    /**
     * @description Gets all fundraisers
     * <AUTHOR>
     * @returns {Promise<Array>} The fundraisers
     */
    static async getAllFundraisers () {
        return await Fundraiser.scan().where('isDeleted').eq(0).exec();
    }

    /**
     * @description Gets all posts
     * <AUTHOR>
     * @returns {Promise<Array>} The posts
     */
    static async getAllPosts () {
        return await Post.scan().where('isDeleted').eq(0).exec();
    }

    /**
     * @description Gets all fundraiser registrations
     * <AUTHOR>
     * @returns {Promise<Array>} The fundraiser registrations
     */
    static async getAllFundraiserRegistrations () {
        return await FundraiserSignup.scan().exec();
    }

    /**
     * @description Gets all organizations
     * <AUTHOR>
     * @returns {Promise<Array>} The organizations
     */
    static async getAllOrganizations () {
        return await Organization.scan().where('isDeleted').eq(0).exec();
    }

    /**
     * @description Gets all event registrations
     * <AUTHOR>
     * @returns {Promise<Array>} The event registrations
     */
    static async getAllEventRegistrations () {
        return await EventSignup.scan().exec();
    }

    /**
     * @description Gets the organization details in batch
     * <AUTHOR>
     * @param {Array} organizationIds - The organization ids
     * @returns {Promise<Array>} The organization details
     */
    static async batchGetOrganizationDetails (organizationIds) {
        const chunkSize = 100;
        const organizations = [];
        for (let i = 0; i < organizationIds.length; i += chunkSize) {
            const chunk = organizationIds.slice(i, i + chunkSize);
            const organizationsChunk = await Organization.batchGet(chunk, { attributes: ['id', 'name'] });
            organizations.push(...organizationsChunk);
        }
        return organizations;
    }
}

module.exports = DBModelHelperService;
