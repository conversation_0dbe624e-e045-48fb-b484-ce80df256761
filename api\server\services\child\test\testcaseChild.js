module.exports = {
    followChild: [
        {
            it: 'As a user I should validate followerChildId is passed',
            options: {
                followerChildId: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate followerChildId matches uuid regex',
            options: {
                followerChildId: '1234'
            },
            status: 0
        },
        {
            it: 'As a user I should validate followingChildId is passed',
            options: {
                followingChildId: ''
            },
            status: 0
        }
    ],
    connectChild: [
        {
            it: 'As a user I should validate requesterChildId is passed',
            options: {
                requesterChildId: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate requesterChildId matches uuid regex',
            options: {
                requesterChildId: '1234'
            },
            status: 0
        },
        {
            it: 'As a user I should validate requestedChildId is passed',
            options: {
                requesterChildId: '123e4567-e89b-12d3-a456-************',
                requestedChildId: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate requestedChildId matches uuid regex',
            options: {
                requesterChildId: '123e4567-e89b-12d3-a456-************',
                requestedChildId: '1234'
            },
            status: 0
        }
    ],
    changeFollowRequestStatus: [
        {
            it: 'As a user I should validate followerChildId is passed',
            options: {
                followerChildId: '',
                followingChildId: '123e4567-e89b-12d3-a456-************',
                status: 'accepted'
            },
            status: 0
        },
        {
            it: 'As a user I should validate followerChildId matches uuid regex',
            options: {
                followerChildId: '1234',
                followingChildId: '123e4567-e89b-12d3-a456-************',
                status: 'accepted'
            },
            status: 0
        },
        {
            it: 'As a user I should validate followingChildId is passed',
            options: {
                followerChildId: '123e4567-e89b-12d3-a456-************',
                followingChildId: '',
                status: 'accepted'
            },
            status: 0
        },
        {
            it: 'As a user I should validate followingChildId matches uuid regex',
            options: {
                followerChildId: '123e4567-e89b-12d3-a456-************',
                followingChildId: '1234',
                status: 'accepted'
            },
            status: 0
        },
        {
            it: 'As a user I should validate status is passed',
            options: {
                followerChildId: '123e4567-e89b-12d3-a456-************',
                followingChildId: '123e4567-e89b-12d3-a456-************',
                status: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate status is either accepted or rejected',
            options: {
                followerChildId: '123e4567-e89b-12d3-a456-************',
                followingChildId: '123e4567-e89b-12d3-a456-************',
                status: 'invalid'
            },
            status: 0
        }
    ],
    changeConnectionRequestStatus: [
        {
            it: 'As a user I should validate requesterChildId is passed',
            options: {
                requesterChildId: '',
                requestedChildId: '123e4567-e89b-12d3-a456-************',
                status: 'accepted'
            },
            status: 0
        },
        {
            it: 'As a user I should validate requesterChildId matches uuid regex',
            options: {
                requesterChildId: '1234',
                requestedChildId: '123e4567-e89b-12d3-a456-************',
                status: 'accepted'
            },
            status: 0
        },
        {
            it: 'As a user I should validate requestedChildId is passed',
            options: {
                requesterChildId: '123e4567-e89b-12d3-a456-************',
                requestedChildId: '',
                status: 'accepted'
            },
            status: 0
        },
        {
            it: 'As a user I should validate requestedChildId matches uuid regex',
            options: {
                requesterChildId: '123e4567-e89b-12d3-a456-************',
                requestedChildId: '1234',
                status: 'accepted'
            },
            status: 0
        },
        {
            it: 'As a user I should validate status is passed',
            options: {
                requesterChildId: '123e4567-e89b-12d3-a456-************',
                requestedChildId: '123e4567-e89b-12d3-a456-************',
                status: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate status is either accepted or rejected',
            options: {
                requesterChildId: '123e4567-e89b-12d3-a456-************',
                requestedChildId: '123e4567-e89b-12d3-a456-************',
                status: 'invalid'
            },
            status: 0
        }
    ],
    removeFollower: [
        {
            it: 'As a user I should validate followerChildId is passed',
            options: {
                followerChildId: '',
                followingChildId: '123e4567-e89b-12d3-a456-************'
            },
            status: 0
        },
        {
            it: 'As a user I should validate followerChildId matches uuid regex',
            options: {
                followerChildId: '1234',
                followingChildId: '123e4567-e89b-12d3-a456-************'
            },
            status: 0
        },
        {
            it: 'As a user I should validate followingChildId is passed',
            options: {
                followerChildId: '123e4567-e89b-12d3-a456-************',
                followingChildId: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate followingChildId matches uuid regex',
            options: {
                followerChildId: '123e4567-e89b-12d3-a456-************',
                followingChildId: '1234'
            },
            status: 0
        }
    ],
    removeConnection: [
        {
            it: 'As a user I should validate requesterChildId is passed',
            options: {
                requesterChildId: '',
                requestedChildId: '123e4567-e89b-12d3-a456-************'
            },
            status: 0
        },
        {
            it: 'As a user I should validate requesterChildId matches uuid regex',
            options: {
                requesterChildId: '1234',
                requestedChildId: '123e4567-e89b-12d3-a456-************'
            },
            status: 0
        },
        {
            it: 'As a user I should validate requestedChildId is passed',
            options: {
                requesterChildId: '123e4567-e89b-12d3-a456-************',
                requestedChildId: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate requestedChildId matches uuid regex',
            options: {
                requesterChildId: '123e4567-e89b-12d3-a456-************',
                requestedChildId: '1234'
            },
            status: 0
        }
    ],
    searchChild: [
        {
            it: 'As a user, I should validate that childId is passed',
            options: {
                childId: '',
                searchValue: 'John'
            },
            status: 0
        },
        {
            it: 'As a user, I should validate that childId matches uuid regex',
            options: {
                childId: '1234',
                searchValue: 'John'
            },
            status: 0
        },
        {
            it: 'As a user, I should validate that searchValue is passed',
            options: {
                childId: '123e4567-e89b-12d3-a456-************',
                searchValue: ''
            },
            status: 0
        }
    ],
    orgChildSearch: [
        {
            it: 'As a user, I should validate that childId is passed',
            options: {
                childId: '',
                searchValue: 'John'
            },
            status: 0
        },
        {
            it: 'As a user, I should validate that childId matches uuid regex',
            options: {
                childId: '1234',
                searchValue: 'John'
            },
            status: 0
        },
        {
            it: 'As a user, I should validate that searchValue is passed',
            options: {
                childId: '123e4567-e89b-12d3-a456-************',
                searchValue: ''
            },
            status: 0
        }
    ],
    unfollowChild: [
        {
            it: 'As a user, I should validate that followerChildId is passed',
            options: {
                followerChildId: '',
                followingChildId: '123e4567-e89b-12d3-a456-************'
            },
            status: 0
        },
        {
            it: 'As a user, I should validate that followerChildId matches uuid regex',
            options: {
                followerChildId: '1234',
                followingChildId: '123e4567-e89b-12d3-a456-************'
            },
            status: 0
        },
        {
            it: 'As a user, I should validate that followingChildId is passed',
            options: {
                followerChildId: '123e4567-e89b-12d3-a456-************',
                followingChildId: ''
            },
            status: 0
        },
        {
            it: 'As a user, I should validate that followingChildId matches uuid regex',
            options: {
                followerChildId: '123e4567-e89b-12d3-a456-************',
                followingChildId: '1234'
            },
            status: 0
        }
    ],
    getRelationships: [
        {
            it: 'As a user, I should validate that childId is passed',
            options: {
                childId: ''
            },
            status: 0
        },
        {
            it: 'As a user, I should validate that childId matches uuid regex',
            options: {
                childId: '1234'
            },
            status: 0
        },
        {
            it: 'As a user, I should validate relationshipType is passed correctly',
            options: {
                childId: '123e4567-e89b-12d3-a456-************',
                relationshipType: 'invalid'
            },
            status: 0
        }
    ],
    getConnections: [
        {
            it: 'As a user, I should validate that childId is passed',
            options: {
                childId: ''
            },
            status: 0
        },
        {
            it: 'As a user, I should validate that childId matches uuid regex',
            options: {
                childId: '1234'
            },
            status: 0
        },
        {
            it: 'As a user, I should validate connectionType is passed correctly',
            options: {
                childId: '123e4567-e89b-12d3-a456-************',
                connectionType: 'invalid'
            },
            status: 0
        }
    ],
    updateChild: [
        {
            it: 'As a user I should validate childId is passed',
            options: {
                childId: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate childId matches uuid regex',
            options: {
                childId: '1234'
            },
            status: 0
        },
        {
            it: 'As a user I should validate firstName is passed',
            options: {
                childId: '123e4567-e89b-12d3-a456-************',
                firstName: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate lastName is passed',
            options: {
                childId: '123e4567-e89b-12d3-a456-************',
                firstName: 'John',
                lastName: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate dob is in correct format',
            options: {
                childId: '123e4567-e89b-12d3-a456-************',
                firstName: 'John',
                lastName: 'Doe',
                dob: '12-34-5678'
            },
            status: 0
        },
        {
            it: 'As a user I should validate schoolId is passed',
            options: {
                childId: '123e4567-e89b-12d3-a456-************',
                firstName: 'John',
                lastName: 'Doe',
                dob: '01/01/2010',
                schoolId: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate homeroomId is passed',
            options: {
                childId: '123e4567-e89b-12d3-a456-************',
                firstName: 'John',
                lastName: 'Doe',
                dob: '01/01/2010',
                schoolId: '123e4567-e89b-12d3-a456-************',
                homeroomId: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate zipCode is passed',
            options: {
                childId: '123e4567-e89b-12d3-a456-************',
                firstName: 'John',
                lastName: 'Doe',
                dob: '01/01/2010',
                schoolId: '123e4567-e89b-12d3-a456-************',
                homeroomId: '123e4567-e89b-12d3-a456-************',
                zipCode: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate associatedColor is passed',
            options: {
                childId: '123e4567-e89b-12d3-a456-************',
                firstName: 'John',
                lastName: 'Doe',
                dob: '01/01/2010',
                schoolId: '123e4567-e89b-12d3-a456-************',
                homeroomId: '123e4567-e89b-12d3-a456-************',
                zipCode: '12345',
                associatedColor: ''
            },
            status: 0
        }
    ]
};
