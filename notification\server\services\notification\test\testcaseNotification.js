module.exports = {
    addNotification: [
        {
            it: 'should return error if title is not provided',
            request: {
                title: '',
                description: 'test description',
                userId: 'test user id',
                associatedChildId: 'test child id',
                notificationAction: 'test action',
                payload: 'test payload'
            }
        },
        {
            it: 'should return error if description is not provided',
            request: {
                title: 'test title',
                description: '',
                userId: 'test user id',
                associatedChildId: 'test child id',
                notificationAction: 'test action',
                payload: 'test payload'
            }
        },
        {
            it: 'should return error if associatedChildId is not provided',
            request: {
                title: 'test title',
                description: 'test description',
                userId: 'test user id',
                associatedChildId: '',
                notificationAction: 'test action',
                payload: 'test payload'
            }
        }
    ],
    sendNotification: [
        {
            it: 'should return error if description is not passed',
            request: {
                description: ''
            }
        },
        {
            it: 'should return error if organizationId is not passed',
            request: {
                description: 'Notification description',
                organizationId: ''
            }
        },
        {
            it: 'should return error if children is not passed',
            request: {
                description: 'Notification description',
                organizationId: 'organization-id',
                allParticipants: false
            }
        },
        {
            it: 'should return error if eventId is not passed',
            request: {
                description: 'Notification description',
                organizationId: 'organization-id',
                allParticipants: true,
                eventId: ''
            }
        }
    ]
};
