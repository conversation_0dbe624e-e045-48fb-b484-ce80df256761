/**
 * This file contains routes used for AI Companion.
 * <AUTHOR>
 * @since 11/06/2025
 * @name aiCompanionRoutes
 */
const router = require('express').Router();

const AiCompanionGroupController = require('../services/aiCompanionGroup/aiCompanionGroupController');
const AuthMiddleware = require('../middleware/auth');

router.post('/add-update-feedback', AuthMiddleware, AiCompanionGroupController.addUpdateAiCompanionGroupFeedback);
router.get('/feedback-list', AuthMiddleware, AiCompanionGroupController.getAiCompanionGroupFeedbackList);

module.exports = router;
