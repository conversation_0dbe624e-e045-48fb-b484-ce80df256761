const GroupMembers = require('../server/models/groupMembers.model');
const SocketConnections = require('../server/models/socketConnections.model');
const MessageReactions = require('../server/models/messageReactions.model');
const {
    ApiGatewayManagementApiClient,
    PostToConnectionCommand
} = require('@aws-sdk/client-apigatewaymanagementapi');
const apigateway = new ApiGatewayManagementApiClient({
    endpoint: process.env.SOCKET_API_GATEWAY_ENDPOINT
});
const CONSOLE_LOGGER = require('./logger');
const Utils = require('./Utils');
const RedisUtil = require('./redisUtil');
const CONSTANTS = require('./constants');
const { v4: uuidv4 } = require('uuid');

class ConversationService {
    static async getGroupMemberIds (groupId) {
        return (
            await GroupMembers.query('groupId')
                .eq(groupId)
                .using('groupId-index')
                .attributes(['userId'])
                .exec()
        ).map((member) => member.userId);
    }

    static async getActiveSocketConnections (groupMemberIds) {
        groupMemberIds = [...new Set(groupMemberIds)];
        const socketConnectionPromises = groupMemberIds.map(async (userId) => {
            return SocketConnections.query('userId')
                .eq(userId)
                .using('userId-index')
                .exec();
        });
        return (await Promise.all(socketConnectionPromises)).flat();
    }

    static async sendMessagesToConnections (activeSocketConnections, messageData, statusCode = 200) {
        const sendMessagePromises = activeSocketConnections.map(
            async (connection) => {
                const connectionId = connection.id;
                await this.sendMessageToConnection({
                    connectionId,
                    messageData,
                    statusCode
                });
            }
        );
        await Promise.all(sendMessagePromises);
    }

    /**
     * @description Sends a message to a connection
     * @param {Object} params
     * @param {string} params.connectionId
     * @param {Object} params.messageData
     * @param {number} [params.statusCode]
     * @param {boolean} [params.isChunk]
     * @param {number} [params.chunkIndex]
     * @param {number} [params.totalChunks]
     * @returns {Promise<void>}
     */
    static async sendMessageToConnection ({
        connectionId,
        messageData,
        statusCode = 200,
        isChunk = false,
        chunkIndex = 0,
        totalChunks = 0,
        chunkKey = null
    }) {
        const stringifiedMessageData = isChunk
            ? JSON.stringify({ chunk: messageData, chunkIndex, totalChunks, isChunk, chunkKey })
            : JSON.stringify({ ...messageData, statusCode, isChunk });

        if (!isChunk && Buffer.byteLength(stringifiedMessageData, 'utf8') > CONSTANTS.ALLOWED_MESSAGE_SIZE) {
            const generatedChunkKey = uuidv4();
            const chunks = this.chunkMessageData(stringifiedMessageData);
            for (const [index, chunk] of chunks.entries()) {
                await this.sendMessageToConnection({
                    connectionId,
                    statusCode,
                    chunkKey: generatedChunkKey,
                    messageData: chunk,
                    isChunk: true,
                    chunkIndex: index,
                    totalChunks: chunks.length
                });
            }
        } else {
            const postParams = {
                ConnectionId: connectionId,
                Data: stringifiedMessageData
            };
            try {
                await apigateway.send(new PostToConnectionCommand(postParams));
            } catch (error) {
                if (error.statusCode === 410) {
                    await SocketConnections.delete({ id: connectionId });
                    CONSOLE_LOGGER.error(`--> Stale connection removed --> ${connectionId}`);
                } else {
                    CONSOLE_LOGGER.error(`--> Error while sending message to connection --> ${connectionId}`, error);
                }
            }
        }
    }

    /**
     * @description Chunks a message data into chunks of ALLOWED_MESSAGE_SIZE, ensuring each chunk (with metadata) is under the limit
     * @param {string} data
     * @returns {Array}
     */
    static chunkMessageData (data) {
        const MAX_CHARS_ESTIMATE = Math.floor(CONSTANTS.ALLOWED_MESSAGE_SIZE / 4);
        const chunks = [];
        let currentIndex = 0;
        while (currentIndex < data.length) {
            const chunk = data.slice(currentIndex, currentIndex + MAX_CHARS_ESTIMATE);
            chunks.push(chunk);
            currentIndex += MAX_CHARS_ESTIMATE;
        }
        return chunks;
    }

    static async getReactionsForMessage ({ messageIds = [] }) {
        try {
            if (messageIds.length === 0) {
                return {};
            }

            const reactionsForMessagesPromise = [];
            for (const messageId of messageIds) {
                reactionsForMessagesPromise.push(MessageReactions.query('messageId').eq(messageId).exec());
            }

            const reactionsForMessages = await Promise.all(reactionsForMessagesPromise);
            const reactions = reactionsForMessages.flat().reduce((acc, reaction) => {
                (acc[reaction.messageId] ?? (acc[reaction.messageId] = {}))[reaction.userId] = reaction;
                return acc;
            }, {});

            return reactions;
        } catch (error) {
            return {};
        }
    }

    /**
     * <AUTHOR>
     * @since 25/02/2025
     * @description Adds or updates a group member to the hash set
     * @param {Redis} redis - The Redis client
     * @param {Object} groupMemberDetails - The group member details
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async addOrUpdateGroupMemberToHashSet (redis, groupMemberDetails, versionPrefix) {
        const { id, groupId, userId, status } = groupMemberDetails;

        const conversationMembersKey = Utils.getConversationMembersKey({ versionPrefix, groupId });
        const userConversationListKey = Utils.getUserConversationsKey({ versionPrefix, userId });

        const data = { groupId, id, status, isPersonalConversation: false };

        await RedisUtil.setHashValue(redis, userConversationListKey, groupId, data);
        return await RedisUtil.setHashValue(redis, conversationMembersKey, id, groupMemberDetails);
    }

    /**
     * <AUTHOR>
     * @since 25/02/2025
     * @description Removes a group from the conversation list
     * @param {Redis} redis - The Redis client
     * @param {Object} groupMemberDetails - The group member details
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async removeGroupFromConversationList (redis, groupMemberDetails, versionPrefix) {
        const { id, groupId } = groupMemberDetails;
        const userConversationListKey = Utils.getUserConversationsKey({ versionPrefix, userId: id });

        return await RedisUtil.removeMemberFromHashSet(redis, userConversationListKey, groupId);
    }

    /**
     * <AUTHOR>
     * @since 25/02/2025
     * @description Removes a group member from the hash set
     * @param {Redis} redis - The Redis client
     * @param {Object} groupMemberDetails - The group member details
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async removeGroupMemberFromHashSet (redis, groupMemberDetails, versionPrefix) {
        const { id, groupId } = groupMemberDetails;
        const conversationMembersKey = Utils.getConversationMembersKey({ versionPrefix, groupId });

        await this.removeGroupFromConversationList(redis, groupMemberDetails, versionPrefix);
        return await RedisUtil.removeMemberFromHashSet(redis, conversationMembersKey, id);
    }

    /**
     * <AUTHOR>
     * @since 25/02/2025
     * @description Adds or updates a group details to the hash set
     * @param {Redis} redis - The Redis client
     * @param {Object} groupDetails - The group details
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async addOrUpdateGroupDetailsToHashSet (redis, groupDetails, versionPrefix) {
        const { groupId } = groupDetails;
        groupDetails.isPersonalConversation = false;

        const groupDetailsKey = Utils.getConversationDetailsKey({ versionPrefix, conversationId: groupId });

        return await RedisUtil.setHashValue(redis, groupDetailsKey, 'details', groupDetails);
    }

    /**
     * <AUTHOR>
     * @since 25/02/2025
     * @description Removes a group details from the hash set
     * @param {Redis} redis - The Redis client
     * @param {Object} groupDetails - The group details
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async removeGroupDetailsFromHashSet (redis, groupDetails, versionPrefix) {
        const { groupId } = groupDetails;
        const groupDetailsKey = Utils.getConversationDetailsKey({ versionPrefix, conversationId: groupId });

        return await RedisUtil.removeMemberFromHashSet(redis, groupDetailsKey, 'details');
    }

    /**
     * <AUTHOR>
     * @since 25/02/2025
     * @description Handles the update of a group details
     * @param {Redis} redis - The Redis client
     * @param {Object} oldGroupDetails - The old group details
     * @param {Object} newGroupDetails - The new group details
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async handleGroupDetailsUpdate (redis, oldGroupDetails, newGroupDetails, versionPrefix) {
        if (
            oldGroupDetails.status === CONSTANTS.GROUP_STATUS.ACTIVE
            && newGroupDetails.status !== CONSTANTS.GROUP_STATUS.ACTIVE
        ) {
            return await this.removeGroupDetailsFromHashSet(redis, oldGroupDetails, versionPrefix);
        } else {
            return await this.addOrUpdateGroupDetailsToHashSet(redis, newGroupDetails, versionPrefix);
        }
    }

    /**
     * <AUTHOR>
     * @since 25/02/2025
     * @description Adds or updates a personal conversation details to the hash set
     * @param {Redis} redis - The Redis client
     * @param {Object} personalConversationDetails - The personal conversation details
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async addOrUpdatePersonalConversationDetailsToHashSet (redis, personalConversationDetails, versionPrefix) {
        const { conversationId, userAId } = personalConversationDetails;
        personalConversationDetails.isPersonalConversation = true;

        const personalConversationKey = Utils.getConversationDetailsKey({ versionPrefix, conversationId });

        await this.addOrUpdatePersonalConversationToUserConversationList(
            redis,
            personalConversationDetails,
            versionPrefix
        );

        return await RedisUtil.setHashValue(redis, personalConversationKey, userAId, personalConversationDetails);
    }

    /**
     * <AUTHOR>
     * @since 25/02/2025
     * @description Adds or updates a personal conversation to the user conversation list
     * @param {Redis} redis - The Redis client
     * @param {Object} personalConversationDetails - The personal conversation details
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async addOrUpdatePersonalConversationToUserConversationList (redis, personalConversationDetails, versionPrefix) {
        const { conversationId, userAId, userBId } = personalConversationDetails;
        const userAConversationListKey = Utils.getUserConversationsKey({ versionPrefix, userId: userAId });
        const data = { conversationId, isPersonalConversation: true, userBId, userAId };

        return await RedisUtil.setHashValue(redis, userAConversationListKey, conversationId, data);
    }

    /**
     * <AUTHOR>
     * @since 25/02/2025
     * @description Removes a personal conversation details from the hash set
     * @param {Redis} redis - The Redis client
     * @param {Object} personalConversationDetails - The personal conversation details
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<void>}
     */
    static async removePersonalConversationDetailsFromHashSet (redis, personalConversationDetails, versionPrefix) {
        const { conversationId, userAId, userBId } = personalConversationDetails;
        const userAConversationListKey = Utils.getUserConversationsKey({ versionPrefix, userId: userAId });
        const userBConversationListKey = Utils.getUserConversationsKey({ versionPrefix, userId: userBId });
        const personalConversationKey = Utils.getConversationDetailsKey({ versionPrefix, conversationId });

        await RedisUtil.removeMemberFromHashSet(redis, userAConversationListKey, conversationId);
        await RedisUtil.removeMemberFromHashSet(redis, userBConversationListKey, conversationId);
        await RedisUtil.removeMemberFromHashSet(redis, personalConversationKey, userAId);
        return await RedisUtil.removeMemberFromHashSet(redis, personalConversationKey, userBId);
    }

    /**
     * <AUTHOR>
     * @since 26/02/2025
     * @description Handles the addition of a message to the cache
     * @param {Redis} redis - The Redis client
     * @param {Object} messageDetails - The message details
     * @param {String} conversationMessagesKey - The conversation messages key
     * @param {String} messageId - The message id
     * @param {Boolean} isPopulatingCache - Whether the cache is being populated
     * @returns {Promise<void>}
     */
    static async handleAddMessageToCache ({ redis, messageDetails, conversationMessagesKey, messageId, isPopulatingCache = false }) {
        const messageIds = await RedisUtil.getHashValue(redis, conversationMessagesKey, 'messageIds');
        let parsedMessageIds = [];
        try {
            parsedMessageIds = JSON.parse(messageIds);
        } catch (error) {
            // Do nothing, messageIds is not a valid array
        }

        parsedMessageIds = parsedMessageIds ?? [];

        if (parsedMessageIds.length >= CONSTANTS.TOP_RECENT_GROUP_MESSAGES_LIMIT) {
            const lastMessageId = parsedMessageIds.pop();
            await RedisUtil.removeMemberFromHashSet(redis, conversationMessagesKey, lastMessageId);
        }

        if (isPopulatingCache) {
            parsedMessageIds.push(messageId);
        } else {
            parsedMessageIds.unshift(messageId);
        }

        await RedisUtil.setHashValue(redis, conversationMessagesKey, 'messageIds', parsedMessageIds);
        return await RedisUtil.setHashValue(
            redis,
            conversationMessagesKey,
            messageId,
            messageDetails
        );
    }

    /**
     * <AUTHOR>
     * @since 25/02/2025
     * @description Adds or updates a message to the sorted set
     * @param {Redis} redis - The Redis client
     * @param {Object} messageDetails - The message details
     * @param {String} versionPrefix - The version prefix
     * @param {Boolean} isPopulatingCache - Whether the cache is being populated
     * @returns {Promise<void>}
     */
    static async addGroupMessageToCache ({ redis, messageDetails, versionPrefix, isPopulatingCache = false }) {
        const { groupId, id } = messageDetails;
        const conversationMessagesKey = Utils.getConversationMessagesKey({ versionPrefix, conversationId: groupId });

        return await this.handleAddMessageToCache({
            redis,
            messageDetails,
            conversationMessagesKey,
            isPopulatingCache,
            messageId: id
        });
    }

    /**
     * <AUTHOR>
     * @since 26/02/2025
     * @description Handles the update of a message details in the cache
     * @param {Redis} redis - The Redis client
     * @param {Object} messageDetails - The message details
     * @returns {Promise<boolean>}
     */
    static async handleUpdateMessageDetailsInCache ({ redis, messageDetails, conversationMessagesKey, messageId }) {
        const doesMessageExist = await RedisUtil.getHashValue(redis, conversationMessagesKey, messageId);

        if (doesMessageExist) {
            return await RedisUtil.setHashValue(redis, conversationMessagesKey, messageId, messageDetails);
        }

        return false;
    }

    /**
     * <AUTHOR>
     * @since 26/02/2025
     * @description Updates a message details in the cache
     * @param {Redis} redis - The Redis client
     * @param {Object} messageDetails - The message details
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<boolean>}
     */
    static async updateGroupMessageDetailsInCache (redis, messageDetails, versionPrefix) {
        const { groupId, id } = messageDetails;
        const conversationMessagesKey = Utils.getConversationMessagesKey({ versionPrefix, conversationId: groupId });

        return await this.handleUpdateMessageDetailsInCache({
            redis,
            messageDetails,
            conversationMessagesKey,
            messageId: id
        });
    }

    /**
     * <AUTHOR>
     * @since 26/02/2025
     * @description Adds a personal message to the cache
     * @param {Redis} redis - The Redis client
     * @param {Object} personalMessageDetails - The personal message details
     * @param {String} versionPrefix - The version prefix
     * @param {Boolean} isPopulatingCache - Whether the cache is being populated
     * @returns {Promise<void>}
     */
    static async addPersonalMessageToCache ({ redis, personalMessageDetails, versionPrefix, isPopulatingCache = false }) {
        const { conversationId, messageId } = personalMessageDetails;
        const conversationMessagesKey = Utils.getConversationMessagesKey({ versionPrefix, conversationId });

        return await this.handleAddMessageToCache({
            redis,
            conversationMessagesKey,
            messageId,
            isPopulatingCache,
            messageDetails: personalMessageDetails
        });
    }

    /**
     * <AUTHOR>
     * @since 26/02/2025
     * @description Updates a personal message details in the cache
     * @param {Redis} redis - The Redis client
     * @param {Object} personalMessageDetails - The personal message details
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<boolean>}
     */
    static async updatePersonalMessageDetailsInCache (redis, personalMessageDetails, versionPrefix) {
        const { conversationId, messageId } = personalMessageDetails;
        const conversationMessagesKey = Utils.getConversationMessagesKey({ versionPrefix, conversationId });

        return await this.handleUpdateMessageDetailsInCache({
            redis,
            conversationMessagesKey,
            messageId,
            messageDetails: personalMessageDetails
        });
    }
}

module.exports = ConversationService;
