/* eslint-disable max-len */
const EventValidator = require('./eventValidator');
const { v4: uuidv4 } = require('uuid');
const UploadService = require('../../util/uploadService');
const Event = require('../../models/event.model');
const Organization = require('../../models/organization.model');
const OrganizationMember = require('../../models/organizationMember.model');
const ChildOrganizationMapping = require('../../models/childOrganizationMapping.model');
const lodash = require('lodash');
const EmailService = require('../../util/sendEmail');
const EventSignup = require('../../models/eventSignup.model');
const Children = require('../../models/child.model');
const User = require('../../models/user.model');
const Validator = require('../../util/validation');
const GeneralError = require('../../util/GeneralError');
const AwsOpenSearchService = require('../../util/opensearch');

/**
 * Class represents services for signin.
 */
class EventService {
    /**
     * @desc This function is being used to add event
     * <AUTHOR>
     * @since 08/11/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {String} req.body.eventType eventType - Event / Fundraiser
     * @param {String} req.body.title eventTitle
     * @param {String} req.body.eventDescription eventDescription
     * @param {String} req.body.pricingModel free / paid
     * @param {String} req.body.isRecurring isRecurring
     * @param {String} req.body.recurringFrequency recurringFrequency
     * @param {String} req.body.eventScope eventScope - Public / Private
     * @param {String} req.body.startDateTime startDateTime
     * @param {String} req.body.endDateTime endDateTime
     * @param {String} req.body.venue venue
     * @param {String} req.body.fee fee
     * @param {String} req.body.participantsLimit participantsLimit
     * @param {File} req.body.image event image
     * @param {Object} req.body.membershipBenefitDetails membershipBenefitDetails
     * @param {String} req.body.volunteerSignupURL google sheet URL
     * @param {Object} user User
     * @param {Object} res Response
     */
    static async addEvent (req, user, locale) {
        const Validator = new EventValidator(req.body, locale, req.files);
        Validator.validateAddEvent();

        const {
            eventType,
            title,
            eventDescription,
            isRecurring,
            recurringFrequency,
            eventStatus,
            eventScope,
            startDate,
            startTime,
            endDate,
            endTime,
            venue,
            fee,
            participantsLimit,
            volunteerSignupURL,
            organizationId,
            volunteerRequired,
            isPaid,
            isDonatable,
            quantityType,
            quantityInstruction,
            membershipBenefitDetails,
            products,
            eventId,
            bannerImageName
        } = req.body;

        const orgAssociation = await this.validateOrganizationAssociation(
            organizationId,
            user
        );

        const org = await Organization.get(organizationId);

        const startDateTime = MOMENT.utc(
            `${startDate} ${startTime}`,
            'MM/DD/YYYY HH:mm',
            true
        );

        const endDateTime = MOMENT.utc(
            `${endDate} ${endTime}`,
            'MM/DD/YYYY HH:mm',
            true
        );

        await this.validateDateTime(
            startDate,
            startTime,
            endDate,
            endTime,
            startDateTime,
            endDateTime,
            locale
        );

        await this.validatePaymentMethod(org, isPaid);

        const uuid = eventId ?? uuidv4();
        const documentFileNames = new Set();
        if (req.files && req.files.length > 0) {
            for (const file of req.files) {
                if (file.fieldname === 'documents') {
                    const documentFileName = `${process.env.NODE_ENV
                    }-events/organization-${organizationId}/${uuid}/${file.originalname
                    }-${Date.now()}`;
                    documentFileNames.add(documentFileName);
                    await UploadService.uploadFile(file, documentFileName, true);
                }
            }
        }

        const parsedProducts = products ? JSON.parse(products) : [];
        for (const product of parsedProducts) {
            const optionPrices = product.optionPrices;
            if (optionPrices) {
                for (const option of optionPrices) {
                    option.itemCost = parseFloat(option.itemCost);
                }
                product.optionPrices = optionPrices;
            }
        }
        let membershipBenefitDetailsParsed;
        if (membershipBenefitDetails) {
            membershipBenefitDetailsParsed = JSON.parse(membershipBenefitDetails);
            membershipBenefitDetailsParsed.benefitDiscount = JSON.stringify(
                membershipBenefitDetailsParsed.benefitDiscount
            );
        }
        try {
            const event = await Event.create({
                id: uuid,
                createdBy: user.id,
                photoURL: bannerImageName,
                participantsLimit: this.parseNumber(participantsLimit),
                fee: this.parseBoolean(isPaid) ? this.parseNumber(fee) : undefined,
                isPaid: this.parseBoolean(isPaid),
                membershipBenefitDetails: membershipBenefitDetails ? membershipBenefitDetailsParsed : undefined,
                isDonatable: (this.parseBoolean(isPaid) && isDonatable !== undefined)
                    ? this.parseBoolean(isDonatable) : false,
                volunteerRequired: this.parseBoolean(volunteerRequired),
                status:
                    orgAssociation.associatedOrgRole === CONSTANTS.ORG_ROLE.SUPER_ADMIN
                        ? eventStatus
                        : 'draft',
                organizationId: organizationId.trim(),
                title: title.trim(),
                eventType: eventType && eventType.trim() ? eventType.trim() : undefined,
                eventScope: eventScope ? eventScope.trim() : undefined,
                volunteerSignupUrl: this.parseBoolean(volunteerRequired)
                    ? volunteerSignupURL.trim()
                    : undefined,
                quantityType: quantityType || undefined,
                quantityInstruction: quantityInstruction ? quantityInstruction.trim() : undefined,
                details: {
                    details: eventDescription.trim(),
                    startDateTime: startDateTime.toDate(),
                    endDateTime: endDateTime.toDate(),
                    isRecurring: this.parseBoolean(isRecurring),
                    documentURLs: documentFileNames.size ? documentFileNames : undefined,
                    venue: venue.trim(),
                    recurringFrequency: this.parseBoolean(isRecurring)
                        ? recurringFrequency.trim()
                        : undefined
                },
                products: parsedProducts ?? []
            });
            if (event.status === CONSTANTS.STATUS.PUBLISHED) {
                if (event.eventType === 'calendar') {
                    await this.insertCalendarEventsInOpensearch(event);
                } else {
                    await this.insertEventsInChildFeeds(event);
                }
            }
            return event.id;
        } catch (error) {
            await this.handleFileDeletion([bannerImageName]);
            await this.handleFileDeletion(documentFileNames);
            throw error;
        }
    }

    static async insertEventsInChildFeeds (event) {
        const formattedEvent = await this.eventSearchFormattedData(event);
        await AwsOpenSearchService.create(CONSTANTS.OPEN_SEARCH.COLLECTION.EVENT, event.id, formattedEvent);
        await this.insertChildFeedsInChildren(event);
    }

    static async eventSearchFormattedData (event) {
        return {
            id: event.id,
            title: event.title,
            eventType: event.eventType,
            details: {
                details: event.details.details,
                startDateTime: event.details.startDateTime,
                endDateTime: event.details.endDateTime,
                venue: event.details.venue
            },
            photoURL: event.photoURL,
            organizationId: event.organizationId,
            isPaid: event.isPaid,
            isDonatable: event.isDonatable,
            fee: event.fee,
            membershipBenefitDetails: event.membershipBenefitDetails,
            eventScope: event.eventScope,
            status: event.status,
            products: event.products
        };
    }

    /**
     * @desc This function is being used to add event
     * <AUTHOR>
     * @since 08/11/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody - Array
     * @param {String} req.body.eventType eventType - Event / Fundraiser
     * @param {String} req.body.title eventTitle
     * @param {String} req.body.eventDescription eventDescription
     * @param {String} req.body.pricingModel free / paid
     * @param {String} req.body.isRecurring isRecurring
     * @param {String} req.body.recurringFrequency recurringFrequency
     * @param {String} req.body.eventScope eventScope - Public / Private
     * @param {String} req.body.startDateTime startDateTime
     * @param {String} req.body.endDateTime endDateTime
     * @param {String} req.body.venue venue
     * @param {String} req.body.fee fee
     * @param {String} req.body.participantsLimit participantsLimit
     * @param {File} req.body.image event image
     * @param {String} req.body.volunteerSignupURL google sheet URL
     * @param {Object} user User
     * @param {Object} res Response
     */
    // eslint-disable-next-line consistent-return
    static async addMultipleEvent (req, user, locale) {
        if (req.body.length > 0) {
            var promises = req.body.map(async (eventObj) => {
                const Validator = new EventValidator(eventObj, locale);
                Validator.validateAddMultiEvent();

                const {
                    title,
                    eventDescription,
                    startDate,
                    startTime,
                    endDate,
                    endTime,
                    organizationId
                } = eventObj;

                const startDateTime = MOMENT.utc(
                    `${startDate} ${startTime}`,
                    'MM/DD/YYYY HH:mm',
                    true
                );
                const endDateTime = MOMENT.utc(
                    `${endDate} ${endTime}`,
                    'MM/DD/YYYY HH:mm',
                    true
                );
                await this.validateMultipleEventDateTime(
                    startDate,
                    startTime,
                    endDate,
                    endTime,
                    startDateTime,
                    endDateTime,
                    locale
                );


                const uuid = uuidv4();

                const eventData = {
                    id: uuid,
                    createdBy: user.id,
                    isPaid: false,
                    membershipBenefitDetails: {
                        benefitDiscount: '[]',
                        isOnlyForMembers: false
                    },
                    isDonatable: false,
                    volunteerRequired: false,
                    status: CONSTANTS.STATUS.PUBLISHED,
                    organizationId: organizationId.trim(),
                    title: title.trim(),
                    eventType: 'calendar',
                    eventScope: undefined,
                    volunteerSignupUrl: undefined,
                    details: {
                        details: eventDescription.trim(),
                        startDateTime: startDateTime.toDate(),
                        endDateTime: endDateTime.toDate(),
                        isRecurring: false,
                        venue: '',
                        recurringFrequency: undefined
                    }
                };
                this.insertCalendarEventsInOpensearch(eventData);
                return eventData;
            });
            return Promise.all(promises).then(async (results) => {
                return await Event.batchPut(results);
            });
        }
        return null;
    }

    static async insertCalendarEventsInOpensearch (eventData) {
        const formattedEvent = await this.eventSearchFormattedData(eventData);
        await AwsOpenSearchService.create(CONSTANTS.OPEN_SEARCH.COLLECTION.EVENT, eventData.id, formattedEvent);
        await this.insetCalendarEventsInChildren(eventData);
    }

    static async insetCalendarEventsInChildren (event) {
        const children = await ChildOrganizationMapping.query('organizationId').eq(event.organizationId).exec();
        for (const child of children) {
            await AwsOpenSearchService.registerMultipleEvents(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, child.childId, [event.id]);
        }
    }

    static async insertChildFeedsInChildren (event) {
        const children = await ChildOrganizationMapping.query('organizationId').eq(event.organizationId).exec();
        for (const child of children) {
            await AwsOpenSearchService.registerEventInChildFeeds(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, child.childId, event.id);
        }
    }

    /**
     * @desc This function is being used to validate organization association
     * <AUTHOR>
     * @since 14/12/2023
     * @param {String} organizationId organization id
     */
    static async validateOrganizationAssociation (organizationId, user) {
        const orgMembers = await OrganizationMember.get(organizationId);
        if (!orgMembers) {
            throw {
                message: MESSAGES.UNAUTHORIZED_ACTION_FOR_ORGANIZATION,
                statusCode: 400
            };
        }

        const orgAssociation = orgMembers.users.find(
            (member) => member.id === user.id
        );
        if (!orgAssociation) {
            throw {
                message: MESSAGES.UNAUTHORIZED_ACTION_FOR_ORGANIZATION,
                statusCode: 400
            };
        }
        if (orgAssociation.status !== CONSTANTS.STATUS.ACTIVE) {
            throw {
                message: MESSAGES.UNAUTHORIZED_ACTION_FOR_ORGANIZATION,
                statusCode: 400
            };
        }
        return orgAssociation;
    }

    static async handleFileDeletion (fileNames) {
        if (!fileNames) {
            return;
        }

        for (const fileName of fileNames) {
            await UploadService.deleteObject(fileName);
        }
    }

    static parseNumber (value) {
        if (typeof value === 'number') return value;
        return value ? Number(value.trim()) : undefined;
    }

    static parseBoolean (value) {
        return value.trim() === 'true';
    }

    static async validateDateTime (
        startDate,
        startTime,
        endDate,
        endTime,
        startDateTime,
        endDateTime,
        locale
    ) {
        const isValidDate = (date) => MOMENT(date, 'MM/DD/YYYY', true).isValid();
        const isValidTime = (time) => MOMENT(time, 'HH:mm', true).isValid();

        if (!isValidDate(startDate) || !isValidDate(endDate)) {
            throw new GeneralError(
                locale(MESSAGES.DATE_FORMAT_ERROR, 'Start or End Date'),
                400
            );
        }

        if (!isValidTime(startTime) || !isValidTime(endTime)) {
            throw new GeneralError(
                locale(MESSAGES.TIME_FORMAT_ERROR, 'Start or End Time'),
                400
            );
        }

        const currentDateTime = MOMENT().utc();

        if (startDateTime.isBefore(currentDateTime)) {
            throw new GeneralError(MESSAGES.EVENT_START_DATE_ERROR, 400);
        }

        if (endDateTime.isBefore(startDateTime)) {
            throw new GeneralError(MESSAGES.EVENT_END_DATE_BEFORE_START_DATE, 400);
        }
    }

    static async validatePaymentMethod (org, isPaid) {
        if (
            isPaid.trim() === 'true' && (
                (lodash.get(org, 'allowedPaymentType.cash') && lodash.get(org, 'paymentInstructions.cashInstruction') === '')
                || (lodash.get(org, 'allowedPaymentType.cheque') && lodash.get(org, 'paymentInstructions.chequeInstruction') === '')
                || (lodash.get(org, 'allowedPaymentType.venmo') && lodash.get(org, 'paymentInstructions.venmoInstruction') === '')
            )
        ) {
            throw {
                message: MESSAGES.PAYMENT_INSTRUCTIONS_REQUIRED,
                statusCode: 400
            };
        }

        if (
            isPaid.trim() === 'true' &&
            ((lodash.get(org, 'paymentDetails.stripeConnectAccountId') === '' ||
                lodash.get(org, 'paymentDetails.stripeOnboardingStatus') !== 'active') && lodash.get(org, 'allowedPaymentType.stripe'))
        ) {
            throw {
                message: MESSAGES.PTO_REONBOARD,
                statusCode: 400
            };
        }

        if (isPaid.trim() === 'true' && lodash.get(org, 'allowedPaymentType.venmo') && !lodash.get(org, 'paymentDetails.venmoPaymentURL')) {
            throw {
                message: MESSAGES.VENMO_URL_REQUIRED,
                statusCode: 400
            };
        }
    }


    static async validateMultipleEventDateTime (
        startDate,
        startTime,
        endDate,
        endTime,
        startDateTime,
        endDateTime,
        locale
    ) {
        const isValidDate = (date) => MOMENT(date, 'MM/DD/YYYY', true).isValid();
        const isValidTime = (time) => MOMENT(time, 'HH:mm', true).isValid();

        if (!isValidDate(startDate) || !isValidDate(endDate)) {
            throw new GeneralError(
                locale(MESSAGES.DATE_FORMAT_ERROR, 'Start or End Date'),
                400
            );
        }

        if (!isValidTime(startTime) || !isValidTime(endTime)) {
            throw new GeneralError(
                locale(MESSAGES.TIME_FORMAT_ERROR, 'Start or End Time'),
                400
            );
        }

        if (endDateTime.isBefore(startDateTime)) {
            throw new GeneralError(MESSAGES.EVENT_END_DATE_BEFORE_START_DATE, 400);
        }
    }

    static async validateEvent (event, fee, volunteerSignupURL, eventStatus) {
        if (!event) {
            throw {
                message: MESSAGES.EVENT_NOT_FOUND,
                statusCode: 404
            };
        }

        if (event.status === 'published' && eventStatus === 'unpublished') {
            throw new GeneralError(MESSAGES.EVENT_STATUS_CANT_CHANGE, 400);
        }
    }

    static async handleDocumentDeletion (documentURLs, event, newDocumentURLs) {
        // delete documents and image not in request
        if (documentURLs && event.details.documentURLs) {
            for (const document of JSON.parse(documentURLs)) {
                const url = decodeURI(document);
                const baseUrl = url.split('?')[0];
                const fileName = baseUrl.split('amazonaws.com/')[1];
                newDocumentURLs.push(fileName);
            }

            const documentToDelete = _.difference(
                [...event.details.documentURLs],
                newDocumentURLs
            );
            for (const file of documentToDelete) {
                await UploadService.deleteObject(file);
                event.details.documentURLs.delete(file);
            }
        }
    }

    /**
     * @desc This function is being used to update event
     * <AUTHOR>
     * @since 16/11/2023
     * @param {Object} req Request
     * @param {Object} user User
     * @param {Object} locale locale
     */
    static async updateEvent (req, user, locale) {
        const validator = new EventValidator(req.body, locale, req.files);
        validator.validateUpdateEvent();
        const venueValidator = new Validator(locale);
        const {
            eventId,
            documentURLs,
            eventType,
            title,
            eventDescription,
            isRecurring,
            recurringFrequency,
            eventScope,
            startDate,
            startTime,
            endDate,
            endTime,
            venue,
            isPaid,
            isDonatable,
            fee,
            membershipBenefitDetails,
            participantsLimit,
            volunteerRequired,
            volunteerSignupURL,
            eventStatus,
            quantityType,
            quantityInstruction,
            bannerImageName
        } = req.body;

        const event = await Event.get({ id: eventId });
        await this.validateEvent(event, fee, volunteerSignupURL, eventStatus);

        event.eventType !== 'calendar' && venueValidator.field(venue, 'Event Venue');

        const orgAssociation = await this.validateOrganizationAssociation(
            event.organizationId,
            user
        );

        const org = await Organization.get(event.organizationId);

        this.validatePaymentMethod(org, isPaid);

        const startDateTime = MOMENT.utc(
            `${startDate} ${startTime}`,
            'MM/DD/YYYY HH:mm',
            true
        );
        const endDateTime = MOMENT.utc(
            `${endDate} ${endTime}`,
            'MM/DD/YYYY HH:mm',
            true
        );

        event.eventType !== 'calendar' && await this.validateDateTime(
            startDate,
            startTime,
            endDate,
            endTime,
            startDateTime,
            endDateTime,
            locale
        );

        const newDocumentURLs = [];

        await this.handleDocumentDeletion(documentURLs, event, newDocumentURLs);

        // upload new
        const documentFileNames = new Set();
        if (req.files.length > 0) {
            for (const file of req.files) {
                if (file.fieldname === 'documents') {
                    const documentFileName = `${process.env.NODE_ENV
                    }-events/organization-${event.organizationId}/${eventId}/${file.originalname
                    }-${Date.now()}`;
                    documentFileNames.add(documentFileName);
                    await UploadService.uploadFile(file, documentFileName, true);
                }
            }
        }

        const documentUrlsIfPassed =
            documentFileNames.size > 0
                ? new Set(newDocumentURLs.concat([...documentFileNames]))
                : newDocumentURLs.length > 0
                    ? new Set(newDocumentURLs)
                    : undefined;

        const documentURLsIfNotPassed = event.details.documentURLs
            ? new Set([...event.details.documentURLs].concat([...documentFileNames]))
            : documentFileNames.size > 0
                ? new Set(documentFileNames)
                : undefined;

        const products = req.body.products ? JSON.parse(req.body.products) : [];
        for (const product of products) {
            const optionPrices = product.optionPrices;
            if (optionPrices) {
                for (const option of optionPrices) {
                    option.itemCost = parseFloat(option.itemCost);
                }
                product.optionPrices = optionPrices;
            }
        }

        event.products = products;
        event.updatedBy = user.id;
        event.photoURL = bannerImageName ? bannerImageName : event.photoURL;
        event.participantsLimit = participantsLimit
            ? Number(participantsLimit.trim())
            : event.participantsLimit;
        event.fee = this.parseBoolean(isPaid) ? this.parseNumber(fee) : undefined;
        event.isPaid = this.parseBoolean(isPaid);
        if (membershipBenefitDetails) {
            const newMembershipBenefitDetailsParsed = JSON.parse(
                membershipBenefitDetails
            );
            newMembershipBenefitDetailsParsed.benefitDiscount = JSON.stringify(
                newMembershipBenefitDetailsParsed.benefitDiscount
            );

            event.membershipBenefitDetails = newMembershipBenefitDetailsParsed;
        }
        event.isDonatable = (this.parseBoolean(isPaid) && isDonatable !== undefined) ?
            this.parseBoolean(isDonatable) :
            false;
        event.fee = event.isPaid ? Number(fee) : undefined;
        event.volunteerRequired = this.parseBoolean(volunteerRequired);
        event.volunteerSignupUrl = this.parseBoolean(volunteerRequired)
            ? volunteerSignupURL
            : undefined;
        event.quantityType = quantityType ? quantityType : undefined;
        event.quantityInstruction = quantityInstruction ? quantityInstruction.trim() : undefined;
        event.details = {
            details: eventDescription.trim(),
            startDateTime: startDateTime.toDate(),
            endDateTime: endDateTime.toDate(),
            isRecurring: isRecurring === 'true',
            documentURLs: documentURLs
                ? documentUrlsIfPassed
                : documentURLsIfNotPassed,
            venue: venue ? venue.trim() : '',
            recurringFrequency: isRecurring.trim() === 'true' ? recurringFrequency.trim() : undefined
        };
        event.title = title;
        event.eventType = eventType || event.eventType;
        event.eventScope = eventScope;
        event.status =
            orgAssociation.associatedOrgRole === CONSTANTS.ORG_ROLE.SUPER_ADMIN
                ? eventStatus
                : 'draft';

        const updatedEvent = await event.save();

        const formattedEvent = await this.eventSearchFormattedData(updatedEvent);
        await AwsOpenSearchService.updateField(CONSTANTS.OPEN_SEARCH.COLLECTION.EVENT, updatedEvent.id, formattedEvent);
    }

    /**
     * @desc This function is being used to get list of Events
     * <AUTHOR>
     * @since 15/11/2023
     */
    static async getEventList (req, user) {
        const { status, organizationId } = req.query;

        const eventAttributes = [
            'id',
            'title',
            'fee',
            'eventType',
            'details',
            'eventScope',
            'status',
            'isDeleted',
            'isPaid',
            'isDonatable',
            'volunteerSignupUrl',
            'volunteerRequired',
            'organizationId',
            'quantityType',
            'quantityInstruction',
            'participantsCount',
            'updatedAt',
            'createdAt'
        ];

        let events;

        if (organizationId) {
            await this.validateOrganizationAssociation(organizationId, user);

            const query = Event.query('organizationId')
                .eq(organizationId.trim())
                .using('organizationId-index')
                .attributes(eventAttributes);

            if (
                status &&
                CONSTANTS.ALLOWED_EVENT_STATUS.includes(status.toLowerCase().trim())
            ) {
                events = await query
                    .where('status')
                    .eq(status.toLowerCase().trim())
                    .exec();
            } else {
                events = await query.exec();
            }
        } else {
            const queryPromises = user.associatedOrganizations.map(async (org) => {
                const orgMembers = await OrganizationMember.get(org.organizationId);

                const orgAssociation = orgMembers.users.find(
                    (member) => member.id === user.id
                );

                if (orgAssociation.status === CONSTANTS.STATUS.ACTIVE) {
                    const query = Event.query('organizationId')
                        .eq(org.organizationId)
                        .using('organizationId-index')
                        .attributes(eventAttributes);

                    if (
                        status &&
                        CONSTANTS.ALLOWED_EVENT_STATUS.includes(status.toLowerCase().trim())
                    ) {
                        return query.where('status').eq(status.toLowerCase().trim()).exec();
                    } else {
                        return query.exec();
                    }
                } else {
                    return null;
                }
            });

            const eventsArrays = await Promise.all(queryPromises);
            events = eventsArrays.filter(Boolean).flatMap((eventArray) => eventArray);
        }
        events.sort((a, b) => b.createdAt - a.createdAt);
        return events.length === 0 ? [] : this.formatEvents(events);
    }

    /**
     * @desc This function is being used to get list of Events participants
     * <AUTHOR>
     * @since 20/11/2023
     */
    static async getParticipantEventList (req, user, locale) {
        const { eventId } = req.query;
        const validator = new Validator(locale);
        validator.field(eventId, 'Event Id');

        const event = await Event.get({ id: eventId });

        if (!event) {
            throw {
                message: MESSAGES.EVENT_NOT_FOUND,
                statusCode: 400
            };
        }

        await this.validateOrganizationAssociation(event.organizationId, user);

        let eventSignups = await EventSignup.query('eventId')
            .eq(eventId)
            .using('eventId-index')
            .where('organizationId')
            .eq(event.organizationId)
            .exec();

        eventSignups = eventSignups.filter(event => event.paymentDetails.paymentStatus !== 'payment-initiated');
        const eventDetails = await Event.get({ id: eventId });
        return eventSignups.length === 0
            ? {
                title: eventDetails.title
            }
            : {
                title: eventDetails.title,
                eventFee: eventDetails.fee + (eventDetails.transactionFee || 0),
                quantityType: eventDetails.quantityType,
                quantityInstruction: eventDetails.quantityInstruction,
                membershipBenefitDetails: eventDetails.membershipBenefitDetails,
                signups: await this.formatSignupEvents(eventSignups, eventDetails.fee, eventDetails.isPaid, eventDetails.products ?? [])
            };
    }

    /**
     * @desc This function is being used to return formatted signup events
     * <AUTHOR>
     * @since 20/11/2023
     * @param {Array} eventSignups array of participated events
     */
    static async formatSignupEvents (eventSignups, eventFee, isPaid, eventProducts) {
        const filteredEventSignups = eventSignups.filter(
            ({ isDeleted }) => !isDeleted
        );
        const schoolName = await Organization.get(
            { id: eventSignups[0].organizationId },
            { attributes: ['name'] }
        );

        const eventProductLookup = eventProducts.reduce((acc, ep) => {
            ep.optionPrices.forEach(op => {
                acc[op.id] = { ...ep, optionPrice: op };
            });
            return acc;
        }, {});

        return await Promise.all(
            filteredEventSignups.map(async (event) => {
                const primaryParent = await User.query('id')
                    .eq(event.parentId)
                    .attributes(['firstName', 'lastName'])
                    .exec();
                const childDetails = await Children.get({ id: event.childId });
                const homeroomName =
            childDetails.homeRoom &&
            (await Organization.get(
                { id: childDetails.homeRoom },
                { attributes: ['name'] }
            ));
                const purchasedProducts = event.purchasedProducts;
                let enrichedPurchasedProducts = [];
                let purchasedProductsAmount = 0;
                if (purchasedProducts) {
                    enrichedPurchasedProducts = purchasedProducts.map((purchasedProduct) => {
                        const eventProductEntry = eventProductLookup[purchasedProduct.id];

                        if (!eventProductEntry) {
                            return purchasedProduct;
                        }

                        const { optionPrice, options } = eventProductEntry;
                        const optionDetails = [];

                        Object.keys(optionPrice).forEach((key) => {
                            if (!['id', 'itemName', 'itemCost'].includes(key)) {
                                const option = options.find((o) => o.name.toLowerCase() === key.toLowerCase());
                                if (option) {
                                    optionDetails.push({
                                        name: key.charAt(0).toUpperCase() + key.slice(1),
                                        selectedVariant: optionPrice[key]
                                    });
                                }
                            }
                        });

                        return {
                            ...purchasedProduct,
                            optionDetails
                        };
                    });
                    purchasedProductsAmount = purchasedProducts.reduce((acc, product) => {
                        return acc + (product.itemCost * product.quantity);
                    }, 0);
                }

                const transactionFee = event.paymentDetails.transactionFee || 0;
                const platformFeeCoveredBy =
            event.paymentDetails.platformFeeCoveredBy || '';
                let totalEventFee =
            eventFee * (event.quantityCount || 1) + transactionFee + purchasedProductsAmount || 0;
                totalEventFee +=
            isPaid && event.donationAmount ? event.donationAmount : 0;
                const discountedEventFee =
              event.paymentDetails.membershipDiscount > 0
                  ? totalEventFee - event.paymentDetails.membershipDiscount
                  : totalEventFee;
                const membershipBenefitDiscountAmount =
              event.paymentDetails.membershipDiscount > 0
                  ? event.paymentDetails.membershipDiscount
                  : 0;
                return {
                    id: event.id,
                    childId: event.childId,
                    childName: `${childDetails.firstName} ${childDetails.lastName}`,
                    parentName: `${primaryParent[0]?.firstName} ${primaryParent[0]?.lastName}`,
                    school: schoolName.name,
                    homeroom: homeroomName ? homeroomName.name : '',
                    paymentStatus: event.paymentDetails.paymentStatus,
                    paymentType:
              event.paymentDetails.paymentType === 'cheque'
                  ? 'check'
                  : event.paymentDetails.paymentType,
                    quantityCount: event.quantityCount || 1,
                    signedUpAt: MOMENT(event.createdAt),
                    donationAmount: event.donationAmount || 0,
                    totalFee: totalEventFee,
                    totalFeeWithDiscount: discountedEventFee,
                    membershipBenefitDiscount: membershipBenefitDiscountAmount,
                    platformFeeCoveredBy,
                    transactionFee,
                    products: enrichedPurchasedProducts
                };
            })
        );
    }

    /**
     * @desc This function is being used to get event details
     * <AUTHOR>
     * @since 15/11/2023
     * @param {Object} req Request
     * @param {Object} locale Locale passed from request
     */
    static async getEventDetails (req, user, locale) {
        const Validator = new EventValidator(req.body, locale, null, req.query);
        Validator.validateEventId();

        const { eventId } = req.query;
        const eventAttributes = [
            'id',
            'title',
            'fee',
            'eventType',
            'details',
            'eventScope',
            'status',
            'photoURL',
            'isDeleted',
            'organizationId',
            'isPaid',
            'membershipBenefitDetails',
            'isDonatable',
            'volunteerSignupUrl',
            'volunteerRequired',
            'quantityType',
            'quantityInstruction',
            'participantsCount',
            'participantsLimit',
            'products'
        ];

        const eventDetails = await Event.get(
            { id: eventId },
            {
                attributes: eventAttributes
            }
        );

        if (!eventDetails || eventDetails.isDeleted) {
            throw {
                message: MESSAGES.EVENT_NOT_FOUND,
                statusCode: 400
            };
        }

        await this.validateOrganizationAssociation(
            eventDetails.organizationId,
            user
        );
        const productsObj = eventDetails?.products;
        for (let i = 0; i < productsObj?.length; i++) {
            const product = productsObj[i];
            const signedURLs = [];
            for (let j = 0; j < product?.images?.length; j++) {
                const imageURL = product.images[j];
                if (imageURL) {
                    const signedURL = await UploadService.getSignedUrl(imageURL);
                    signedURLs.push(signedURL);
                }
            }
            product.imageURLs = signedURLs;
        }
        eventDetails.products = productsObj || [];
        const formattedEvent = this.formatEvents([eventDetails])[0];

        return {
            ...formattedEvent,
            documentURLs: eventDetails.details.documentURLs
                ? await Promise.all(
                    [...eventDetails.details.documentURLs].map(
                        UploadService.getSignedUrl
                    )
                )
                : [],
            photoUrl: eventDetails.photoURL
                ? await UploadService.getSignedUrl(eventDetails.photoURL)
                : '',
            canBeDeleted: formattedEvent.status === 'unpublished'
        };
    }

    /**
     * @desc This function is being used to change the status of event to publish
     * <AUTHOR>
     * @since 15/11/2023
     */
    static async publishEvent (req, user, locale) {
        const Validator = new EventValidator(req.body, locale, null, req.query);
        Validator.validateEventId();
        const event = await Event.get(req.query.eventId);

        if (!event || event.isDeleted) {
            throw {
                message: MESSAGES.EVENT_NOT_FOUND,
                statusCode: 400
            };
        }

        const orgAssociation = await this.validateOrganizationAssociation(
            event.organizationId,
            user
        );

        if (orgAssociation.associatedOrgRole !== CONSTANTS.ORG_ROLE.SUPER_ADMIN) {
            throw new GeneralError(MESSAGES.ACCESS_DENIED, 403);
        } else if (event.status === 'published') {
            throw {
                message: MESSAGES.EVENT_ALREADY_PUBLISHED,
                statusCode: 200
            };
        } else {
            event.status = 'published';
            await event.save();
        }
    }

    /**
     * @desc This function is being used to change the status of cash or cheque payment status
     * <AUTHOR>
     * @since 21/11/2023
     */
    static async updatePaymentStatus (req, user, locale) {
        const { id, status } = req.body;
        const validator = new Validator(locale);
        validator.field(id, 'Participant Id');
        validator.field(status, 'Signup Status');

        if (CONSTANTS.ALLOWED_SIGNUP_STATUS.indexOf(status.trim().toLowerCase()) === -1) {
            throw {
                message: 'Invalid signup status!',
                statusCode: 400
            };
        }

        const event = await EventSignup.get(id);
        if (!event || event.isDeleted) {
            throw {
                message: 'Participant not found!',
                statusCode: 400
            };
        }

        const orgAssociation = await this.validateOrganizationAssociation(
            event.organizationId,
            user
        );

        if (orgAssociation.associatedOrgRole !== CONSTANTS.ORG_ROLE.SUPER_ADMIN) {
            throw new GeneralError(MESSAGES.ACCESS_DENIED, 403);
        }

        if (
            event.paymentDetails.paymentStatus === 'approved' &&
            status.trim().toLowerCase() === 'approved'
        ) {
            throw {
                message: 'User has already paid for this event!',
                statusCode: 400
            };
        }

        const paymentType = event.paymentDetails.paymentType;
        if (paymentType === CONSTANTS.PAYMENT_TYPES.CASH
            || paymentType === CONSTANTS.PAYMENT_TYPES.CHECK
            || paymentType === CONSTANTS.PAYMENT_TYPES.VENMO) {
            event.paymentDetails.paymentStatus = status.trim().toLowerCase();
            await event.save();
            const parentUser = (
                await User.query('id').eq(event.parentId).exec()
            ).toJSON()[0];
            const child = await Children.get(event.childId);
            const childName = `${child.firstName} ${child.lastName}`;
            const eventDetails = await Event.get(event.eventId);
            const eventName = eventDetails.title;
            const eventStartTime = MOMENT(eventDetails.details.startDateTime).format(
                'MMMM Do YYYY, h:mm:ss a'
            );
            const eventEndTime = MOMENT(eventDetails.details.endDateTime).format(
                'MMMM Do YYYY, h:mm:ss a'
            );
            const eventLocation = eventDetails.details.venue;
            eventDetails.participantsCount += event.quantityCount;
            await eventDetails.save();
            await this.sendEventRegistrationMail({
                reqObj: {
                    childName,
                    eventName,
                    eventStartTime,
                    eventEndTime,
                    eventLocation,
                    isGuestSignup: event.isGuestSignup,
                    userEmail: parentUser.email,
                    eventReferenceId: event.id
                },
                templateFile: 'eventRegistration.html',
                subject: `Event Registration for ${eventName}`,
                guestTemplateFile: 'guestSignupConfirmation.html'
            });
            await this.updateChildEventStatus({ childId: child.id, eventId: event.eventId, isGuestSignup: event.isGuestSignup });
        } else {
            throw {
                message: MESSAGES.INVALID_PAYMENT_METHOD,
                statusCode: 400
            };
        }
    }

    /**
     * @desc This function is being used to update child event status
     * @param {String} childId childId
     * @param {String} eventId eventId
     * @param {Boolean} isGuestSignup isGuestSignup
     */
    static async updateChildEventStatus ({ childId, eventId, isGuestSignup }) {
        if (isGuestSignup) {
            return;
        }
        await AwsOpenSearchService.removeEventFromChildFeeds(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, childId, eventId);
        await AwsOpenSearchService.removeEventFromChildPendingEvents(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, childId, eventId);
        await AwsOpenSearchService.registerEventInChildEvents(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, childId, eventId);
    }

    /**
     * @desc This function is being used to delete event
     * <AUTHOR>
     * @since 15/11/2023
     */
    static async deleteEvent (req, user, locale) {
        const Validator = new EventValidator(req.body, locale, null, req.query);
        Validator.validateEventId();
        const event = await Event.get(req.query.eventId);
        if (!event || event.isDeleted) {
            throw {
                message: MESSAGES.EVENT_NOT_FOUND,
                statusCode: 400
            };
        } else if (event.status === 'published') {
            const orgAssociation = await this.validateOrganizationAssociation(
                event.organizationId,
                user
            );
            if (orgAssociation.associatedOrgRole !== CONSTANTS.ORG_ROLE.SUPER_ADMIN) {
                throw new GeneralError(MESSAGES.ACCESS_DENIED, 403);
            }
            throw {
                message: MESSAGES.EVENT_CANT_BE_DELETED,
                statusCode: 400
            };
        } else {
            // const orgAssociation = await this.validateOrganizationAssociation(
            //     event.organizationId,
            //     user
            // );
            // if (orgAssociation.associatedOrgRole !== CONSTANTS.ORG_ROLE.SUPER_ADMIN) {
            //     throw new GeneralError(MESSAGES.ACCESS_DENIED, 403);
            // }
            event.isDeleted = 1;
            await event.save();
            await AwsOpenSearchService.delete(CONSTANTS.OPEN_SEARCH.COLLECTION.EVENT, event.id);
        }
    }


    /**
     * @desc This function is being used to get searched list of Events
     * <AUTHOR>
     * @since 21/02/2024
    */
    static async getSearchEventList (req, user, locale) {
        let { query } = req.query;
        const { childId } = req.query;
        const validator = new Validator(locale);
        validator.field(query, 'Search Query');
        query = query.trim();
        let childrenIds;
        if (childId) {
            childrenIds = [childId];
        } else {
            childrenIds = user.children;
        }
        const allChildrenFromOpensearch = await AwsOpenSearchService.getAllChildEvents(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, {
            childrenIds
        });
        const { childEventMap, eventIds } = await this.formattedChildEvents(allChildrenFromOpensearch);
        const eventList = await AwsOpenSearchService.getAllEvents(CONSTANTS.OPEN_SEARCH.COLLECTION.EVENT, { searchText: query, eventIds });
        const filteredChildEventMap = childEventMap.filter(({ eventId }) => eventList.find(({ _source }) => _source.id === eventId));

        if (eventList.length === 0) {
            return [];
        } else {
            let responseSortedList = await this.formattedEventMapping(eventList, filteredChildEventMap);
            responseSortedList = responseSortedList.sort((a, b) => {
                const dateA = MOMENT(a.startDateTime);
                const dateB = MOMENT(b.startDateTime);
                return dateA.valueOf() - dateB.valueOf();
            });
            return responseSortedList;
        }
    }

    /**
     * Formats the event mapping for a given set of events and child events.
     *
     * @param {Array} events - The events to be formatted.
     * @param {Array} childEventMap - The child events to be included in the formatted events.
     */
    static async formattedEventMapping (events, childEventMap) {
        return await Promise.all(childEventMap.map(async ({ eventId, associatedChild }) => {
            const { _source } = events.find(({ _source }) => _source.id === eventId);
            const { id, title, eventType, details: eventDetails, organizationId } = _source;
            const { startDateTime, endDateTime, venue, details } = eventDetails;
            const org = await Organization.get(organizationId);
            return {
                id,
                title,
                eventType,
                venue,
                details,
                associatedChild,
                startDateTime: MOMENT(startDateTime),
                endDateTime: MOMENT(endDateTime),
                organizationName: org.name,
                isSignedUp: true,
                paymentStatus: CONSTANTS.PAYMENT_STATUS.APPROVED,
                score: MOMENT(startDateTime).valueOf()
            };
        }));
    }

    /**
     * Formats the child events for a given set of children.
     *
     * @param {Array} children - The children to be formatted.
     */
    static async formattedChildEvents (children) {
        const childEventMap = [];
        const eventIds = [];
        for (const { _source } of children) {
            const { id, childEvents, firstName, lastName, associatedColor, photoURL, childCalendarEvents } = _source;
            if (childEvents && childEvents.length > 0) {
                for (let i = 0; i < childEvents.length; i++) {
                    const obj = {
                        associatedChild: {
                            id,
                            firstName,
                            lastName,
                            associatedColor,
                            photoURL: photoURL ? await UploadService.getSignedUrl(photoURL) : null
                        },
                        childId: id,
                        eventId: childEvents[i]
                    };
                    childEventMap.push(obj);
                }
                eventIds.push(...childEvents);
            }
            if (childCalendarEvents && childCalendarEvents.length > 0) {
                for (let i = 0; i < childCalendarEvents.length; i++) {
                    const obj = {
                        associatedChild: {
                            id,
                            firstName,
                            lastName,
                            associatedColor,
                            photoURL: photoURL ? await UploadService.getSignedUrl(photoURL) : null
                        },
                        childId: id,
                        eventId: childCalendarEvents[i]
                    };
                    childEventMap.push(obj);
                }
                eventIds.push(...childCalendarEvents);
            }
        }

        return { childEventMap, eventIds: [...new Set(eventIds)] };
    }

    /**
     * @desc This function is being used to return formatted events
     * <AUTHOR>
     * @since 15/11/2023
     * @param {Array} events array of events
     */
    static formatEvents (events) {
        return events
            .filter(({ isDeleted }) => !isDeleted)
            .map(
                ({
                    id,
                    title,
                    isPaid,
                    isDonatable,
                    fee,
                    membershipBenefitDetails,
                    eventType,
                    eventScope,
                    status,
                    details,
                    volunteerSignupUrl,
                    volunteerRequired,
                    organizationId,
                    quantityType,
                    quantityInstruction,
                    participantsCount,
                    participantsLimit,
                    products
                }) => ({
                    id,
                    title,
                    eventType,
                    eventScope,
                    status,
                    organizationId,
                    isPaid,
                    isDonatable,
                    fee,
                    membershipBenefitDetails,
                    volunteerRequired,
                    quantityType,
                    quantityInstruction,
                    participantsCount,
                    participantsLimit,
                    volunteerSignupURL: volunteerSignupUrl,
                    venue: details.venue,
                    startDate: MOMENT(details.startDateTime).format('L'),
                    startTime: MOMENT(details.startDateTime).format('HH:mm'),
                    endDate: MOMENT(details.endDateTime).format('L'),
                    endTime: MOMENT(details.endDateTime).format('HH:mm'),
                    isRecurring: details.isRecurring,
                    recurringFrequency: details.recurringFrequency,
                    details: details.details,
                    products
                })
            );
    }

    /**
     * @desc This function is being used to send mail for success event registration
     * <AUTHOR>
     * @since 23/11/2023
     * @param {Object} reqObj reqObj
     * @param {String} templateFile templateFile
     * @param {String} guestTemplateFile guestTemplateFile
     * @param {String} subject subject
     */
    static async sendEventRegistrationMail ({ reqObj, templateFile, guestTemplateFile, subject }) {
        const {
            childName,
            eventName,
            eventStartTime,
            eventEndTime,
            eventLocation,
            isGuestSignup,
            eventReferenceId,
            userEmail: email,
            pendingReason = ''
        } = reqObj;
        let template;
        let templateVariables;

        const currentDate = new Date();
        const year = currentDate.getFullYear();
        if (isGuestSignup) {
            template = `emailTemplates/${guestTemplateFile}`;
            templateVariables = {
                year,
                eventName,
                eventStartTime,
                eventEndTime,
                eventLocation,
                pendingReason,
                email: CONSTANTS.CLIENT_INFO.HELP_EMAIL,
                appname: CONSTANTS.APP_NAME,
                username: `${childName}`,
                referenceId: eventReferenceId,
                trackingLink: `${process.env.FAMILY_WEB_APP_URL}/track-event-signup?referenceId=${eventReferenceId}`
            };
        } else {
            template = `emailTemplates/${templateFile}`;
            templateVariables = {
                year,
                eventName,
                eventStartTime,
                eventEndTime,
                eventLocation,
                email: CONSTANTS.CLIENT_INFO.HELP_EMAIL,
                appname: CONSTANTS.APP_NAME,
                username: `${childName}`
            };
        }
        await EmailService.prepareAndSendEmail(
            [email],
            subject,
            template,
            templateVariables
        );
    }

    /**
     * @desc This function is being used to start chunk upload
     * <AUTHOR>
     * @since 05/12/2024
     */
    static async startChunkUpload (req, user, locale) {
        const Validator = new EventValidator(req.body, locale, null, req.query);
        Validator.validateStartChunkUpload();

        const { organizationId, originalname, fileSize, eventId } = req.body;

        await this.validateOrganizationAssociation(
            organizationId,
            user
        );

        const checkedEventId = eventId ?? uuidv4();
        const fileName = `${process.env.NODE_ENV}-events/organization-${organizationId}/${checkedEventId}/${originalname}-${Date.now()}`;

        if (fileSize < CONSTANTS.MIN_FILE_SIZE_FOR_CHUNK_UPLOAD) {
            Validator.validateFilePresence(req.file);
            await UploadService.uploadFile(req.file, fileName, false);
            return {
                fileName,
                checkedEventId,
                isChunkUpload: false
            };
        }

        const { UploadId } = await UploadService.initiateUploadFileInChunks(fileName);
        return {
            uploadId: UploadId,
            isChunkUpload: true,
            checkedEventId,
            fileName
        };
    }

    /**
     * @desc This function is being used to upload chunk
     * <AUTHOR>
     * @since 05/12/2024
     */
    static async uploadChunk (req, _, locale) {
        const Validator = new EventValidator(req.body, locale, null, req.query);
        Validator.validateUploadChunk();
        Validator.validateFilePresence(req.file);

        const { buffer } = req.file;
        const { fileName, uploadId, partNumber } = req.body;

        const { ETag } = await UploadService.uploadFileInChunks(fileName, uploadId, partNumber, buffer);
        return {
            etag: ETag,
            isChunkUpload: true,
            partNumber: parseInt(partNumber),
            fileName
        };
    }

    /**
     * @desc This function is being used to complete chunk upload
     * <AUTHOR>
     * @since 05/12/2024
     */
    static async completeChunkUpload (req, _, locale) {
        const Validator = new EventValidator(req.body, locale, null, req.query);
        Validator.validateCompleteChunkUpload();

        const { fileName, uploadId, parts } = req.body;
        const { Location } = await UploadService.completeUploadFileInChunks(fileName, uploadId, parts);
        return {
            location: Location,
            isChunkUpload: true,
            fileName
        };
    }

    /**
     * @desc This function is being used to abort chunk upload
     * <AUTHOR>
     * @since 05/12/2024
     */
    static async abortChunkUpload (req, _, locale) {
        const Validator = new EventValidator(req.body, locale, null, req.query);
        Validator.validateAbortChunkUpload();

        const { fileName, uploadId } = req.body;
        await UploadService.abortUploadFileInChunks(fileName, uploadId);
        return {
            isChunkUpload: true,
            fileName
        };
    }

    /**
     * @desc This function is being used to generate presigned url
     * <AUTHOR>
     * @since 10/12/2024
     */
    static async generatePresignedUrl (req, user, locale) {
        const Validator = new EventValidator(req.body, locale, null, req.query);
        Validator.validateGeneratePresignedUrl();

        const { fileName, organizationId, eventId } = req.query;

        await this.validateOrganizationAssociation(
            organizationId,
            user
        );

        const checkedEventId = eventId ?? uuidv4();

        const generatedFileName = `${process.env.NODE_ENV}-events/organization-${organizationId}/${checkedEventId}/${fileName}-${Date.now()}`;

        const presignedUrl = await UploadService.getPreSignedUrlForUpload(
            generatedFileName
        );
        return {
            presignedUrl,
            fileName: generatedFileName,
            eventId: checkedEventId
        };
    }
}

module.exports = EventService;
