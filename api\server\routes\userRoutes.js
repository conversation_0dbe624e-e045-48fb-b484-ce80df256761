/**
 * This file is used to auth API's routes.
 * Created by Growexx on 19/10/2023.
 * @name authRoutes
 */
const router = require('express').Router();

const AuthMiddleware = require('../middleware/auth');
const AclMiddleWare = require('../middleware/acl');

const UserProfileController = require('../services/userProfile/userProfileController');
const UserFeedController = require('../services/userFeed/userFeedController');
const LogoutController = require('../services/logout/logoutController');

router.get('/details', AuthMiddleware, UserProfileController.getUserDetails);
router.get('/feeds', AuthMiddleware, AclMiddleWare, UserFeedController.getUserFeeds);
router.post('/signout', AuthMiddleware, LogoutController.logout);
router.put('/change-password', AuthMiddleware, UserProfileController.changePassword);
router.patch('/update-user', AuthMiddleware, UserProfileController.updateUserProfile);
router.get('/connections', AuthMiddleware, UserProfileController.getConnectionsList);
router.delete('/', AuthMiddleware, UserProfileController.deleteUser);
router.get('/check-feed-status', AuthMiddleware, UserProfileController.checkIsFeedGenerated);
router.get('/membership-list', AuthMiddleware, UserProfileController.getPurchasedMembershipsList);
router.patch('/chat-guidelines-read', AuthMiddleware, UserProfileController.updateChatGuidelinesRead);

module.exports = router;
