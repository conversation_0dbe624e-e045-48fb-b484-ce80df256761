const SendMessageService = require('../../sendSocketMessageService');
const socketConnectionsModel = require('../../../models/socketConnections.model');
const sinon = require('sinon');
const { ApiGatewayManagementApiClient } = require('@aws-sdk/client-apigatewaymanagementapi');
const { beforeEach, afterEach } = require('mocha');
const CONSTANTS = require('../../../util/constants');

describe('sendSocketMessageService', () => {
    let sendStub;

    beforeEach(() => {
        sendStub = sinon.stub(ApiGatewayManagementApiClient.prototype, 'send');
    });

    afterEach(() => {
        sinon.restore();
    });

    it('should send message to users', async () => {
        const userIds = ['test_user_id_1', 'test_user_id_2'];
        const messageData = { id: 'test_message_id', senderId: 'test_sender_id', message: 'Hello, how are you?' };

        sinon.stub(socketConnectionsModel, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    exec: sinon.stub().resolves([
                        { id: 'test_connection_id' },
                        { id: 'test_connection_id_2' },
                        { id: 'test_connection_id_3' }
                    ])
                })
            })
        });

        sendStub.onFirstCall().resolves();
        sendStub.onSecondCall().rejects({ statusCode: 410, message: 'Test error' });
        sendStub.onThirdCall().rejects({ statusCode: 500, message: 'Test error' });

        const deleteStub = sinon.stub(socketConnectionsModel, 'delete').resolves();

        await SendMessageService.sendMessagesToUsers(userIds, messageData);

        sinon.assert.callCount(sendStub, 6);
        sinon.assert.calledWith(deleteStub, { id: 'test_connection_id_2' });
    });

    it('should send chunked message when data is large and statusCode is passed', async () => {
        const userIds = ['test_user_id_1'];
        const statusCode = 201;
        const chunkSize = CONSTANTS.ALLOWED_MESSAGE_SIZE;
        const largeString = 'x'.repeat(chunkSize * 2 + 1000);
        const messageData = { id: 'test_message_id', senderId: 'test_sender_id', message: largeString };

        sinon.stub(socketConnectionsModel, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    exec: sinon.stub().resolves([
                        { id: 'test_connection_id' }
                    ])
                })
            })
        });

        sendStub.resetHistory();
        await SendMessageService.sendMessagesToUsers(userIds, messageData, statusCode);
        sinon.assert.called(sendStub);
    });
});
