const handler = require('../index');
const AWS = require('aws-sdk');
const AWSMock = require('jest-aws-sdk-mock');
const sinon = require('sinon');
const Groups = require('../server/models/groups.model');
const GroupMembers = require('../server/models/groupMembers.model');
const CONSOLE_LOGGER = require('../server/logger');

describe('Organization Members', () => {
    before(() => {
        AWSMock.setSDKInstance(AWS);
    });

    after(() => {
        AWSMock.restore();
    });

    it('should process MODIFY organization member', async () => {
        sinon.stub(Groups, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({ exec: sinon.stub().resolves([{ id: 'group1', save: () => { } }]) })
                    })
                })
            })
        });

        sinon.stub(GroupMembers, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            exec: sinon.stub().onFirstCall().resolves([
                                { id: 'groupMember1', childrenIds: [], delete: () => { }, save: () => { } }
                            ]).onSecondCall().resolves([
                                { id: 'groupMember2', childrenIds: ['child1'], save: () => { } }
                            ]).onThirdCall().resolves([])
                        })
                    })
                })
            })
        });

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/OrganizationMembers/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                OldImage: {
                    organizationId: { S: '123' },
                    users: {
                        L: [
                            { M: { id: { S: '123' }, role: { S: 'admin' }, status: { S: 'active' } } },
                            { M: { id: { S: '456' }, role: { S: 'admin' }, status: { S: 'active' } } },
                            { M: { id: { S: '789' }, role: { S: 'admin' }, status: { S: 'active' } } }
                        ]
                    }
                },
                NewImage: {
                    organizationId: { S: '123' },
                    users: {
                        L: [
                            { M: { id: { S: '123' }, role: { S: 'admin' }, status: { S: 'deleted' } } },
                            { M: { id: { S: '456' }, role: { S: 'admin' }, status: { S: 'deleted' } } },
                            { M: { id: { S: '789' }, role: { S: 'admin' }, status: { S: 'deleted' } } }
                        ]
                    }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(Groups.query);

        Groups.query.restore();
        GroupMembers.query.restore();
    });

    it('should process MODIFY organization member and add new members', async () => {
        sinon.stub(Groups, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({ exec: sinon.stub().resolves([{ id: 'group1', save: () => { } }]) })
                    })
                })
            })
        });

        sinon.stub(GroupMembers, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    exec: sinon.stub().onFirstCall().resolves([
                        { id: 'groupMember1', childrenIds: [], save: () => { } },
                        { id: 'groupMember2', childrenIds: ['child1'], save: () => { }, userId: '102' }
                    ]),
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            exec: sinon.stub().onFirstCall().resolves([
                                { id: 'groupMember1', childrenIds: [], delete: () => { }, save: () => { } }
                            ]).onSecondCall().resolves([
                                { id: 'groupMember2', childrenIds: ['child1'], save: () => { } }
                            ]).onThirdCall().resolves([])
                        })
                    })
                })
            })
        });

        sinon.stub(GroupMembers, 'batchPut').resolves();

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/OrganizationMembers/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                OldImage: {
                    organizationId: { S: '123' },
                    users: {
                        L: [
                            { M: { id: { S: '123' }, role: { S: 'admin' }, status: { S: 'active' } } },
                            { M: { id: { S: '456' }, role: { S: 'admin' }, status: { S: 'active' } } },
                            { M: { id: { S: '789' }, role: { S: 'admin' }, status: { S: 'active' } } }
                        ]
                    }
                },
                NewImage: {
                    organizationId: { S: '123' },
                    users: {
                        L: [
                            { M: { id: { S: '123' }, role: { S: 'admin' }, status: { S: 'deleted' } } },
                            { M: { id: { S: '456' }, role: { S: 'admin' }, status: { S: 'deleted' } } },
                            { M: { id: { S: '789' }, role: { S: 'admin' }, status: { S: 'deleted' } } },
                            { M: { id: { S: '101' }, role: { S: 'admin' }, status: { S: 'active' } } },
                            { M: { id: { S: '102' }, role: { S: 'admin' }, status: { S: 'active' } } }
                        ]
                    }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(Groups.query);

        Groups.query.restore();
        GroupMembers.query.restore();
        GroupMembers.batchPut.restore();
    });

    it('should process MODIFY organization member with no new members', async () => {

        const loggerStub = sinon.stub(CONSOLE_LOGGER, 'info');

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/OrganizationMembers/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                OldImage: {
                    organizationId: { S: '123' },
                    users: {
                        L: [
                            { M: { id: { S: '123' }, role: { S: 'admin' }, status: { S: 'active' } } },
                            { M: { id: { S: '456' }, role: { S: 'admin' }, status: { S: 'active' } } },
                            { M: { id: { S: '789' }, role: { S: 'admin' }, status: { S: 'active' } } }
                        ]
                    }
                },
                NewImage: {
                    organizationId: { S: '123' },
                    users: {
                        L: [
                            { M: { id: { S: '123' }, role: { S: 'admin' }, status: { S: 'active' } } },
                            { M: { id: { S: '456' }, role: { S: 'admin' }, status: { S: 'active' } } },
                            { M: { id: { S: '789' }, role: { S: 'admin' }, status: { S: 'active' } } }
                        ]
                    }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(loggerStub);

        loggerStub.restore();
    });

    it('should handle error when modifying organization member', async () => {
        sinon.stub(Groups, 'query').rejects();

        const record = {
            eventName: 'MODIFY',
            eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/OrganizationMembers/stream/2023-10-27T13:01:18.061',
            dynamodb: {
                OldImage: {
                    organizationId: { S: '123' },
                    users: {
                        L: [
                            { M: { id: { S: '123' }, role: { S: 'admin' }, status: { S: 'active' } } },
                            { M: { id: { S: '456' }, role: { S: 'admin' }, status: { S: 'active' } } },
                            { M: { id: { S: '789' }, role: { S: 'admin' }, status: { S: 'active' } } }
                        ]
                    }
                },
                NewImage: {
                    organizationId: { S: '123' },
                    users: {
                        L: [
                            { M: { id: { S: '123' }, role: { S: 'admin' }, status: { S: 'deleted' } } },
                            { M: { id: { S: '456' }, role: { S: 'admin' }, status: { S: 'deleted' } } },
                            { M: { id: { S: '789' }, role: { S: 'admin' }, status: { S: 'deleted' } } },
                            { M: { id: { S: '101' }, role: { S: 'admin' }, status: { S: 'active' } } },
                            { M: { id: { S: '102' }, role: { S: 'admin' }, status: { S: 'active' } } }
                        ]
                    }
                }
            }
        };

        const event = {
            Records: [record]
        };

        await handler.handler(event);

        sinon.assert.called(Groups.query);

        Groups.query.restore();
    });
});
