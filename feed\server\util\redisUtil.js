const Redis = require('ioredis');
const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);

class RedisUtil {
    static async getMembersByScore (key, cursor, pageSize = CONSTANTS.DEFAULT_PAGE_SIZE) {
        if (process.env.NODE_ENV !== 'testing') {
            return await redis.zrevrangebyscore(
                key, cursor, '-inf', 'WITHSCORES', 'LIMIT', 0, pageSize
            );
        } else {
            return Promise.resolve();
        }
    }

    static async getAllMembersWithScores (key, cursor = '+inf') {
        if (process.env.NODE_ENV !== 'testing') {
            return await redis.zrevrangebyscore(
                key, cursor, '-inf', 'WITHSCORES'
            );
        } else {
            return Promise.resolve();
        }
    }

    static async getElementsOfSortedSetByScore (key, start, stop) {
        if (process.env.NODE_ENV !== 'testing') {
            return await redis.zrangebyscore(key, start, stop, 'WITHSCORES');
        } else {
            return Promise.resolve();
        }
    }

    static async getHashValue (key) {
        if (process.env.NODE_ENV !== 'testing') {
            return await redis.hget(key, 'details');
        } else {
            return Promise.resolve();
        }
    }

    static async getElementsByScore (key, min, max) {
        if (process.env.NODE_ENV !== 'testing') {
            return await redis.zrangebyscore(
                key, min, max
            );
        } else {
            return Promise.resolve();
        }
    }

    static async setHashValue (key, field, value) {
        if (process.env.NODE_ENV !== 'testing') {
            await redis.hset(key, field, JSON.stringify(value));
        } else {
            return;
        }
    }
}

module.exports = RedisUtil;
