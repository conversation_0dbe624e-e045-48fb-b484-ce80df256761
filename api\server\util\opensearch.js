/* eslint-disable max-len */
const { Client } = require('@opensearch-project/opensearch');
const UploadService = require('./uploadService');

let client;

if (process.env.NODE_ENV !== 'testing') {
    client = new Client({
        node: process.env.OPENSEARCH_ENDPOINT,
        auth: {
            username: process.env.OPENSEARCH_USERNAME,
            password: process.env.OPENSEARCH_PASSWORD
        },
        ssl: { rejectUnauthorized: false }
    });
}

/**
 * Class represents Utilities function for AWS Opensearch.
 */
class AwsOpenSearchService {
    /**
     * Function to create a new document in AWS Opensearch.
     * @param {String} collectionName Name of the collection.
     * @param {String} id Unique id of the document.
     * @param {Object} body Body of the document.
     * @returns {Object} Response from AWS Opensearch.
     */
    static async create (collectionName, id, body) {
        if (process.env.NODE_ENV !== 'testing') {
            try {
                return await client.index({
                    index: collectionName,
                    id,
                    body
                });
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error creating index', collectionName, id, body);
                return error;
            }
        } else {
            return '';
        }
    }

    /**
     * Function to get a document from AWS Opensearch.
     * @param {String} collectionName Name of the collection.
     * @param {String} id Unique id of the document.
     * @returns {Object} Response from AWS Opensearch.
     */
    static async updateField (collectionName, searchId, fieldUpdates) {
        if (process.env.NODE_ENV !== 'testing') {
            try {
                return await client.update({
                    index: collectionName,
                    id: searchId,
                    body: {
                        doc: fieldUpdates,
                        doc_as_upsert: true
                    }
                });
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error updating index', collectionName, searchId, fieldUpdates);
                return error;
            }
        } else {
            return '';
        }
    }

    /**
     * Function to delete a document from AWS OpenSearch.
     * @param {String} collectionName Name of the collection.
     * @param {String} id Unique id of the document.
     * @returns {Object} Response from AWS OpenSearch.
     */
    static async delete (collectionName, id) {
        if (process.env.NODE_ENV !== 'testing') {
            try {
                return await client.delete({
                    index: collectionName,
                    id
                });
            } catch (error) {
                return error;
            }
        } else {
            return '';
        }
    }

    static async registerMultipleEvents (collectionName, childId, eventIds) {
        if (process.env.NODE_ENV !== 'testing') {
            try {
                return await client.update({
                    index: collectionName,
                    id: childId,
                    body: {
                        script: {
                            source: `
                                if (!ctx._source.containsKey("childCalendarEvents")) { 
                                    ctx._source.childCalendarEvents = new ArrayList(); 
                                } 
                                for (def eventId : params.tags) {
                                    if (!ctx._source.childCalendarEvents.contains(eventId)) { 
                                        ctx._source.childCalendarEvents.add(eventId); 
                                    }
                                }
                            `,
                            lang: 'painless',
                            params: {
                                tags: eventIds
                            }
                        }
                    }
                });
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error registering multiple events', collectionName, childId, eventIds);
                return error;
            }
        } else {
            return '';
        }
    }

    static async emptyChildCalendarEvents (collectionName, childId) {
        if (process.env.NODE_ENV !== 'testing') {
            try {
                return await client.update({
                    index: collectionName,
                    id: childId,
                    body: {
                        script: {
                            source: `
                                if (!ctx._source.containsKey("childCalendarEvents")) { 
                                    ctx._source.childCalendarEvents = new ArrayList(); 
                                } else {
                                    ctx._source.childCalendarEvents.clear();
                                }
                            `,
                            lang: 'painless'
                        }
                    }
                });
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error emptying child calendar events', collectionName, childId);
                return error;
            }
        } else {
            return '';
        }
    }

    static async searchChild (index, body) {
        if (process.env.NODE_ENV !== 'testing') {
            const { searchValue, school } = body;
            try {
                const size = 100;
                let from = 0;
                let continueFetching = true;
                const allRecords = [];

                while (continueFetching) {
                    const lowerCaseSearchValue = searchValue.toLowerCase();
                    const response = await client.search({
                        index,
                        size,
                        from,
                        body: {
                            query: {
                                bool: {
                                    should: [
                                        {
                                            wildcard: {
                                                'firstName': {
                                                    value: `*${lowerCaseSearchValue}*`
                                                }
                                            }
                                        },
                                        {
                                            wildcard: {
                                                'lastName': {
                                                    value: `*${lowerCaseSearchValue}*`
                                                }
                                            }
                                        },
                                        {
                                            match_phrase: {
                                                'firstName': lowerCaseSearchValue
                                            }
                                        },
                                        {
                                            match_phrase: {
                                                'lastName': lowerCaseSearchValue
                                            }
                                        }
                                    ],
                                    filter: {
                                        match: { school }
                                    },
                                    minimum_should_match: 1
                                }
                            }
                        }
                    });

                    allRecords.push(...response.body.hits.hits);
                    if (response.body.hits.hits.length < size) {
                        continueFetching = false;
                    } else {
                        from += size;
                    }
                }

                return allRecords;
            } catch (error) {
                CONSOLE_LOGGER.error(JSON.stringify(error), 'error searching child', index);
                return error;
            }
        } else {
            return '';
        }
    }
    static async searchOrganization (index, data) {
        const lowerCaseSearchValue = data.searchValue.toLowerCase();
        const { body } = await client.search({
            index,
            body: {
                query: {
                    bool: {
                        should: [
                            {
                                wildcard: {
                                    'name': {
                                        value: `*${lowerCaseSearchValue}*`
                                    }
                                }
                            },
                            {
                                match_phrase: {
                                    'name': lowerCaseSearchValue
                                }
                            }
                        ],
                        must_not: [
                            {
                                match: {
                                    'category': CONSTANTS.CATEGORIES.SUPER_ORGANIZATION
                                }
                            },
                            {
                                match: {
                                    'category': CONSTANTS.CATEGORIES.PTO
                                }
                            },
                            {
                                match: {
                                    'category': CONSTANTS.CATEGORIES.SCHOOL
                                }
                            },
                            {
                                match: {
                                    'category': CONSTANTS.CATEGORIES.HOME_ROOM
                                }
                            }
                        ]
                    }
                }
            }
        });

        const totalItems = body.hits.total.value;
        const totalPages = Math.ceil(totalItems / data.limit);
        const organization = body.hits.hits.map((hit) => hit._source);

        const excludeToJoinOrganization = await Promise.all(organization.map(async (org) => {
            if (CONSTANTS.ORGANIZATION_NOT_ALLOWED.includes(org.category)) {
                return { ...org, isAllowedToJoin: false };
            } else {
                return { ...org, isAllowedToJoin: true, logo: org.logo ? await UploadService.getSignedUrl(org.logo) : null };
            }
        }));

        return {
            page: parseInt(data.page),
            organization: excludeToJoinOrganization,
            count: body.hits.hits.length,
            totalPages,
            totalItems
        };
    }
}

module.exports = AwsOpenSearchService;
