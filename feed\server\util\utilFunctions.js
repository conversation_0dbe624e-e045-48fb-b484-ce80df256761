/* eslint-disable max-len */
const HTTPStatus = require('../util/http-status');
const crypto = require('crypto');
const secretKey = process.env.HMAC_SECRET_KEY;

/**
 * This class reprasents common utilities for application
 */
class Utils {
    static errorResponse () {
        return JSON.parse(
            JSON.stringify({
                status: 0,
                data: {},
                message: ''
            })
        );
    }

    static successResponse () {
        return JSON.parse(
            JSON.stringify({
                status: 1,
                data: {},
                message: ''
            })
        );
    }

    /**
   * This function is being used to add pagination for user table
   * @auther Growexx
   * @param {string} error Error Message
   * @param {Object} data Object to send in response
   * @param {Object} res Response Object
   * @param {string} successMessage success message
   * @param {Object} additionalData additional data outside of data object in response
   * @param {string} successMessageVars
   * @since 01/03/2021
   */
    static sendResponse (error, data, res, successMessage, successMessageVars) {
        let responseObject;

        if (error) {
            let status;
            responseObject = Utils.errorResponse();
            if (typeof error === 'object') {
                responseObject.message = error.message
                    ? error.message
                    : res.__('ERROR_MSG');
                status = error.statusCode ? error.statusCode : HTTPStatus.BAD_REQUEST;
            } else {
                responseObject.message = res.__(error);
                status = HTTPStatus.BAD_REQUEST;
            }

            responseObject.data = error.data;
            res.status(status).send(responseObject);
        } else {
            responseObject = Utils.successResponse();
            responseObject.message = successMessageVars
                ? res.__.apply('', [successMessage].concat(successMessageVars))
                : successMessage;
            responseObject.data = data;
            res.status(HTTPStatus.OK).send(responseObject);
        }
    }
    static generateHmac (url) {
        const currentDate = MOMENT().utc().format('MMYYYYDD'); // 03202426
        const reqURL = url; // /feed/path
        const message = currentDate + reqURL;
        const calculatedHmac = crypto.createHmac('sha256', secretKey).update(message).digest('hex');
        return calculatedHmac;
    }

    /**
   * This function is being used to overwrite request function for adding reqtoken
   * @auther Growexx
   * @param {import('supertest')} request
   * @since 28/03/2024
   */
    static addCommonReqTokenForHMac (request) {
        const originalEnd = request.Test.prototype.end;

        request.Test.prototype.end = function (callback) {
            const currentParsedUrl = new URL(this.url);

            this.set('reqtoken', Utils.generateHmac(currentParsedUrl.pathname));
            return originalEnd.call(this, callback);
        };
    }

    /**
     * @description Generates a prefix for a key
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} keyPrefix - The key prefix
     * @returns {String} The prefix
     */
    static generatePrefixForKey ({ versionPrefix, keyPrefix }) {
        return `${versionPrefix}:${keyPrefix}`;
    }

    /**
     * @description Generates a key
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} keyPrefix - The key prefix
     * @param {String} id - The id
     * @returns {String} The key
     */
    static generateKey ({ versionPrefix, keyPrefix, id }) {
        return `${this.generatePrefixForKey({ versionPrefix, keyPrefix })}:${id}`;
    }

    /**
     * @description Generates a key for a user
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} userId - The user id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getUserKey ({ versionPrefix, userId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_USER_EVENTS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_USER_EVENTS, id: userId });
    }

    /**
     * @description Generates a key for a registered user
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} userId - The user id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getRegisteredUserKey ({ versionPrefix, userId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_USER_REGISTERED_EVENTS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_USER_REGISTERED_EVENTS, id: userId });
    }

    /**
     * @description Generates a key for a calendar user
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} userId - The user id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getCalendarUserKey ({ versionPrefix, userId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_USER_CALENDAR_EVENTS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_USER_CALENDAR_EVENTS, id: userId });
    }

    /**
     * @description Generates a key for a child
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} childId - The child id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getChildKey ({ versionPrefix, childId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CHILD_EVENTS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CHILD_EVENTS, id: childId });
    }

    /**
     * @description Generates a key for a registered child
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} childId - The child id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getRegisteredChildKey ({ versionPrefix, childId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CHILD_REGISTERED_EVENTS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CHILD_REGISTERED_EVENTS, id: childId });
    }

    /**
     * @description Generates a key for a calendar child
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} childId - The child id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getCalendarChildKey ({ versionPrefix, childId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CHILD_CALENDAR_EVENTS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CHILD_CALENDAR_EVENTS, id: childId });
    }

    /**
     * @description Generates a key for a child details
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} childId - The child id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getChildDetailsKey ({ versionPrefix, childId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CHILD_DETAILS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_CHILD_DETAILS, id: childId });
    }

    /**
     * @description Generates a key for a event details
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} eventId - The event id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getEventDetailsKey ({ versionPrefix, eventId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_EVENT_DETAILS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_EVENT_DETAILS, id: eventId });
    }

    /**
     * @description Generates a key for a fundraiser details
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} fundraiserId - The fundraiser id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getFundraiserDetailsKey ({ versionPrefix, fundraiserId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_FUNDRAISER_DETAILS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_FUNDRAISER_DETAILS, id: fundraiserId });
    }

    /**
     * @description Generates a key for a post details
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} postId - The post id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getPostDetailsKey ({ versionPrefix, postId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_POST_DETAILS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_POST_DETAILS, id: postId });
    }

    /**
     * @description Generates a key for a organization details
     * <AUTHOR>
     * @param {String} versionPrefix - The version prefix
     * @param {String} organizationId - The organization id
     * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
     * @returns {String} The key
     */
    static getOrganizationDetailsKey ({ versionPrefix, organizationId, shouldGenerateKeyPrefix = false }) {
        return shouldGenerateKeyPrefix
            ? this.generatePrefixForKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_ORGANIZATION_DETAILS })
            : this.generateKey({ versionPrefix, keyPrefix: CONSTANTS.KEY_FOR_ORGANIZATION_DETAILS, id: organizationId });
    }
}

module.exports = Utils;
