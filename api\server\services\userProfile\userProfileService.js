const Validation = require('../../util/validation');
const User = require('../../models/user.model');
const Child = require('../../models/child.model');
const Organization = require('../../models/organization.model');
const OrganizationMember = require('../../models/organizationMember.model');
const Notification = require('../../models/notification.model');
const PendingPushNotification = require('../../models/pendingPushNotification.model');
const PendingPartnerInvite = require('../../models/pendingPartnerInvite.model');
const UploadService = require('../../util/uploadService');
const { selectAttributes } = require('../../util/utilFunctions');
const Cognito = require('../../util/cognito');
const UserProfileValidator = require('./userProfileValidator');
const EventSignup = require('../../models/eventSignup.model');
const Event = require('../../models/event.model');
const ChildOrganizationMapping = require('../../models/childOrganizationMapping.model');
const AwsOpenSearchService = require('../../util/opensearch');
const dynamoose = require('dynamoose');
const RedisUtil = require('../../util/redisUtil');
const Redis = require('ioredis');
const fundraiserSignupModel = require('../../models/fundraiserSignup.model');
const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
const GroupMember = require('../../models/groupMembers.model');
const ConversationService = require('./ConversationService');
const ConstantModel = require('../../models/constant.model');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents services for user profile.
 */
class UserProfileService {
    /**
     * @desc This function is being used to get user profile
     * <AUTHOR>
     * @since 18/10/2023
     * @param {Object} user user
     */
    static async getUserDetails (req, user, locale) {
        if (user.accessLevel === CONSTANTS.ACCESS_LEVEL.ROOT && req.query.userId) {
            const { userId } = req.query;
            const Validator = new Validation(locale);
            Validator.field(userId, 'User Id');
            return await User.query('id').eq(userId).exec();
        } else {
            const children = user.children.length && await Child.batchGet(user.children);
            const childrenWithSignedUrls = user.children.length && await Promise.all(children.map(async child => {
                const photoURL = child.photoURL ? await UploadService.getSignedUrl(child.photoURL) : null;
                const selectedChildAttributes = ['id', 'firstName', 'lastName', 'gender',
                    'associatedColor', 'school', 'homeRoom', 'dob', 'zipCode', 'associatedOrganizations'];
                const formattedChild = selectAttributes(selectedChildAttributes, child);
                return { ...formattedChild, photoURL };
            }));

            const organizations = user.associatedOrganizations.length &&
            (await Promise.all(user.associatedOrganizations.map(async organization => {
                const org = await Organization.get(organization.organizationId);

                if (org.isDeleted === 1) {
                    return null;
                }
                const selectedAttributes = ['address', 'country', 'name', 'state', 'city', 'category', 'id', 'zipCode'];
                const associatedOrgRole = user.associatedOrganizations.find(
                    (organization) => organization.organizationId === org.id
                ).role;
                return {
                    ...selectAttributes(selectedAttributes, org),
                    associatedOrgRole
                };
            }))).filter(org => org !== null);

            const selectedUserAttributes = ['id', 'firstName', 'lastName',
                'accessLevel', 'email', 'countryCode', 'phoneNumber', 'status', 'stripeCustomerId', 'token', 'refreshToken',
                'provider', 'hasReadChatGuidelines'];
            const formattedUser = selectAttributes(selectedUserAttributes, user);

            return { ...formattedUser, children: childrenWithSignedUrls || [], associatedOrganizations: organizations || [] };
        }
    }

    /**
     * @desc This function is being used to change password of user
     * <AUTHOR>
     * @since 22/02/2024
     * @param {Object} req Request
     * @param {Object} user User
     * @param {Object} locale Locale
     */
    static async changePassword (req, user, locale) {
        const Validator = new UserProfileValidator(req.body, locale);
        Validator.validateChangePassword();

        const { oldPassword, newPassword } = req.body;

        if (!user.provider) {
            user.provider = CONSTANTS.PROVIDER.EMAIL;
        }

        if (user.provider === CONSTANTS.PROVIDER.EMAIL && !oldPassword) {
            throw {
                message: locale(MESSAGES.FIELD_REQUIRED, 'Old Password'),
                statusCode: 400
            };
        }

        if (user.provider === CONSTANTS.PROVIDER.EMAIL) {

            let cognitoUser;

            try {
                cognitoUser = await Cognito.login({ email: user.email, password: oldPassword });
            } catch (error) {
                if (error.code === 'NotAuthorizedException') {
                    throw {
                        message: MESSAGES.INVALID_OLD_PASSWORD,
                        statusCode: 400
                    };
                } else {
                    throw {
                        message: error.message,
                        statusCode: 500
                    };
                }
            }

            if (cognitoUser) {
                await Cognito.setCognitoUserPassword(user.email, newPassword);
            } else {
                throw {
                    message: MESSAGES.INVALID_OLD_PASSWORD,
                    statusCode: 400
                };
            }
        } else {
            // We need to create a new user in cognito
            user.password = newPassword;

            await Cognito.signup(user);
            await Cognito.updateConfirmStatus(user.email);
            await Cognito.updateUserToActive(user.email);
            await User.update({ id: user.id, email: user.email }, { provider: CONSTANTS.PROVIDER.EMAIL });
        }
    }

    /**
     * @desc This function is being used to update profile of user
     * @param {Object} req Request
     * @param {Object} user User
     * @param {Object} locale Locale
     */
    static async updateUserProfile (req, user, locale) {
        const Validator = new UserProfileValidator(req.body, locale);
        Validator.validateUpdateProfile();

        const { firstName, lastName } = req.body;

        const updatedUser = await User.update({ id: user.id, email: user.email }, { firstName, lastName });
        updatedUser.provider === CONSTANTS.PROVIDER.EMAIL && await Cognito.updateUserFirstNameAndLastName(user.email, firstName, lastName);
        const organizationIds = updatedUser.associatedOrganizations.map(organization => organization.organizationId);

        if (organizationIds.length > 0) {
            const organizationMembers = await OrganizationMember.batchGet(organizationIds);
            await Promise.all(organizationMembers.map(async organizationMember => {
                const getUser = organizationMember.users.find(user => user.id === updatedUser.id);
                getUser.firstName = firstName;
                getUser.lastName = lastName;
                await organizationMember.save();
            }));
        }

        const allGroupIds = (await GroupMember.query('userId').eq(user.id).using('userId-index').exec())
            .map(groupMember => groupMember.groupId);
        let allGroupMemberIds = (await Promise.all(
            allGroupIds.map(async groupId => await ConversationService.getGroupMemberIds(groupId)))).flat();
        allGroupMemberIds = [...new Set(allGroupMemberIds)];
        const activeSocketConnections = await ConversationService.getActiveSocketConnections(allGroupMemberIds);
        const messageData = {
            action: 'sendMessage',
            actionType: 'USER_UPDATED',
            message: 'User\'s profile has been updated',
            data: {
                userId: user.id,
                firstName,
                lastName,
                userGroupIds: allGroupIds
            }
        };
        await ConversationService.sendMessagesToConnections(activeSocketConnections, messageData);
    }

    /**
     * @desc This function is being used to get connections list of user
     * <AUTHOR>
     * @since 04/03/2024
     * @param {Object} req Request
     * @param {Object} user User
     */
    static async getConnectionsList (req, user) {
        if (!user.children.length) {
            return [];
        }

        const children = await Child.batchGet(user.children, { attributes: ['id', 'connections'] });
        const connectedChildIds = this.getConnectedChildIds(children);

        if (!connectedChildIds.length) {
            return [];
        }

        const connectedChildren = await this.getConnectedChildren(connectedChildIds);
        const organizationMap = await this.getOrganizationMap(connectedChildren);

        this.assignOrganizationNames(connectedChildren, organizationMap);

        const childMap = new Map(connectedChildren.map(child => [child.id, child]));

        return await this.buildResult(children, childMap);
    }

    static getConnectedChildIds (children) {
        return [...new Set(children.flatMap(child =>
            child.connections
                .filter(connection => connection.status === 'connected')
                .map(connection => connection.childId)
        ))];
    }

    static async getConnectedChildren (childIds) {
        return await Child.batchGet(childIds, {
            attributes: ['id', 'firstName', 'lastName', 'photoURL', 'school', 'homeRoom', 'associatedColor']
        });
    }

    static async getOrganizationMap (connectedChildren) {
        const schoolIds = [...new Set(connectedChildren.map(child => child.school))];
        const homeroomIds = [...new Set(connectedChildren.map(child => child.homeRoom).filter(Boolean))];
        const organizationIds = [...new Set([...schoolIds, ...homeroomIds])];
        const organizations = await Organization.batchGet(organizationIds, { attributes: ['id', 'name'] });

        return new Map(organizations.map(org => [org.id, org]));
    }

    static assignOrganizationNames (connectedChildren, organizationMap) {
        connectedChildren.forEach(child => {
            child.school = organizationMap.get(child.school).name;
            child.homeRoom = organizationMap.get(child.homeRoom)?.name;
        });
    }

    static async buildResult (children, childMap) {
        return await Promise.all(children.map(async child => ({
            childId: child.id,
            connections: await Promise.all(child.connections
                .filter(connection => connection.status === CONSTANTS.CONNECTION_STATUS.CONNECTED)
                .map(async connection => {
                    const connectedChild = childMap.get(connection.childId);
                    return {
                        ...connectedChild,
                        photoURL: connectedChild.photoURL ? await UploadService.getSignedUrl(connectedChild.photoURL) : null,
                        id: undefined,
                        childId: connectedChild.id
                    };
                }))
        })));
    }

    /**
     * @desc This function is being used to delete user
     * <AUTHOR>
     * @since 26/04/2024
     * @param {Object} req Request
     * @param {Object} user User
     */
    static async deleteUser (req, user) {
        const actions = [];

        await this.checkUserAssociation(user);

        // soft delete user so that user cannot login anymore
        await User.update({ id: user.id, email: user.email }, { isDeleted: 1, status: CONSTANTS.STATUS.INACTIVE });
        CONSOLE_LOGGER.info(`User ${user.id} -> ${user.email} is soft deleted`);

        const versionPrefixFromDb = await ConstantModel.get(CONSTANTS.FEED_VERSION_PREFIX);
        const versionPrefix = versionPrefixFromDb?.value ?? '';

        const userChildren = await this.getUserChildren(user);
        const userChildrenIds = userChildren.map(child => child.id);
        const profilePicturesUrls = userChildren.map(child => child.photoURL).filter(Boolean);

        // delete pending push notification for user children
        const pendingPushNotifications = [];
        for (const child of userChildrenIds) {
            pendingPushNotifications.push(
                ...await PendingPushNotification.query('associatedChildId').eq(child).using('associatedChildId-index').exec()
            );
        }
        const pendingPushNotificationIds = pendingPushNotifications.map(notification => notification.id);

        for (const pendingPushNotificationId of pendingPushNotificationIds) {
            actions.push(PendingPushNotification.transaction.delete(pendingPushNotificationId));
        }
        CONSOLE_LOGGER.info(`Deleted ${pendingPushNotificationIds.length} pending push notifications for user ${user.id}`);


        // delete notification of user
        const notifications = await Notification.query('userId').eq(user.id).using('userId-index').attributes(['id']).exec();
        const notificationIds = notifications.map(notification => notification.id);

        for (const notificationId of notificationIds) {
            actions.push(Notification.transaction.delete(notificationId));
        }
        CONSOLE_LOGGER.info(`Deleted ${notificationIds.length} notifications for user ${user.id}`);

        // delete user event signup
        const userEventSignups = [];
        for (const child of userChildrenIds) {
            const eventSignups = await EventSignup.query('childId').eq(child)
                .using('childId-index').attributes(['id', 'eventId', 'quantityCount']).exec();
            userEventSignups.push(...eventSignups);
            for (const eventSignup of eventSignups) {
                const event = await Event.get(eventSignup.eventId);
                if (event) {
                    event.participantsCount = event.participantsCount - (eventSignup.quantityCount ?? 1);
                    await event.save();
                }
            }
            CONSOLE_LOGGER.info(`Deleted ${userEventSignups.length} event signups for child ${child} of user ${user.id}`);
        }

        const userEventSignupIds = userEventSignups.map(eventSignup => eventSignup.id);
        for (const userEventSignupId of userEventSignupIds) {
            actions.push(EventSignup.transaction.delete(userEventSignupId));
        }
        CONSOLE_LOGGER.info(`Deleted ${userEventSignupIds.length} event signups for user ${user.id}`);

        // delete user fundraiser signups
        const userFundraiserSignups = [];
        for (const child of userChildrenIds) {
            const fundraiserSignups = await fundraiserSignupModel.query('childId').eq(child)
                .using('childId-index').attributes(['id', 'eventId']).exec();
            userFundraiserSignups.push(...fundraiserSignups);

            CONSOLE_LOGGER.info(`Deleted ${fundraiserSignups.length} fundraiser signups for child ${child} of user ${user.id}`);
        }

        const userFundraiserSignupIds = userFundraiserSignups.map(fundraiserSignup => fundraiserSignup.id);
        for (const userFundraiserSignupId of userFundraiserSignupIds) {
            actions.push(fundraiserSignupModel.transaction.delete(userFundraiserSignupId));
        }
        CONSOLE_LOGGER.info(`Deleted ${userFundraiserSignupIds.length} fundraiser signups for user ${user.id}`);

        // delete child organization mapping and child connections with other child
        for (const child of userChildren) {
            for (const organization of child.associatedOrganizations) {
                const childOrganizationMapping = await ChildOrganizationMapping.query('organizationId')
                    .eq(organization).using('organizationId-index')
                    .where('childId').eq(child.id)
                    .attributes(['childOrganizationMappingId']).exec();
                if (childOrganizationMapping.length) {
                    actions.push(ChildOrganizationMapping.transaction.delete(childOrganizationMapping[0].childOrganizationMappingId));
                    CONSOLE_LOGGER.info(`Deleted child organization mapping for child ${child.id} of user ${user.id}`);
                }
            }

            // delete child connections with other child
            for (const connection of child.connections) {
                const childToUpdate = await Child.get(connection.childId);
                const updatedConnections = childToUpdate.connections.filter(conn => conn.childId !== child.id);
                await Child.update({ id: childToUpdate.id }, { connections: updatedConnections });
            }
            CONSOLE_LOGGER.info(`Deleted child connections for child ${child.id} of user ${user.id}`);
        }

        // delete send invites of this user
        // from send invites get the accepted children
        // from accepted children remove this child from that user
        for (const userSendInvites of (user.sendInvites ?? [])) {
            const currentInvitedUser = await User.query('email').eq(userSendInvites.invitedPartnerEmail).using('email-index').exec();
            const acceptedChildren = userSendInvites.children.filter(child => child.status === 'accepted');
            if (acceptedChildren.length && currentInvitedUser.length) {
                const acceptedChildIds = acceptedChildren.map(child => child.childId);
                // filter the acceptedChildIds from the acceptedInviteUser
                const acceptedInviteUserChildren = currentInvitedUser[0].children.filter(child => !acceptedChildIds.includes(child));
                await User.update({ id: currentInvitedUser[0].id, email: currentInvitedUser[0].email },
                    { children: acceptedInviteUserChildren });
                CONSOLE_LOGGER.info(`Deleted accepted children from user ${currentInvitedUser[0].id}`);
            }
            const pendingChildren = userSendInvites.children.filter(child => child.status === 'pending');
            if (pendingChildren.length && currentInvitedUser.length) {
                // filter the partnerInvites of this user
                const updatedPendingInviteOfUser = currentInvitedUser[0]?.partnerInvites
                    .filter(invitedPartner => invitedPartner.inviterPartnerEmail !== user.email);
                await User.update({ id: currentInvitedUser[0].id, email: currentInvitedUser[0].email },
                    { partnerInvites: updatedPendingInviteOfUser });
                CONSOLE_LOGGER.info(`Deleted pending children from user ${currentInvitedUser[0].id}`);
            }
        }


        // delete partner invites of this user
        if (user.partnerInvites && user.partnerInvites.length) {
            for (const partnerInvite of user.partnerInvites) {
                const partnerInviteUser = await User.query('email').eq(partnerInvite.inviterPartnerEmail).using('email-index').exec();
                await User.update({ id: partnerInviteUser[0].id, email: partnerInviteUser[0].email },
                    { sendInvites: partnerInviteUser[0].sendInvites.filter(sendInvite => sendInvite.invitedPartnerEmail !== user.email) });
                CONSOLE_LOGGER.info(`Deleted partner invites from user ${partnerInviteUser[0].id}`);
            }
        }

        // delete pending partner invites send by this user
        const pendingPartnerInvites = await PendingPartnerInvite.query('inviterPartnerId').eq(user.id)
            .using('inviterPartnerId-index').exec();
        const pendingPartnerInviteIds = pendingPartnerInvites.map(invite => invite.id);
        for (const pendingPartnerInviteId of pendingPartnerInviteIds) {
            actions.push(PendingPartnerInvite.transaction.delete(pendingPartnerInviteId));
        }
        CONSOLE_LOGGER.info(`Deleted ${pendingPartnerInviteIds.length} pending partner invites for user ${user.id}`);

        // delete all children feeds of this user from redis
        const pipeline = redis.pipeline();
        for (const childId of userChildrenIds) {
            const childEventsKey = Utils.getChildKey({ versionPrefix, childId });
            const childRegisteredEventsKey = Utils.getRegisteredChildKey({ versionPrefix, childId });
            const childCalendarEventsKey = Utils.getCalendarChildKey({ versionPrefix, childId });
            const childDetailsKey = Utils.getChildDetailsKey({ versionPrefix, childId });

            RedisUtil.deleteKey(pipeline, childEventsKey);
            RedisUtil.deleteKey(pipeline, childRegisteredEventsKey);
            RedisUtil.deleteKey(pipeline, childCalendarEventsKey);
            RedisUtil.deleteKey(pipeline, childDetailsKey);
        }
        CONSOLE_LOGGER.info(`Deleted all children feeds from redis for user ${user.id}`);

        // delete all children of this user
        for (const childId of userChildrenIds) {
            actions.push(Child.transaction.delete(childId));
        }
        CONSOLE_LOGGER.info(`Deleted ${userChildrenIds.length} children for user ${user.id}`);

        // delete all group members of this user
        const userGroupMembers = await GroupMember.query('userId').eq(user.id)
            .using('userId-index').exec();
        let allGroupMemberIds = [];
        const userGroupIds = [];
        for (const groupMember of userGroupMembers) {
            actions.push(GroupMember.transaction.delete(groupMember.id));
            userGroupIds.push(groupMember.groupId);
            const groupMembers = await ConversationService.getGroupMemberIds(groupMember.groupId);
            allGroupMemberIds.push(...groupMembers);
        }
        allGroupMemberIds = [...new Set(allGroupMemberIds)];
        const activeSocketConnections = await ConversationService.getActiveSocketConnections(allGroupMemberIds);
        const messageData = {
            action: 'sendMessage',
            actionType: 'USER_DELETED',
            message: 'User has been deleted',
            data: {
                userId: user.id,
                userGroupIds
            }
        };
        await ConversationService.sendMessagesToConnections(activeSocketConnections, messageData);

        CONSOLE_LOGGER.info(`Deleted ${userGroupMembers.length} group members for user ${user.id}`);

        // delete user from cognito
        try {
            await Cognito.deleteUser(user.email);
        } catch (error) {
            CONSOLE_LOGGER.error(`Error while deleting user from cognito for user ${user.id}`, error);
        }
        CONSOLE_LOGGER.info(`Deleted user from cognito for user ${user.id}`);

        // delete this user
        actions.push(User.transaction.delete({ id: user.id, email: user.email }));
        CONSOLE_LOGGER.info(`Deleted user from dynamo for user ${user.id}`);

        // delete child from the opensearch
        if (userChildrenIds.length) {
            for (const childId of userChildrenIds) {
                await AwsOpenSearchService.delete(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, childId);
            }
        }
        CONSOLE_LOGGER.info(`Deleted ${userChildrenIds.length} children from opensearch for user ${user.id}`);

        // delete profile pictures from s3
        for (const profilePicture of profilePicturesUrls) {
            await UploadService.deleteObject(profilePicture);
        }
        CONSOLE_LOGGER.info(`Deleted ${profilePicturesUrls.length} profile pictures from s3 for user ${user.id}`);

        const chunks = [];
        for (let i = 0; i < actions.length; i += 100) {
            chunks.push(actions.slice(i, i + 100));
        }

        // Perform transaction operation on each chunk
        for (const chunk of chunks) {
            await dynamoose.transaction(chunk);
        }

        const userEventsKey = Utils.getUserKey({ versionPrefix, userId: user.id });
        const userRegisteredEventsKey = Utils.getRegisteredUserKey({ versionPrefix, userId: user.id });
        const userCalendarEventsKey = Utils.getCalendarUserKey({ versionPrefix, userId: user.id });
        const userDetailsKey = Utils.getUserDetailsKey({ userId: user.id });

        RedisUtil.deleteKey(pipeline, userEventsKey);
        RedisUtil.deleteKey(pipeline, userRegisteredEventsKey);
        RedisUtil.deleteKey(pipeline, userCalendarEventsKey);
        RedisUtil.deleteKey(pipeline, userDetailsKey);

        await pipeline.exec();
    }

    static async checkUserAssociation (user) {
        if (user.associatedOrganizations.length) {
            throw {
                message: MESSAGES.USER_ASSOCIATED_ORGANIZATION,
                statusCode: 400
            };
        }
    }

    static async getUserChildren (user) {
        const childrenIds = user.children;
        const children = [];
        if (childrenIds.length) {
            children.push(...await Child.batchGet(childrenIds));
        }

        return children.filter(child => child.createdBy === user.id);
    }

    /**
     * Checks if the user's feeds have been generated.
     * <AUTHOR>
     * @since 26/07/2024
     * @param {Object} user - The user object.
     * @returns {boolean} - Returns true if the user's feeds have been generated, false otherwise.
     * If the user object does not have the 'isFeedsGenerated' property, it returns true by default.
    */
    static async checkIsFeedGenerated (user) {
        return user.isFeedsGenerated ?? true;
    }

    /**
     * Retrieves a list of purchased memberships for a user, including organization and child details.
     * <AUTHOR>
     * @since 31/07/2024
     * @param {Object} user - The user object.
     * @returns {Promise<Object[]>} - A promise that resolves to an array of objects, each representing a purchased membership.
    */
    static async getPurchasedMembershipsList (user) {
        const formattedResponse = [];
        const uniqueOrgIds = new Set();
        const fundraiserSignupIdSet = new Set();
        const childIdsArray = user.children?.filter(Boolean) || [];

        user.membershipsPurchased?.forEach(membership => {
            uniqueOrgIds.add(membership.organizationId);
            fundraiserSignupIdSet.add(membership.fundraiserSignupId);
        });


        const children = await this.batchGetChildren(childIdsArray,
            ['id', 'firstName', 'lastName', 'associatedColor', 'photoURL', 'membershipsPurchased']);

        children?.forEach(child => {
            child.membershipsPurchased?.forEach(membership => {
                if (!fundraiserSignupIdSet.has(membership.fundraiserSignupId)) {
                    uniqueOrgIds.add(membership.organizationId);
                }
            });
        });

        const organizations = await this.batchGetOrganizations(Array.from(uniqueOrgIds).filter(Boolean),
            ['id', 'name', 'zipCode', 'category', 'address', 'country', 'state', 'city']);

        const orgMap = new Map(organizations.map(org => [org.id, org]));

        const signedUrlCache = new Map();

        user.membershipsPurchased?.forEach(membership => {
            const orgDetails = orgMap.get(membership.organizationId);
            formattedResponse.push({
                ...membership,
                organizationDetails: orgDetails,
                associatedChild: null
            });
        });

        for (const child of children) {
            for (const membership of child.membershipsPurchased ?? []) {
                if (!fundraiserSignupIdSet.has(membership.fundraiserSignupId)) {
                    const orgDetails = orgMap.get(membership.organizationId);
                    const childDetails = {
                        id: child.id,
                        firstName: child.firstName,
                        lastName: child.lastName,
                        associatedColor: child.associatedColor,
                        photoURL: child.photoURL
                    };
                    if (childDetails.photoURL) {
                        if (!signedUrlCache.has(childDetails.photoURL)) {
                            const signedUrl = await UploadService.getSignedUrl(childDetails.photoURL);
                            signedUrlCache.set(childDetails.photoURL, signedUrl);
                        }
                        childDetails.photoURL = signedUrlCache.get(childDetails.photoURL);
                    }
                    formattedResponse.push({
                        ...membership,
                        organizationDetails: orgDetails,
                        associatedChild: childDetails
                    });
                }
            }
        }

        return formattedResponse;
    }

    /**
     * Retrieves a batch of organizations from DynamoDB using their unique IDs.
     * <AUTHOR>
     * @since 31/07/2024
     * @param {string[]} orgIds - An array of unique IDs of the organizations to retrieve.
     * @param {string[]} [attributes] - An optional array of attribute names to retrieve.
     * @returns {Promise<Organization[]>} - A promise that resolves to an array of Organization objects.
    */
    static async batchGetOrganizations (orgIds, attributes) {
        if (orgIds.length === 0) {
            return [];
        }
        return await Organization.batchGet(orgIds, { attributes });
    }

    /**
     * Retrieves a batch of children from DynamoDB using their unique IDs.
     * <AUTHOR>
     * @since 31/07/2024
     * @param {string[]} childIds - An array of unique IDs of the children to retrieve.
     * @param {string[]} [attributes] - An optional array of attribute names to retrieve.
     * @returns {Promise<Child[]>} - A promise that resolves to an array of Child objects.
    */
    static async batchGetChildren (childIds, attributes) {
        if (childIds.length === 0) {
            return [];
        }
        return await Child.batchGet(childIds, { attributes });
    }

    /**
     * This function updates the chat guidelines read status for a user.
     * <AUTHOR>
     * @since 12/09/2024
     * @param {Object} user - The user object.
     * @returns {Promise<Object>} - A promise that resolves to the updated user object.
    */
    static async updateChatGuidelinesRead (user) {
        return await User.update({ id: user.id, email: user.email }, { hasReadChatGuidelines: true });
    }
}

module.exports = UserProfileService;
