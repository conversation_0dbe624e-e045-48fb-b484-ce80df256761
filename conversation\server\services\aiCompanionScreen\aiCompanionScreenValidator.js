const GeneralError = require('../../util/GeneralError');
const validation = require('../../util/validation');
const CONSTANTS = require('../../util/constants');

/**
 * Class represents validations for AI Companion Screen.
 */
class AiCompanionScreenValidator extends validation {
    constructor ({ body, query, locale }) {
        super(locale);
        this.body = body;
        this.locale = locale;
        this.query = query;
    }

    /**
     * @desc This function is being used to validate add/update AI Companion Screen Feedback
     * <AUTHOR>
     * @since 12/06/2025
     */
    validateAddUpdateAiCompanionScreenFeedback () {
        const {
            aiResponseMessageId,
            userQueryMessageId,
            userQueryMessage,
            aiResponseMessage,
            aiResponseMessageContexts,
            feedback
        } = this.body;
        super.field(aiResponseMessageId, 'AI Response Message Id');
        super.uuid(aiResponseMessageId, 'AI Response Message Id');
        super.field(userQueryMessageId, 'User Query Message Id');
        super.uuid(userQueryMessageId, 'User Query Message Id');
        super.field(userQueryMessage, 'User Query Message');
        super.field(aiResponseMessage, 'AI Response Message');
        super.field(aiResponseMessageContexts, 'AI Response Message Contexts');
        super.field(feedback, 'Feedback');
        super.enum(feedback, CONSTANTS.AI_COMPANION_FEEDBACK_VALUES, 'Feedback');
        if (aiResponseMessageId === userQueryMessageId) {
            throw new GeneralError(this.locale(MESSAGES.AI_COMPANION_SCREEN_FEEDBACK_NOT_FOUND), 400);
        }
    }

    /**
     * @desc This function is being used to validate get AI Companion Screen Feedback List
     * <AUTHOR>
     * @since 12/06/2025
     */
    validateGetAiCompanionScreenFeedbackList () {
        const { startAiResponseMessageId } = this.query;
        if (startAiResponseMessageId) {
            super.field(startAiResponseMessageId, 'Start AI Response Message Id');
            super.uuid(startAiResponseMessageId, 'Start AI Response Message Id');
        }
    }
}

module.exports = AiCompanionScreenValidator;
