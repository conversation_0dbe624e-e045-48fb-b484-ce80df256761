const Utils = require('./Utils');

class OrganizationHelperService {
    /**
     * @description Add or updated organization details to cache
     * <AUTHOR>
     * @param {Redis} redis The redis client
     * @param {Object} organizationDetails THe organization details
     * @param {String} versionPrefix The version prefix
     */
    static async addOrUpdateOrganizationDetailsToCache (redis, organizationDetails, versionPrefix) {
        return await Utils.upsertOrganizationDetailsToHashSet({
            redis,
            key: Utils.getOrganizationDetailsKey({ versionPrefix, organizationId: organizationDetails.id }),
            field: 'details',
            organizationDetails
        });
    }
}

module.exports = OrganizationHelperService;
