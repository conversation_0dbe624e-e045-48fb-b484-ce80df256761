const Constants = require('../util/constants');
const {
    S3Client,
    PutObjectCommand,
    GetObjectCommand,
    DeleteObjectCommand,
    CreateMultipartUploadCommand,
    UploadPartCommand,
    CompleteMultipartUploadCommand,
    AbortMultipartUploadCommand
} = require('@aws-sdk/client-s3');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
const sharp = require('sharp');

const s3 = new S3Client({
    region: 'us-east-2'
});

class UploadService {
    /**
     * @desc This function is used to upload file to S3
     * <AUTHOR>
     * @param {Object} file file object
     * @param {String} filename file name in S3
     * @since 26/09/2023
     */
    static async uploadFile (file, filename, compressImage) {
        let buffer = file.buffer;
        if (compressImage) {
            buffer = await this.compressImageQuality(file);
        }
        const data = {
            Key: filename,
            Bucket: Constants.AWS_S3_PUBLIC_BUCKET,
            Body: buffer,
            ContentType: file.mimetype,
            ContentEncoding: 'base64'
        };
        if (process.env.NODE_ENV !== 'testing') {
            const command = new PutObjectCommand(data);
            return s3.send(command);
        } else {
            return Promise.resolve();
        }
    }

    static async compressImageQuality (file) {
        if (file.size > 1048576) {
            return await sharp(file.buffer)
                .resize({
                    width: 2000,
                    height: 2000,
                    fit: sharp.fit.inside,
                    withoutEnlargement: true
                })
                .jpeg({ quality: Constants.COMPRESSION_QUALITY })
                .toBuffer();
        }
        return file.buffer;
    }

    /**
     * @desc This function is used to delete file in S3
     * <AUTHOR>
     * @param {String} filename file name in S3
     * @since 26/09/2023
     */
    static async deleteObject (filename) {
        const data = {
            Key: filename,
            Bucket: Constants.AWS_S3_PUBLIC_BUCKET
        };
        if (process.env.NODE_ENV !== 'testing') {
            const command = new DeleteObjectCommand(data);
            return s3.send(command);
        } else {
            return Promise.resolve();
        }
    }

    /**
     * @desc This function is used to get signed url of the S3 file
     * <AUTHOR>
     * @param {String} filename file name in S3
     * @since 26/09/2023
     */
    static async getSignedUrl (filename) {
        if (process.env.NODE_ENV !== 'testing') {
            const command = new GetObjectCommand({
                Bucket: Constants.AWS_S3_PUBLIC_BUCKET,
                Key: filename
            });
            return getSignedUrl(s3, command, {
                expiresIn: 60 * 60 * 24 * 7
            });
        } else {
            return Promise.resolve();
        }
    }

    /**
     * @desc This function is used to get signed url of the S3 file for attachments
     * <AUTHOR>
     * @param {String} filename file name in S3
     * @since 27/11/2024
     */
    static async getSignedUrlForAttachments (filename) {
        if (process.env.NODE_ENV !== 'testing') {
            const command = new GetObjectCommand({
                Bucket: Constants.AWS_S3_PUBLIC_BUCKET,
                Key: filename,
                ResponseContentDisposition: 'attachment'
            });
            return getSignedUrl(s3, command, {
                expiresIn: 60 * 60 * 24 * 7
            });
        } else {
            return Promise.resolve();
        }
    }

    static async initiateUploadFileInChunks (filename) {
        if (process.env.NODE_ENV !== 'testing') {
            const data = await s3.send(
                new CreateMultipartUploadCommand({
                    Bucket: Constants.AWS_S3_PUBLIC_BUCKET,
                    Key: filename
                })
            );
            return {
                UploadId: data.UploadId
            };
        } else {
            return Promise.resolve({ UploadId: 'test' });
        }
    }

    static async uploadFileInChunks (filename, uploadId, partNumber, buffer) {
        if (process.env.NODE_ENV !== 'testing') {
            const data = await s3.send(
                new UploadPartCommand({
                    Bucket: Constants.AWS_S3_PUBLIC_BUCKET,
                    Key: filename,
                    UploadId: uploadId,
                    PartNumber: partNumber,
                    Body: buffer
                })
            );
            return {
                ETag: data.ETag
            };
        } else {
            return Promise.resolve({ ETag: 'test' });
        }
    }

    static async completeUploadFileInChunks (filename, uploadId, parts) {
        if (process.env.NODE_ENV !== 'testing') {
            const data = await s3.send(
                new CompleteMultipartUploadCommand({
                    Bucket: Constants.AWS_S3_PUBLIC_BUCKET,
                    Key: filename,
                    UploadId: uploadId,
                    MultipartUpload: {
                        Parts: parts
                    }
                })
            );
            return {
                Location: data.Location
            };
        } else {
            return Promise.resolve({ Location: 'test' });
        }
    }

    static async abortUploadFileInChunks (filename, uploadId) {
        if (process.env.NODE_ENV !== 'testing') {
            return await s3.send(
                new AbortMultipartUploadCommand({
                    Bucket: Constants.AWS_S3_PUBLIC_BUCKET,
                    Key: filename,
                    UploadId: uploadId
                })
            );
        } else {
            return Promise.resolve();
        }
    }

    static async getPreSignedUrlForUpload (filename, expiresInMinutes = 5) {
        if (process.env.NODE_ENV !== 'testing') {
            const command = new PutObjectCommand({
                Bucket: Constants.AWS_S3_PUBLIC_BUCKET,
                Key: filename
            });
            return getSignedUrl(s3, command, { expiresIn: 60 * expiresInMinutes });
        } else {
            return Promise.resolve();
        }
    }
}

module.exports = UploadService;
