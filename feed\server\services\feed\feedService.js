const RedisUtil = require('../../util/redisUtil');
const FeedValidator = require('./feedValidator');
const UploadService = require('../../util/uploadService');
const Event = require('../../models/event.model');
const Organization = require('../../models/organization.model');
const Children = require('../../models/child.model');
const EventSignups = require('../../models/eventSignup.model');
const PostViews = require('../../models/postView.model');
const GeneralError = require('../../util/GeneralError');
const AwsOpenSearchService = require('../../util/opensearch');
const ConstantModel = require('../../models/constant.model');
const Fundraiser = require('../../models/fundraiser.model');
const FundraiserSignup = require('../../models/fundraiserSignup.model');
const EventSignup = require('../../models/eventSignup.model');
const Utils = require('../../util/utilFunctions');
const Child = require('../../models/child.model');

/**
 * Class represents services for signin.
 */
class FeedService {
    /**
     * @desc This function is being used to get list of Events
     * <AUTHOR>
     * @since 17/11/2023
    */
    static async getFeedList (req, user) {
        const { childId } = req.query;
        const feeds = [];
        const uniqueEventIds = new Set();

        const addUniqueEvents = (events, childId) => {
            return Promise.all(events.map(async (event) => {
                const eventDetails = await this.getEventDetails(event, childId);
                if (!uniqueEventIds.has(eventDetails.id)) {
                    uniqueEventIds.add(eventDetails.id);
                    feeds.push(eventDetails);
                }
            }));
        };

        if (childId) {
            const child = await this.getChildDetails(childId, user);
            // eslint-disable-next-line max-len
            const events = await Event.scan('organizationId').in(child.associatedOrganizations).where('isDeleted').eq(0).where('status').eq('published').exec();
            await addUniqueEvents(events, child.id);
        } else {
            await user.populate();

            await Promise.all(user.children.flatMap(async (child) => {
                // eslint-disable-next-line max-len
                const events = await Event.scan('organizationId').in(child.associatedOrganizations).where('isDeleted').eq(0).where('status').eq('published').exec();
                await addUniqueEvents(events, child.id);
            }));
        }

        return feeds;
    }

    static async getRegisteredFeeds (req, user) {
        const { childId } = req.query;
        const feeds = [];

        if (childId) {
            if (!user.children.includes(childId)) {
                throw {
                    message: MESSAGES.NO_CHILD_FOUND,
                    statusCode: 400
                };
            }
            const signups = await EventSignups.scan('childId').eq(childId).exec();
            await this.generateMyFeeds(signups, feeds);
        } else {
            const signups = await EventSignups.scan('parentId').eq(user.id).exec();
            await this.generateMyFeeds(signups, feeds);
        }

        return feeds;
    }

    static async generateMyFeeds (signups, feeds) {
        await Promise.all(signups.map(async (signup) => {
            const event = await Event.scan('id').eq(signup.eventId).where('isDeleted').eq(0).where('status').eq('published').exec();
            if (event.count > 0) {
                const eventDetails = await this.getEventDetails(event.toJSON()[0]);
                feeds.push(eventDetails);
            }
        }));
    }

    static async getChildDetails (childId, user) {
        if (!user.children.includes(childId)) {
            throw {
                message: MESSAGES.NO_CHILD_FOUND,
                statusCode: 400
            };
        }
        return Children.get({ id: childId });
    }

    static async getOrganizationName (organizationId) {
        const organization = await Organization.get({ id: organizationId }, {
            attributes: ['name']
        });
        return organization.name;
    }

    static async getEventParticipants (eventId) {
        const signups = await EventSignups.scan('eventId').eq(eventId).exec();
        // eslint-disable-next-line consistent-return
        return await Promise.all(signups.map(async (participant) => {
            const childDetails = await Children.get({ id: participant.childId }, {
                attributes: ['firstName', 'lastName', 'associatedColor']
            });

            return {
                id: participant.id,
                firstName: childDetails.firstName,
                lastName: childDetails.lastName,
                photoUrl: '',
                associatedColor: childDetails.associatedColor
            };
        }));
    }

    static async getEventDetails (event, childId) {
        const organizationName = await this.getOrganizationName(event.organizationId);
        const participants = await this.getEventParticipants(event.id);

        return {
            ...event,
            childId,
            organizationName,
            participants,
            documentURLs: event.details.documentURLs
                ? await Promise.all([...event.details.documentURLs].map(UploadService.getSignedUrl))
                : [],
            photoUrl: event.photoURL ? await UploadService.getSignedUrl(event.photoURL) : ''
        };
    }

    // Redis feeds start

    /**
     * @desc This function is being used to get list of Events
     * <AUTHOR>
     * @since 17/11/2023
    */
    static async getFeedListRedis (req, user) {
        const { childId, nextIndex, pageSize, organizationId } = req.query;

        const versionPrefix = await this.getVersionPrefix();

        if (organizationId) {
            const key = childId
                ? Utils.getChildKey({ versionPrefix, childId })
                : Utils.getUserKey({ versionPrefix, userId: user.id });
            return await this.getOrganizationFeedsRedis({
                user,
                childId,
                nextIndex,
                pageSize,
                organizationId,
                key,
                versionPrefix
            });
        } else {
            return await this.getFeedsFromRedis({ user, childId, nextIndex, pageSize, versionPrefix });
        }

    }

    static async getVersionPrefix () {
        const versionPrefixFromDb = await ConstantModel.get(CONSTANTS.FEED_VERSION_PREFIX);
        return versionPrefixFromDb?.value ?? '';
    }

    /**
     * @desc This function is being used to get list of Events
     * <AUTHOR>
     * @param {Object} req - The request object.
     * @param {Object} user - The user object.
     * @returns {Promise<Object>} The list of events.
    */
    static async getRegisteredFeedsRedis (req, user) {
        const { childId, nextIndex, nextChildId, pageSize, organizationId } = req.query;

        const versionPrefix = await this.getVersionPrefix();

        if (organizationId) {
            const key = childId
                ? Utils.getRegisteredChildKey({ versionPrefix, childId })
                : Utils.getRegisteredUserKey({ versionPrefix, userId: user.id });

            return await this.getOrganizationFeedsRedis({
                user,
                childId,
                nextIndex,
                nextChildId,
                pageSize,
                key,
                organizationId,
                versionPrefix
            });
        } else {
            return await this.getFeedsFromRedis({ user, childId, nextIndex, pageSize, versionPrefix, shouldFetchSignedUpFeeds: true });
        }
    }

    static validateChildId (childId, user) {
        if (!user.children.includes(childId)) {
            throw {
                message: MESSAGES.NO_CHILD_FOUND,
                statusCode: 400
            };
        }
    }

    /**
     * @desc This function is being used to get list of Events
     * <AUTHOR>
     * @param {Object} user - The user object.
     * @param {String} childId - The id of the child.
     * @param {String} nextIndex - The next index.
     * @param {Number} pageSize - The page size.
     * @param {Boolean} shouldFetchSignedUpFeeds - Whether to fetch signed up feeds.
     * @param {String} versionPrefix - The version prefix.
     * @returns {Promise<Object>} The list of events.
    */
    static async getFeedsFromRedis ({ user, childId, nextIndex, pageSize, shouldFetchSignedUpFeeds = false, versionPrefix }) {
        const page = Number(pageSize) || CONSTANTS.DEFAULT_PAGE_SIZE;
        if (user.children.length === 0) {
            throw {
                message: MESSAGES.NO_CHILD_ADDED,
                statusCode: 400
            };
        }

        var nextCursor = null;
        const feeds = [];

        if (childId) {
            this.validateChildId(childId, user);
        }

        const cursor = Number(nextIndex) || '+inf';

        const childKeyPrefix = shouldFetchSignedUpFeeds
            ? Utils.getRegisteredChildKey({ versionPrefix, shouldGenerateKeyPrefix: true })
            : Utils.getChildKey({ versionPrefix, shouldGenerateKeyPrefix: true });
        const userKeyPrefix = shouldFetchSignedUpFeeds
            ? Utils.getRegisteredUserKey({ versionPrefix, shouldGenerateKeyPrefix: true })
            : Utils.getUserKey({ versionPrefix, shouldGenerateKeyPrefix: true });

        const key = childId ? `${childKeyPrefix}:${childId}` : `${userKeyPrefix}:${user.id}`;

        const getFeeds = async (cursor, pageSize) => {
            const data = await RedisUtil.getMembersByScore(key, cursor, pageSize - feeds.length);

            if (!data || data?.length === 0) {
                return;
            }

            feeds.push(...await this.generateFeeds(data, versionPrefix));

            if (data.length < pageSize - feeds.length + 1) {
                return;
            }

            if (feeds.length > page) {
                nextCursor = feeds[page].score;
                feeds.pop();
            } else if (data.length > 0 && feeds.length < page) {
                // We pop the last feed to avoid duplication when fetching again
                // This is necessary because we'll use the last score as the starting point
                // for the next fetch, and that score corresponds to the last feed we just added
                feeds.pop();

                // The data array contains alternating event and score values
                // So we need to get the last score which is at data.length - 1
                const lastScore = data[data.length - 1];
                await getFeeds(lastScore, pageSize);
            } else {
                // Do nothing, we've fetched all the feeds we need
            }
        };

        await getFeeds(cursor, page + 1);

        return {
            feeds,
            nextIndex: nextCursor
        };
    }

    /**
     * @desc This function is being used to get list of Events filtered by organizationId
     * <AUTHOR>
     * @param {Object} user - The user object.
     * @param {String} childId - The id of the child.
     * @param {String} nextIndex - The next index.
     * @param {Number} pageSize - The page size.
     * @param {String} key - The key.
     * @param {String} organizationId - The id of the organization.
     * @param {String} versionPrefix - The version prefix.
     * @returns {Promise<Object>} The list of events.
    */
    static async getOrganizationFeedsRedis ({ user, childId, nextIndex, pageSize, key, organizationId, versionPrefix }) {
        const page = Number(pageSize) || CONSTANTS.DEFAULT_PAGE_SIZE;
        if (user.children.length === 0) {
            throw {
                message: MESSAGES.NO_CHILD_ADDED,
                statusCode: 400
            };
        }

        var nextCursor = null;
        let feeds = [];

        const fetchAllFeeds = async (key, cursor, feeds) => {
            const data = await RedisUtil.getAllMembersWithScores(key, cursor);
            feeds.push(...(await this.generateFeeds(data, versionPrefix)));
        };

        if (childId) {
            this.validateChildId(childId, user);
        }

        const cursor = Number(nextIndex) || '+inf';
        await fetchAllFeeds(key, cursor, feeds);
        feeds = feeds.filter(feed => feed.organizationId === organizationId);

        if (feeds.length > page) {
            nextCursor = feeds[page].score;
            feeds = feeds.slice(0, page);
        }

        return {
            feeds,
            nextIndex: nextCursor
        };
    }

    /**
     * @desc This function is being used to get list of calendar feeds
     * <AUTHOR>
     * @param {Object} req - The request object.
     * @param {Object} user - The user object.
     * @returns {Array} An array containing the feeds.
    */
    static async getCalendarFeedsFromRedis (req, user) {
        const date = req.query.date || new Date();
        const childId = req.query.childId;

        if (childId) {
            this.validateChildId(childId, user);
        }

        if (user.children.length === 0) {
            throw new GeneralError(MESSAGES.NO_CHILD_ADDED, 400);
        }

        const versionPrefix = await this.getVersionPrefix();

        return this.getFeedsForCalendarFromRedis(user, date, childId, versionPrefix);
    }

    /**
     * Retrieves calendar feeds from Redis for a child of a user.
     * @param {Object} user - The user object.
     * @param {Date} date - The date for which the feeds are to be retrieved.
     * @param {String} child - The id of the child.
     * @param {String} versionPrefix - The version prefix.
     * @returns {Array} An array containing the feeds.
     */
    static async getFeedsForCalendarFromRedis (user, date, child, versionPrefix) {
        const feeds = [];
        const academicYear = await this.getAcademicYear(date);
        const startDate = academicYear.academicYearStart.toDate();
        const endDate = academicYear.academicYearEnd.toDate();

        await this.getFeedsForCalendarForChild(child, feeds, startDate, endDate, user.id, versionPrefix);

        feeds.sort((a, b) => a.score - b.score);

        return feeds;
    }

    /**
     * Retrieves calendar feeds from Redis for a child of a user.
     * @param {String} childId - The id of the child.
     * @param {Array} feeds - The array to which the feeds are to be added.
     * @param {Date} startDate - The start date of the academic year.
     * @param {Date} endDate - The end date of the academic year.
     * @param {String} userId - The id of the user.
     * @param {String} versionPrefix - The version prefix.
     * @returns {Array} An array containing the feeds.
    */
    static async getFeedsForCalendarForChild (childId, feeds, startDate, endDate, userId, versionPrefix) {
        const childRegisteredKey = Utils.getRegisteredChildKey({ versionPrefix, childId });
        const childCalendarKey = Utils.getCalendarChildKey({ versionPrefix, childId });

        const userRegisteredKey = Utils.getRegisteredUserKey({ versionPrefix, userId });
        const userCalendarKey = Utils.getCalendarUserKey({ versionPrefix, userId });

        const keyForRegisteredEvents = childId ? childRegisteredKey : userRegisteredKey;
        const keyForCalendarEvents = childId ? childCalendarKey : userCalendarKey;

        const registeredFeeds = await RedisUtil.getElementsOfSortedSetByScore(
            keyForRegisteredEvents,
            startDate.getTime(),
            endDate.getTime()
        );

        const calendarFeeds = await RedisUtil.getElementsOfSortedSetByScore(
            keyForCalendarEvents,
            startDate.getTime(),
            endDate.getTime()
        );

        feeds.push(...(await this.generateCalendarFeeds(registeredFeeds, versionPrefix)));
        feeds.push(...(await this.generateCalendarFeeds(calendarFeeds, versionPrefix)));
    }

    static async getAcademicYear (date) {
        const eventDate = MOMENT(date);

        const academicYearStart = MOMENT(eventDate).subtract(3, 'months').startOf('month');
        const academicYearEnd = MOMENT(eventDate).add(12, 'months').endOf('month');

        return { academicYearStart, academicYearEnd };
    }

    static async processCalendarEvent (event, score, childDetails, organizationDetailsMap, versionPrefix) {
        const calendarEventKey = Utils.getEventDetailsKey({ versionPrefix, eventId: event.eventId });
        const calendarFeed = JSON.parse(
            await RedisUtil.getHashValue(calendarEventKey)
        );
        const { organizationId } = calendarFeed;

        const organizationDetails = await this.getOrganizationDetails(organizationId, organizationDetailsMap, versionPrefix);

        const feed = await this.getFormattedCalendarFeeds(
            calendarFeed,
            event,
            childDetails,
            organizationDetails
        );
        const feeds = [];
        if ((feed?.isSignedUp && feed?.paymentStatus === CONSTANTS.PAYMENT_STATUS.APPROVED) || feed?.eventType === 'calendar') {
            feeds.push(
                {
                    ...feed,
                    score
                }
            );
        }
        return feeds;
    }

    static async generateCalendarFeeds (data, versionPrefix) {
        const feeds = [];

        const childrenDetailsMap = {};
        const organizationDetailsMap = {};

        for (let i = 0; i < data.length; i += 2) {
            const event = JSON.parse(data[i]);

            const { childId } = event;
            let childDetails = childrenDetailsMap[childId];

            if (!childDetails) {
                const childDetailsKey = Utils.getChildDetailsKey({ versionPrefix, childId });
                childDetails = await RedisUtil.getHashValue(childDetailsKey);
                childrenDetailsMap[childId] = childDetails;
            }

            const score = data[i + 1];
            const eventFeeds = await this.processCalendarEvent(event, score, childDetails, organizationDetailsMap, versionPrefix);
            feeds.push(...eventFeeds);
        }
        return feeds;
    }

    static async getFormattedCalendarFeeds (feed, eventDetails, childDetails, organizationDetails) {
        if (!feed || !childDetails || !organizationDetails) {
            return null;
        }

        const { id, firstName, lastName, associatedColor, photoURL } = JSON.parse(childDetails);
        const { name: organizationName, logo: organizationLogo, id: organizationId } = JSON.parse(organizationDetails);

        return {
            id: feed.id,
            startDateTime: MOMENT(feed.startDateTime),
            endDateTime: MOMENT(feed.endDateTime),
            title: feed.title,
            eventType: feed.eventType,
            venue: feed.venue,
            isSignedUp: feed.eventType === 'event' ? (eventDetails.isSignedUp || false) : true,
            paymentStatus: feed.eventType === 'event' ? eventDetails.status : CONSTANTS.PAYMENT_STATUS.APPROVED,
            associatedChild: {
                id,
                firstName,
                lastName,
                associatedColor,
                photoURL: photoURL ? await UploadService.getSignedUrl(photoURL) : null
            },
            organizationId,
            organizationName,
            organizationLogo: organizationLogo ? await UploadService.getSignedUrl(organizationLogo) : null
        };
    }

    static async generateFeeds (data, versionPrefix) {
        const feeds = [];
        const childrenDetailsMap = {};
        const organizationDetailsMap = {};

        for (let i = 0; i < data.length; i += 2) {
            const event = JSON.parse(data[i]);
            const { childId, isPost, eventId, isFundraiser } = event;
            let childDetails = childrenDetailsMap[childId];

            if (!childDetails) {
                const childDetailsKey = Utils.getChildDetailsKey({ versionPrefix, childId });
                childDetails = await RedisUtil.getHashValue(childDetailsKey);
                childrenDetailsMap[childId] = childDetails;
            }

            if (isPost) {
                const postDetailsKey = Utils.getPostDetailsKey({ versionPrefix, postId: eventId });
                const feed = JSON.parse(await RedisUtil.getHashValue(postDetailsKey));
                const { organizationId } = feed;

                const organizationDetails = await this.getOrganizationDetails(organizationId, organizationDetailsMap, versionPrefix);
                const parsedOrganizationDetails = this.getParsedOrganizationDetails({ organizationDetails });
                const parsedChildDetails = this.getParsedChildDetails({ childDetails });

                const formattedPost =
                    await this.formatFeed({
                        feed,
                        childDetails: parsedChildDetails,
                        organizationDetails: parsedOrganizationDetails
                    });
                if (formattedPost) {
                    feeds.push(
                        {
                            ...formattedPost,
                            score: data[i + 1]
                        }
                    );
                }
            } else if (isFundraiser) {
                const fundraiserDetailsKey = Utils.getFundraiserDetailsKey({ versionPrefix, fundraiserId: eventId });
                const feed = JSON.parse(await RedisUtil.getHashValue(fundraiserDetailsKey));
                const { organizationId } = feed;

                const organizationDetails = await this.getOrganizationDetails(organizationId, organizationDetailsMap, versionPrefix);
                const parsedOrganizationDetails = this.getParsedOrganizationDetails({ organizationDetails });

                const parsedChildDetails = this.getParsedChildDetails({ childDetails });

                const formattedFundraiser =
                    await this.formatFeed({
                        feed,
                        childDetails: parsedChildDetails,
                        organizationDetails: parsedOrganizationDetails
                    });
                const formattedFundraiserForChild =
                    await this.formatFeedForChild({
                        feedDetails: event,
                        feed
                    });

                if (formattedFundraiser) {
                    feeds.push(
                        {
                            ...formattedFundraiser,
                            ...formattedFundraiserForChild,
                            score: data[i + 1]
                        }
                    );
                }
            } else {
                const eventDetailsKey = Utils.getEventDetailsKey({ versionPrefix, eventId });
                const feed = JSON.parse(await RedisUtil.getHashValue(eventDetailsKey));
                const { organizationId } = feed;

                const organizationDetails = await this.getOrganizationDetails(organizationId, organizationDetailsMap, versionPrefix);
                const parsedOrganizationDetails = this.getParsedOrganizationDetails({ organizationDetails });

                const parsedChildDetails = this.getParsedChildDetails({ childDetails });

                const formattedEvent =
                    await this.formatFeed({
                        feed,
                        childDetails: parsedChildDetails,
                        organizationDetails: parsedOrganizationDetails
                    });
                const formattedEventForChild =
                    await this.formatFeedForChild({
                        feedDetails: event,
                        feed
                    });
                if (formattedEvent) {
                    feeds.push(
                        {
                            ...formattedEvent,
                            ...formattedEventForChild,
                            score: data[i + 1]
                        }
                    );
                }
            }
        }
        return feeds;
    }

    /**
     * @desc This function is being used to get organization details
     * <AUTHOR>
     * @param {string} organizationId - The id of the organization.
     * @param {Object} organizationDetailsMap - The map of organization details.
     * @param {String} versionPrefix - The version prefix.
     * @returns {Promise<Object>} The organization details.
    */
    static async getOrganizationDetails (organizationId, organizationDetailsMap, versionPrefix) {
        let organizationDetails = organizationDetailsMap[organizationId];

        if (!organizationDetails) {
            const organizationDetailsKey = Utils.getOrganizationDetailsKey({ versionPrefix, organizationId });
            organizationDetails = await RedisUtil.getHashValue(organizationDetailsKey);
            organizationDetailsMap[organizationId] = organizationDetails;
        }

        return organizationDetails;
    }

    /**
     * @desc This function is being used to get parsed organization details
     * @param {string} organizationDetails - The organization details.
     * @returns {Promise<Object>} The parsed organization details.
     * <AUTHOR>
     * @since 26/06/2025
    */
    static getParsedOrganizationDetails ({ organizationDetails }) {
        const parsedOrganizationDetails = JSON.parse(organizationDetails);
        const keysToRemove = [
            'archievedAssociatedOrganizations',
            'associatedOrganizations',
            'parentOrganization',
            'paymentDetails',
            'createdAt',
            'isDeleted',
            'updatedBy',
            'createdBy',
            'parentOrganizationName',
            'updatedAt',
            'isEnabled'
        ];
        for (const key of keysToRemove) {
            delete parsedOrganizationDetails[key];
        }
        return parsedOrganizationDetails;
    }

    /**
     * @desc This function is being used to get parsed child details
     * @param {string} childDetails - The child details.
     * @returns {Object} The parsed child details.
     * <AUTHOR>
    */
    static getParsedChildDetails ({ childDetails }) {
        return childDetails ? JSON.parse(childDetails) : null;
    }

    /**
     * @desc This function is being used to get list of Events
     * <AUTHOR>
     * @since 17/11/2023
    */
    static async getFeedDetails (req, user, locale) {
        const Validator = new FeedValidator(req.query, locale);
        Validator.validateQueryParams();
        const { eventId, score, isPost, isFundraiser, isMyFeed, fundraiserSignupId } = req.query;
        let { isGuestUser, childId } = req.query;
        isGuestUser = isGuestUser?.toString()?.toLowerCase() === 'true';

        if (childId && !user.children.includes(childId)) {
            throw {
                message: MESSAGES.NO_CHILD_FOUND,
                statusCode: 400
            };
        }

        const versionPrefix = await this.getVersionPrefix();

        const feed = await this.getFeedByType({ eventId, isPost, isFundraiser, versionPrefix });

        if (!feed) {
            throw {
                message: MESSAGES.NO_EVENT_FOUND,
                statusCode: 404
            };
        }

        const { organizationId } = feed;
        const organizationDetails = await this.getOrganizationDetailsForFeed({ organizationId, versionPrefix });
        const parsedOrganizationDetails = this.getParsedOrganizationDetails({ organizationDetails });

        const imageURLs = await this.processImageURLs({ feed });

        if (isGuestUser) {
            if ([CONSTANTS.FUNDRAISER_TYPE.BOOSTER, CONSTANTS.FUNDRAISER_TYPE.MEMBERSHIP].includes(feed.fundraiserType)) {
                throw {
                    message: MESSAGES.NO_EVENT_FOUND,
                    statusCode: 404
                };
            }
            return this.assembleFeedResponse({
                feed,
                parsedOrganizationDetails,
                imageURLs
            });
        }

        const events = [];

        const { eventDetails, matchingFeeds } = await this.getElementsFromRedis({
            childId,
            score: score ? Number(score) : null,
            events,
            eventId,
            fundraiserSignupId,
            versionPrefix,
            isFundraiser: isFundraiser === 'true',
            isMyFeed: isMyFeed === 'true',
            userId: user.id
        });

        const { updatedChildId, childDetails, associatedChildFeeds } =
            await this.processChildDetails({ childId, matchingFeeds, feed, versionPrefix });

        const parsedChildDetails = this.getParsedChildDetails({ childDetails });

        childId = updatedChildId || childId;

        if (isPost && childId) {
            await this.handlePostViews({ feed, childId, eventId, user, versionPrefix });
        }

        const { membershipEnabledForUser, membershipDiscount } =
            await this.processMembershipDetails({ feed, childId });

        const enrichedPurchasedProducts = await this.getEnrichedProductsIfNeeded({
            childId,
            feed,
            eventDetails
        });

        return this.assembleFeedResponse({
            childDetails: parsedChildDetails,
            feed,
            childId,
            eventDetails,
            parsedOrganizationDetails,
            enrichedPurchasedProducts,
            imageURLs,
            membershipEnabledForUser,
            membershipDiscount,
            associatedChildFeeds
        });
    }

    /**
     * @desc This function is being used to get feed signup details for guest signup
     * @param {string} referenceId - The id of the reference.
     * @param {boolean} isFundraiser - Whether the event is a fundraiser.
     * @returns {Promise<Object>} The feed signup details.
     * <AUTHOR>
     * @since 08/07/2025
    */
    static async getFeedSignupDetailsForGuestSignup ({ referenceId, isFundraiser }) {
        let feedSignupDetails;
        if (isFundraiser) {
            feedSignupDetails = await FundraiserSignup.get(referenceId);
        } else {
            feedSignupDetails = await EventSignup.get(referenceId);
        }

        if (!feedSignupDetails) {
            throw {
                message: MESSAGES.NO_GUEST_SIGNUP_FOUND,
                statusCode: 404
            };
        }

        const formattedFeedSignupDetails = {
            eventId: feedSignupDetails.eventId,
            childId: feedSignupDetails.childId,
            isSignedUp: true,
            status: feedSignupDetails.paymentDetails.paymentStatus,
            purchasedProducts: feedSignupDetails.purchasedProducts,
            donationAmount: feedSignupDetails.donationAmount,
            quantityCount: feedSignupDetails.quantityCount,
            transactionFee: feedSignupDetails.paymentDetails.transactionFee,
            platformFeeCoveredBy: feedSignupDetails.paymentDetails.platformFeeCoveredBy,
            paymentType: feedSignupDetails.paymentDetails.paymentType,
            membershipDiscount: feedSignupDetails.paymentDetails.membershipDiscount

        };

        return formattedFeedSignupDetails;
    }

    /**
     * @desc This function is being used to get feed details for guest signup
     * @param {string} eventId - The id of the event.
     * @param {boolean} isFundraiser - Whether the event is a fundraiser.
     * @returns {Promise<Object>} The feed details.
     * <AUTHOR>
     * @since 08/07/2025
    */
    static async getFeedDetailsForGuestSignup ({ eventId, isFundraiser }) {
        let feedDetails;
        if (isFundraiser) {
            feedDetails = await Fundraiser.get(eventId);
        } else {
            feedDetails = await Event.get(eventId);
        }

        if (!feedDetails) {
            throw {
                message: MESSAGES.NO_EVENT_FOUND,
                statusCode: 404
            };
        }

        const {
            id, title, details, photoURL, eventType, eventScope,
            volunteerRequired, volunteerSignupUrl, isPaid, isDonatable, fee, membershipBenefitDetails,
            status, quantityType, quantityInstruction, participantsCount, isRecurring, recurringFrequency,
            documentURLs, products, organizationId, description, startDate, endDate,
            fundraiserType, boosterGoal, boosterGoalForChild, boosterMessageForChild, imageURL
        } = feedDetails;

        const formattedFeedDetails = {
            startDateTime: details?.startDateTime,
            endDateTime: details?.endDateTime,
            description: isFundraiser ? description : details?.details,
            photoURL: isFundraiser ? imageURL : photoURL,
            venue: details?.venue,
            organizationId,
            id,
            title,
            eventType,
            eventScope,
            volunteerRequired,
            volunteerSignupUrl,
            isPaid,
            isDonatable,
            fee,
            membershipBenefitDetails,
            status,
            quantityType,
            quantityInstruction,
            participantsCount,
            isRecurring,
            recurringFrequency,
            documentURLs,
            products,
            isFundraiser,
            startDate,
            endDate,
            fundraiserType,
            boosterGoal,
            boosterGoalForChild,
            boosterMessageForChild
        };

        return formattedFeedDetails;
    }

    /**
     * @desc This function is being used to get organization details for guest signup
     * @param {string} organizationId - The id of the organization.
     * @returns {Promise<Object>} The organization details.
     * <AUTHOR>
     * @since 08/07/2025
    */
    static async getOrganizationDetailsForGuestSignup ({ organizationId }) {
        const organizationDetails = await Organization.get(organizationId);
        if (!organizationDetails) {
            throw {
                message: MESSAGES.NO_GUEST_SIGNUP_FOUND,
                statusCode: 404
            };
        }
        return organizationDetails;
    }

    /**
     * @desc This function is being used to get child details for guest signup
     * @param {string} childId - The id of the child.
     * @returns {Promise<Object>} The child details.
     * <AUTHOR>
     * @since 08/07/2025
    */
    static async getChildDetailsForGuestSignup ({ childId }) {
        const childDetails = await Child.get(childId);
        if (!childDetails) {
            throw {
                message: MESSAGES.NO_GUEST_SIGNUP_FOUND,
                statusCode: 404
            };
        }

        return childDetails;
    }

    /**
     * @desc This function is being used to get guest signup feed details
     * @param {Object} req Request
     * @param {String} locale Locale
     * @returns {Promise<Object>} The guest signup feed details.
     * <AUTHOR>
     * @since 08/07/2025
    */
    static async getGuestSignupFeedDetails (req, locale) {
        const validator = new FeedValidator(req.query, locale);
        validator.validateGuestSignupFeedDetails();
        const { referenceId, isFundraiser } = req.query;

        const feedSignupDetails = await this.getFeedSignupDetailsForGuestSignup({ referenceId, isFundraiser });
        const { childId, eventId } = feedSignupDetails;

        const feedDetails = await this.getFeedDetailsForGuestSignup({ eventId, isFundraiser });
        const childDetails = await this.getChildDetailsForGuestSignup({ childId });

        const { organizationId } = feedDetails;
        const parsedOrganizationDetails = await this.getOrganizationDetailsForGuestSignup({ organizationId });

        const imageURLs = await this.processImageURLs({ feed: feedDetails });

        const enrichedPurchasedProducts = await this.getEnrichedProductsIfNeeded({
            childId,
            feed: feedDetails,
            eventDetails: feedSignupDetails
        });

        return this.assembleFeedResponse({
            feed: feedDetails,
            eventDetails: feedSignupDetails,
            childId,
            childDetails,
            parsedOrganizationDetails,
            enrichedPurchasedProducts,
            imageURLs
        });
    }

    /**
     * @desc This function is being used to get feed by type
     * @param {string} eventId - The id of the event.
     * @param {boolean} isPost - Whether the event is a post.
     * @param {boolean} isFundraiser - Whether the event is a fundraiser.
     * @param {string} versionPrefix - The version prefix.
     * @returns {Promise<Object>} The feed.
     * <AUTHOR>
     * @since 28/04/2025
    */
    static async getFeedByType ({ eventId, isPost, isFundraiser, versionPrefix }) {
        let feed;
        if (isPost === 'true') {
            const postDetailsKey = Utils.getPostDetailsKey({ versionPrefix, postId: eventId });
            feed = JSON.parse(await RedisUtil.getHashValue(postDetailsKey));
        } else if (isFundraiser === 'true') {
            const fundraiserDetailsKey = Utils.getFundraiserDetailsKey({ versionPrefix, fundraiserId: eventId });
            feed = JSON.parse(await RedisUtil.getHashValue(fundraiserDetailsKey));
        } else {
            const eventDetailsKey = Utils.getEventDetailsKey({ versionPrefix, eventId });
            feed = JSON.parse(await RedisUtil.getHashValue(eventDetailsKey));
        }
        return feed;
    }

    /**
     * @desc This function is being used to process child details
     * @param {string} childId - The id of the child.
     * @param {Array} matchingFeeds - The matching feeds.
     * @param {Object} feed - The feed.
     * @param {string} versionPrefix - The version prefix.
     * @returns {Promise<Object>} The child details.
     * <AUTHOR>
     * @since 28/04/2025
    */
    static async processChildDetails ({ childId, matchingFeeds, feed, versionPrefix }) {
        const associatedChildFeeds = [];
        let childDetails;
        let updatedChildId = childId;

        if (matchingFeeds.length === 1 && !childId) {
            updatedChildId = matchingFeeds[0].childId;
        }

        if (updatedChildId) {
            const childDetailsKey = Utils.getChildDetailsKey({ versionPrefix, childId: updatedChildId });
            childDetails = await RedisUtil.getHashValue(childDetailsKey);
        } else {
            const childDetailsMap = {};

            for (const matchedFeed of matchingFeeds) {
                const { childId } = matchedFeed;

                if (!childDetailsMap[childId]) {
                    const childDetailsKey = Utils.getChildDetailsKey({ versionPrefix, childId });
                    const childDetails = JSON.parse(await RedisUtil.getHashValue(childDetailsKey));
                    childDetails.photoURL = childDetails.photoURL
                        ? await UploadService.getSignedUrl(childDetails.photoURL)
                        : null;
                    childDetailsMap[childId] = childDetails;
                }
                matchedFeed.childDetails = childDetailsMap[childId];

                const childFeedDetails = await this.formatFeedForChild({
                    feedDetails: matchedFeed,
                    feed
                });

                associatedChildFeeds.push({
                    ...matchedFeed,
                    ...childFeedDetails
                });
            }
        }

        return { updatedChildId, childDetails, associatedChildFeeds };
    }

    /**
     * @desc This function is being used to get organization details for feed
     * @param {string} organizationId - The id of the organization.
     * @param {string} versionPrefix - The version prefix.
     * @returns {Promise<Object>} The organization details.
     * <AUTHOR>
     * @since 28/04/2025
    */
    static async getOrganizationDetailsForFeed ({ organizationId, versionPrefix }) {
        const organizationDetailsKey = Utils.getOrganizationDetailsKey({ versionPrefix, organizationId });
        return await RedisUtil.getHashValue(organizationDetailsKey);
    }

    /**
     * @desc This function is being used to handle post views
     * @param {Object} feed - The feed.
     * @param {string} childId - The id of the child.
     * @param {string} eventId - The id of the event.
     * @param {Object} user - The user.
     * @param {string} versionPrefix - The version prefix.
     * <AUTHOR>
     * @since 28/04/2025
    */
    static async handlePostViews ({ feed, childId, eventId, user, versionPrefix }) {
        const postViewsChild = feed?.postViews?.filter(viewerChildId => (viewerChildId === childId))[0];
        if (!postViewsChild) {
            await PostViews.create({
                childId,
                postId: eventId,
                createdBy: user.id
            });
            await this.generatePostViewsForPosts({ postId: eventId, childId }, feed, versionPrefix);
        }
    }

    /**
     * @desc This function is being used to process image URLs
     * @param {Object} feed - The feed.
     * @returns {Promise<Array>} The image URLs.
     * <AUTHOR>
     * @since 28/04/2025
    */
    static async processImageURLs ({ feed }) {
        let imageURLs;

        if (feed.isFundraiser) {
            feed.products = JSON.parse(feed.products);
            imageURLs = await Promise.all(feed.products.map(async (product) => {
                if (product.images?.length) {
                    return await Promise.all(product.images.map(async (image) => {
                        return await UploadService.getSignedUrl(image);
                    }));
                } else {
                    return [];
                }
            }));
        }

        if (feed.eventType === 'event' && feed.products && feed.products?.length > 0) {
            imageURLs = await Promise.all(feed.products.map(async (product) => {
                if (product.images?.length) {
                    return await Promise.all(product.images.map(async (image) => {
                        return await UploadService.getSignedUrl(image);
                    }));
                } else {
                    return [];
                }
            }));
        }

        return imageURLs;
    }

    /**
     * @desc This function is being used to process membership details
     * @param {Object} feed - The feed.
     * @param {string} childId - The id of the child.
     * @returns {Promise<Object>} The membership details.
     * <AUTHOR>
     * @since 28/04/2025
    */
    static async processMembershipDetails ({ feed, childId }) {
        let activeMembership = {};
        let membershipEnabledForUser = false;
        let membershipDiscount = 0;

        if (feed.membershipBenefitDetails?.benefitDiscount && childId) {
            const eventStartDate = feed.isFundraiser ? MOMENT(feed.startDate).startOf('day') : MOMENT(feed.startDateTime).startOf('day');
            const activeMemberships = await this.getActiveMemberships(feed.organizationId, childId, eventStartDate);
            if (activeMemberships.length) {
                membershipEnabledForUser = true;
                activeMembership = activeMemberships.find(membership => membership.membershipType === 'family') || activeMemberships[0];
                const parsedMembershipBenefit = JSON.parse(feed.membershipBenefitDetails.benefitDiscount);
                membershipDiscount = parsedMembershipBenefit.find(item => `${item.id}` === activeMembership.membershipId)?.value ?? 0;
            }
        }

        return { membershipEnabledForUser, membershipDiscount };
    }

    /**
     * @desc This function is being used to get enriched products if needed
     * @param {string} childId - The id of the child.
     * @param {Object} feed - The feed.
     * @param {Object} eventDetails - The event details.
     * @returns {Promise<Object>} The enriched products.
     * <AUTHOR>
     * @since 28/04/2025
    */
    static async getEnrichedProductsIfNeeded ({ childId, feed, eventDetails }) {
        let enrichedPurchasedProducts = {};

        if (
            childId &&
            (feed.isFundraiser ||
                (feed.eventType === 'event' &&
                    eventDetails.purchasedProducts &&
                    eventDetails.purchasedProducts?.length > 0)
            )
        ) {
            enrichedPurchasedProducts = await this.getEnrichedPurchasedProducts(
                feed,
                eventDetails
            );
        }

        return enrichedPurchasedProducts;
    }

    /**
     * @desc This function is being used to assemble the feed response
     * @param {Object} feed - The feed.
     * @param {string} childId - The id of the child.
     * @param {Object} childDetails - The child details.
     * @param {Object} eventDetails - The event details.
     * @param {Object} parsedOrganizationDetails - The parsed organization details.
     * @param {Object} enrichedPurchasedProducts - The enriched purchased products.
     * @param {Array} imageURLs - The image URLs.
     * @param {boolean} membershipEnabledForUser - Whether the membership is enabled for the user.
     * @param {number} membershipDiscount - The membership discount.
     * @param {Array} associatedChildFeeds - The associated child feeds.
     * @returns {Promise<Object>} The feed response.
     * <AUTHOR>
     * @since 28/04/2025
    */
    static async assembleFeedResponse ({
        feed,
        childId,
        childDetails,
        eventDetails,
        parsedOrganizationDetails,
        enrichedPurchasedProducts,
        imageURLs,
        membershipEnabledForUser,
        membershipDiscount,
        associatedChildFeeds
    }) {
        return {
            ...await this.formatFeed({ feed, childDetails, organizationDetails: parsedOrganizationDetails }),
            ...(
                childId
                    ? await this.formatFeedForChild({
                        feedDetails: eventDetails,
                        feed,
                        enrichedPurchasedProducts
                    })
                    : null
            ),
            organization: {
                ...parsedOrganizationDetails,
                minPlatformFeeAmount: parsedOrganizationDetails.minPlatformFeeAmount ?? CONSTANTS.DEFAULT_MIN_PLATFORM_FEE_AMOUNT
            },
            imageURLs,
            membershipEnabled: membershipEnabledForUser,
            membershipDiscount,
            associatedChildFeeds
        };
    }

    static async getEnrichedPurchasedProducts (feed, eventDetails) {
        const parsedEventProducts = feed.products;
        const eventProductLookup = parsedEventProducts.reduce((acc, ep) => {
            ep.optionPrices?.forEach(op => {
                acc[op.id] = { ...ep, optionPrice: op };
            });
            return acc;
        }, {});

        let parsedPurchasedProducts = [];
        if (feed.eventType === 'event') {
            parsedPurchasedProducts = eventDetails.isSignedUp ? eventDetails.purchasedProducts : [];
        } else {
            try {
                parsedPurchasedProducts = eventDetails.isSignedUp ? JSON.parse(eventDetails.purchasedProducts) : [];
            } catch (error) {
                CONSOLE_LOGGER.error('Failed to parse purchasedProducts: ', error, eventDetails.purchasedProducts);
            }
        }

        const enrichedPurchasedProducts = parsedPurchasedProducts.map((purchasedProduct) => {
            const eventProductEntry = eventProductLookup[purchasedProduct.id];

            if (!eventProductEntry) {
                return purchasedProduct;
            }

            const { optionPrice, options } = eventProductEntry;
            const optionDetails = [];

            Object.keys(optionPrice).forEach((key) => {
                if (!['id', 'itemName', 'itemCost'].includes(key)) {
                    const option = options.find((o) => o.name.toLowerCase() === key.toLowerCase());
                    if (option) {
                        optionDetails.push({
                            name: key.charAt(0).toUpperCase() + key.slice(1),
                            selectedVariant: optionPrice[key]
                        });
                    }
                }
            });

            return {
                ...purchasedProduct,
                optionDetails
            };
        });

        return enrichedPurchasedProducts;
    }

    static async generatePostViewsForPosts (postViews, feed, versionPrefix) {
        if (!feed.postViews) {
            feed.postViews = [];
        }
        feed.postViews.push(postViews.childId);

        const postDetailsKey = Utils.getPostDetailsKey({ versionPrefix, postId: postViews.postId });
        await RedisUtil.setHashValue(postDetailsKey, 'details', feed);
    }

    /**
     * @desc This function is being used to check if the fundraiser event is matching
     * <AUTHOR>
     * @since 29/04/2025
     * @param {Object} parsedEvent - The parsed event.
     * @param {string} eventId - The id of the event.
     * @param {boolean} isMyFeed - Whether the feed is my feed.
     * @param {string} fundraiserSignupId - The id of the fundraiser signup.
     * @returns {Object} The is matching fundraiser event.
     */
    static isMatchingFundraiserEvent ({ parsedEvent, eventId, isMyFeed, fundraiserSignupId }) {
        const isFundraiserMatch = parsedEvent.fundraiserId === eventId;
        const isMyFeedMatch = isMyFeed
            && isFundraiserMatch
            && parsedEvent.fundraiserSignupId === fundraiserSignupId;

        return {
            isMatch: isMyFeedMatch || isFundraiserMatch,
            isMyFeedMatch,
            isFundraiserMatch
        };
    }

    /**
     * @desc This function is being used to check if the regular event is matching
     * <AUTHOR>
     * @since 29/04/2025
     * @param {Object} parsedEvent - The parsed event.
     * @param {string} eventId - The id of the event.
     * @returns {boolean} The is matching regular event.
     */
    static isMatchingRegularEvent ({ parsedEvent, eventId }) {
        return parsedEvent.eventId === eventId;
    }

    /**
     * @desc This function is being used to handle the fundraiser event
     * <AUTHOR>
     * @since 29/04/2025
     * @param {Object} parsedEvent - The parsed event.
     * @param {string} eventId - The id of the event.
     * @param {boolean} isMyFeed - Whether the feed is my feed.
     * @param {string} fundraiserSignupId - The id of the fundraiser signup.
     * @param {Array} feeds - The feeds.
     * @param {Object} feedDetails - The feed details.
     * @returns {Object} The feeds and feed details.
     */
    static handleFundraiserEvent ({ parsedEvent, eventId, isMyFeed, fundraiserSignupId, feeds, feedDetails }) {
        const { isMatch, isMyFeedMatch, isFundraiserMatch } =
            this.isMatchingFundraiserEvent({ parsedEvent, eventId, isMyFeed, fundraiserSignupId });

        if (isMatch) {
            feeds.push(parsedEvent);
            if (!feedDetails && (isMyFeedMatch || (!isMyFeed && isFundraiserMatch))) {
                feedDetails = parsedEvent;
            }
        }
        return { feeds, feedDetails };
    }

    /**
     * @desc This function is being used to handle the regular event
     * <AUTHOR>
     * @since 29/04/2025
     * @param {Object} parsedEvent - The parsed event.
     * @param {string} eventId - The id of the event.
     * @param {Array} feeds - The feeds.
     * @param {Object} feedDetails - The feed details.
     * @returns {Object} The feeds and feed details.
     */
    static handleRegularEvent ({ parsedEvent, eventId, feeds, feedDetails }) {
        if (this.isMatchingRegularEvent({ parsedEvent, eventId })) {
            feeds.push(parsedEvent);
            if (!feedDetails) {
                feedDetails = parsedEvent;
            }
        }
        return { feeds, feedDetails };
    }

    /**
     * @desc This function is being used to find the event details
     * <AUTHOR>
     * @since 29/04/2025
     * @param {Array} events - The events.
     * @param {string} eventId - The id of the event.
     * @param {boolean} isFundraiser - Whether the event is a fundraiser.
     * @param {boolean} isMyFeed - Whether the feed is my feed.
     * @param {string} fundraiserSignupId - The id of the fundraiser signup.
     * @returns {Object} The feeds and feed details.
     */
    static async findEventDetails ({ events, eventId, isFundraiser, isMyFeed, fundraiserSignupId }) {
        let feeds = [];
        let feedDetails = null;

        for (const event of events) {
            const parsedEvent = JSON.parse(event);

            if (isFundraiser) {
                ({ feeds, feedDetails } = this.handleFundraiserEvent({
                    parsedEvent,
                    eventId,
                    isMyFeed,
                    fundraiserSignupId,
                    feeds,
                    feedDetails
                }));
            } else {
                ({ feeds, feedDetails } = this.handleRegularEvent({
                    parsedEvent,
                    eventId,
                    feeds,
                    feedDetails
                }));
            }
        }

        return { feedDetails, feeds };
    }

    /**
     * @desc This function is being used to get the redis keys
     * @param {string} childId - The id of the child.
     * @param {string} userId - The id of the user.
     * @param {string} versionPrefix - The version prefix.
     * @returns {Object} The redis keys.
     * <AUTHOR>
     * @since 28/04/2025
    */
    static getRedisKeys ({ childId, userId, versionPrefix }) {
        const redisKey = childId
            ? Utils.getChildKey({ versionPrefix, childId })
            : Utils.getUserKey({ versionPrefix, userId });

        const registeredRedisKey = childId
            ? Utils.getRegisteredChildKey({ versionPrefix, childId })
            : Utils.getRegisteredUserKey({ versionPrefix, userId });

        return {
            redisKey,
            registeredRedisKey
        };
    }

    static async getElementsFromRedis ({
        childId,
        userId,
        score,
        events,
        eventId,
        isFundraiser,
        fundraiserSignupId,
        versionPrefix,
        isMyFeed = false,
        count = 0
    }) {
        const { redisKey, registeredRedisKey } = this.getRedisKeys({ childId, userId, versionPrefix });

        const minScore = score ?? '-inf';
        const maxScore = score ?? '+inf';

        if (childId) {
            if (!isMyFeed) {
                events.push(...await RedisUtil.getElementsByScore(redisKey, minScore, maxScore));
            } else {
                events.push(...await RedisUtil.getElementsByScore(registeredRedisKey, minScore, maxScore));
            }
        } else {
            events.push(...await RedisUtil.getElementsByScore(redisKey, minScore, maxScore));
            events.push(...await RedisUtil.getElementsByScore(registeredRedisKey, minScore, maxScore));
        }

        let eventDetails = null;
        let matchingFeeds = [];

        const { feedDetails, feeds } = await this.findEventDetails({
            events,
            eventId,
            isFundraiser,
            isMyFeed,
            fundraiserSignupId
        });

        eventDetails = feedDetails;
        matchingFeeds = feeds;

        if (count === 10 && !eventDetails) {
            events.push(...await RedisUtil.getElementsByScore(redisKey, '-inf', '+inf'));
            events.push(
                ...await RedisUtil.getElementsByScore(
                    registeredRedisKey,
                    '-inf',
                    '+inf'
                )
            );
            const { feedDetails, feeds } = await this.findEventDetails({ events, eventId, isFundraiser, isMyFeed, fundraiserSignupId });
            eventDetails = feedDetails;
            matchingFeeds = feeds;

            if (!eventDetails) {
                throw {
                    message: MESSAGES.NO_EVENT_FOUND,
                    statusCode: 404
                };
            }
            return { eventDetails, matchingFeeds };
        }

        if (!eventDetails && count < 10) {
            return await this.getElementsFromRedis({
                childId,
                userId,
                score: score + 1,
                events: [],
                count: count + 1,
                eventId,
                isFundraiser,
                isMyFeed,
                fundraiserSignupId,
                versionPrefix
            });
        }

        return { eventDetails, matchingFeeds };
    }

    /**
     * @desc This function is being used to format the feed
     * <AUTHOR>
     * @param {Object} feed - The feed object.
     * @param {String} childDetails - The child details object.
     * @param {String} organizationDetails - The organization details object.
     * @returns {Object} The formatted feed object.
    */
    static async formatFeed ({ feed, childDetails, organizationDetails }) {
        if (!feed || !organizationDetails) {
            return null;
        }

        const { id, firstName, lastName, associatedColor, photoURL } = childDetails ?? {};
        const { name: organizationName, logo: organizationLogo, id: organizationId } = organizationDetails;

        const feedObject = {
            id: feed.id,
            associatedChild:
                childDetails
                    ? {
                        id,
                        firstName,
                        lastName,
                        associatedColor,
                        photoURL: photoURL ? await UploadService.getSignedUrl(photoURL) : null
                    }
                    : null,
            organizationId,
            organizationName,
            organizationLogo: organizationLogo ? await UploadService.getSignedUrl(organizationLogo) : null,
            title: feed.title
        };

        if (feed.isPost) {
            return await this.formatPostFeedDetails(feedObject, feed);
        } else if (feed.isFundraiser) {
            return await this.formatFundraiserFeedDetails(feedObject, feed);
        } else {
            return await this.formatEventFeedDetails(feedObject, feed);
        }
    }

    /**
     * @desc This function formats post feed details
     * <AUTHOR>
     * @param {Object} feedObject - Base feed object with common properties
     * @param {Object} feed - The original feed data
     * @returns {Object} Formatted post feed
     */
    static async formatPostFeedDetails (feedObject, feed) {
        return {
            ...feedObject,
            postViews: feed.postViews || [],
            subTitle: feed.subTitle,
            content: feed.content,
            publishedDate: MOMENT(feed.publishedDate),
            status: feed.status,
            isPost: feed.isPost
        };
    }

    /**
     * @desc This function formats fundraiser feed details
     * <AUTHOR>
     * @param {Object} feedObject - Base feed object with common properties
     * @param {Object} feed - The original feed data
     * @returns {Object} Formatted fundraiser feed
     */
    static async formatFundraiserFeedDetails (feedObject, feed) {
        return {
            ...feedObject,
            isFundraiser: feed.isFundraiser,
            photoURL: feed.photoURL ? await UploadService.getSignedUrl(feed.photoURL) : '',
            title: feed.title,
            description: feed.description,
            startDate: MOMENT(feed.startDate),
            endDate: MOMENT(feed.endDate),
            fundraiserType: feed.fundraiserType,
            products: feed.products,
            membershipBenefitDetails: feed.membershipBenefitDetails,
            boosterGoal: feed.boosterGoal,
            fundraiserLink: `${process.env.FUNDRAISER_BASE_URL}?id=${feed.id}`,
            defaultBoosterMessageForChild: feed.boosterMessageForChild,
            defaultBoosterGoalForChild: feed.boosterGoalForChild
        };
    }

    /**
     * @desc This function formats event feed details
     * <AUTHOR>
     * @param {Object} feedObject - Base feed object with common properties
     * @param {Object} feed - The original feed data
     * @returns {Object} Formatted event feed
     */
    static async formatEventFeedDetails (feedObject, feed) {
        return {
            ...feedObject,
            isPaid: feed.isPaid,
            isDonatable: feed.isDonatable,
            photoURL: feed.photoURL ? await UploadService.getSignedUrl(feed.photoURL) : '',
            documentURLs: feed.documentURLs
                ? await Promise.all([...feed.documentURLs].map(UploadService.getSignedUrl))
                : [],
            fee: feed.fee,
            membershipBenefitDetails: feed.membershipBenefitDetails,
            startDateTime: MOMENT(feed.startDateTime),
            endDateTime: MOMENT(feed.endDateTime),
            participants: feed.participants,
            description: feed.description,
            volunteerRequired: feed.volunteerRequired,
            volunteerSignupUrl: feed.volunteerSignupUrl,
            eventType: feed.eventType,
            eventScope: feed.eventScope,
            commentsCount: feed.commentsCount,
            participantsCount: feed.participantsCount,
            quantityType: feed.quantityType,
            quantityInstruction: feed.quantityInstruction,
            venue: feed.venue,
            products: feed.products
        };
    }

    /**
     * @desc This function is being used to format the feed for the child
     * <AUTHOR>
     * @param {Object} feedDetails - The feed details object.
     * @param {Object} feed - The feed object.
     * @param {Object} enrichedPurchasedProducts - The enriched purchased products object.
     * @returns {Object} The formatted feed object.
     */
    static async formatFeedForChild ({ feedDetails, feed, enrichedPurchasedProducts }) {
        if (!feedDetails || !feed) {
            return null;
        }

        if (feed.isFundraiser) {
            return this.formatFundraiserChildDetails(feedDetails, enrichedPurchasedProducts);
        } else {
            return this.formatEventChildDetails(feedDetails, feed, enrichedPurchasedProducts);
        }
    }

    /**
     * @desc Format fundraiser details specifically for a child
     * <AUTHOR>
     * @param {Object} feedDetails - Details of the feed
     * @param {Object} enrichedPurchasedProducts - Enhanced product data
     * @returns {Object} Formatted fundraiser child details
     */
    static formatFundraiserChildDetails (feedDetails, enrichedPurchasedProducts) {
        return {
            purchasedProducts: feedDetails.isSignedUp ? JSON.parse(feedDetails.purchasedProducts) : [],
            isSignedUp: feedDetails.isSignedUp || false,
            paymentStatus: feedDetails.status,
            paymentType: feedDetails.paymentType,
            platformFee: feedDetails.isSignedUp ? feedDetails.transactionFee : undefined,
            platformFeeCoveredBy: feedDetails.isSignedUp ? feedDetails.platformFeeCoveredBy : undefined,
            fundraiserSignupId: feedDetails.fundraiserSignupId,
            membershipDiscountForSignup: feedDetails.membershipDiscount,
            boosterGoalForChild: feedDetails.boosterGoalForChild,
            boosterMessageForChild: feedDetails.boosterMessageForChild,
            totalAmountRaised: feedDetails.amountRaised,
            purchasedProductsWithDetails: enrichedPurchasedProducts
        };
    }

    /**
     * @desc Format event details specifically for a child
     * <AUTHOR>
     * @param {Object} feedDetails - Details of the feed
     * @param {Object} feed - The original feed data
     * @param {Object} enrichedPurchasedProducts - Enhanced product data
     * @returns {Object} Formatted event child details
     */
    static formatEventChildDetails (feedDetails, feed, enrichedPurchasedProducts) {
        return {
            isSignedUp: feedDetails.isSignedUp || false,
            donationAmount: feedDetails.donationAmount || 0,
            paymentStatus: feedDetails.status,
            paymentType: feedDetails.paymentType,
            quantityCount: feedDetails.quantityCount,
            platformFee: feedDetails.isSignedUp ? feedDetails.transactionFee : undefined,
            totalAmount: feedDetails.isSignedUp ? feedDetails.transactionFee + (feed.fee * feedDetails.quantityCount) : undefined,
            platformFeeCoveredBy: feedDetails.isSignedUp ? feedDetails.platformFeeCoveredBy : undefined,
            membershipDiscountForSignup: feedDetails.membershipDiscount,
            purchasedProductsWithDetails: enrichedPurchasedProducts,
            purchasedProducts:
                feedDetails.isSignedUp &&
                    feedDetails.purchasedProducts?.length > 0
                    ? feedDetails.purchasedProducts
                    : []
        };
    }

    static async getActiveMemberships (organizationId, childId, eventStartDate) {
        const child = await Children.get(childId, { attributes: ['membershipsPurchased'] });

        if (!child) {
            return [];
        }

        return child.membershipsPurchased?.filter(membership =>
            membership.organizationId === organizationId &&
                MOMENT(membership.endDate).isAfter(MOMENT().utc()) && MOMENT(membership.startDate).isBefore(eventStartDate)
        ) ?? [];
    }

    static async searchFeed (req, user) {
        const { searchValue, childId } = req.query;
        if (!searchValue) {
            throw {
                message: MESSAGES.SEARCH_VALUE_REQUIRED,
                statusCode: 400
            };
        }
        let childListArray = [];
        if (childId) {
            this.validateChildId(childId, user);
            childListArray.push(childId);
        } else {
            childListArray = user.children;
        }
        return await AwsOpenSearchService.getAllEventsWithChild(childListArray, searchValue);
    }
}

module.exports = FeedService;
