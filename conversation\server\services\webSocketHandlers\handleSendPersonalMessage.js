const PersonalMessage = require('../../models/personalMessage.model');
const SendMessageService = require('../sendSocketMessageService');
const dynamoose = require('dynamoose');
const PersonalConversation = require('../../models/personalConversation.model');
const Users = require('../../models/user.model');
const { v4: uuidv4 } = require('uuid');
const SQSPushNotificationService = require('../sqsPushNotificationService');
const EnrichmentService = require('../enrichmentService');
const { handleMutePersonalConversation } = require('./handlers/muteConversationHandler');
const { handleBlockPersonalConversation } = require('./handlers/blockPersonalConversationHandler');
const { handleAddOrUpdateReaction, handleRemoveReaction } = require('./handlers/messageReactionsHandler');
const CONSTANTS = require('../../util/constants');
const { handleDeletePersonalConversation } = require('./handlers/deletePersonalConversationHandler');

module.exports = async (event) => {
    const eventBody = event.body;
    switch (eventBody.actionType) {
        case CONSTANTS.ACTION_TYPES.SEND_PERSONAL_MESSAGE.NEW_PERSONAL_MESSAGE:
            return await sendPersonalMessage(eventBody);
        case CONSTANTS.ACTION_TYPES.SEND_PERSONAL_MESSAGE.START_PERSONAL_CONVERSATION:
            return await startPersonalConversation(eventBody);
        case CONSTANTS.ACTION_TYPES.SEND_PERSONAL_MESSAGE.READ_PERSONAL_MESSAGE:
            return await readPersonalMessage(eventBody);
        case CONSTANTS.ACTION_TYPES.SEND_PERSONAL_MESSAGE.EDIT_PERSONAL_MESSAGE:
            return await editPersonalMessage(eventBody);
        case CONSTANTS.ACTION_TYPES.SEND_PERSONAL_MESSAGE.DELETE_PERSONAL_MESSAGE:
            return await deletePersonalMessage(eventBody);
        case CONSTANTS.ACTION_TYPES.SEND_PERSONAL_MESSAGE.MUTE_PERSONAL_CONVERSATION:
            return await handleMutePersonalConversation(eventBody);
        case CONSTANTS.ACTION_TYPES.SEND_PERSONAL_MESSAGE.BLOCK_PERSONAL_CONVERSATION:
            return await handleBlockPersonalConversation(eventBody);
        case CONSTANTS.ACTION_TYPES.SEND_PERSONAL_MESSAGE.ADD_PERSONAL_MESSAGE_REACTION:
            return await handleAddOrUpdateReaction({ eventBody, isGroupMessage: false, isAddReaction: true });
        case CONSTANTS.ACTION_TYPES.SEND_PERSONAL_MESSAGE.UPDATE_PERSONAL_MESSAGE_REACTION:
            return await handleAddOrUpdateReaction({ eventBody, isGroupMessage: false });
        case CONSTANTS.ACTION_TYPES.SEND_PERSONAL_MESSAGE.REMOVE_PERSONAL_MESSAGE_REACTION:
            return await handleRemoveReaction({ eventBody, isGroupMessage: false });
        case CONSTANTS.ACTION_TYPES.SEND_PERSONAL_MESSAGE.DELETE_PERSONAL_CONVERSATION:
            return await handleDeletePersonalConversation(eventBody);
        default:
            return {
                statusCode: 400,
                body: 'Invalid event'
            };
    }
};

const sendPersonalMessage = async (eventBody) => {
    const { messageId, senderId, message,
        mediaName, mediaType, mediaDisplayName, mediaThumbnailName, receiverId, isNewConversation } = eventBody;
    let { conversationId, replyMessage } = eventBody;
    let actionType = 'NEW_PERSONAL_MESSAGE';
    let responseMessage = 'Personal message sent successfully!';
    let senderDetails;
    let receiverDetails;
    try {
        if (!conversationId || isNewConversation) {
            actionType = 'START_PERSONAL_CONVERSATION';
            responseMessage = 'Conversation started successfully!';
            conversationId = conversationId ?? uuidv4();
            await dynamoose.transaction([
                PersonalConversation.transaction.create({ conversationId, userAId: senderId, userBId: receiverId }),
                PersonalConversation.transaction.create({ conversationId, userAId: receiverId, userBId: senderId })
            ]);
            const users = (
                await Promise.all([
                    Users.query('id').eq(senderId).attributes(['id', 'firstName', 'lastName']).exec(),
                    Users.query('id').eq(receiverId).attributes(['id', 'firstName', 'lastName']).exec()
                ])
            ).flat();
            senderDetails = users.find((user) => user.id === senderId);
            receiverDetails = users.find((user) => user.id === receiverId);
        }
        if (replyMessage?.messageId && replyMessage?.mediaUrl) {
            const replyToMessage = await PersonalMessage.get(replyMessage.messageId);
            if (replyToMessage) {
                replyMessage = { ...replyToMessage };
            }
        }
        const savedMessage = await PersonalMessage.create({
            messageId, conversationId, senderId, message, mediaName, mediaType, mediaDisplayName, mediaThumbnailName, replyMessage
        });
        await PersonalConversation.update({ userAId: senderId, userBId: receiverId },
            { lastReadMessage: { messageId, createdAt: savedMessage.createdAt } });
        const messageData = await EnrichmentService.enrichMessageWithMediaUrls(savedMessage);
        if (senderDetails && receiverDetails) {
            await Promise.all([
                SendMessageService.sendMessagesToUsers([senderId], {
                    action: 'sendPersonalMessage',
                    actionType: 'START_PERSONAL_CONVERSATION',
                    data: {
                        conversationId,
                        receiver: receiverDetails,
                        message: messageData
                    }
                }),
                SendMessageService.sendMessagesToUsers([receiverId], {
                    action: 'sendPersonalMessage',
                    actionType: 'START_PERSONAL_CONVERSATION',
                    data: {
                        conversationId,
                        receiver: senderDetails,
                        message: messageData
                    }
                })
            ]);
        } else {
            await SendMessageService.sendMessagesToUsers([senderId, receiverId], { data: messageData, actionType });
        }
        await handleSendNotificationToReceiver({ receiverId, senderId, senderDetails, conversationId, message });
        return {
            statusCode: 200,
            message: responseMessage,
            actionType,
            action: 'sendPersonalMessage',
            data: messageData
        };
    } catch (error) {
        CONSOLE_LOGGER.error('--> Error while sending personal message --> ', error);
        responseMessage =
          actionType === 'NEW_PERSONAL_MESSAGE'
              ? 'Failed to send personal message!'
              : 'Failed to start personal conversation!';
        return {
            statusCode: 500,
            message: responseMessage,
            actionType,
            action: 'sendPersonalMessage',
            data: { messageId }
        };
    }
};

const handleSendNotificationToReceiver = async ({ receiverId, senderId, senderDetails, conversationId, message }) => {
    const hasReceiverMutedConversation = await PersonalConversation.get(
        { userAId: receiverId, userBId: senderId },
        { attributes: ['isMuted'] }
    );

    if (!hasReceiverMutedConversation?.isMuted) {
        await SQSPushNotificationService.sendPersonalMessage(receiverId, senderId, senderDetails, conversationId, message);
    }
};

const startPersonalConversation = async (eventBody) => {
    const { senderId, receiverId } = eventBody;
    const conversationId = uuidv4();
    try {
        await dynamoose.transaction([
            PersonalConversation.transaction.create({ conversationId, userAId: senderId, userBId: receiverId }),
            PersonalConversation.transaction.create({ conversationId, userAId: receiverId, userBId: senderId })
        ]);

        const users = (
            await Promise.all([
                Users.query('id').eq(senderId).attributes(['id', 'firstName', 'lastName']).exec(),
                Users.query('id').eq(receiverId).attributes(['id', 'firstName', 'lastName']).exec()
            ])
        ).flat();

        await Promise.all([
            SendMessageService.sendMessagesToUsers([senderId], createUserDetailsResponse(receiverId, users)),
            SendMessageService.sendMessagesToUsers([receiverId], createUserDetailsResponse(senderId, users))
        ]);

        return {
            statusCode: 200,
            message: 'Conversation started successfully!',
            actionType: 'START_PERSONAL_CONVERSATION',
            action: 'sendPersonalMessage',
            data: { conversationId, senderId, receiverId }
        };
    } catch (error) {
        CONSOLE_LOGGER.error('--> Error while starting personal conversation --> ', error);
        return {
            statusCode: 500,
            message: 'Failed to start personal conversation!',
            actionType: 'START_PERSONAL_CONVERSATION',
            action: 'sendPersonalMessage',
            data: { conversationId, senderId, receiverId }
        };
    }

    function createUserDetailsResponse (id, users) {
        const user = users.find((user) => user.id === id);
        return {
            statusCode: 200,
            message: 'Conversation started successfully!',
            actionType: 'START_PERSONAL_CONVERSATION',
            action: 'sendPersonalMessage',
            data: {
                id: user?.id,
                firstName: user?.firstName,
                lastName: user?.lastName
            }
        };
    }
};

const readPersonalMessage = async (eventBody) => {
    const { conversationId, userId, messageId, createdAt } = eventBody;
    try {
        const personalConversation = await PersonalConversation.query('userAId').eq(userId)
            .using('userAId-index')
            .where('conversationId')
            .eq(conversationId)
            .exec();

        if (!personalConversation || personalConversation.length === 0) {
            return {
                statusCode: 404,
                message: 'Personal conversation not found!',
                actionType: 'READ_PERSONAL_MESSAGE',
                action: 'sendPersonalMessage',
                data: { conversationId, userId }
            };
        }
        personalConversation[0].lastReadMessage = { messageId, createdAt };
        await personalConversation[0].save();
        return {
            statusCode: 200,
            message: 'Personal message read successfully!',
            actionType: 'READ_PERSONAL_MESSAGE',
            action: 'sendPersonalMessage',
            data: { conversationId, userId }
        };
    } catch (error) {
        CONSOLE_LOGGER.error('--> Error while reading personal message --> ', error);
        return {
            statusCode: 500,
            message: 'Failed to read personal message!',
            actionType: 'READ_PERSONAL_MESSAGE',
            action: 'sendPersonalMessage',
            data: { conversationId, userId }
        };
    }
};

const editPersonalMessage = async (eventBody) => {
    const { senderId, receiverId, messageId, message } = eventBody;
    try {
        const updatedMessage = await PersonalMessage.update({ messageId }, { message, isEdited: true });
        const messageData = { ...updatedMessage };
        const responseData = { data: messageData, actionType: 'EDIT_PERSONAL_MESSAGE' };
        await SendMessageService.sendMessagesToUsers([senderId, receiverId], responseData);
        return {
            statusCode: 200,
            message: 'Personal message edited successfully!',
            actionType: 'EDIT_PERSONAL_MESSAGE',
            action: 'sendPersonalMessage',
            data: messageData
        };
    } catch (error) {
        CONSOLE_LOGGER.error('--> Error while editing personal message --> ', error);
        return {
            statusCode: 500,
            message: 'Failed to edit personal message!',
            actionType: 'EDIT_PERSONAL_MESSAGE',
            action: 'sendPersonalMessage',
            data: { messageId }
        };
    }
};

const deletePersonalMessage = async (eventBody) => {
    const { senderId, receiverId, messageId } = eventBody;
    try {
        const deletedMessage = await PersonalMessage.update({ messageId }, { isDeleted: true });
        const messageData = { ...deletedMessage };
        const responseData = { data: messageData, actionType: 'DELETE_PERSONAL_MESSAGE' };
        await SendMessageService.sendMessagesToUsers([senderId, receiverId], responseData);
        return {
            statusCode: 200,
            message: 'Personal message deleted successfully!',
            actionType: 'DELETE_PERSONAL_MESSAGE',
            action: 'sendPersonalMessage',
            data: messageData
        };
    } catch (error) {
        CONSOLE_LOGGER.error('--> Error while deleting personal message --> ', error);
        return {
            statusCode: 500,
            message: 'Failed to delete personal message!',
            actionType: 'DELETE_PERSONAL_MESSAGE',
            action: 'sendPersonalMessage',
            data: { messageId }
        };
    }
};
