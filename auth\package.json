{"name": "vaalee-api", "version": "0.1.0", "description": "Vaalee API", "main": "index.js", "scripts": {"prestart": "./prestart.sh", "start": "NODE_ENV=local nodemon .", "start:dev": "NODE_ENV=development node index.js", "test": "NODE_ENV=testing nyc mocha test/alltests.js --exit", "jsdoc": "./node_modules/.bin/jsdoc server/* -r  --destination jsdocs/jsdocs", "commit": "git-cz", "up": "npx migrate up --env=local.env", "down": "npx migrate down --env=local.env"}, "author": "Growexx", "license": "ISC", "dependencies": {"@aws-sdk/client-dynamodb": "^3.427.0", "@aws-sdk/client-s3": "^3.485.0", "@aws-sdk/client-ses": "^3.409.0", "@aws-sdk/s3-request-presigner": "^3.485.0", "amazon-cognito-identity-js": "^5.2.4", "aws-sdk": "^2.1060.0", "body-parser": "^1.17.2", "compression": "^1.7.4", "cookie-parser": "^1.4.3", "cors": "^2.8.3", "dotenv": "^14.2.0", "dynamoose": "^3.2.1", "express": "^4.15.3", "fast-xml-parser": "^4.3.5", "helmet": "^3.21.3", "i18n": "^0.8.3", "jsonwebtoken": "^8.2.1", "jwks-rsa": "^3.1.0", "lodash": "^4.17.21", "method-override": "^2.3.9", "migrate": "^2.0.0", "moment": "^2.19.2", "morgan": "^1.9.1", "nodemon": "^3.1.0", "serverless-http": "^3.2.0", "sharp": "^0.33.5", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1"}, "devDependencies": {"chai": "^4.3.8", "chai-http": "^4.4.0", "jsdoc": "^4.0.2", "mocha": "^10.2.0", "nyc": "^15.1.0", "sinon": "^15.2.0", "supertest": "^6.3.3"}, "nyc": {"lines": 5, "statements": 5, "functions": 5, "branches": 5, "check-coverage": true, "exclude": ["node_modules", "**/test/**", "coverage", "migrations", "jsdocs", ".eslintrc.js", "server/util/http-status.js", "server/util/logger.js", "server/util/sendEmail.js", "server/util/sendSMS.js", "server/util/cognito.js", ".scannerwork", "server/middleware/serverless.js", "server/connection.js", "migrate-dynamodb-config.js", "index.js"], "reporter": ["lcov", "html"], "cache": true, "all": true}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "git+https://github.com/growexx/vaalee-be.git"}, "release": {"repositoryUrl": "https://github.com/growexx/vaalee-be.git", "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git"], "publish": [{"path": "@semantic-release/npm", "npmPublish": false, "tarballDir": "dist"}]}, "homepage": "https://github.com/growexx/vaalee-be.git#readme"}