/* eslint-disable max-len */
// correctness Evaluator : refrence answer & actual answer

export const correctnessInstructions = `
You are an expert evaluator assessing whether the Actual Answer is factually correct when compared to the Ground Truth Answer.

Your goal is to evaluate factual alignment — not an exact wording match. An answer can be correct even if phrased differently, as long as it does not introduce inaccuracies.

Evaluation Guidelines:

1. The Actual Answer must not contradict or misrepresent the Ground Truth.
2. Differences in tone, detail, or phrasing are acceptable, as long as no false or misleading information is introduced.
3. Answers that provide helpful fallback or guidance (e.g., "please contact admin") are acceptable if they do not conflict with the Ground Truth.

Mark as 'True' if:
- The Actual Answer aligns factually with the Ground Truth, even if it uses different words, OR
- If it contains some extra information related to the same context.
- It handles uncertainty responsibly and does not introduce false information.

Mark as 'False' if:
- The answer contains incorrect, misleading, or fabricated facts, OR
- It contradicts the Ground Truth
`;


// relevancy Evaluator : question & actual answer

export const relevancyInstructions = `
You are an expert evaluator assessing whether an answer is relevant to the given Question.

Follow these guidelines:

1. The answer should directly address or acknowledge the intent of the Question.
2. It does not need to be a complete answer — partial, directional, or contextual relevance is acceptable.
3. Responses that guide the user (e.g., suggest contacting support) or explain why an answer can't be provided can still be relevant.

Mark as 'True' if:
- The answer meaningfully responds to the Question, even if it's partial, OR
- It acknowledges the Question and offers a reasonable next step or guidance.

Mark as 'False' if:
- The answer is off-topic, unrelated, or too generic to address the Question's intent.
`;

// groundness Evaluator : retrieved chunks & actual answer

export const groundnessInstructions = `
You are an expert evaluator assessing whether the Actual Answer is grounded in the provided Facts.

"Grounded" means:
- The answer is directly supported by the Facts or can be reasonably inferred from them, OR
- The answer appropriately acknowledges missing information and responds safely (e.g., defers, apologizes, or redirects).

Evaluation Criteria:
1. The answer should accurately reflect or logically build upon the facts.
2. It must NOT include hallucinated, fabricated, or unverifiable information.
3. It's acceptable for the answer to gracefully handle missing context with fallback language (e.g., "I'm not sure", "please contact support").
4. Summarising or paraphrasing is allowed as long as it preserves the original meaning of the facts.

Mark as 'True' if:
- The answer is consistent with the facts provided, OR
- It responsibly acknowledges the lack of information without making things up.

Mark as 'False' if:
- The answer includes unsupported or fabricated claims, OR
- It misrepresents or distorts the provided facts.
`;


// retrieval Evaluator : question & retrieved chunks

export const retrievalInstructions = `
You are evaluating whether the retrieved Facts are relevant to the given Question.

Guidelines for Evaluation:

1. At least one retrieved fact must have a meaningful connection to the Question — this could be shared keywords, related concepts, or topical alignment.
2. It is acceptable if other facts are unrelated, as long as one relevant fact exists.
3. If none of the facts relate to the Question — either semantically or contextually — consider them irrelevant.

Mark as 'True' if:
- At least one fact clearly supports or connects to the Question.
- You are unsure, but there appears to be partial relevance.
- The facts comply with the above rules.

Mark as 'False' if:
- None of the facts are relevant to the Question.
- There is no meaningful or contextual link between any fact and the Question.
`;
