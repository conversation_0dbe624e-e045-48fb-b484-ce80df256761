const AWS = require('aws-sdk');
AWS.config.credentials = new AWS.CognitoIdentityCredentials({ 'IdentityPoolId': process.env.AWS_COGNITO_IDENTITY_ID });
AWS.config.region = process.env.AWS_REGION_COGNITO;
AWS.config.accessKeyId = process.env.ACCESS_KEY_ID;
AWS.config.secretAccessKey = process.env.SECRET_ACCESS_KEY;
AWS.config.credentials.accessKeyId = process.env.ACCESS_KEY_ID;
AWS.config.credentials.secretAccessKey = process.env.SECRET_ACCESS_KEY;
const AmazonCognitoIdentity = require('amazon-cognito-identity-js');
const { CognitoUserPool, CognitoUser, CognitoUserAttribute } = AmazonCognitoIdentity;

let UserPoolId;
let ClientId;
let poolData;
let UserPool;
let cognitoIdentityServiceProvider;

if (process.env.NODE_ENV !== 'testing') {
    UserPoolId = process.env.AWS_COGNITO_USER_ID;
    ClientId = process.env.AWS_CLIENT_ID;
    poolData = {
        UserPoolId,
        ClientId
    };
    UserPool = new CognitoUserPool(poolData);

    cognitoIdentityServiceProvider = new AWS.CognitoIdentityServiceProvider();
}

/**
 * Class represents Utilities function for AWS Cognito in App.
 */
class AwsCognitoService {
    /**
     * @desc This function is used to validate email on cognito
     * <AUTHOR>
     * @param {Object} params params
     * @param {Object} params.UserPoolId params.UserPoolId
     * @param {Object} params.Username params.Username
     * @since 19/09/2023
     */
    static checkEmailExistsOnCognito (params) {
        if (process.env.NODE_ENV !== 'testing') {
            return new Promise((resolve) => {
                cognitoIdentityServiceProvider.adminGetUser(params, (err, data) => {
                    if (err) {
                        resolve(err);
                    } else {
                        resolve(data);
                    }
                });
            });
        } else {
            return {};
        }
    }
    /**
     * @desc This function is used to recover password on cognito
     * <AUTHOR>
     * @param {Object} cognitoUser cognitoUser
     * @since 19/09/2023
     */
    static cognitoForgotPassword (cognitoUser) {
        if (process.env.NODE_ENV !== 'testing') {
            return new Promise((resolve) => {
                cognitoUser.forgotPassword({
                    onSuccess: resolve,
                    onFailure: resolve,
                    inputVerificationCode: resolve
                });
            });
        } else {
            return {};
        }
    }
    /**
     * @desc This function is used to reset user password on cognito
     * <AUTHOR>
     * @param {Object} cognitoUser cognitoUser
     * @param {Object} payload payload
     * @param {Object} payload.verificationCode verificationCode
     * @param {Object} payload.newPassword newPassword
     * @since 19/09/2023
     */
    static cognitoConfirmPassword (cognitoUser, payload) {
        if (process.env.NODE_ENV !== 'testing') {
            return new Promise((resolve) => {
                cognitoUser.confirmPassword(payload.verificationCode, payload.newPassword, {
                    onSuccess: resolve,
                    onFailure: resolve
                });
            });
        } else {
            return {};
        }
    }

    /**
     * @desc This function is used to authenticate user on cognito
     * <AUTHOR>
     * @param {Object} cognitoUser
     * @param {Object} cognitoAuthenticationDetails
     * @since 19/09/2023
     */
    static cognitoAuthenticateUser (cognitoUser, cognitoAuthenticationDetails) {
        if (process.env.NODE_ENV !== 'testing') {
            return new Promise((resolve) => {
                cognitoUser.authenticateUser(cognitoAuthenticationDetails, {
                    onSuccess: resolve,
                    onFailure: resolve,
                    newPasswordRequired: resolve
                });
            });
        } else {
            return {};
        }
    }

    /**
     * @desc This function is used to sign up on cognito
     * <AUTHOR>
     * @param {Object} UserPool awsUserPool
     * @param {Object} dataEmail email
     * @param {Object} dataPassword password
     * @param {Object} attributeList userSignupAttributes
     * @since 19/09/2023
     */
    static cognitoSignupUser (UserPool, dataEmail, dataPassword, attributeList) {
        if (process.env.NODE_ENV !== 'testing') {
            return new Promise((resolve) => {
                UserPool.signUp(dataEmail, dataPassword, attributeList, null, (err, result) => {
                    if (err) {
                        resolve(err);
                        return;
                    }
                    resolve(result);
                });
            });
        } else {
            return {};
        }
    }



    /**
     * @desc This function is used to update email_verified and phone_number_verified field on cognito
     * <AUTHOR>
     * @param {String} email email
     * @param {String} phoneNumber phone_number (optional)
     * @since 19/09/2023
     */
    static async updateUserToActive (username, phoneNumber) {
        const listAttributes = [{ Name: 'email_verified', Value: 'true' }];
        if (phoneNumber) {
            listAttributes.push({ Name: 'phone_number_verified', Value: 'true' });
        }
        const data = {
            UserPoolId,
            Username: username,
            UserAttributes: listAttributes
        };
        process.env.NODE_ENV !== 'testing' && await cognitoIdentityServiceProvider.adminUpdateUserAttributes(data).promise();
    }


    /**
     * @desc This function is used to confirm user on cognito
     * <AUTHOR>
     * @param {String} email email
     * @param {String} phoneNumber phone_number (optional)
     * @since 19/09/2023
     */
    static async updateConfirmStatus (username) {
        const params = {
            UserPoolId,
            Username: username
        };
        process.env.NODE_ENV !== 'testing' && await cognitoIdentityServiceProvider.adminConfirmSignUp(params).promise();
    }

    /**
     * @desc This function is used to fill user attribute
     * <AUTHOR>
     * @param {Object} payload
     * @param {Object} payload.email email
     * @param {Object} payload.password password
     * @param {Object} payload.name name (optional)
     * @param {Object} payload.phone_number phone_number (optional)
     * @param {Object} payload.username username (optional)
     * @since 19/09/2023
     */
    static fillAttributeList (payload) {
        const attributeList = [];
        if (payload.firstName) {
            const dataName = {
                Name: 'given_name',
                Value: payload.firstName
            };
            const attributeName = new CognitoUserAttribute(dataName);
            attributeList.push(attributeName);
        }
        if (payload.lastName) {
            const dataName = {
                Name: 'family_name',
                Value: payload.lastName
            };
            const attributeName = new CognitoUserAttribute(dataName);
            attributeList.push(attributeName);
        }
        if (payload.email) {
            const dataName = {
                Name: 'email',
                Value: payload.email
            };
            const attributeName = new CognitoUserAttribute(dataName);
            attributeList.push(attributeName);
        }
        if (payload.phoneNumber) {
            const dataPhone = {
                Name: 'phone_number',
                Value: payload.countryCode + payload.phoneNumber
            };
            const attributePhone = new CognitoUserAttribute(dataPhone);
            attributeList.push(attributePhone);
        }
        return attributeList;
    }


    /**
     * @desc This function is being used to sign in user
     * <AUTHOR>
     * @since 19/09/2023
     * @param {Object} payload
     * @param {Object} payload.email email
     * @param {Object} payload.password password
     */
    static async login ({ email, password }) {
        if (process.env.NODE_ENV !== 'testing') {
            const payload = {
                UserPoolId,
                AuthFlow: 'ADMIN_USER_PASSWORD_AUTH',
                ClientId,
                AuthParameters: {
                    USERNAME: email,
                    PASSWORD: password
                }
            };
            const cognitoResponse = await cognitoIdentityServiceProvider.adminInitiateAuth(payload).promise();
            if (cognitoResponse.code) {
                return null;
            }
            return cognitoResponse;
        } else {
            return {};
        }

    }

    /**
     * @desc This function is being used to sign up user
     * <AUTHOR>
     * @since 19/09/2023
     * @param {Object} payload
     * @param {Object} payload.email email
     * @param {Object} payload.password password
     * @param {Object} payload.name name (optional)
     * @param {Object} payload.phone_number phone_number (optional)
     * @param {Object} payload.username username (optional)
     */
    static async signup (payload) {
        if (process.env.NODE_ENV !== 'testing') {
            const attributeList = AwsCognitoService.fillAttributeList(payload);
            const cognitoResponse = await AwsCognitoService.cognitoSignupUser(UserPool, payload.email, payload.password, attributeList);
            if (cognitoResponse.code) {
                return null;
            }
            return cognitoResponse;
        } else {
            return {};
        }
    }

    /**
     * @desc This function is being used to sign up user
     * <AUTHOR>
     * @since 19/09/2023
     * @param {Object} payload
     * @param {Object} payload.email email
     */
    static async forgotPassword (payload) {
        if (process.env.NODE_ENV !== 'testing') {
            const cognitoUser = new CognitoUser({
                Username: payload.email,
                Pool: UserPool
            });

            const cognitoResponse = await AwsCognitoService.cognitoForgotPassword(cognitoUser);
            if (cognitoResponse.code) {
                return null;
            }
            return cognitoResponse;
        } else {
            return {};
        }
    }

    /**
     * @desc This function is being used to sign up user
     * <AUTHOR>
     * @since 19/09/2023
     * @param {Object} payload
     * @param {Object} payload.email email
     * @param {Object} payload.verificationCode verificationCode
     * @param {Object} payload.newPassword newPassword
     */
    static async confirmPassword (payload) {
        if (process.env.NODE_ENV !== 'testing') {
            const cognitoUser = new CognitoUser({
                Username: payload.email,
                Pool: UserPool
            });

            const cognitoResponse = await AwsCognitoService.cognitoConfirmPassword(cognitoUser, payload);
            if (cognitoResponse.code) {
                return null;
            }
            return cognitoResponse;
        } else {
            return {};
        }
    }

    /**
     * @desc This function is being used to validate email on cognito
     * <AUTHOR>
     * @since 19/09/2023
     * @param {Object} payload
     * @param {Object} payload.email email
     */
    static async validateEmailExists (userEmail) {
        if (process.env.NODE_ENV !== 'testing') {
            const params = {
                Username: userEmail,
                UserPoolId
            };

            const cognitoResponse = await AwsCognitoService.checkEmailExistsOnCognito(params);
            if (cognitoResponse.code) {
                return null;
            }
            return cognitoResponse;
        } else {
            return {};
        }
    }

    /**
     * @desc This function is used to delete user
     * <AUTHOR>
     * @param {Object} username username of the user
     * @since 09/10/2023
     */
    static async deleteUser (username) {
        if (process.env.NODE_ENV !== 'testing') {
            var params = {
                UserPoolId,
                Username: username
            };

            const cognitoResponse = await cognitoIdentityServiceProvider.adminDeleteUser(params).promise();
            if (cognitoResponse.code) {
                return null;
            }
            return cognitoResponse;
        } else {
            return {};
        }
    }

    /**
     * @desc This function is being used to add email/user on cognito
     * <AUTHOR>
     * @since 19/09/2023
     * @param {Object} payload
     * @param {Object} payload.email email
     */
    static async addCognitoUser (Username) {
        if (process.env.NODE_ENV !== 'testing') {
            var params = {
                UserPoolId,
                Username,
                email: Username,
                TemporaryPassword: 'Test@1234',
                MessageAction: 'SUPPRESS'
            };

            const cognitoResponse = await cognitoIdentityServiceProvider.adminCreateUser(params).promise();
            if (cognitoResponse.code) {
                return null;
            }
            return cognitoResponse;
        } else {
            return {};
        }
    }

    /**
     * @desc This function is being used to set cognito user's Password and Enable the User
     * <AUTHOR>
     * @since 19/09/2023
     * @param dataEmail email
     * @param dataPassword password
     */
    static async setCognitoUserPassword (dataEmail, dataPassword) {
        if (process.env.NODE_ENV !== 'testing') {
            var params = {
                UserPoolId,
                Password: dataPassword,
                Username: dataEmail,
                Permanent: true
            };

            const cognitoResponse = await cognitoIdentityServiceProvider.adminSetUserPassword(params).promise();
            if (cognitoResponse.code) {
                return null;
            }
            return cognitoResponse;
        } else {
            return {};
        }
    }

    static async updateUserFirstNameAndLastName (username, firstName, lastName) {
        if (process.env.NODE_ENV !== 'testing') {
            var params = {
                UserPoolId,
                Username: username,
                UserAttributes: [
                    {
                        Name: 'given_name',
                        Value: firstName
                    },
                    {
                        Name: 'family_name',
                        Value: lastName
                    }
                ]
            };

            const cognitoResponse = await cognitoIdentityServiceProvider.adminUpdateUserAttributes(params).promise();
            if (cognitoResponse.code) {
                return null;
            }
            return cognitoResponse;
        } else {
            return {};
        }
    }

    /**
     * @desc This function is being used to generate Refreshed token
     * <AUTHOR>
     * @since 19/09/2023
     * @param refreshToken refreshToken
     */
    static async generateRefreshToken (refreshToken) {
        if (process.env.NODE_ENV !== 'testing') {
            var params = {
                ClientId,
                AuthFlow: 'REFRESH_TOKEN_AUTH',
                AuthParameters: {
                    'REFRESH_TOKEN': refreshToken
                }
            };
            const cognitoResponse = await cognitoIdentityServiceProvider.initiateAuth(params).promise();
            if (cognitoResponse.code) {
                return null;
            }
            return cognitoResponse;
        } else {
            return {};
        }
    }

    /**
     * @desc This function is being used to remove token /logout from Cognito
     * <AUTHOR>
     * @since 19/09/2023
     * @param refreshToken refreshToken
     */
    static async revokeToken (refreshToken) {
        if (process.env.NODE_ENV !== 'testing') {
            var params = {
                ClientId,
                Token: refreshToken
            };

            const cognitoResponse = await cognitoIdentityServiceProvider.revokeToken(params).promise();
            if (cognitoResponse.code) {
                return null;
            }
            return cognitoResponse;
        } else {
            return {};
        }
    }

    /**
     * @desc This function is being used to change user password
     * <AUTHOR>
     * @since 19/09/2023
     * @param accessToken accessToken
     * @param oldPassword oldPassword
     * @param newPassword newPassword
     */
    static async changeCognitoUserPassword (accessToken, oldPassword, newPassword) {
        if (process.env.NODE_ENV !== 'testing') {
            var params = {
                AccessToken: accessToken,
                PreviousPassword: oldPassword,
                ProposedPassword: newPassword
            };

            const cognitoResponse = await cognitoIdentityServiceProvider.changePassword(params).promise();
            if (cognitoResponse.code) {
                return null;
            }
            return cognitoResponse;
        } else {
            return {};
        }
    }
}
module.exports = AwsCognitoService;
