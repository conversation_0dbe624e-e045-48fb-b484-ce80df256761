const ParentValidator = require('./parentValidator');
const User = require('../../models/user.model');
const Child = require('../../models/child.model');
const Organization = require('../../models/organization.model');
const Event = require('../../models/event.model');
const ChildOrganizationMapping = require('../../models/childOrganizationMapping.model');
const PendingParentInvite = require('../../models/pendingPartnerInvite.model');
const OrganizationValidator = require('../organization/organizationValidator');
const Validation = require('../../util/validation');
const GeneralError = require('../../util/GeneralError');
const EmailService = require('../../util/sendEmail');
const UploadService = require('../../util/uploadService');
const AwsOpenSearchService = require('../../util/opensearch');
const { selectAttributes } = require('../../util/utilFunctions');
const { v4: uuidv4 } = require('uuid');
const dynamoose = require('dynamoose');

/**
 * Class represents services for parent
 */
class ParentService {
    /**
     * @desc This function is being used to get school list
     * <AUTHOR>
     * @since 19/10/2023
     */
    static async getSchoolList (req, locale) {
        let { category } = req.query;
        const { schoolId } = req.query;
        const attributes = ['id', 'name', 'country', 'zipCode', 'city', 'state', 'isDeleted'];
        if (category) {
            const Validator = new OrganizationValidator(req.body, locale, req.query);
            const validation = new Validation(locale);
            Validator.verifyCategoryParams();
            validation.field(schoolId, 'School Id');
            validation.uuid(schoolId, 'School Id');
            const homerooom = await Organization.query('category')
                .eq(category).using('category-index').where('parentOrganization').eq(schoolId)
                .where('isDeleted').eq(0).attributes(attributes).exec();
            if (homerooom.length === 0) {
                return [];
            }
            return homerooom;
        } else {
            category = CONSTANTS.CATEGORIES.SCHOOL;
        }

        const organizations = await Organization.query('category')
            .eq(category).using('category-index').where('isDeleted').eq(0).attributes(attributes).exec();
        if (organizations.length === 0) {
            return [];
        }

        return organizations;
    }

    static getValidMembershipsForChild (user, associatedOrganizations) {
        const currentDate = MOMENT();

        return user.membershipsPurchased?.filter(membership =>
            MOMENT(membership.endDate).isAfter(currentDate) && associatedOrganizations.includes(membership.organizationId)
        );
    }

    /**
     * @desc This function is being used to add child by parent
     * <AUTHOR>
     * @since 19/10/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {String} req.body.firstName firstName
     * @param {String} req.body.lastName lastName
     * @param {String} req.body.dob Date of Birth
     * @param {String} req.body.gender Gender
     * @param {String} req.body.schoolId schoolId
     * @param {String} req.body.zipCode zipCode
     * @param {String} req.body.associatedColor associatedColor
     * @param {Object} user User
     * @param {Object} res Response
     */
    static async addChild (req, user, locale) {
        const Validator = new ParentValidator(req.body, locale, req.file);
        Validator.validate();
        const { firstName, lastName, schoolId, zipCode, associatedColor, homeroomId, childId } = req.body;

        CONSOLE_LOGGER.info(`Add Child API called with data 
            lastName: ${lastName} schoolId: ${schoolId} homeRoomId: ${homeroomId} associatedColor: ${associatedColor} by user: ${user.id}`);
        CONSOLE_LOGGER.info('Getting organization details for adding new child');

        const school = await Organization.get({ id: schoolId });
        const associatedOrganizations = [];
        if (!school || school.category !== CONSTANTS.CATEGORIES.SCHOOL) {
            throw new GeneralError(MESSAGES.SCHOOL_DOES_NOT_EXIST, 400);
        }
        associatedOrganizations.push(schoolId);
        if (homeroomId) {
            const homeroom = await Organization.get({ id: homeroomId });
            if (!homeroom || homeroom.category !== CONSTANTS.CATEGORIES.HOME_ROOM || homeroom.parentOrganization !== schoolId) {
                throw new GeneralError(MESSAGES.HOMEROOM_DOES_NOT_EXIST, 400);
            }
            associatedOrganizations.push(homeroomId);
        }

        const ptoOfSchool = await Organization.query('parentOrganization').eq(schoolId)
            .where('category').eq(CONSTANTS.CATEGORIES.PTO).attributes(['id']).exec();
        const vaaleeOrganization = await Organization.query('category').eq(CONSTANTS.CATEGORIES.SUPER_ORGANIZATION)
            .attributes(['id']).exec();

        associatedOrganizations.push(vaaleeOrganization[0].id);
        associatedOrganizations.push(ptoOfSchool[0].id);

        CONSOLE_LOGGER.info('Organization details fetched successfully');
        CONSOLE_LOGGER.info('Getting valid memberships for child');

        const validMemberships = this.getValidMembershipsForChild(user, associatedOrganizations);

        CONSOLE_LOGGER.info('Valid memberships fetched successfully');

        const id = childId ?? uuidv4();

        CONSOLE_LOGGER.info(`Uploading profile picture for child, profile picture exists: ${req.file ? 'Yes' : 'No'}`);

        const fileName = await this.uploadProfilePicture(req, user, id);

        CONSOLE_LOGGER.info('Profile picture uploaded successfully');

        const createChild = {
            guardians: [user.id],
            school: schoolId,
            createdBy: user.id,
            membershipsPurchased: validMemberships ?? [],
            id,
            photoURL: fileName,
            associatedOrganizations,
            firstName,
            lastName,
            zipCode,
            associatedColor
        };
        if (homeroomId) {
            createChild.homeRoom = homeroomId;
        }
        // await AwsOpenSearchService.create(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, createChild.id, createChild);
        const child = await Child.create(createChild);

        CONSOLE_LOGGER.info('Child created successfully');
        CONSOLE_LOGGER.info('Creating child organization mapping for child');

        for (const organization of associatedOrganizations) {
            await ChildOrganizationMapping.create({ childId: child.id, organizationId: organization });
        }

        CONSOLE_LOGGER.info('Child organization mapping created successfully');
        CONSOLE_LOGGER.info('Updating user with new child');

        await User.update({ id: user.id, email: user.email }, { '$ADD': { 'children': child } });

        CONSOLE_LOGGER.info('User updated successfully');

        this.insertChildInOpensearch();

        await this.updateChildPhotoURL(child, fileName);

        return child;
    }

    /**
     * @desc This function is being used to update child's photo URL
     * @param {Object} child Child
     * @param {String} fileName File name
    */
    static async updateChildPhotoURL (child, fileName) {
        if (fileName) {
            child.photoURL = await UploadService.getSignedUrl(fileName);
        }
    }

    /**
     * @desc This function is being used to upload child's profile picture
     * @param {Object} req Request
     * @param {Object} loggedInUser Logged in user
     * @param {String} childId Child ID
     * @returns {String} File name
    */
    static async uploadProfilePicture (req, loggedInUser, childId) {
        let fileName = req.body.fileName;

        if (req.file) {
            fileName = `${process.env.NODE_ENV}-profile-pictures/${loggedInUser.id}/${childId}`;
            await UploadService.uploadFile(req.file, fileName, true);
        }

        return fileName;
    }

    static async insertChildInOpensearch () {
        /**
         * commenting for now just to debug the issue
         * await AwsOpenSearchService.create(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, child.id, child);
         * await this.setCalendarEventsInOpensearch(child);
        */
    }

    static async setCalendarEventsInOpensearch (child) {
        let calendarEventIds = [];
        for (const organizationId of child.associatedOrganizations) {
            const orgEvents = await Event.query('organizationId').eq(organizationId).where('eventType').eq('calendar').exec();
            const eventIds = orgEvents.map(event => event.id);
            calendarEventIds.push(...eventIds);
        }
        calendarEventIds = [...new Set(calendarEventIds)];
        await AwsOpenSearchService.registerMultipleEvents(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, child.id, Array.from(calendarEventIds));
    }

    /**
     * @desc This function is being used to invite partner
     * <AUTHOR>
     * @since 17/01/2024
     * @param {Object} req - The request object, containing the email and childId in the query.
     * @param {Object} user - The user object, containing information about the user who is inviting the partner.
     * @param {string} locale - The locale for validation messages.
     */
    static async invitePartner (req, user, locale) {
        let { email, childrenIds } = req.body;
        const validation = new Validation(locale);
        validation.email(email, 'Email');
        validation.array(childrenIds, 'Children Ids');
        if (childrenIds.length === 0) {
            throw new GeneralError(MESSAGES.CHILDREN_IDS_REQUIRED, 400);
        }
        email = email.toLowerCase().trim();
        childrenIds = childrenIds.map(childId => {
            validation.uuid(childId, 'Child Id');
            return childId.trim();
        });
        if (!childrenIds.length) {
            throw new GeneralError(MESSAGES.CHILD_ARRAY_EMPTY, 400);
        }
        if (email === user.email) {
            throw new GeneralError(MESSAGES.CANNOT_INVITE_YOURSELF, 400);
        }
        const userAlreadyInvited = user?.sendInvites?.find(invite => invite.invitedPartnerEmail === email);
        if (userAlreadyInvited) {
            throw new GeneralError(MESSAGES.PARTNER_ALREADY_INVITED, 400);
        }

        for (const childId of childrenIds) {
            if (!user.children.includes(childId)) {
                throw new GeneralError(MESSAGES.CHILD_NOT_FOUND, 400);
            }
        }

        const transactionItems = [];

        const existingUser = await User.query('email').eq(email).exec();
        if (!existingUser.length) {
            transactionItems.push(
                PendingParentInvite.transaction.create({
                    invitedPartner: email,
                    inviterPartnerId: user.id,
                    inviterPartnerEmail: user.email,
                    children: childrenIds.map(childId => ({ childId, invitedAt: MOMENT().utc().toDate() }))
                })
            );
        } else {
            existingUser[0].partnerInvites.push({
                children: childrenIds.map(childId => ({ childId, invitedAt: MOMENT().utc().toDate() })),
                inviterPartnerId: user.id,
                inviterPartnerEmail: user.email
            });
            transactionItems.push(User.transaction.update({
                id: existingUser[0].id,
                email: existingUser[0].email
            }, {
                partnerInvites: existingUser[0].partnerInvites
            }));
        }
        for (const childId of childrenIds) {
            const child = await Child.get({ id: childId });
            this.sendInviteToPartner(email, user, child, 'invitePartner.html',
                `${user.firstName} ${user.lastName} Invited You to Connect on ` + CONSTANTS.APP_NAME);
        }

        const loggedInUser = await User.get({ id: user.id, email: user.email });

        if (!loggedInUser.sendInvites) {
            loggedInUser.sendInvites = [];
        }

        const sendInviteObject = {
            invitedPartnerEmail: email,
            children: childrenIds.map(childId => ({ childId, status: CONSTANTS.INVITE_PARTNER_STATUS.PENDING }))
        };
        loggedInUser.sendInvites.push(sendInviteObject);
        transactionItems.push(User.transaction.update({
            id: loggedInUser.id,
            email: loggedInUser.email
        }, {
            sendInvites: loggedInUser.sendInvites
        }));

        await dynamoose.transaction(transactionItems);
    }

    /**
     * @desc This function is being used to send invite to the partner
     * <AUTHOR>
     * @since 28/12/2023
     * @param {Object} reqObj reqObj
     */
    static async sendInviteToPartner (email, user, child, templateFile, subject) {
        const { firstName, lastName } = user;
        const { firstName: childFirstName, lastName: childLastName } = child;
        const template = `emailTemplates/${templateFile}`;
        const currentDate = new Date();
        const year = currentDate.getFullYear();
        const templateVariables = {
            year,
            email: CONSTANTS.CLIENT_INFO.EMAIL,
            appname: CONSTANTS.APP_NAME,
            fromUser: `${firstName} ${lastName}`,
            childName: `${childFirstName} ${childLastName}`,
            androidAppURL: CONSTANTS.APP_URL.ANDROID,
            iOSAppURL: CONSTANTS.APP_URL.IOS,
            WEBPAGEURL: CONSTANTS.APP_URL.WEBPAGE
        };
        await EmailService.prepareAndSendEmail([email], subject, template, templateVariables);
    }

    /**
     * @desc This function is being used to get invite partner list
     * <AUTHOR>
     * @since 18/01/2024
     */
    static async getInvitePartnerList (user) {
        if (!user.partnerInvites || (user.partnerInvites && !user.partnerInvites.length)) {
            return [];
        }

        const partnerChildMap = user.partnerInvites.flatMap(invite =>
            invite.children.map(child => ({
                inviterPartnerId: invite.inviterPartnerId,
                inviterPartnerEmail: invite.inviterPartnerEmail,
                childId: child.childId,
                invitedAt: MOMENT(child.invitedAt)
            }))
        );

        const childIds = [...new Set(partnerChildMap.map(item => item.childId))];
        const partnerIdentifiers = Array.from(
            new Set(
                partnerChildMap.map(item => JSON.stringify({ id: item.inviterPartnerId, email: item.inviterPartnerEmail }))
            )
        ).map(item => JSON.parse(item));

        const children = await Child.batchGet(childIds);
        const partners = await User.batchGet(partnerIdentifiers);

        const childMap = children.reduce((map, child) => ({ ...map, [child.id]: child }), {});
        const partnerMap = partners.reduce((map, partner) => ({ ...map, [partner.id]: partner }), {});

        for (const item of partnerChildMap) {
            const child = childMap[item.childId];
            const partner = partnerMap[item.inviterPartnerId];

            const school = await Organization.get({ id: child.school });

            item.homeRoom = child.homeRoom ? (await Organization.get({ id: child.homeRoom })).name : null;
            item.schoolName = school.name;
            item.childFirstName = child.firstName;
            item.childLastName = child.lastName;
            item.associatedColor = child.associatedColor;
            item.photoURL = child.photoURL ? await UploadService.getSignedUrl(child.photoURL) : null;
            item.inviterPartnerFirstName = partner.firstName;
            item.inviterPartnerLastName = partner.lastName;
        }
        partnerChildMap.sort((a, b) => b.invitedAt - a.invitedAt);
        return partnerChildMap;
    }

    /**
     * @desc This function is being used to update status of invite partner
     * <AUTHOR>
     * @since 18/01/2024
     */
    static async updateInvitePartner (req, user, locale) {
        let { inviterPartnerEmail, childId, status } = req.query;
        const validation = new Validation(locale);
        validation.email(inviterPartnerEmail, 'Inviter Partner Email');
        validation.field(childId, 'Child Id');
        validation.uuid(childId, 'Child Id');
        validation.field(status, 'Status');
        validation.enum(status, [CONSTANTS.INVITE_PARTNER_STATUS.ACCEPTED, CONSTANTS.INVITE_PARTNER_STATUS.REJECTED], 'Status');
        inviterPartnerEmail = inviterPartnerEmail.toLowerCase().trim();
        childId = childId.trim();
        status = status.trim();

        const existingUser = await User.get({ id: user.id, email: user.email });

        let message;
        let child;

        const invite = existingUser.partnerInvites.find(invite =>
            invite.inviterPartnerEmail === inviterPartnerEmail &&
            invite.children.some(child => child.childId === childId)
        );

        if (!invite) {
            message = MESSAGES.INVITE_NOT_FOUND;
            const responseList = await this.getInvitePartnerList(existingUser);
            return { responseList, message };
        }

        const transactionItems = [];
        const invitedPartner = await User.get({ email: invite.inviterPartnerEmail, id: invite.inviterPartnerId });

        if (existingUser.children.includes(childId)) {
            message = MESSAGES.CHILD_ALREADY_EXISTS;
            const responseList = await this.getInvitePartnerList(existingUser);
            return { responseList, message };
        }

        if (status === CONSTANTS.INVITE_PARTNER_STATUS.ACCEPTED) {
            existingUser.children.push(childId);
            message = `You're now linked with ${invitedPartner.firstName} ${invitedPartner.lastName}'s child.`;
            child = await Child.get({ id: childId });
            child.guardians.push(existingUser.id);
            child.photoURL = child.photoURL ? await UploadService.getSignedUrl(child.photoURL) : null;
            transactionItems.push(
                Child.transaction.update({
                    id: child.id
                }, {
                    guardians: child.guardians
                })
            );
        } else {
            message = `You declined the request from ${invitedPartner.firstName} ${invitedPartner.lastName}.`;
        }

        await this.updateSendInvites(invitedPartner, childId, user, status, transactionItems);

        const newPartnerInvites = existingUser.partnerInvites.map(invite => {
            if (invite.inviterPartnerEmail === inviterPartnerEmail) {
                invite.children = invite.children.filter(child => child.childId !== childId);
            }
            if (invite.children.length === 0) {
                return null;
            } else {
                return invite;
            }
        }).filter(invite => invite !== null);

        existingUser.partnerInvites = newPartnerInvites;
        transactionItems.push(
            User.transaction.update({
                id: existingUser.id,
                email: existingUser.email
            }, {
                partnerInvites: existingUser.partnerInvites,
                children: existingUser.children
            })
        );
        await dynamoose.transaction(transactionItems);

        const responseList = await this.getInvitePartnerList(existingUser);
        return { responseList, child, message };
    }

    static async updateSendInvites (inviterPartner, childId, user, status, transactionItems) {
        const sendInvite = inviterPartner.sendInvites.find(invite => invite.invitedPartnerEmail === user.email);
        const child = sendInvite.children.find(child => child.childId === childId);
        if (status === CONSTANTS.INVITE_PARTNER_STATUS.ACCEPTED) {
            child.status = status;
        } else {
            sendInvite.children = sendInvite.children.filter(child => child.childId !== childId);
            if (sendInvite.children.length === 0) {
                inviterPartner.sendInvites = inviterPartner.sendInvites.filter(invite => invite.invitedPartnerEmail !== user.email);
            }
        }
        transactionItems.push(
            User.transaction.update({
                id: inviterPartner.id,
                email: inviterPartner.email
            }, {
                sendInvites: inviterPartner.sendInvites
            })
        );
    }

    static async getLoggedInUserChildren (user) {
        const children = await Child.batchGet(user.children);
        let sharedByOtherChildren = children.filter(child => child.createdBy !== user.id);
        let loggedInUserChildren = children.filter(child => child.createdBy === user.id);
        const selectedChildAttributes = ['id', 'firstName', 'lastName', 'associatedColor', 'photoURL'];
        loggedInUserChildren = loggedInUserChildren.map(child => selectAttributes(selectedChildAttributes, child));
        sharedByOtherChildren = sharedByOtherChildren.map(child => selectAttributes(selectedChildAttributes, child));
        for (const child of loggedInUserChildren) {
            child.photoURL = child.photoURL ? await UploadService.getSignedUrl(child.photoURL) : null;
        }
        for (const child of sharedByOtherChildren) {
            child.photoURL = child.photoURL ? await UploadService.getSignedUrl(child.photoURL) : null;
        }
        return { loggedInUserChildren, sharedByOtherChildren };
    }

    static async getInvitedPartner (invite) {
        let invitedPartner = await User.query('email').eq(invite.invitedPartnerEmail)
            .attributes(['id', 'email', 'firstName', 'lastName', 'children']).exec();
        if (!invitedPartner || !invitedPartner.length) {
            invitedPartner = await PendingParentInvite.query('invitedPartner').eq(invite.invitedPartnerEmail).exec();
        }
        return invitedPartner[0];
    }

    static async getSendInvites (user) {
        const { loggedInUserChildren, sharedByOtherChildren } = (user.children.length > 0 && await this.getLoggedInUserChildren(user))
            || { loggedInUserChildren: [], sharedByOtherChildren: [] };
        const loggedInUserChildrenIds = user.children.length > 0 && loggedInUserChildren.map(child => child.id);

        const familyMembers = [];
        const sentRequests = [];

        for (const invite of (user.sendInvites ?? [])) {
            const acceptedChildren = invite.children.filter(child => child.status === CONSTANTS.INVITE_PARTNER_STATUS.ACCEPTED);
            const pendingChildren = invite.children.filter(child => child.status === CONSTANTS.INVITE_PARTNER_STATUS.PENDING);
            const isEmpty = this.isEmpty(acceptedChildren, pendingChildren);

            const invitedPartner = await this.getInvitedPartner(invite);
            const hasAcceptedChildren = invitedPartner.children.some(childId => loggedInUserChildrenIds.includes(childId));

            if (this.isFamilyMembers(acceptedChildren, isEmpty, hasAcceptedChildren)) {
                delete invitedPartner.children;
                familyMembers.push({ invitedPartner, invitedChildren: acceptedChildren.map(child => child.childId) });
            }

            if ((pendingChildren.length === invite.children.length && pendingChildren.length > 0) && !hasAcceptedChildren) {
                sentRequests.push({
                    invitedPartner: { email: invite.invitedPartnerEmail },
                    invitedChildren: pendingChildren.map(child => child.childId)
                });
            }
        }

        return { children: loggedInUserChildren, sharedByOtherChildren, familyMembers, sentRequests };
    }

    static isEmpty (acceptedChildren, pendingChildren) {
        return acceptedChildren.length === 0 && pendingChildren.length === 0;
    }

    static isFamilyMembers (acceptedChildren, isEmpty, hasAcceptedChildren) {
        return acceptedChildren.length > 0 || isEmpty || hasAcceptedChildren;
    }

    static async saveSendInvites (req, user, locale) {
        const { invitedEmail, invitedId, childrenIds } = req.body;
        const validation = new Validation(locale);
        validation.email(invitedEmail, 'Partner Email');
        validation.field(invitedId, 'Partner Id');
        validation.array(childrenIds, 'Children Ids');

        const transactionItems = [];

        const invitedPartner = await User.get({ id: invitedId, email: invitedEmail });
        const loggedInUser = await User.get({ id: user.id, email: user.email });

        const sendInvites = loggedInUser.sendInvites.filter(invite => invite.invitedPartnerEmail === invitedEmail);

        const acceptedChild = sendInvites.flatMap(invite =>
            invite.children.filter(child => child.status === CONSTANTS.INVITE_PARTNER_STATUS.ACCEPTED));
        const acceptedChildIds = acceptedChild.map(child => child.childId);
        const invitePartnerAcceptedChildIds = acceptedChild.map(child => child.childId);
        const commonChildren = childrenIds.filter(childId => invitePartnerAcceptedChildIds.includes(childId));

        const remainingChildren = childrenIds.filter(childId => !commonChildren.includes(childId));

        const removeChildIds = acceptedChildIds.filter(childId => !commonChildren.includes(childId));

        if (childrenIds.length === 0) {
            for (const invite of sendInvites) {
                if (invite.invitedPartnerEmail === invitedEmail) {
                    invite.children = [];
                }
            }
            invitedPartner.partnerInvites = invitedPartner.partnerInvites.filter(invite => invite.inviterPartnerEmail !== user.email);
            invitedPartner.children = invitedPartner.children.filter(child => !removeChildIds.includes(child));
            transactionItems.push(
                User.transaction.update({
                    id: loggedInUser.id,
                    email: loggedInUser.email
                }, {
                    sendInvites: loggedInUser.sendInvites
                }),
                User.transaction.update({
                    id: invitedPartner.id,
                    email: invitedEmail
                }, {
                    partnerInvites: invitedPartner.partnerInvites,
                    children: invitedPartner.children
                })
            );
            await this.removeGuardiansFromAllChildren(removeChildIds, invitedPartner, transactionItems);
        } else {
            if (removeChildIds.length) {
                for (const childId of removeChildIds) {
                    loggedInUser.sendInvites = loggedInUser.sendInvites.map(invite => {
                        if (invite.invitedPartnerEmail === invitedEmail) {
                            invite.children = invite.children.filter(child => child.childId !== childId);
                        }
                        return invite;
                    });
                    invitedPartner.children = invitedPartner.children.filter(child => child !== childId);
                    await this.removeGaurdianFromChild(childId, invitedPartner.id, transactionItems);
                }
            }

            if (remainingChildren.length) {
                const pendingInvites = invitedPartner.partnerInvites.find(invite => invite.inviterPartnerEmail === user.email);
                if (pendingInvites) {
                    pendingInvites.children = [];
                    for (const childId of remainingChildren) {
                        pendingInvites.children.push({ childId, invitedAt: MOMENT().utc().toDate() });
                    }
                    loggedInUser.sendInvites = loggedInUser.sendInvites.map(invite => {
                        if (invite.invitedPartnerEmail === invitedEmail) {
                        /**  keep those children who have accepted and in the common children */
                            invite.children = invite.children.filter(child =>
                                commonChildren.includes(child.childId) && child.status === CONSTANTS.INVITE_PARTNER_STATUS.ACCEPTED);
                            for (const childId of remainingChildren) {
                                invite.children.push({ childId, status: CONSTANTS.INVITE_PARTNER_STATUS.PENDING });
                            }
                        }
                        return invite;
                    });
                } else {
                    invitedPartner.partnerInvites.push({
                        children: remainingChildren.map(childId => ({ childId, invitedAt: MOMENT().utc().toDate() })),
                        inviterPartnerId: user.id,
                        inviterPartnerEmail: user.email
                    });
                    loggedInUser.sendInvites = loggedInUser.sendInvites.map(invite => {
                        if (invite.invitedPartnerEmail === invitedEmail) {
                            invite.children = invite.children.filter(child =>
                                commonChildren.includes(child.childId) && child.status === CONSTANTS.INVITE_PARTNER_STATUS.ACCEPTED);
                            for (const childId of remainingChildren) {
                                invite.children.push({ childId, status: CONSTANTS.INVITE_PARTNER_STATUS.PENDING });
                            }
                        }
                        return invite;
                    });
                }
            }

            transactionItems.push(
                User.transaction.update({
                    id: invitedPartner.id,
                    email: invitedPartner.email
                }, {
                    partnerInvites: invitedPartner.partnerInvites,
                    children: invitedPartner.children
                }),
                User.transaction.update({
                    id: loggedInUser.id,
                    email: loggedInUser.email
                }, {
                    sendInvites: loggedInUser.sendInvites
                })
            );

            for (const childId of remainingChildren) {
                const child = await Child.get({ id: childId });
                this.sendInviteToPartner(invitedEmail, invitedPartner, child, 'invitePartner.html',
                    `${user.firstName} ${user.lastName} Invited You to Connect on ` + CONSTANTS.APP_NAME);
            }
        }
        await dynamoose.transaction(transactionItems);
    }

    static async removeGuardiansFromAllChildren (removeChildIds, invitedPartner, transactionItems) {
        for (const childId of removeChildIds) {
            await this.removeGaurdianFromChild(childId, invitedPartner.id, transactionItems);
        }
    }

    static async removeGaurdianFromChild (childId, guardianId, transactionItems) {
        const child = await Child.get({ id: childId });
        child.guardians = child.guardians.filter(guardian => guardian !== guardianId);
        transactionItems.push(
            Child.transaction.update({
                id: child.id,
                guardians: child.guardians
            })
        );
    }

    static async removeMember (req, user, locale) {
        const { invitedEmail, invitedId } = req.query;
        const validation = new Validation(locale);
        validation.email(invitedEmail, 'Partner Email');
        validation.field(invitedId, 'Partner Id');
        const invitedPartner = await User.get({ id: invitedId, email: invitedEmail });
        const loggedInUser = await User.get({ id: user.id, email: user.email });
        const sendInvites = loggedInUser.sendInvites.filter(invite => invite.invitedPartnerEmail === invitedEmail);
        const acceptedInvites = sendInvites.flatMap(invite =>
            invite.children.filter(child => child.status === CONSTANTS.INVITE_PARTNER_STATUS.ACCEPTED));
        if (acceptedInvites.length) {
            throw new GeneralError(MESSAGES.CANNOT_REMOVE_MEMBER, 400);
        }
        const transactionItems = [];

        const pendingInvites = invitedPartner.partnerInvites.find(invite => invite.inviterPartnerEmail === user.email);
        if (pendingInvites) {
            invitedPartner.partnerInvites = invitedPartner.partnerInvites.filter(invite => invite.inviterPartnerEmail !== user.email);
            transactionItems.push(
                User.transaction.update({
                    id: invitedPartner.id,
                    email: invitedPartner.email
                }, {
                    partnerInvites: invitedPartner.partnerInvites
                })
            );
        }
        loggedInUser.sendInvites = loggedInUser.sendInvites.filter(invite => invite.invitedPartnerEmail !== invitedEmail);
        transactionItems.push(
            User.transaction.update({
                id: loggedInUser.id,
                email: loggedInUser.email
            }, {
                sendInvites: loggedInUser.sendInvites
            })
        );

        await dynamoose.transaction(transactionItems);
    }
}

module.exports = ParentService;
