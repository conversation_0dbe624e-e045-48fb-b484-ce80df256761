# Vaalee BE

## Prerequisites
Before you begin, ensure you have the following installed:

- Node.js (v18.15.0)
- npm (v9.5.0 or later)

## Installation

Follow these steps to install and set up the project on your local machine:

1. **Clone the Repository**: Clone this repository to your local machine using the following command:
   ```bash
   git clone <repository-url>
   ```

2. **Navigate to Project Directory**: Navigate to the directory of the cloned repository:
   ```bash
   cd vaalee-be
   ```

3. **Install Dependencies**: Run the following command to install the project dependencies:
   ```bash
   npm install
   ```

3. **Get the configuration file**: Obtain the config.json file and paste it in the root directory of the project.

4. **Set up the environment**: Run the following script to set up the environment variables for all microservices:
```bash
npm run setup:local
```
This script sets up all the services with development environment variables. To set up for staging or production, replace the environment variables in the configuration file.

## Running a Microservice Locally

To run any of the microservices locally, navigate to the respective folder and start the service. For example, to run the auth microservice:

1. **Navigate to the microservice directory**:
```bash
cd auth
```

2. **Start the microservice**:
```bash
npm start
```

3. **Get the HMAC middleware token**:

- Console log the calculatedHmac in the hmac.js file of the specific service.
- After adding log for calculatedHmac, hit the API from Postman to get the reqtoken.

4. **Hit the API**:

- Get the Bearer token by using the login API and add it.
- Add the HMAC token in the header with the key reqtoken.
- Hit the API.