const dynamoose = require('dynamoose');
const { v4: uuidv4 } = require('uuid');

const groupMembersSchema = new dynamoose.Schema({
    id: {
        hashKey: true,
        type: String,
        default: () => uuidv4()
    },
    groupId: {
        type: String,
        required: true,
        index: {
            name: 'groupId-index',
            global: true,
            project: true
        }
    },
    userId: {
        type: String,
        required: true,
        index: {
            name: 'userId-index',
            global: true,
            project: true
        }
    },
    isAdmin: {
        type: Boolean,
        default: false
    },
    childrenIds: {
        type: Array,
        schema: [String],
        default: []
    },
    lastReadMessage: {
        type: Object,
        schema: {
            messageId: {
                type: String
            },
            createdAt: {
                type: Date
            }
        }
    },
    muteConversation: {
        type: Boolean,
        default: false
    },
    status: {
        type: String,
        enum: ['active', 'disabled', 'removed'],
        default: 'active'
    }
}, {
    timestamps: {
        createdAt: 'createdAt'
    }
});

module.exports = dynamoose.model('GroupMembers', groupMembersSchema);
