{"name": "vaalee-api", "version": "0.1.0", "description": "Vaalee API", "main": "index.js", "scripts": {"prestart": "./prestart.sh", "start": "NODE_ENV=local nodemon .", "start:dev": "NODE_ENV=development node index.js", "test": "NODE_ENV=testing nyc mocha test/alltests.js --exit", "jsdoc": "./node_modules/.bin/jsdoc server/* -r  --destination jsdocs/jsdocs", "commit": "git-cz"}, "author": "Growexx", "license": "ISC", "dependencies": {"@aws-sdk/client-apigatewaymanagementapi": "^3.675.0", "@aws-sdk/client-dynamodb": "^3.427.0", "@aws-sdk/client-ses": "^3.409.0", "@aws-sdk/client-sqs": "^3.460.0", "@aws-sdk/util-dynamodb": "^3.427.0", "@opensearch-project/opensearch": "^2.6.0", "aws-sdk": "^2.1060.0", "dotenv": "^14.2.0", "dynamoose": "^3.2.1", "ioredis": "^5.3.2", "lodash": "^4.17.21", "moment": "^2.19.2", "stripe": "^14.4.0", "@aws-sdk/client-s3": "^3.420.0", "@aws-sdk/s3-request-presigner": "^3.420.0", "sharp": "^0.33.5", "uuid": "^9.0.1"}, "devDependencies": {"jest-aws-sdk-mock": "^1.0.2", "jsdoc": "^4.0.2", "mocha": "^10.2.0", "nyc": "^15.1.0", "sinon": "^15.2.0", "chai": "^4.3.8"}, "nyc": {"lines": 5, "statements": 5, "functions": 5, "branches": 5, "check-coverage": true, "exclude": ["node_modules", "**/test/**", "coverage", "migrations", "jsdocs", ".eslintrc.js", ".scannerwork", "server/connection.js", "server/logger.js", "server/redisUtil.js", "server/opensearchUtil.js"], "reporter": ["lcov", "html"], "cache": true, "all": true}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "git+https://bitbucket.org/Growexx-master/Growexx-api.git"}, "release": {"repositoryUrl": "https://bitbucket.org/Growexx-master/Growexx-api.git", "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git"], "publish": [{"path": "@semantic-release/npm", "npmPublish": false, "tarballDir": "dist"}]}, "homepage": "https://bitbucket.org/ZsigDevelopment/semver-demo.git#readme"}