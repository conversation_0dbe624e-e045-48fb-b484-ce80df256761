/**
 * @name Server Configuration
 */

const compression = require('compression');
const express = require('express');
const cookieParser = require('cookie-parser');
const app = express();
const bodyParser = require('body-parser');
const swaggerUi = require('swagger-ui-express');
const swaggerDoc = require('swagger-jsdoc');
const swaggerDef = require('./public/swagger');
const cors = require('cors');
const methodOverride = require('method-override');
const i18n = require('i18n');
const morgan = require('morgan');
const helmet = require('helmet');
const userRoutes = require('./routes/userRoutes');
const parentRoutes = require('./routes/parentRoutes');
const paymentRoutes = require('./routes/paymentRoutes');
const organizationRoutes = require('./routes/organizationRoutes');
const childRoutes = require('./routes/childRoutes');
const DynamoDBConnection = require('./connection');
const AWS = require('aws-sdk');
const HmacMiddleware = require('./middleware/hmac');
const { Client } = require('@opensearch-project/opensearch');
const { eventSchema, childSchema, organizationSchema } = require('./opensearchDB/index');

AWS.config.update({
    region: process.env.AWS_REGION_DB,
    endpoint: process.env.DB_HOST,
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});

// Global Variables
global.CONSOLE_LOGGER = require('./util/logger');
global.CONSTANTS = require('./util/constants');
global.MESSAGES = require('./locales/en.json');
global.MOMENT = require('moment');
global._ = require('lodash');

const connectDB = async () => await DynamoDBConnection.connectToDB();
if (process.env.NODE_ENV !== 'testing') {
    connectDB();
}

if (process.env.LOCAL === 'true') {
    app.use(express.static('../jsdocs/jsdocs'));
    app.use(
        '/auth/coverage',
        express.static(`${__dirname}/../coverage/lcov-report`)
    );
}

// Configure i18n for multilingual
i18n.configure({
    locales: ['en'],
    directory: `${__dirname}/locales`,
    extension: '.json',
    prefix: '',
    logDebugFn (msg) {
        if (process.env.LOCAL === 'true') {
            CONSOLE_LOGGER.debug(`i18n::${CONSTANTS.LOG_LEVEL}`, msg);
        }
    }
});

app.use(compression());
app.use(helmet());
app.use(i18n.init);
app.use(cookieParser());

app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));
app.use(
    bodyParser.json({
        limit: '50mb',
        extended: true,
        verify: (req, res, buf) => {
            req.rawBody = buf.toString();
        }
    })
);

app.use(cors({
    origin: true,
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true,
    exposedHeaders: ['x-auth-token']
}));

app.use(morgan('dev'));
app.use(methodOverride());

let openSearchClient;
if (process.env.NODE_ENV !== 'testing') {
    openSearchClient = new Client({
        node: process.env.OPENSEARCH_ENDPOINT,
        auth: {
            username: process.env.OPENSEARCH_USERNAME,
            password: process.env.OPENSEARCH_PASSWORD
        },
        ssl: { rejectUnauthorized: false }
    });
}

const schemaMapping = {
    [CONSTANTS.OPEN_SEARCH.COLLECTION.EVENT]: eventSchema,
    [CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD]: childSchema,
    [CONSTANTS.OPEN_SEARCH.COLLECTION.ORGANIZATION]: organizationSchema
};

const createOpenSearchIndicesIfNotExists = async () => {
    const allOpenSearchTables = Object.keys(CONSTANTS.OPEN_SEARCH.INDICES);
    for (let i = 0; i < allOpenSearchTables.length; i++) {
        const value = allOpenSearchTables[i];
        const indexExists = await openSearchClient.indices.exists({ index: value });
        if (indexExists.statusCode !== 200) {
            CONSOLE_LOGGER.info(value, 'index created');
            await openSearchClient.indices.create(schemaMapping[value]);
        }
    }
};

process.env.NODE_ENV !== 'testing' && createOpenSearchIndicesIfNotExists();

// Landing Page
app.get('/api', HmacMiddleware, (req, res) => {
    res.send({
        status: 'ok',
        date: MOMENT(),
        message: 'Hello from API service.'
    });
});

app.use('/api/user', HmacMiddleware, userRoutes);
app.use('/api/parent', HmacMiddleware, parentRoutes);
app.use('/api/payment', paymentRoutes);
app.use('/api/organization', HmacMiddleware, organizationRoutes);
app.use('/api/child', HmacMiddleware, childRoutes);

const spec = swaggerDoc(swaggerDef);

if (process.env.NODE_ENV !== 'production') {
    app.use('/api/api-docs/', swaggerUi.serve, swaggerUi.setup(spec));
}

module.exports = app;
