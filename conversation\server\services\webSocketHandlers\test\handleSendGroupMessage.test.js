const { assert, expect } = require('chai');
const sinon = require('sinon');
const GroupMembers = require('../../../models/groupMembers.model');
const SendMessageService = require('../../../services/sendSocketMessageService');
const handleSendGroupMessage = require('../handleSendGroupMessage');
const { afterEach } = require('mocha');
const websocketRoutes = require('../../../webSocketHandler');
const MessageReactionsModel = require('../../../models/messageReactions.model');

describe('Handle Send Group Message, MUTE_GROUP_CONVERSATION', () => {
    afterEach(() => {
        sinon.restore();
    });

    it('should throw error if user is not a member of the group', async () => {
        const event = {
            body: { actionType: 'MUTE_GROUP_CONVERSATION', groupId: '123', muteConversation: true, userId: '456' },
            requestContext: { connectionId: 'connectionId1' }
        };

        sinon.stub(GroupMembers, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            exec: sinon.stub().resolves([])
                        })
                    })
                })
            })
        });

        const response = await handleSendGroupMessage(event);
        assert.equal(response.statusCode, 404);
    });

    it('should handle error while updating group member status', async () => {
        const event = {
            body: { actionType: 'MUTE_GROUP_CONVERSATION', groupId: '123', muteConversation: false, userId: '456' },
            requestContext: { connectionId: 'connectionId1' }
        };

        sinon.stub(GroupMembers, 'update').rejects(new Error('Failed to update group member status'));

        sinon.stub(GroupMembers, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            exec: sinon.stub().resolves([
                                {
                                    id: '123',
                                    groupId: '123',
                                    userId: '456',
                                    muteConversation: false
                                }
                            ])
                        })
                    })
                })
            })
        });

        const response = await handleSendGroupMessage(event);
        assert.equal(response.statusCode, 500);
    });

    it('should return success response and send event to user if group member is updated successfully', async () => {
        const event = {
            body: { actionType: 'MUTE_GROUP_CONVERSATION', groupId: '123', muteConversation: true, userId: '456' },
            requestContext: { connectionId: 'connectionId1' }
        };

        sinon.stub(GroupMembers, 'update').resolves();

        sinon.stub(GroupMembers, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            exec: sinon.stub().resolves([
                                {
                                    id: '123',
                                    groupId: '123',
                                    userId: '456',
                                    muteConversation: false
                                }
                            ])
                        })
                    })
                })
            })
        });

        sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();

        const response = await handleSendGroupMessage(event);
        assert.equal(response.statusCode, 200);
        assert(SendMessageService.sendMessagesToUsers.calledOnce);
    });
});

describe('Reactions for messages', () => {
    try {
        it('should throw error if invalid reaction is passed', async () => {
            const event = {
                body: {
                    action: 'sendMessage',
                    actionType: 'ADD_REACTION',
                    messageId: 'messageId1',
                    reaction: 'reaction1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(400);
            expect(result.message).to.equal('Invalid reaction');
        });

        it('should handle error in adding reaction for a message', async () => {
            const event = {
                body: {
                    action: 'sendMessage',
                    actionType: 'ADD_REACTION',
                    messageId: 'messageId1',
                    userId: 'userId',
                    reaction: CONSTANTS.ALLOWED_REACTIONS[0],
                    groupId: 'groupId'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            sinon.stub(MessageReactionsModel, 'update').rejects(new Error('Something went wrong!'));

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(500);
            expect(result.message).to.equal('Failed to add reaction');

            MessageReactionsModel.update.restore();
        });

        it('should handle success in adding reaction for a message', async () => {
            const messageId = 'messageId1';
            const userId = 'userId';
            const reaction = CONSTANTS.ALLOWED_REACTIONS[0];
            const groupId = 'groupId';
            const event = {
                body: {
                    action: 'sendMessage',
                    actionType: 'ADD_REACTION',
                    messageId,
                    userId,
                    reaction,
                    groupId
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            sinon.stub(MessageReactionsModel, 'update').resolves();
            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        attributes: sinon.stub().returns({
                            exec: sinon.stub().resolves([
                                { userId: 'userId' },
                                { userId: 'userId2' }
                            ])
                        })
                    })
                })
            });

            sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);
            expect(result.message).to.equal('Reaction added successfully');
            expect(result.data).to.deep.equal(JSON.stringify({
                messageId,
                userId,
                reaction,
                groupId
            }));

            MessageReactionsModel.update.restore();
            GroupMembers.query.restore();
            SendMessageService.sendMessagesToUsers.restore();
        });

        it('should handle error in updating reaction for a message', async () => {
            const event = {
                body: {
                    action: 'sendMessage',
                    actionType: 'UPDATE_REACTION',
                    messageId: 'messageId1',
                    userId: 'userId',
                    reaction: CONSTANTS.ALLOWED_REACTIONS[0],
                    groupId: 'groupId'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            sinon.stub(MessageReactionsModel, 'update').rejects(new Error('Something went wrong!'));

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(500);
            expect(result.message).to.equal('Failed to update reaction');

            MessageReactionsModel.update.restore();
        });

        it('should handle success in UPDATE_REACTION websocket route', async () => {
            const messageId = 'messageId1';
            const userId = 'userId';
            const reaction = CONSTANTS.ALLOWED_REACTIONS[0];
            const groupId = 'groupId';
            const event = {
                body: {
                    action: 'sendMessage',
                    actionType: 'UPDATE_REACTION',
                    messageId,
                    userId,
                    reaction,
                    groupId
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            sinon.stub(MessageReactionsModel, 'update').resolves();
            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        attributes: sinon.stub().returns({
                            exec: sinon.stub().resolves([
                                { userId: 'userId' },
                                { userId: 'userId2' }
                            ])
                        })
                    })
                })
            });

            sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);
            expect(result.message).to.equal('Reaction updated successfully');
            expect(result.data).to.deep.equal(JSON.stringify({
                messageId,
                userId,
                reaction,
                groupId
            }));

            MessageReactionsModel.update.restore();
            GroupMembers.query.restore();
            SendMessageService.sendMessagesToUsers.restore();
        });

        it('should handle error in REMOVE_REACTION websocket route', async () => {
            const messageId = 'messageId1';
            const userId = 'userId';
            const groupId = 'groupId';
            const event = {
                body: {
                    action: 'sendMessage',
                    actionType: 'REMOVE_REACTION',
                    messageId,
                    userId,
                    groupId
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            sinon.stub(MessageReactionsModel, 'delete').rejects(new Error('Something went wrong!'));

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(500);
            expect(result.message).to.equal('Failed to remove reaction');

            MessageReactionsModel.delete.restore();
        });

        it('should handle success in REMOVE_REACTION websocket route', async () => {
            const messageId = 'messageId1';
            const userId = 'userId';
            const groupId = 'groupId';
            const event = {
                body: {
                    action: 'sendMessage',
                    actionType: 'REMOVE_REACTION',
                    messageId,
                    userId,
                    groupId
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            sinon.stub(MessageReactionsModel, 'delete').resolves();
            sinon.stub(GroupMembers, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        attributes: sinon.stub().returns({
                            exec: sinon.stub().resolves([
                                { userId: 'userId' },
                                { userId: 'userId2' }
                            ])
                        })
                    })
                })
            });

            sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);
            expect(result.message).to.equal('Reaction removed successfully');
            expect(result.data).to.deep.equal(JSON.stringify({
                messageId,
                userId,
                groupId
            }));

            MessageReactionsModel.delete.restore();
            GroupMembers.query.restore();
            SendMessageService.sendMessagesToUsers.restore();
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
