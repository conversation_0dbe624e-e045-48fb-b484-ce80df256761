const { assert, expect } = require('chai');
const sinon = require('sinon');
const GroupMembers = require('../../../models/groupMembers.model');
const { afterEach, beforeEach } = require('mocha');
const CONSTANTS = require('../../../util/constants');
const handleCompanionMessage = require('../handleCompanionMessage');
const messageModel = require('../../../models/message.model');
const userModel = require('../../../models/user.model');
const { Chat } = require('@incubyte/ai');
const SendMessageService = require('../../sendSocketMessageService');
const { ChatOpenAI } = require('@langchain/openai');
const { CloudWatchClient, PutMetricDataCommand } = require('@aws-sdk/client-cloudwatch');
const constantModel = require('../../../models/constant.model');
const RedisUtil = require('../../../util/redisUtil');
const childModel = require('../../../models/child.model');
const organizationModel = require('../../../models/organization.model');

describe('handleGetCompanionMessage', () => {
    let addMetricForQueryTimeStub;

    beforeEach(() => {
        sinon.stub(constantModel, 'get').resolves({});
        addMetricForQueryTimeStub = sinon.stub(CloudWatchClient.prototype, 'send').resolves();
    });

    afterEach(() => {
        sinon.restore();
    });

    it('should return 400 if actionType is invalid', async () => {
        const event = {
            body: {
                actionType: 'INVALID_ACTION_TYPE',
                messageId: '123',
                groupId: '456',
                senderId: '789',
                message: 'Hello',
                organizationId: 'org-123',
                groupMemberId: 'group-123',
                isExistingMessage: false
            },
            requestContext: {
                connectionId: 'conn-123'
            }
        };

        const response = await handleCompanionMessage(event);
        assert.equal(response.statusCode, 400);
    });

    it('should throw error if group member is not found', async () => {
        const event = {
            body: {
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_GROUP_COMPANION_MESSAGE,
                messageId: '123',
                groupId: '456',
                senderId: '789',
                message: 'Hello',
                organizationId: 'org-123',
                groupMemberId: 'group-123',
                isExistingMessage: false
            },
            requestContext: {
                connectionId: 'conn-123'
            }
        };

        sinon.stub(GroupMembers, 'get').resolves();

        const response = await handleCompanionMessage(event);
        assert.equal(response.statusCode, 404);

        sinon.assert.calledOnce(addMetricForQueryTimeStub);
        const commandArg = addMetricForQueryTimeStub.firstCall.args[0];
        expect(commandArg).to.be.instanceOf(PutMetricDataCommand);
        assert.equal(commandArg.input.Namespace, 'Vaalee/AICompanion');
        assert.equal(commandArg.input.MetricData[0].MetricName, CONSTANTS.CLOUDWATCH_METRIC_NAMES.AI_COMPANION_QUERY_TIME);
        assert.equal(commandArg.input.MetricData[0].Unit, 'Milliseconds');
    });

    it('should not save message if isExistingMessage is true', async () => {
        const sendChatStreamStub = sinon.stub(Chat.prototype, 'sendChatStream');

        const event = {
            body: {
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_GROUP_COMPANION_MESSAGE,
                messageId: '123',
                groupId: '456',
                senderId: '789',
                message: 'Hello',
                organizationId: 'org-123',
                groupMemberId: 'group-123',
                isExistingMessage: true
            },
            requestContext: {
                connectionId: 'conn-123'
            }
        };

        sinon.stub(GroupMembers, 'get').resolves({ userId: 'user-123', save: () => { } });

        const getHashValueStub = sinon.stub(RedisUtil, 'getHashValue');
        const getAllHashValuesStub = sinon.stub(RedisUtil, 'getAllHashValues');

        getHashValueStub
            .withArgs(':user-details:789')
            .resolves(JSON.stringify({ id: 'user-123' }));
        getHashValueStub.resolves();

        getAllHashValuesStub
            .withArgs(':conversation-members:456')
            .resolves([
                JSON.stringify({ userId: 'user-123', status: CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE }),
                JSON.stringify({ userId: 'user-456', status: CONSTANTS.GROUP_MEMBER_STATUS.REMOVED })
            ]);
        getAllHashValuesStub.resolves();

        sendChatStreamStub.resolves({
            async *[Symbol.asyncIterator] () {
                yield { type: 'message', data: 'AI response chunk' };
            }
        });

        sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();
        const messageModelCreateStub = sinon.stub(messageModel, 'create').resolves({});

        const response = await handleCompanionMessage(event);
        assert.equal(response.statusCode, 200);

        expect(messageModelCreateStub.calledWith(sinon.match({ id: event.body.messageId, senderId: event.body.senderId }))).to.be.false;
        expect(messageModelCreateStub.calledWith(sinon.match({ senderId: CONSTANTS.AI_COMPANION_SENDER_ID }))).to.be.true;

        sinon.assert.calledOnce(addMetricForQueryTimeStub);
        const commandArg = addMetricForQueryTimeStub.firstCall.args[0];
        expect(commandArg).to.be.instanceOf(PutMetricDataCommand);
        assert.equal(commandArg.input.Namespace, 'Vaalee/AICompanion');
        assert.equal(commandArg.input.MetricData[0].MetricName, CONSTANTS.CLOUDWATCH_METRIC_NAMES.AI_COMPANION_QUERY_TIME);
        assert.equal(commandArg.input.MetricData[0].Unit, 'Milliseconds');
    });

    it('should save message and respond with stream if isExistingMessage is false', async () => {
        const sendChatStreamStub = sinon.stub(Chat.prototype, 'sendChatStream');

        const event = {
            body: {
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_GROUP_COMPANION_MESSAGE,
                messageId: '123',
                aiMessageId: '456',
                groupId: '456',
                senderId: '789',
                message: 'Hello',
                organizationId: 'org-123',
                groupMemberId: 'group-123',
                isExistingMessage: false
            },
            requestContext: {
                connectionId: 'conn-123'
            }
        };

        sinon.stub(GroupMembers, 'get').resolves({ userId: 'user-123', save: () => { } });

        const getHashValueStub = sinon.stub(RedisUtil, 'getHashValue');
        const getAllHashValuesStub = sinon.stub(RedisUtil, 'getAllHashValues');

        getHashValueStub
            .withArgs(':user-details:789', 'details')
            .resolves(JSON.stringify({ id: 'user-123', children: ['child-123', 'child-456', 'child-789'] }));
        getHashValueStub
            .withArgs(':child-details:child-123', 'details')
            .resolves(JSON.stringify({
                id: 'child-123',
                firstName: 'Child 1',
                lastName: 'Child 1',
                associatedOrganizations: ['org-123']
            }));
        getHashValueStub
            .withArgs(':child-details:child-456', 'details')
            .resolves(JSON.stringify({ id: 'child-456', associatedOrganizations: ['org-123'] }));
        getHashValueStub
            .withArgs(':child-details:child-789', 'details')
            .resolves(JSON.stringify({ id: 'child-789' }));
        getHashValueStub
            .withArgs(':organization-details:org-123', 'details')
            .resolves(JSON.stringify({ id: 'org-123', name: 'Organization 1' }));
        getHashValueStub.resolves();

        getAllHashValuesStub
            .withArgs(':conversation-members:456')
            .resolves([JSON.stringify({ userId: 'user-123', status: CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE })]);
        getAllHashValuesStub.resolves();

        sendChatStreamStub.resolves({
            async *[Symbol.asyncIterator] () {
                yield { type: 'message', data: 'AI response chunk' };
                yield { type: 'context', data: 'AI context chunk' };
                yield { type: 'end' };
            }
        });

        sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();
        const messageModelCreateStub = sinon.stub(messageModel, 'create').resolves({});

        const response = await handleCompanionMessage(event);
        assert.equal(response.statusCode, 200);

        sinon.assert.calledWith(
            messageModelCreateStub,
            {
                id: event.body.messageId,
                senderId: event.body.senderId,
                groupId: event.body.groupId,
                message: event.body.message
            }
        ); sinon.assert.calledWith(
            messageModelCreateStub,
            {
                id: event.body.messageId,
                senderId: event.body.senderId,
                groupId: event.body.groupId,
                message: event.body.message
            }
        );

        sinon.assert.calledWith(
            messageModelCreateStub,
            {
                id: event.body.aiMessageId,
                senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
                groupId: event.body.groupId,
                userQueryMessageId: event.body.messageId,
                message: 'AI response chunk',
                contexts: JSON.stringify(['AI context chunk'])
            }
        );

        sinon.assert.calledOnce(addMetricForQueryTimeStub);
        const commandArg = addMetricForQueryTimeStub.firstCall.args[0];
        expect(commandArg).to.be.instanceOf(PutMetricDataCommand);
        assert.equal(commandArg.input.Namespace, 'Vaalee/AICompanion');
        assert.equal(commandArg.input.MetricData[0].MetricName, CONSTANTS.CLOUDWATCH_METRIC_NAMES.AI_COMPANION_QUERY_TIME);
        assert.equal(commandArg.input.MetricData[0].Unit, 'Milliseconds');
    });

    it('should fetch data from DB as a fallback option if data not found in cache', async () => {
        const sendChatStreamStub = sinon.stub(Chat.prototype, 'sendChatStream');

        const event = {
            body: {
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_GROUP_COMPANION_MESSAGE,
                messageId: '123',
                aiMessageId: '456',
                groupId: '456',
                senderId: '789',
                message: 'Hello',
                organizationId: 'org-123',
                groupMemberId: 'group-123',
                isExistingMessage: false
            },
            requestContext: {
                connectionId: 'conn-123'
            }
        };

        sinon.stub(GroupMembers, 'get').resolves({ userId: 'user-123', save: () => { } });

        const getHashValueStub = sinon.stub(RedisUtil, 'getHashValue');
        const getAllHashValuesStub = sinon.stub(RedisUtil, 'getAllHashValues');

        getHashValueStub
            .withArgs(':user-details:789', 'details')
            .resolves(null);

        sinon.stub(userModel, 'query').returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().resolves([
                        {
                            id: 'user-123',
                            children: ['child-123', 'child-456', 'child-789'],
                            firstName: 'User 1',
                            lastName: 'User 1',
                            email: '<EMAIL>'
                        }
                    ])
                })
            })
        });

        getHashValueStub
            .withArgs(':child-details:child-123', 'details')
            .resolves();

        getHashValueStub
            .withArgs(':child-details:child-456', 'details')
            .resolves();

        getHashValueStub
            .withArgs(':child-details:child-789', 'details')
            .resolves();

        const childModelGetStub = sinon.stub(childModel, 'get');
        childModelGetStub.withArgs('child-123').resolves({ id: 'child-123', associatedOrganizations: ['org-123'] });
        childModelGetStub.withArgs('child-456').resolves({ id: 'child-456', associatedOrganizations: ['org-456'] });
        childModelGetStub.withArgs('child-789').resolves(null);

        getHashValueStub
            .withArgs(':organization-details:org-123', 'details')
            .resolves();
        getHashValueStub.resolves();

        const organizationModelGetStub = sinon.stub(organizationModel, 'get');
        organizationModelGetStub.withArgs('org-123').resolves({ id: 'org-123', name: 'Organization 1', category: 'Category 1' });
        organizationModelGetStub.withArgs('org-456').resolves(null);

        getAllHashValuesStub
            .withArgs(':conversation-members:456')
            .resolves([JSON.stringify({ userId: 'user-123', status: CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE })]);
        getAllHashValuesStub.resolves();

        sendChatStreamStub.resolves({
            async *[Symbol.asyncIterator] () {
                yield { type: 'message', data: 'AI response chunk' };
                yield { type: 'context', data: 'AI context chunk' };
                yield { type: 'end' };
            }
        });

        sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();
        const messageModelCreateStub = sinon.stub(messageModel, 'create').resolves({});

        const response = await handleCompanionMessage(event);
        assert.equal(response.statusCode, 200);

        sinon.assert.calledWith(
            messageModelCreateStub,
            {
                id: event.body.messageId,
                senderId: event.body.senderId,
                groupId: event.body.groupId,
                message: event.body.message
            }
        ); sinon.assert.calledWith(
            messageModelCreateStub,
            {
                id: event.body.messageId,
                senderId: event.body.senderId,
                groupId: event.body.groupId,
                message: event.body.message
            }
        );

        sinon.assert.calledWith(
            messageModelCreateStub,
            {
                id: event.body.aiMessageId,
                senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
                groupId: event.body.groupId,
                userQueryMessageId: event.body.messageId,
                message: 'AI response chunk',
                contexts: JSON.stringify(['AI context chunk'])
            }
        );

        sinon.assert.calledOnce(addMetricForQueryTimeStub);
        const commandArg = addMetricForQueryTimeStub.firstCall.args[0];
        expect(commandArg).to.be.instanceOf(PutMetricDataCommand);
        assert.equal(commandArg.input.Namespace, 'Vaalee/AICompanion');
        assert.equal(commandArg.input.MetricData[0].MetricName, CONSTANTS.CLOUDWATCH_METRIC_NAMES.AI_COMPANION_QUERY_TIME);
        assert.equal(commandArg.input.MetricData[0].Unit, 'Milliseconds');
    });

    it('should return error, if stream is not configured properly', async () => {
        const sendChatStreamStub = sinon.stub(Chat.prototype, 'sendChatStream');

        const event = {
            body: {
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_GROUP_COMPANION_MESSAGE,
                messageId: '123',
                aiMessageId: '456',
                groupId: '456',
                senderId: '789',
                message: 'Hello',
                organizationId: 'org-123',
                groupMemberId: 'group-123',
                isExistingMessage: false
            },
            requestContext: {
                connectionId: 'conn-123'
            }
        };

        sinon.stub(GroupMembers, 'get').resolves({ userId: 'user-123', save: () => { } });

        const getHashValueStub = sinon.stub(RedisUtil, 'getHashValue');
        const getAllHashValuesStub = sinon.stub(RedisUtil, 'getAllHashValues');

        getHashValueStub
            .withArgs(':user-details:789')
            .resolves(JSON.stringify({ id: 'user-123' }));
        getHashValueStub.resolves();

        getAllHashValuesStub
            .withArgs(':conversation-members:456')
            .resolves([JSON.stringify({ userId: 'user-123', status: CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE })]);
        getAllHashValuesStub.resolves();

        sendChatStreamStub.resolves(null);

        sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();
        const messageModelCreateStub = sinon.stub(messageModel, 'create').resolves({});

        const response = await handleCompanionMessage(event);

        sinon.assert.calledWith(
            messageModelCreateStub,
            {
                id: event.body.messageId,
                senderId: event.body.senderId,
                groupId: event.body.groupId,
                message: event.body.message
            }
        );

        assert.equal(response.statusCode, 200);
        assert.equal(response.message, 'AI service stream configuration error');

        sinon.assert.calledOnce(addMetricForQueryTimeStub);
        const commandArg = addMetricForQueryTimeStub.firstCall.args[0];
        expect(commandArg).to.be.instanceOf(PutMetricDataCommand);
        assert.equal(commandArg.input.Namespace, 'Vaalee/AICompanion');
        assert.equal(commandArg.input.MetricData[0].MetricName, CONSTANTS.CLOUDWATCH_METRIC_NAMES.AI_COMPANION_QUERY_TIME);
        assert.equal(commandArg.input.MetricData[0].Unit, 'Milliseconds');
    });

    it('should return error, if storing message to DB fails', async () => {
        const event = {
            body: {
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_GROUP_COMPANION_MESSAGE,
                messageId: '123',
                aiMessageId: '456',
                groupId: '456',
                senderId: '789',
                message: 'Hello',
                organizationId: 'org-123',
                groupMemberId: 'group-123',
                isExistingMessage: false
            },
            requestContext: {
                connectionId: 'conn-123'
            }
        };

        sinon.stub(GroupMembers, 'get').resolves({ userId: 'user-123', save: () => { } });

        const getHashValueStub = sinon.stub(RedisUtil, 'getHashValue');
        const getAllHashValuesStub = sinon.stub(RedisUtil, 'getAllHashValues');

        getHashValueStub
            .withArgs(':user-details:789')
            .resolves(JSON.stringify({ id: 'user-123' }));
        getHashValueStub.resolves();

        getAllHashValuesStub
            .withArgs(':conversation-members:456')
            .resolves([JSON.stringify({ userId: 'user-123', status: CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE })]);
        getAllHashValuesStub.resolves();

        const messageModelCreateStub = sinon.stub(messageModel, 'create').rejects({
            message: 'Something went wrong!'
        });

        const response = await handleCompanionMessage(event);

        sinon.assert.calledWith(
            messageModelCreateStub,
            {
                id: event.body.messageId,
                senderId: event.body.senderId,
                groupId: event.body.groupId,
                message: event.body.message
            }
        );

        assert.equal(response.statusCode, 500);
        assert.equal(response.message, 'Something went wrong!');
        assert.equal(response.action, 'sendCompanionMessage');
        assert.equal(response.actionType, 'NEW_GROUP_MESSAGE');
        assert.equal(response.data.id, event.body.messageId);
        assert.equal(response.data.senderId, event.body.senderId);
        assert.equal(response.data.groupId, event.body.groupId);
        assert.equal(response.data.message, 'Something went wrong!');

        sinon.assert.notCalled(addMetricForQueryTimeStub);
    });

    it('should return Companion not setup error, if companion is not setup', async () => {
        const sendChatStreamStub = sinon.stub(Chat.prototype, 'sendChatStream');

        const event = {
            body: {
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_GROUP_COMPANION_MESSAGE,
                messageId: '123',
                aiMessageId: '456',
                groupId: '456',
                senderId: '789',
                message: 'Hello',
                organizationId: 'org-123',
                groupMemberId: 'group-123',
                isExistingMessage: false
            },
            requestContext: {
                connectionId: 'conn-123'
            }
        };

        sinon.stub(GroupMembers, 'get').resolves({ userId: 'user-123', save: () => { } });

        const getHashValueStub = sinon.stub(RedisUtil, 'getHashValue');
        const getAllHashValuesStub = sinon.stub(RedisUtil, 'getAllHashValues');

        getHashValueStub
            .withArgs(':user-details:789')
            .resolves(JSON.stringify({ id: 'user-123' }));
        getHashValueStub.resolves();

        getAllHashValuesStub
            .withArgs(':conversation-members:456')
            .resolves([JSON.stringify({ userId: 'user-123', status: CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE })]);
        getAllHashValuesStub.resolves();

        sendChatStreamStub.rejects(new Error('index_not_found_exception'));

        sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();
        const sendMessageToConnectionStub = sinon.stub(SendMessageService, 'sendMessageToConnection').resolves();
        const messageModelCreateStub = sinon.stub(messageModel, 'create').resolves({});

        const response = await handleCompanionMessage(event);

        sinon.assert.calledWith(
            sendMessageToConnectionStub,
            {
                connectionId: event.requestContext.connectionId,
                statusCode: 200,
                messageData: {
                    data: {
                        id: event.body.aiMessageId,
                        senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
                        userQueryMessageId: event.body.messageId,
                        message: 'Companion not setup for this group!',
                        createdAt: response.data.createdAt,
                        updatedAt: response.data.updatedAt,
                        groupId: event.body.groupId
                    },
                    action: 'sendCompanionMessage',
                    actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.AI_COMPANION_GROUP_MESSAGE_STREAMING
                }
            }
        );

        sinon.assert.calledWith(
            messageModelCreateStub,
            {
                id: event.body.messageId,
                senderId: event.body.senderId,
                groupId: event.body.groupId,
                message: event.body.message
            }
        );

        assert.equal(response.statusCode, 200);
        assert.equal(response.message, 'Companion not setup for this group!');
        assert.equal(response.action, 'sendCompanionMessage');
        assert.equal(response.actionType, CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.NEW_GROUP_MESSAGE);

        sinon.assert.calledOnce(addMetricForQueryTimeStub);
        const commandArg = addMetricForQueryTimeStub.firstCall.args[0];
        expect(commandArg).to.be.instanceOf(PutMetricDataCommand);
        assert.equal(commandArg.input.Namespace, 'Vaalee/AICompanion');
        assert.equal(commandArg.input.MetricData[0].MetricName, CONSTANTS.CLOUDWATCH_METRIC_NAMES.AI_COMPANION_QUERY_TIME);
        assert.equal(commandArg.input.MetricData[0].Unit, 'Milliseconds');
    });

    it('should return 500 error, if fetching user details fails', async () => {
        const event = {
            body: {
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_GROUP_COMPANION_MESSAGE,
                messageId: '123',
                aiMessageId: '456',
                groupId: '456',
                senderId: '789',
                message: 'Hello',
                organizationId: 'org-123',
                groupMemberId: 'group-123',
                isExistingMessage: false
            },
            requestContext: {
                connectionId: 'conn-123'
            }
        };

        sinon.stub(GroupMembers, 'get').resolves({ userId: 'user-123', save: () => { } });

        sinon.stub(userModel, 'query').returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().rejects(new Error())
                })
            })
        });

        const getHashValueStub = sinon.stub(RedisUtil, 'getHashValue');
        const getAllHashValuesStub = sinon.stub(RedisUtil, 'getAllHashValues');
        getHashValueStub.resolves();

        getAllHashValuesStub
            .withArgs(':conversation-members:456')
            .resolves([JSON.stringify({ userId: 'user-123', status: CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE })]);
        getAllHashValuesStub.resolves();

        sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();
        const sendMessageToConnectionStub = sinon.stub(SendMessageService, 'sendMessageToConnection').resolves();
        const messageModelCreateStub = sinon.stub(messageModel, 'create').resolves({});

        const response = await handleCompanionMessage(event);

        sinon.assert.calledWithMatch(
            sendMessageToConnectionStub,
            {
                connectionId: event.requestContext.connectionId,
                statusCode: 500,
                messageData: {
                    data: {
                        id: event.body.aiMessageId,
                        senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
                        userQueryMessageId: event.body.messageId,
                        message: 'Something went wrong!',
                        groupId: event.body.groupId
                    },
                    action: 'sendCompanionMessage',
                    actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.AI_COMPANION_GROUP_MESSAGE_STREAMING
                }
            }
        );

        sinon.assert.calledWith(
            messageModelCreateStub,
            {
                id: event.body.messageId,
                senderId: event.body.senderId,
                groupId: event.body.groupId,
                message: event.body.message
            }
        );

        assert.equal(response.statusCode, 500);
        assert.equal(response.message, 'Something went wrong!');
        assert.equal(response.action, 'sendCompanionMessage');
        assert.equal(response.actionType, CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.NEW_GROUP_MESSAGE);
        assert.equal(response.data.id, event.body.aiMessageId);
        assert.equal(response.data.senderId, CONSTANTS.AI_COMPANION_SENDER_ID);
        assert.equal(response.data.groupId, event.body.groupId);
        assert.equal(response.data.message, 'Something went wrong!');

        sinon.assert.calledOnce(addMetricForQueryTimeStub);
        const commandArg = addMetricForQueryTimeStub.firstCall.args[0];
        expect(commandArg).to.be.instanceOf(PutMetricDataCommand);
        assert.equal(commandArg.input.Namespace, 'Vaalee/AICompanion');
        assert.equal(commandArg.input.MetricData[0].MetricName, CONSTANTS.CLOUDWATCH_METRIC_NAMES.AI_COMPANION_QUERY_TIME);
        assert.equal(commandArg.input.MetricData[0].Unit, 'Milliseconds');
    });

    it('should return 404 error, if user is not found', async () => {
        const event = {
            body: {
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_GROUP_COMPANION_MESSAGE,
                messageId: '123',
                aiMessageId: '456',
                groupId: '456',
                senderId: '789',
                message: 'Hello',
                organizationId: 'org-123',
                groupMemberId: 'group-123',
                isExistingMessage: false
            },
            requestContext: {
                connectionId: 'conn-123'
            }
        };

        sinon.stub(GroupMembers, 'get').resolves({ userId: 'user-123', save: () => { } });

        sinon.stub(userModel, 'query').returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().resolves([])
                })
            })
        });

        const getHashValueStub = sinon.stub(RedisUtil, 'getHashValue');
        const getAllHashValuesStub = sinon.stub(RedisUtil, 'getAllHashValues');
        getHashValueStub.resolves();

        getAllHashValuesStub
            .withArgs(':conversation-members:456')
            .resolves([JSON.stringify({ userId: 'user-123', status: CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE })]);
        getAllHashValuesStub.resolves();

        sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();
        const sendMessageToConnectionStub = sinon.stub(SendMessageService, 'sendMessageToConnection').resolves();
        const messageModelCreateStub = sinon.stub(messageModel, 'create').resolves({});

        const response = await handleCompanionMessage(event);

        sinon.assert.calledWithMatch(
            sendMessageToConnectionStub,
            {
                connectionId: event.requestContext.connectionId,
                statusCode: 404,
                messageData: {
                    data: {
                        id: event.body.aiMessageId,
                        senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
                        userQueryMessageId: event.body.messageId,
                        message: 'User not found',
                        groupId: event.body.groupId
                    },
                    action: 'sendCompanionMessage',
                    actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.AI_COMPANION_GROUP_MESSAGE_STREAMING
                }
            }
        );

        sinon.assert.calledWith(
            messageModelCreateStub,
            {
                id: event.body.messageId,
                senderId: event.body.senderId,
                groupId: event.body.groupId,
                message: event.body.message
            }
        );

        assert.equal(response.statusCode, 404);
        assert.equal(response.message, 'User not found');
        assert.equal(response.action, 'sendCompanionMessage');
        assert.equal(response.actionType, CONSTANTS.ACTION_TYPES.SEND_GROUP_MESSAGE.NEW_GROUP_MESSAGE);
        assert.equal(response.data.id, event.body.aiMessageId);
        assert.equal(response.data.senderId, CONSTANTS.AI_COMPANION_SENDER_ID);
        assert.equal(response.data.groupId, event.body.groupId);
        assert.equal(response.data.message, 'User not found');

        sinon.assert.calledOnce(addMetricForQueryTimeStub);
        const commandArg = addMetricForQueryTimeStub.firstCall.args[0];
        expect(commandArg).to.be.instanceOf(PutMetricDataCommand);
        assert.equal(commandArg.input.Namespace, 'Vaalee/AICompanion');
        assert.equal(commandArg.input.MetricData[0].MetricName, CONSTANTS.CLOUDWATCH_METRIC_NAMES.AI_COMPANION_QUERY_TIME);
        assert.equal(commandArg.input.MetricData[0].Unit, 'Milliseconds');
    });

    it('should return 500 error, if getting group details fails', async () => {
        const event = {
            body: {
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_GROUP_COMPANION_MESSAGE,
                messageId: '123',
                aiMessageId: '456',
                groupId: '456',
                senderId: '789',
                message: 'Hello',
                organizationId: 'org-123',
                groupMemberId: 'group-123',
                isExistingMessage: false
            },
            requestContext: {
                connectionId: 'conn-123'
            }
        };

        sinon.stub(GroupMembers, 'get').rejects(new Error('Error fetching group details'));

        const response = await handleCompanionMessage(event);

        assert.equal(response.statusCode, 500);
        assert.equal(response.body, 'Error fetching group details');

        sinon.assert.calledOnce(addMetricForQueryTimeStub);
        const commandArg = addMetricForQueryTimeStub.firstCall.args[0];
        expect(commandArg).to.be.instanceOf(PutMetricDataCommand);
        assert.equal(commandArg.input.Namespace, 'Vaalee/AICompanion');
        assert.equal(commandArg.input.MetricData[0].MetricName, CONSTANTS.CLOUDWATCH_METRIC_NAMES.AI_COMPANION_QUERY_TIME);
        assert.equal(commandArg.input.MetricData[0].Unit, 'Milliseconds');
    });
});

describe('handleCompanionMessage - NEW_COMPANION_MESSAGE', () => {
    let addMetricForQueryTimeStub;

    beforeEach(() => {
        sinon.stub(constantModel, 'get').resolves({});
        addMetricForQueryTimeStub = sinon.stub(CloudWatchClient.prototype, 'send').resolves();
    });

    afterEach(() => {
        sinon.restore();
    });

    it('should return 200 and stream AI response', async () => {
        const chatStreamStub = sinon.stub(ChatOpenAI.prototype, 'stream');
        const chatInvokeStub = sinon.stub(ChatOpenAI.prototype, 'invoke');
        const sendChatStub = sinon.stub(Chat.prototype, 'sendChat');
        const event = {
            body: {
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_COMPANION_MESSAGE,
                senderId: 'user-1',
                message: 'Hi',
                context: [{ senderId: 'user-1', message: 'Hi' }],
                aiMessageId: 'ai-123'
            },
            requestContext: { connectionId: 'conn-1' }
        };
        sinon.stub(userModel, 'query').returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().resolves([
                        { id: 'user-1', firstName: 'Test', lastName: 'User', email: '<EMAIL>', children: [] }
                    ])
                })
            })
        });
        sendChatStub.resolves({
            chatResponse: {
                answer: 'AI response chunk',
                context: [{ source: 'test', metadata: { 'organizationid': 'abc' } }]
            }
        });
        chatStreamStub.resolves({
            async *[Symbol.asyncIterator] () {
                yield { content: 'AI response chunk' };
            }
        });
        chatInvokeStub.resolves({
            content: `[
            {
            "childName": "Aarav Sharma",
            "associatedOrganizations": [
                { "id": "1", "name": "Organization Name" },
                { "id": "2", "name": "Another Org" }
            ]
            }
        ]`
        });

        const sendMessagesStub = sinon.stub(SendMessageService, 'sendMessageToConnection').resolves();
        const response = await handleCompanionMessage(event);
        assert.equal(response.statusCode, 200);
        assert.equal(response.message, 'AI response generated successfully');
        assert.equal(response.actionType, CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_COMPANION_MESSAGE);
        sinon.assert.calledOnceWithMatch(
            sendMessagesStub,
            {
                connectionId: event.requestContext.connectionId,
                messageData: {
                    data: {
                        id: event.body.aiMessageId,
                        senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
                        message: 'AI response chunk'
                    },
                    action: 'sendCompanionMessage',
                    actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.AI_COMPANION_MESSAGE_STREAMING
                }
            }
        );

        sinon.assert.calledOnce(addMetricForQueryTimeStub);
        const commandArg = addMetricForQueryTimeStub.firstCall.args[0];
        expect(commandArg).to.be.instanceOf(PutMetricDataCommand);
        assert.equal(commandArg.input.Namespace, 'Vaalee/AICompanion');
        assert.equal(commandArg.input.MetricData[0].MetricName, CONSTANTS.CLOUDWATCH_METRIC_NAMES.AI_COMPANION_QUERY_TIME);
        assert.equal(commandArg.input.MetricData[0].Unit, 'Milliseconds');
    });

    it('should return 200 and stream AI response if aiMessageId is not provided', async () => {
        const chatStreamStub = sinon.stub(ChatOpenAI.prototype, 'stream');
        const sendChatStub = sinon.stub(Chat.prototype, 'sendChat');
        const chatInvokeStub = sinon.stub(ChatOpenAI.prototype, 'invoke');
        const event = {
            body: {
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_COMPANION_MESSAGE,
                senderId: 'user-1',
                message: 'Hi',
                context: [{ senderId: 'user-1', message: 'Hi' }]
            },
            requestContext: { connectionId: 'conn-1' }
        };
        sinon.stub(userModel, 'query').returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().resolves([
                        { id: 'user-1', firstName: 'Test', lastName: 'User', email: '<EMAIL>', children: [] }
                    ])
                })
            })
        });
        sendChatStub.resolves({
            chatResponse: {
                answer: 'AI response chunk',
                context: [{ source: 'test', metadata: { 'organizationid': 'abc' } }]
            }
        });
        chatStreamStub.resolves({
            async *[Symbol.asyncIterator] () {
                yield { content: 'AI response chunk 2' };
            }
        });
        chatInvokeStub.resolves({
            content: `[
            {
            "childName": "Aarav Sharma",
            "associatedOrganizations": [
                { "id": "1", "name": "Organization Name" },
                { "id": "2", "name": "Another Org" }
            ]
            }
        ]`
        });


        const sendMessagesStub = sinon.stub(SendMessageService, 'sendMessageToConnection').resolves();
        const response = await handleCompanionMessage(event);
        assert.equal(response.statusCode, 200);
        assert.equal(response.message, 'AI response generated successfully');
        assert.equal(response.actionType, CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_COMPANION_MESSAGE);
        sinon.assert.calledWithMatch(
            sendMessagesStub,
            {
                connectionId: event.requestContext.connectionId,
                messageData: {
                    data: {
                        id: response.data.id,
                        senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
                        message: 'AI response chunk 2'
                    },
                    action: 'sendCompanionMessage',
                    actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.AI_COMPANION_MESSAGE_STREAMING
                }
            }
        );

        sinon.assert.calledOnce(addMetricForQueryTimeStub);
        const commandArg = addMetricForQueryTimeStub.firstCall.args[0];
        expect(commandArg).to.be.instanceOf(PutMetricDataCommand);
        assert.equal(commandArg.input.Namespace, 'Vaalee/AICompanion');
        assert.equal(commandArg.input.MetricData[0].MetricName, CONSTANTS.CLOUDWATCH_METRIC_NAMES.AI_COMPANION_QUERY_TIME);
        assert.equal(commandArg.input.MetricData[0].Unit, 'Milliseconds');
    });

    it('should return error, if stream is not configured properly', async () => {
        const chatStreamStub = sinon.stub(ChatOpenAI.prototype, 'stream');
        const sendChatStub = sinon.stub(Chat.prototype, 'sendChat');
        const chatInvokeStub = sinon.stub(ChatOpenAI.prototype, 'invoke');

        const event = {
            body: {
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_COMPANION_MESSAGE,
                senderId: 'user-1',
                message: 'Hi',
                context: [{ senderId: 'user-1', message: 'Hi' }],
                aiMessageId: 'ai-123'
            },
            requestContext: { connectionId: 'conn-1' }
        };
        chatInvokeStub.resolves({
            content: '[]'
        });
        sendChatStub.resolves({
            chatResponse: {
                answer: 'Hello',
                context: [{ source: 'test', metadata: { 'organizationid': 'abc' } }]
            }
        });
        sinon.stub(userModel, 'query').returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().resolves([
                        { id: 'user-1', firstName: 'Test', lastName: 'User', email: '<EMAIL>', children: [] }
                    ])
                })
            })
        });
        chatStreamStub.resolves(null);
        const sendMessageToConnectionStub = sinon.stub(SendMessageService, 'sendMessageToConnection').resolves();

        const response = await handleCompanionMessage(event);

        sinon.assert.calledWithMatch(
            sendMessageToConnectionStub,
            {
                connectionId: event.requestContext.connectionId,
                statusCode: 200,
                messageData: {
                    data: {
                        id: event.body.aiMessageId,
                        senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
                        userQueryMessageId: event.body.messageId,
                        message: 'AI service stream configuration error',
                        groupId: event.body.groupId
                    },
                    actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.AI_COMPANION_MESSAGE_STREAMING,
                    action: 'sendCompanionMessage'
                }
            }
        );
        assert.equal(response.statusCode, 200);
        assert.equal(response.message, 'AI service stream configuration error');

        sinon.assert.calledOnce(addMetricForQueryTimeStub);
        const commandArg = addMetricForQueryTimeStub.firstCall.args[0];
        expect(commandArg).to.be.instanceOf(PutMetricDataCommand);
        assert.equal(commandArg.input.Namespace, 'Vaalee/AICompanion');
        assert.equal(commandArg.input.MetricData[0].MetricName, CONSTANTS.CLOUDWATCH_METRIC_NAMES.AI_COMPANION_QUERY_TIME);
        assert.equal(commandArg.input.MetricData[0].Unit, 'Milliseconds');
    });

    it('should return 500 if fetching user details fails', async () => {
        const event = {
            body: {
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_COMPANION_MESSAGE,
                senderId: 'user-1',
                message: 'Hi',
                context: { messages: [{ senderId: 'user-1', message: 'Hi' }] },
                aiMessageId: 'ai-123'
            },
            requestContext: { connectionId: 'conn-1' }
        };
        sinon.stub(userModel, 'query').returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().rejects(new Error('User fetch failed'))
                })
            })
        });
        sinon.stub(SendMessageService, 'sendMessageToConnection').resolves();
        const response = await handleCompanionMessage(event);
        assert.equal(response.statusCode, 500);
        assert.equal(response.body, 'User fetch failed');

        sinon.assert.calledOnce(addMetricForQueryTimeStub);
        const commandArg = addMetricForQueryTimeStub.firstCall.args[0];
        expect(commandArg).to.be.instanceOf(PutMetricDataCommand);
        assert.equal(commandArg.input.Namespace, 'Vaalee/AICompanion');
        assert.equal(commandArg.input.MetricData[0].MetricName, CONSTANTS.CLOUDWATCH_METRIC_NAMES.AI_COMPANION_QUERY_TIME);
        assert.equal(commandArg.input.MetricData[0].Unit, 'Milliseconds');
    });

    it('should return 500 if handler throws unexpectedly', async () => {
        const chatStreamStub = sinon.stub(ChatOpenAI.prototype, 'stream');
        const sendChatStub = sinon.stub(Chat.prototype, 'sendChat');
        const chatInvokeStub = sinon.stub(ChatOpenAI.prototype, 'invoke');
        const event = {
            body: {
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_COMPANION_MESSAGE,
                senderId: 'user-1',
                message: 'Hi',
                context: [{ senderId: 'user-1', message: 'Hi' }],
                aiMessageId: 'ai-123'
            },
            requestContext: { connectionId: 'conn-1' }
        };
        sinon.stub(userModel, 'query').returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().resolves([
                        { id: 'user-1', firstName: 'Test', lastName: 'User', email: '<EMAIL>', children: [] }
                    ])
                })
            })
        });
        sendChatStub.resolves({
            chatResponse: {
                answer: 'AI response chunk',
                context: [{ source: 'test', metadata: { 'organizationid': 'abc' } }]
            }
        });
        chatStreamStub.resolves({
            async *[Symbol.asyncIterator] () {
                yield { content: 'AI response chunk' };
            }
        });
        chatInvokeStub.resolves({
            content: `[
            {
            "childName": "Aarav Sharma",
            "associatedOrganizations": [
                { "id": "1", "name": "Organization Name" },
                { "id": "2", "name": "Another Org" }
            ]
            }
        ]`
        });
        sinon.stub(SendMessageService, 'sendMessageToConnection').throws(new Error('Send failed'));
        const response = await handleCompanionMessage(event);
        assert.equal(response.statusCode, 500);
        assert.equal(response.body, 'Send failed');

        sinon.assert.calledOnce(addMetricForQueryTimeStub);
        const commandArg = addMetricForQueryTimeStub.firstCall.args[0];
        expect(commandArg).to.be.instanceOf(PutMetricDataCommand);
        assert.equal(commandArg.input.Namespace, 'Vaalee/AICompanion');
        assert.equal(commandArg.input.MetricData[0].MetricName, CONSTANTS.CLOUDWATCH_METRIC_NAMES.AI_COMPANION_QUERY_TIME);
        assert.equal(commandArg.input.MetricData[0].Unit, 'Milliseconds');
    });

    it('should handle missing context/messages gracefully', async () => {
        const chatStreamStub = sinon.stub(ChatOpenAI.prototype, 'stream');
        const sendChatStub = sinon.stub(Chat.prototype, 'sendChat');
        const chatInvokeStub = sinon.stub(ChatOpenAI.prototype, 'invoke');
        const event = {
            body: {
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_COMPANION_MESSAGE,
                senderId: 'user-1',
                message: 'Hi',
                context: {},
                aiMessageId: 'ai-123'
            },
            requestContext: { connectionId: 'conn-1' }
        };
        sinon.stub(userModel, 'query').returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().resolves([
                        { id: 'user-1', firstName: 'Test', lastName: 'User', email: '<EMAIL>', children: [] }
                    ])
                })
            })
        });
        sendChatStub.resolves({
            chatResponse: {
                answer: 'AI response chunk',
                context: [{ source: 'test', metadata: { 'organizationid': 'abc' } }]
            }
        });
        chatStreamStub.resolves({
            async *[Symbol.asyncIterator] () {
                yield { content: 'AI response chunk' };
            }
        });
        chatInvokeStub.resolves({
            content: `[
            {
            "childName": "Aarav Sharma",
            "associatedOrganizations": [
                { "id": "1", "name": "Organization Name" },
                { "id": "2", "name": "Another Org" }
            ]
            }
        ]`
        });
        const sendMessagesStub = sinon.stub(SendMessageService, 'sendMessageToConnection').resolves();
        const response = await handleCompanionMessage(event);
        assert.equal(response.statusCode, 200);
        sinon.assert.calledWithMatch(
            sendMessagesStub,
            {
                connectionId: event.requestContext.connectionId,
                messageData: {
                    data: {
                        id: event.body.aiMessageId,
                        senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
                        message: 'AI response chunk'
                    },
                    action: 'sendCompanionMessage',
                    actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.AI_COMPANION_MESSAGE_STREAMING
                }
            }
        );

        sinon.assert.calledOnce(addMetricForQueryTimeStub);
        const commandArg = addMetricForQueryTimeStub.firstCall.args[0];
        expect(commandArg).to.be.instanceOf(PutMetricDataCommand);
        assert.equal(commandArg.input.Namespace, 'Vaalee/AICompanion');
        assert.equal(commandArg.input.MetricData[0].MetricName, CONSTANTS.CLOUDWATCH_METRIC_NAMES.AI_COMPANION_QUERY_TIME);
        assert.equal(commandArg.input.MetricData[0].Unit, 'Milliseconds');
    });

    it('should call the chat method only once when child query plans array is empty', async () => {
        const chatStreamStub = sinon.stub(ChatOpenAI.prototype, 'stream');
        const sendChatStub = sinon.stub(Chat.prototype, 'sendChat');
        const chatInvokeStub = sinon.stub(ChatOpenAI.prototype, 'invoke');
        const event = {
            body: {
                actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_COMPANION_MESSAGE,
                senderId: 'user-1',
                message: 'Hi',
                context: [{ senderId: 'user-1', message: 'Hi' }]
            },
            requestContext: { connectionId: 'conn-1' }
        };
        sinon.stub(userModel, 'query').returns({
            eq: sinon.stub().returns({
                attributes: sinon.stub().returns({
                    exec: sinon.stub().resolves([
                        { id: 'user-1', firstName: 'Test', lastName: 'User', email: '<EMAIL>', children: [] }
                    ])
                })
            })
        });
        sendChatStub.resolves({
            chatResponse: {
                answer: 'AI response chunk',
                context: [{ source: 'test', metadata: { 'organizationid': 'abc' } }]
            }
        });
        chatStreamStub.resolves({
            async *[Symbol.asyncIterator] () {
                yield { content: 'AI response chunk' };
            }
        });
        chatInvokeStub.resolves({
            content: '[]'
        });


        const sendMessagesStub = sinon.stub(SendMessageService, 'sendMessageToConnection').resolves();
        const response = await handleCompanionMessage(event);
        assert.equal(chatInvokeStub.callCount, 1);
        assert.equal(response.statusCode, 200);
        assert.equal(response.message, 'AI response generated successfully');
        assert.equal(response.actionType, CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.NEW_COMPANION_MESSAGE);
        sinon.assert.calledWithMatch(
            sendMessagesStub,
            {
                connectionId: event.requestContext.connectionId,
                messageData: {
                    data: {
                        id: response.data.id,
                        senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
                        message: 'AI response chunk'
                    },
                    action: 'sendCompanionMessage',
                    actionType: CONSTANTS.ACTION_TYPES.SEND_COMPANION_MESSAGE.AI_COMPANION_MESSAGE_STREAMING
                }
            }
        );

        sinon.assert.calledOnce(addMetricForQueryTimeStub);
        const commandArg = addMetricForQueryTimeStub.firstCall.args[0];
        expect(commandArg).to.be.instanceOf(PutMetricDataCommand);
        assert.equal(commandArg.input.Namespace, 'Vaalee/AICompanion');
        assert.equal(commandArg.input.MetricData[0].MetricName, CONSTANTS.CLOUDWATCH_METRIC_NAMES.AI_COMPANION_QUERY_TIME);
        assert.equal(commandArg.input.MetricData[0].Unit, 'Milliseconds');
    });
});
