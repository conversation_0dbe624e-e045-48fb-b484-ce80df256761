/**
 *  routes and schema for Payment
 */

/**
 * @openapi
 * components:
 *  schemas:
 *      checkoutSession:
 *          type: object
 *          required:
 *              - eventId
 *              - childId
 *              - paymentType
 *          properties:
 *              eventId:
 *                  type: string
 *                  description: id of the event
 *              childId:
 *                  type: string
 *                  description: id of the children
 *              paymentType:
 *                  type: string
 *                  description: payment type, can be one of the following; stripe, cash, cheque, venmo, free
 *              quantity:
 *                  type: number
 *                  description: number of Items/People to register
 *              isCoveredByUser:
 *                  type: boolean
 *                  description: whether the fees are covered by the user or not
 *          example:
 *                   eventId: eventId
 *                   childId: childId
 *                   paymentType: paymentType
 *                   quantity: 1
 *
 *      successCheckoutSession:
 *          type: object
 *          properties:
 *              status:
 *                  type: number
 *                  description: status if data exists
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: You have successfully registered for the event!
 */

/**
 * @openapi
 * /event/register:
 *  post:
 *      security:
 *        - bearerAuth: []
 *      tags: [Register]
 *      summary: Register for an event with payment api to pay for an event
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/checkoutSession'
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successCheckoutSession'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */
