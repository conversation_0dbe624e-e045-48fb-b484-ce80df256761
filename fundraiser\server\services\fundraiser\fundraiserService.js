/* eslint-disable max-len */
const FundraiserValidator = require('./fundraiserValidator');
const { v4: uuidv4 } = require('uuid');
const UploadService = require('../../util/uploadService');
const Fundraiser = require('../../models/fundraiser.model');
const Organization = require('../../models/organization.model');
const OrganizationMember = require('../../models/organizationMember.model');
const ChildOrganizationMapping = require('../../models/childOrganizationMapping.model');
const EmailService = require('../../util/sendEmail');
const FundraiserSignup = require('../../models/fundraiserSignup.model');
const Children = require('../../models/child.model');
const User = require('../../models/user.model');
const Validator = require('../../util/validation');
const GeneralError = require('../../util/GeneralError');
const AwsOpenSearchService = require('../../util/opensearch');
const FundraiserRegisterService = require('../register/fundraiserRegisterService');
const Utils = require('../../util/utilFunctions');
/**
 * Class represents services for signin.
 */
class FundraiserService {
    /**
   * @desc This function is being used to add fundraiser
   * @param {Object} req Request
   * @param {Object} req.body RequestBody
   * @param {String} req.body.fundraiserType fundraiserType - Event / Fundraiser
   * @param {String} req.body.title fundraiserTitle
   * @param {String} req.body.description fundraiserDescription
   * @param {String} req.body.status status
   * @param {String} req.body.startDate startDate
   * @param {String} req.body.endDate endDate
   * @param {String} req.body.membershipBenefitDetails membershipBenefitDetails
   * @param {File} req.body.image fundraiser image
   * @param {Object} user User
   * @param {Object} res Response
   */
    static async addFundraiser (req, user, locale) {
        const Validator = new FundraiserValidator(req.body, locale, req.files);

        Validator.validateAddFundraiser();

        const {
            fundraiserType,
            title,
            description,
            status,
            startDate,
            endDate,
            membershipBenefitDetails,
            organizationId,
            boosterGoal,
            boosterMessageForChild,
            boosterGoalForChild,
            bannerImageName,
            eventId
        } = req.body;

        const orgAssociation = await this.validateOrganizationAssociation(
            organizationId,
            user
        );

        const startDateUTC = MOMENT.utc(`${startDate}`, 'MM/DD/YYYY', true);

        const endDateUTC = MOMENT.utc(`${endDate}`, 'MM/DD/YYYY', true);

        await this.validateDate(startDateUTC, endDateUTC, locale);

        const uuid = eventId ?? uuidv4();

        // update products with image filenames.
        const products = req.body.products ? JSON.parse(req.body.products) : [];
        const productsStr = JSON.stringify(products);
        let membershipBenefitDetailsParsed;
        if (membershipBenefitDetails) {
            membershipBenefitDetailsParsed = JSON.parse(membershipBenefitDetails);
            membershipBenefitDetailsParsed.benefitDiscount = JSON.stringify(
                membershipBenefitDetailsParsed.benefitDiscount
            );
        }

        try {
            const event = await Fundraiser.create({
                id: uuid,
                createdBy: user.id,
                imageURL: bannerImageName,
                status:
                orgAssociation.associatedOrgRole ===
                CONSTANTS.ORG_ROLE.SUPER_ADMIN
                    ? status
                    : 'draft',
                organizationId: organizationId.trim(),
                title: title.trim(),
                fundraiserType:
                fundraiserType && fundraiserType.trim()
                    ? fundraiserType.trim()
                    : undefined,
                description: description.trim(),
                products: productsStr,
                startDate: startDateUTC.toDate(),
                endDate: endDateUTC.toDate(),
                membershipBenefitDetails: membershipBenefitDetails
                    ? membershipBenefitDetailsParsed
                    : undefined,
                boosterGoal: Utils.isEmpty(boosterGoal) ? 0 : this.parseNumber(boosterGoal),
                boosterMessageForChild,
                boosterGoalForChild: Utils.isEmpty(boosterGoalForChild) ? undefined : this.parseNumber(boosterGoalForChild)
            });
            if (event.status === CONSTANTS.STATUS.PUBLISHED) {
                await this.insertEventsInChildFeeds(event);
            }
            return event.id;
        } catch (error) {
            await this.handleFileDeletion(bannerImageName);
            throw error;
        }
    }

    static parseNumber (value) {
        if (typeof value === 'number') {
            return value;
        }
        return value ? Number(value.trim()) : undefined;
    }

    static parseBoolean (value) {
        return value.trim() === 'true';
    }

    /**
   * @desc This function is being used to update product
   * <AUTHOR>
   * @since 16/11/2023
   * @param {Object} req Request
   * @param {Object} user User
   * @param {Object} locale locale
   */
    static async updateFundraiser (req, user, locale) {
        const validator = new FundraiserValidator(req.body, locale, req.files);
        validator.validateUpdateFundraiser();
        const {
            eventId,
            fundraiserType,
            title,
            description,
            status,
            startDate,
            endDate,
            documentURLs,
            membershipBenefitDetails,
            boosterGoal,
            boosterGoalForChild,
            boosterMessageForChild,
            bannerImageName
        } = req.body;
        const products = req.body.products ? JSON.parse(req.body.products) : [];
        const event = await Fundraiser.get({ id: eventId });

        await this.validateEvent(event, status);

        const orgAssociation = await this.validateOrganizationAssociation(
            event.organizationId,
            user
        );

        const startDateUTC = MOMENT.utc(
            new Date(`${startDate} 00:00:00`),
            'MM/DD/YYYY HH:mm',
            true
        );
        const endDateUTC = MOMENT.utc(
            new Date(`${endDate} 23:59:59`),
            'MM/DD/YYYY HH:mm',
            true
        );
        await this.validateDate(startDateUTC, endDateUTC, locale);

        const newDocumentURLs = [];

        await this.handleDocumentDeletion(documentURLs, event, newDocumentURLs);

        try {
            event.updatedBy = user.id;
            event.imageURL = bannerImageName ? bannerImageName : event.imageURL;
            event.description = description;
            event.products = JSON.stringify(products);
            event.startDate = startDateUTC.toDate();
            event.endDate = endDateUTC.toDate();
            event.title = title;
            event.fundraiserType = fundraiserType;
            event.status =
            orgAssociation.associatedOrgRole === CONSTANTS.ORG_ROLE.SUPER_ADMIN
                ? status
                : 'draft';
            event.boosterGoal = Utils.isEmpty(boosterGoal) ? 0 : this.parseNumber(boosterGoal);
            event.boosterGoalForChild = Utils.isEmpty(boosterGoalForChild) ? undefined : this.parseNumber(boosterGoalForChild);
            event.boosterMessageForChild = boosterMessageForChild;
            if (membershipBenefitDetails) {
                const newMembershipBenefitDetailsParsed = JSON.parse(
                    membershipBenefitDetails
                );
                newMembershipBenefitDetailsParsed.benefitDiscount = JSON.stringify(
                    newMembershipBenefitDetailsParsed.benefitDiscount
                );
                event.membershipBenefitDetails = newMembershipBenefitDetailsParsed;
            }
            const updatedEvent = await event.save();

            const formattedEvent = await this.eventSearchFormattedData(updatedEvent);

            await AwsOpenSearchService.updateField(
                CONSTANTS.OPEN_SEARCH.COLLECTION.FUNDRAISER,
                updatedEvent.id,
                formattedEvent
            );
        } catch (error) {
            if (bannerImageName) {
                await this.handleFileDeletion(bannerImageName);
            }
            throw error;
        }
    }

    static async insertEventsInChildFeeds (event) {
        const formattedEvent = await this.eventSearchFormattedData(event);
        await AwsOpenSearchService.create(
            CONSTANTS.OPEN_SEARCH.COLLECTION.FUNDRAISER,
            event.id,
            formattedEvent
        );
        await this.insertChildFeedsInChildren(event);
    }

    static async eventSearchFormattedData (event) {
        return {
            id: event.id,
            title: event.title,
            fundraiserType: event.fundraiserType,
            description: event.description,
            startDate: event.startDate,
            endDate: event.endDate,
            imageURL: event.imageURL,
            organizationId: event.organizationId,
            status: event.status,
            products: event.products,
            membershipBenefitDetails: event.membershipBenefitDetails
        };
    }

    static async insertChildFeedsInChildren (event) {
        const children = await ChildOrganizationMapping.query('organizationId')
            .eq(event.organizationId)
            .exec();
        for (const child of children) {
            await AwsOpenSearchService.registerEventInChildFeeds(
                CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD,
                child.childId,
                event.id
            );
        }
    }

    /**
   * @desc This function is being used to validate organization association
   * <AUTHOR>
   * @since 14/12/2023
   * @param {String} organizationId organization id
   */
    static async validateOrganizationAssociation (organizationId, user) {
        const orgMembers = await OrganizationMember.get(organizationId);
        if (!orgMembers) {
            throw {
                message: MESSAGES.UNAUTHORIZED_ACTION_FOR_ORGANIZATION,
                statusCode: 400
            };
        }

        const orgAssociation = orgMembers.users.find(
            (member) => member.id === user.id
        );
        if (!orgAssociation) {
            throw {
                message: MESSAGES.UNAUTHORIZED_ACTION_FOR_ORGANIZATION,
                statusCode: 400
            };
        }
        if (orgAssociation.status !== CONSTANTS.STATUS.ACTIVE) {
            throw {
                message: MESSAGES.UNAUTHORIZED_ACTION_FOR_ORGANIZATION,
                statusCode: 400
            };
        }
        return orgAssociation;
    }

    static async handleFileDeletion (fileNames) {
        if (!fileNames) {
            return;
        }

        for (const fileName of fileNames) {
            await UploadService.deleteObject(fileName);
        }
    }

    static async validateDate (startDate, endDate, locale) {
        const isValidDate = (date) => MOMENT(date, 'MM/DD/YYYY', true).isValid();

        if (!isValidDate(startDate) || !isValidDate(endDate)) {
            throw new GeneralError(
                locale(MESSAGES.DATE_FORMAT_ERROR, 'Start or End Date'),
                400
            );
        }
        if (endDate.isBefore(startDate)) {
            throw new GeneralError(MESSAGES.EVENT_END_DATE_BEFORE_START_DATE, 400);
        }
    }

    static async validateEvent (event, status) {
        if (!event) {
            throw {
                message: MESSAGES.EVENT_NOT_FOUND,
                statusCode: 404
            };
        }
        if (event.status === 'published' && status === 'unpublished') {
            throw new GeneralError(MESSAGES.EVENT_STATUS_CANT_CHANGE, 400);
        }
    }

    static async handleDocumentDeletion (documentURLs, event, newDocumentURLs) {
    // delete documents and image not in request
        if (documentURLs && event.details.documentURLs) {
            for (const document of JSON.parse(documentURLs)) {
                const url = decodeURI(document);
                const baseUrl = url.split('?')[0];
                const fileName = baseUrl.split('amazonaws.com/')[1];
                newDocumentURLs.push(fileName);
            }

            const documentToDelete = _.difference(
                [...event.details.documentURLs],
                newDocumentURLs
            );
            for (const file of documentToDelete) {
                await UploadService.deleteObject(file);
                event.details.documentURLs.delete(file);
            }
        }
    }

    /**
   * @desc This function is being used to get list of Events
   * <AUTHOR>
   * @since 15/11/2023
   */
    static async getFundraiserList (req, user) {
        const { status, organizationId } = req.query;

        const eventAttributes = [
            'id',
            'title',
            'fundraiserType',
            'description',
            'status',
            'boosterGoal',
            'startDate',
            'endDate',
            'isDeleted',
            'updatedAt',
            'createdAt'
        ];

        let events;

        if (organizationId) {
            await this.validateOrganizationAssociation(organizationId, user);

            const query = Fundraiser.query('organizationId')
                .eq(organizationId.trim())
                .using('organizationId-index')
                .attributes(eventAttributes);

            if (
                status &&
        CONSTANTS.ALLOWED_EVENT_STATUS.includes(status.toLowerCase().trim())
            ) {
                events = await query
                    .where('status')
                    .eq(status.toLowerCase().trim())
                    .exec();
            } else {
                events = await query.exec();
            }
        } else {
            const queryPromises = user.associatedOrganizations.map(async (org) => {
                const orgMembers = await OrganizationMember.get(org.organizationId);

                const orgAssociation = orgMembers?.users?.find(
                    (member) => member.id === user.id
                );
                if (orgAssociation?.status === CONSTANTS.STATUS.ACTIVE) {
                    const query = Fundraiser.query('organizationId')
                        .eq(org.organizationId)
                        .using('organizationId-index')
                        .attributes(eventAttributes);

                    if (
                        status &&
            CONSTANTS.ALLOWED_EVENT_STATUS.includes(status.toLowerCase().trim())
                    ) {
                        return query.where('status').eq(status.toLowerCase().trim()).exec();
                    } else {
                        return query.exec();
                    }
                } else {
                    return null;
                }
            });

            const eventsArrays = await Promise.all(queryPromises);
            events = eventsArrays.filter(Boolean).flatMap((eventArray) => eventArray);
        }
        events.sort((a, b) => b.createdAt - a.createdAt);
        return events.length === 0 ? [] : this.formatEvents(events);
    }

    /**
   * @desc This function is being used to get list of Events participants
   * <AUTHOR>
   * @since 20/11/2023
   */
    static async getParticipantFundraiserList (req, user, locale) {
        const { eventId } = req.query;
        const validator = new Validator(locale);
        validator.field(eventId, 'Event Id');

        const event = await Fundraiser.get({ id: eventId });

        if (!event) {
            throw {
                message: MESSAGES.EVENT_NOT_FOUND,
                statusCode: 400
            };
        }

        await this.validateOrganizationAssociation(event.organizationId, user);

        let fundraiserSignups = await FundraiserSignup.query('eventId')
            .eq(eventId)
            .using('eventId-index')
            .where('organizationId')
            .eq(event.organizationId)
            .exec();

        fundraiserSignups = fundraiserSignups.filter(event => event.paymentDetails.paymentStatus !== 'payment-initiated');

        return fundraiserSignups.length === 0
            ? {
                title: event.title
            }
            : {
                title: event.title,
                products: event.products,
                membershipBenefitDetails: event.membershipBenefitDetails,
                signups: await this.formatSignupFundraiser(
                    fundraiserSignups,
                    event.products,
                    event.membershipBenefitDetails
                )
            };
    }

    /**
   * @desc This function is being used to return formatted signup events
   * <AUTHOR>
   * @since 20/11/2023
   * @param {Array} events array of participated events
   */
    static async formatSignupFundraiser (events, eventProducts, membershipBenefitDetails) {
        const filteredEvents = events.filter(({ isDeleted }) => !isDeleted);

        const childIds = [...new Set(filteredEvents.map(event => event.childId))];
        let childrenLookup = {};
        let homeroomLookup = {};
        if (childIds.length === 0) {
            return [];
        }
        const CHUNK_SIZE = 100;
        // Batch get all children details
        for (let i = 0; i < childIds.length; i += CHUNK_SIZE) {
            const childrenDetails = await Children.batchGet(childIds.slice(i, i + CHUNK_SIZE).map(id => ({ id })), { attributes: ['id', 'homeRoom', 'firstName', 'lastName'] });
            childrenLookup = childrenDetails.reduce((acc, child) => {
                acc[child.id] = child;
                return acc;
            }, {});
        }

        // Get all unique organizationIds from children
        const homeroomIds = [...new Set(Object.values(childrenLookup).map(child => child.homeRoom).filter(Boolean))];
        if (homeroomIds.length > 0) {
            // Batch get all organization details without doing it in chunks because homerooms cannot be more than 100 for one organization
            const homerooms = await Organization.batchGet(homeroomIds.map(id => ({ id })), { attributes: ['id', 'name'] });
            homeroomLookup = homerooms.reduce((acc, org) => {
                acc[org.id] = org;
                return acc;
            }, {});
        }

        let parsedEventProducts = [];
        try {
            parsedEventProducts = JSON.parse(eventProducts);
        } catch (error) {
            CONSOLE_LOGGER.error('Failed to parse eventProducts: ', error, eventProducts);
        }

        const eventProductLookup = parsedEventProducts.reduce((acc, ep) => {
            ep.optionPrices.forEach(op => {
                acc[op.id] = { ...ep, optionPrice: op };
            });
            return acc;
        }, {});

        const signups = await Promise.all(
            filteredEvents.map(async (event) => {
                const primaryParent = await User.query('id')
                    .eq(event.parentId)
                    .attributes(['firstName', 'lastName'])
                    .exec();
                const childDetails = childrenLookup[event.childId];

                if (!childDetails) {
                    return null;
                }

                const homeroom = homeroomLookup[childDetails.homeRoom];
                const homeroomName = homeroom?.name || '';

                let parsedPurchasedProducts = [];
                try {
                    parsedPurchasedProducts = JSON.parse(event.purchasedProducts);
                } catch (error) {
                    CONSOLE_LOGGER.error('Failed to parse purchasedProducts: ', error, event.purchasedProducts);
                }

                const enrichedPurchasedProducts = parsedPurchasedProducts.map((purchasedProduct) => {
                    const eventProductEntry = eventProductLookup[purchasedProduct.id];

                    if (!eventProductEntry) {
                        return purchasedProduct;
                    }

                    const { optionPrice, options } = eventProductEntry;
                    const optionDetails = [];

                    Object.keys(optionPrice).forEach((key) => {
                        if (!['id', 'itemName', 'itemCost'].includes(key)) {
                            const option = options.find((o) => o.name.toLowerCase() === key.toLowerCase());
                            if (option) {
                                optionDetails.push({
                                    name: key.charAt(0).toUpperCase() + key.slice(1),
                                    selectedVariant: optionPrice[key]
                                });
                            }
                        }
                    });

                    return {
                        ...purchasedProduct,
                        optionDetails
                    };
                });

                let enrichedPurchasedProductsStr = '{}';
                try {
                    enrichedPurchasedProductsStr = JSON.stringify(enrichedPurchasedProducts);
                } catch (error) {
                    CONSOLE_LOGGER.error('Failed to stringify enrichedPurchasedProducts: ', error);
                }

                let totalAmount = 0;

                totalAmount = parsedPurchasedProducts.reduce((acc, product) => {
                    return acc + (product.itemCost * product.quantity);
                }, 0);

                const transactionFee = event.paymentDetails.transactionFee || 0;
                const platformFeeCoveredBy = event.paymentDetails.platformFeeCoveredBy || '';
                const totalEventFee = totalAmount || 0;
                const discountedEventAmount = event.paymentDetails.isOnlyForMembers
                    ? 0
                    : membershipBenefitDetails?.benefitDiscount
                        ? totalEventFee - (event.paymentDetails.membershipDiscount ?? 0)
                        : totalEventFee;
                const membershipBenefitDiscountAmount = event.paymentDetails
                    .isOnlyForMembers
                    ? 0
                    : membershipBenefitDetails?.benefitDiscount
                        ? (event.paymentDetails.membershipDiscount ?? 0)
                        : 0;

                return {
                    id: event.id,
                    childId: event.childId,
                    childName: `${childDetails.firstName} ${childDetails.lastName}`,
                    parentName: `${primaryParent[0]?.firstName} ${primaryParent[0]?.lastName}`,
                    homeroomName,
                    paymentStatus: event.paymentDetails.paymentStatus,
                    paymentType:
                        event.paymentDetails.paymentType === 'cheque'
                            ? 'check'
                            : event.paymentDetails.paymentType,
                    products: enrichedPurchasedProductsStr,
                    signedUpAt: MOMENT(event.createdAt),
                    totalAmount: totalEventFee,
                    totalAmountWithDiscount: discountedEventAmount,
                    membershipBenefitDiscount: membershipBenefitDiscountAmount,
                    platformFeeCoveredBy,
                    transactionFee,
                    isFulfilled: event.isFulfilled
                };
            })
        );
        return signups.filter(Boolean);
    }

    /**
   * @desc This function is being used to get event details
   * <AUTHOR>
   * @since 15/11/2023
   * @param {Object} req Request
   * @param {Object} locale Locale passed from request
   */
    static async getFundraiserDetails (req, user, locale) {
        const Validator = new FundraiserValidator(
            req.body,
            locale,
            null,
            req.query
        );
        Validator.validateEventId();

        const { eventId } = req.query;
        const eventAttributes = [
            'id',
            'title',
            'fundraiserType',
            'description',
            'status',
            'imageURL',
            'startDate',
            'endDate',
            'products',
            'boosterGoal',
            'isDeleted',
            'membershipBenefitDetails',
            'organizationId',
            'boosterMessageForChild',
            'boosterGoalForChild'
        ];

        const eventDetails = await Fundraiser.get(
            { id: eventId },
            {
                attributes: eventAttributes
            }
        );

        if (!eventDetails || eventDetails.isDeleted) {
            throw {
                message: MESSAGES.EVENT_NOT_FOUND,
                statusCode: 400
            };
        }

        await this.validateOrganizationAssociation(
            eventDetails.organizationId,
            user
        );
        // create object of products array
        const productsObj = JSON.parse(eventDetails.products);
        // for each product object, map through imageURLS and get signed URLS;
        for (let i = 0; i < productsObj.length; i++) {
            const product = productsObj[i];
            const signedURLs = [];
            for (let j = 0; j < product.images?.length; j++) {
                const imageURL = product.images[j];
                if (imageURL) {
                    // sometimes nulls are added to the array
                    const signedURL = await UploadService.getSignedUrl(imageURL);
                    signedURLs.push(signedURL);
                }
            }
            product.imageURLs = signedURLs;
        }
        eventDetails.products = JSON.stringify(productsObj);

        const formattedEvent = this.formatEvents([eventDetails])[0];

        return {
            ...formattedEvent,
            imageUrl: eventDetails.imageURL
                ? await UploadService.getSignedUrl(eventDetails.imageURL)
                : '',
            canBeDeleted: formattedEvent.status === 'draft'
        };
    }

    /**
   * @desc This function is being used to change the status of event to publish
   * <AUTHOR>
   * @since 15/11/2023
   */
    static async publishFundraiser (req, user, locale) {
        const Validator = new FundraiserValidator(
            req.body,
            locale,
            null,
            req.query
        );
        Validator.validateEventId();
        const event = await Fundraiser.get(req.query.eventId);

        if (!event || event.isDeleted) {
            throw {
                message: MESSAGES.EVENT_NOT_FOUND,
                statusCode: 400
            };
        }

        const orgAssociation = await this.validateOrganizationAssociation(
            event.organizationId,
            user
        );

        if (orgAssociation.associatedOrgRole !== CONSTANTS.ORG_ROLE.SUPER_ADMIN) {
            throw new GeneralError(MESSAGES.ACCESS_DENIED, 403);
        } else if (event.status === 'published') {
            throw {
                message: MESSAGES.EVENT_ALREADY_PUBLISHED,
                statusCode: 200
            };
        } else {
            event.status = 'published';
            await event.save();
        }
    }

    /**
   * @desc This function is being used to change the status of cash or cheque payment status
   * <AUTHOR>
   * @since 21/11/2023
   */
    static async updatePaymentStatus (req, user, locale) {
        const { id, status } = req.body;
        const validator = new Validator(locale);
        validator.field(id, 'Participant Id');
        validator.field(status, 'Signup Status');
        if (
            CONSTANTS.ALLOWED_SIGNUP_STATUS.indexOf(status.trim().toLowerCase()) ===
      -1
        ) {
            throw {
                message: 'Invalid signup status!',
                statusCode: 400
            };
        }

        const event = await FundraiserSignup.get(id);
        if (!event || event.isDeleted) {
            throw {
                message: 'Participant not found!',
                statusCode: 400
            };
        }

        const orgAssociation = await this.validateOrganizationAssociation(
            event.organizationId,
            user
        );

        if (orgAssociation.associatedOrgRole !== CONSTANTS.ORG_ROLE.SUPER_ADMIN && orgAssociation.associatedOrgRole !== CONSTANTS.ORG_ROLE.ADMIN) {
            throw new GeneralError(MESSAGES.ACCESS_DENIED, 403);
        }

        if (
            event.paymentDetails.paymentStatus === 'approved' &&
            status.trim().toLowerCase() === 'approved'
        ) {
            throw {
                message: 'User has already paid for this event!',
                statusCode: 400
            };
        }

        const paymentType = event.paymentDetails.paymentType;
        if (
            paymentType === CONSTANTS.PAYMENT_TYPES.CASH ||
            paymentType === CONSTANTS.PAYMENT_TYPES.CHECK ||
            paymentType === CONSTANTS.PAYMENT_TYPES.VENMO
        ) {
            event.paymentDetails.paymentStatus = status.trim().toLowerCase();
            await event.save();
            let parentUser = await User.query('id').eq(event.parentId).exec();
            const child = await Children.get(event.childId);

            const eventDetails = await Fundraiser.get(event.eventId);

            if (parentUser.length) {
                parentUser = parentUser[0];
            }

            if (eventDetails.fundraiserType === CONSTANTS.FUNDRAISER_TYPES.MEMBERSHIP) {
                const purchasedProducts = JSON.parse(event.purchasedProducts);
                await FundraiserRegisterService.updateChildMembershipStatus(
                    eventDetails, parentUser, event.organizationId,
                    purchasedProducts[0].membershipType, child, event.id, purchasedProducts[0].itemId
                );
            }
            await eventDetails.save();

            if (event.isGuestSignup) {
                await this.sendEventRegistrationMail({
                    reqObj: {
                        userEmail: parentUser.email,
                        isGuestSignup: event.isGuestSignup,
                        eventReferenceId: event.id,
                        childName: child.firstName,
                        fundraiserName: eventDetails.title,
                        startDate: eventDetails.startDate,
                        endDate: eventDetails.endDate,
                        fundraiserSignupId: event.id
                    },
                    templateFile: 'fundraiserSignupConfirmation.html',
                    subject: `Fundraiser Registration for ${eventDetails.title}`
                });
            }

            await this.updateChildEventStatus({
                childId: event.childId,
                eventId: event.eventId,
                fundraiserSignupId: event.id,
                purchasedProducts: event.purchasedProducts,
                isGuestSignup: event.isGuestSignup
            });
        } else {
            throw {
                message: MESSAGES.INVALID_PAYMENT_METHOD,
                statusCode: 400
            };
        }
    }

    /**
     * @desc This function is being used to update fulfilled status of fundraiser
     * <AUTHOR>
     * @since 17/10/2024
     * @param {Object} req Request
     * @param {Object} user User details
     * @param {String} locale Locale
     */
    static async updateFundraiserFulfilledStatus (req, user, locale) {
        const { fundraiserSignupId } = req.body;
        const validator = new Validator(locale);
        validator.field(fundraiserSignupId, 'Fundraiser Signup Id');
        const fundraiserSignup = await FundraiserSignup.get(fundraiserSignupId);
        if (!fundraiserSignup) {
            throw {
                message: MESSAGES.SIGNUP_NOT_FOUND,
                statusCode: 400
            };
        }
        if (fundraiserSignup.paymentDetails.paymentStatus !== 'approved') {
            throw {
                message: MESSAGES.SIGNUP_APPROVED_FIRST,
                statusCode: 400
            };
        }
        if (fundraiserSignup.isFulfilled) {
            throw {
                message: MESSAGES.SIGNUP_ALREADY_FULFILLED,
                statusCode: 400
            };
        }

        const orgAssociation = await this.validateOrganizationAssociation(
            fundraiserSignup.organizationId,
            user
        );
        if (orgAssociation.associatedOrgRole !== CONSTANTS.ORG_ROLE.SUPER_ADMIN && orgAssociation.associatedOrgRole !== CONSTANTS.ORG_ROLE.ADMIN) {
            throw new GeneralError(MESSAGES.ACCESS_DENIED, 403);
        }
        fundraiserSignup.isFulfilled = true;
        await fundraiserSignup.save();
    }

    /**
     * @desc This function is being used to update child event status
     * @param {String} childId childId
     * @param {String} eventId eventId
     * @param {String} fundraiserSignupId fundraiserSignupId
     * @param {Array} purchasedProducts purchasedProducts
     * @param {Boolean} isGuestSignup isGuestSignup
     */
    static async updateChildEventStatus ({ childId, eventId, fundraiserSignupId, purchasedProducts, isGuestSignup }) {
        if (isGuestSignup) {
            return;
        }
        await AwsOpenSearchService.removeEventFromChildPendingEvents(
            CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD,
            childId,
            eventId,
            fundraiserSignupId
        );
        await AwsOpenSearchService.registerEventInChildEvents(
            CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD,
            childId,
            {
                fundraiserId: eventId,
                fundraiserSignupId,
                purchasedProducts
            }
        );
    }

    /**
   * @desc This function is being used to delete event
   * <AUTHOR>
   * @since 15/11/2023
   */
    static async deleteFundraiser (req, user, locale) {
        const Validator = new FundraiserValidator(
            req.body,
            locale,
            null,
            req.query
        );
        Validator.validateEventId();
        const event = await Fundraiser.get(req.query.eventId);
        if (!event || event.isDeleted) {
            throw {
                message: MESSAGES.EVENT_NOT_FOUND,
                statusCode: 400
            };
        } else if (event.status === 'published') {
            const orgAssociation = await this.validateOrganizationAssociation(
                event.organizationId,
                user
            );
            if (orgAssociation.associatedOrgRole !== CONSTANTS.ORG_ROLE.SUPER_ADMIN) {
                throw new GeneralError(MESSAGES.ACCESS_DENIED, 403);
            }
            throw {
                message: MESSAGES.EVENT_CANT_BE_DELETED,
                statusCode: 400
            };
        } else {
            event.isDeleted = 1;
            // ned to delete
            event.products = '';

            await event.save();
            await AwsOpenSearchService.delete(
                CONSTANTS.OPEN_SEARCH.COLLECTION.FUNDRAISER,
                event.id
            );
        }
    }

    /**
   * @desc This function is being used to get searched list of Events
   * <AUTHOR>
   * @since 21/02/2024
   */
    static async getSearchFundraiserList (req, user, locale) {
        let { query } = req.query;
        const { childId } = req.query;
        const validator = new Validator(locale);
        validator.field(query, 'Search Query');
        query = query.trim();
        let childrenIds;
        if (childId) {
            childrenIds = [childId];
        } else {
            childrenIds = user.children;
        }
        const allChildrenFromOpensearch =
      await AwsOpenSearchService.getAllChildEvents(
          CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD,
          {
              childrenIds
          }
      );
        const { childEventMap, eventIds } = await this.formattedChildEvents(
            allChildrenFromOpensearch
        );
        const eventList = await AwsOpenSearchService.getAllEvents(
            CONSTANTS.OPEN_SEARCH.COLLECTION.FUNDRAISER,
            { searchText: query, eventIds }
        );
        const filteredChildEventMap = childEventMap.filter(({ eventId }) =>
            eventList.find(({ _source }) => _source.id === eventId)
        );

        if (eventList.length === 0) {
            return [];
        } else {
            let responseSortedList = await this.formattedEventMapping(
                eventList,
                filteredChildEventMap
            );
            responseSortedList = responseSortedList.sort((a, b) => {
                const dateA = MOMENT(a.startDateTime);
                const dateB = MOMENT(b.startDateTime);
                return dateA.valueOf() - dateB.valueOf();
            });
            return responseSortedList;
        }
    }

    /**
   * Formats the event mapping for a given set of events and child events.
   *
   * @param {Array} events - The events to be formatted.
   * @param {Array} childEventMap - The child events to be included in the formatted events.
   */
    static async formattedEventMapping (events, childEventMap) {
        return await Promise.all(
            childEventMap.map(async ({ eventId, associatedChild }) => {
                const { _source } = events.find(
                    ({ _source }) => _source.id === eventId
                );
                const {
                    id,
                    title,
                    eventType,
                    details: eventDetails,
                    organizationId
                } = _source;
                const { startDateTime, endDateTime, venue, details } = eventDetails;
                const org = await Organization.get(organizationId);
                return {
                    id,
                    title,
                    eventType,
                    venue,
                    details,
                    associatedChild,
                    startDateTime: MOMENT(startDateTime),
                    endDateTime: MOMENT(endDateTime),
                    organizationName: org.name,
                    isSignedUp: true,
                    paymentStatus: CONSTANTS.PAYMENT_STATUS.APPROVED,
                    score: MOMENT(startDateTime).valueOf()
                };
            })
        );
    }

    /**
   * Formats the child events for a given set of children.
   *
   * @param {Array} children - The children to be formatted.
   */
    static async formattedChildEvents (children) {
        const childEventMap = [];
        const eventIds = [];
        for (const { _source } of children) {
            const {
                id,
                childEvents,
                firstName,
                lastName,
                associatedColor,
                imageURL,
                childCalendarEvents
            } = _source;
            if (childEvents && childEvents.length > 0) {
                for (let i = 0; i < childEvents.length; i++) {
                    const obj = {
                        associatedChild: {
                            id,
                            firstName,
                            lastName,
                            associatedColor,
                            imageURL: imageURL
                                ? await UploadService.getSignedUrl(imageURL)
                                : null
                        },
                        childId: id,
                        eventId: childEvents[i]
                    };
                    childEventMap.push(obj);
                }
                eventIds.push(...childEvents);
            }
            if (childCalendarEvents && childCalendarEvents.length > 0) {
                for (let i = 0; i < childCalendarEvents.length; i++) {
                    const obj = {
                        associatedChild: {
                            id,
                            firstName,
                            lastName,
                            associatedColor,
                            imageURL: imageURL
                                ? await UploadService.getSignedUrl(imageURL)
                                : null
                        },
                        childId: id,
                        eventId: childCalendarEvents[i]
                    };
                    childEventMap.push(obj);
                }
                eventIds.push(...childCalendarEvents);
            }
        }

        return { childEventMap, eventIds: [...new Set(eventIds)] };
    }

    /**
   * @desc This function is being used to return formatted events
   * <AUTHOR>
   * @since 15/11/2023
   * @param {Array} events array of events
   */
    static formatEvents (events) {
        return events
            .filter(({ isDeleted }) => !isDeleted)
            .map(
                ({
                    id,
                    title,
                    fundraiserType,
                    status,
                    startDate,
                    endDate,
                    description,
                    organizationId,
                    products,
                    membershipBenefitDetails,
                    boosterGoal,
                    boosterMessageForChild,
                    boosterGoalForChild
                }) => ({
                    id,
                    title,
                    fundraiserType,
                    status,
                    description,
                    organizationId,
                    products,
                    membershipBenefitDetails,
                    boosterGoal,
                    boosterMessageForChild,
                    boosterGoalForChild,
                    startDate: MOMENT(startDate).format('L'),
                    endDate: MOMENT(endDate).format('L')
                })
            );
    }

    /**
   * @desc This function is being used to send mail for success event registration
   * @param {Object} reqObj reqObj
   * @param {String} templateFile templateFile
   * @param {String} subject subject
   */
    static async sendEventRegistrationMail ({ reqObj, templateFile, subject }) {
        const {
            childName,
            fundraiserName,
            startDate,
            endDate,
            fundraiserSignupId,
            isGuestSignup,
            userEmail: email,
            pendingReason = ''
        } = reqObj;

        if (!isGuestSignup) {
            return;
        }

        const template = `emailTemplates/${templateFile}`;
        const currentDate = new Date();
        const year = currentDate.getFullYear();
        const templateVariables = {
            year,
            fundraiserName,
            startDate,
            endDate,
            pendingReason,
            referenceId: fundraiserSignupId,
            trackingLink: `${process.env.FAMILY_WEB_APP_URL}/track-fundraiser-signup?referenceId=${fundraiserSignupId}`,
            email: CONSTANTS.CLIENT_INFO.HELP_EMAIL,
            appname: CONSTANTS.APP_NAME,
            username: `${childName}`
        };
        await EmailService.prepareAndSendEmail(
            [email],
            subject,
            template,
            templateVariables
        );
    }

    /**
     * @desc This function is being used to generate presigned url
     * <AUTHOR>
     * @since 18/12/2024
     */
    static async generatePresignedUrl (req, user, locale) {
        const Validator = new FundraiserValidator(req.body, locale, null, req.query);
        Validator.validateGeneratePresignedUrl();

        const { fileName, organizationId, eventId: fundraiserId } = req.query;

        await this.validateOrganizationAssociation(
            organizationId,
            user
        );

        const checkedFundraiserId = fundraiserId ?? uuidv4();

        const generatedFileName = `${process.env.NODE_ENV}-fundraisers/organization-${organizationId}/${checkedFundraiserId}/${fileName}-${Date.now()}`;

        const presignedUrl = await UploadService.getPreSignedUrlForUpload(
            generatedFileName
        );
        return {
            presignedUrl,
            fileName: generatedFileName,
            fundraiserId: checkedFundraiserId
        };
    }
}

module.exports = FundraiserService;
