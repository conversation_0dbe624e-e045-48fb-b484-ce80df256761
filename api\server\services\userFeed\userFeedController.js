
const UserFeedService = require('./userFeedService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for get user feeds.
 */
class UserFeedController {
    /**
     * @desc This function is being used to get user feeds
     * <AUTHOR>
     * @since 19/10/2023
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async getUserFeeds (req, res) {
        try {
            const data = await UserFeedService.getUserFeeds(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in getUserFeeds', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = UserFeedController;
