{"SUCCESS": "Success", "REGISTER_SUCCESS": "User registration successful", "ALREADY_REGISTER": "This user is already registered with us.", "EXISTING_USER_ORG_UPDATED": "This user is already registered with us to login please check your previous email and organization added successfully.", "ORG_EXISTS": "This organization already exists.", "STIPE_ACC_NOT_FOUND": "Stripe connect account not found. Please register with <PERSON><PERSON><PERSON> first.", "INACTIVE_USER": "Please activate your account by verifying email via verification code that has been sent earlier", "INVALID_OTP": "Invalid Registration code. Please check and try again.", "INVALID_VERIFY_OTP": "Verification code doesn't match. Please enter correct verification code.", "INVALID_FORGOT_PASSWORD_OTP": "Verification code doesn't match. Please enter correct verification code.", "INVALID_EMAIL_PHONE": "Email address/phone number not found. Please enter a registered email address/phone number.", "INVALID_ADDRESS": "%s should be between 10 to 200 characters.", "INVALID_ZIP_CODE": "%s should be between 5 to 10 characters.", "INVALID_ORGANIZATION_NAME": "%s should be between 3 to 200 characters.", "USER_VERIFY_SUCCESS": "User is verified successfully", "USER_NOT_FOUND": "Invalid user request", "INVALID_REQUEST": "Request is invalid", "RESEND_OTP_SUCCESS": "Verification code resent successfully", "NEW_USER": "User not found. Please enter a registered email address.", "LOGIN_SUCCESS": "User successfully logged in", "LOGIN_FAILED": "Email & Password do not match", "FIELD_REQUIRED": "%s can't be blank", "PARAM_REQUIRED": "%s is required", "FIELD_NOT_VALID": "%s is not valid.", "NAME_NOT_VALID": "%s can only contain alphabets (A-Z), (a-z) and special character hyphen (-), apostrope (') and periods (.).", "PORTAL_EXISTS": "Jira portal is already exists", "ERROR_MSG": "Something went wrong. please try again.", "ACCESS_DENIED": "You are not authorized to access this resource.", "DEACTIVATE_ACCOUNT_BY_ADMIN": "You account has been deactivate.", "PHOTO_DELETE_SUCCESS": "Your profile picture has been deleted successfully.", "INVALID_JIRA_CREDENTIALS": "The entered email and token are not correct. Please verify it.", "SELECT_EMPLOYEE": "Select the employee first", "INVALID_PORTAL_ID": "Jira portal id is invalid.", "PASSWORD_NOT_MATCH": "The passwords do not match", "CHANGE_PASSWORD_SUCCESS": "Password changed successfully", "FILE_NOT_FOUND": "File not found", "TEMPLATE_NAME_REQUIRED": "Template name is required", "TEMPLATE_SUBJECT_REQUIRED": "Template subject is required", "FORGOT_PASSWORD_LINK_SENT_SUCCESS": "Verification code sent successfully", "MAIL_SENT_SUCCESS": "Mail sent successfully", "LINK_IS_VALID": "<PERSON> validated successfully.", "RESET_PASSWORD_SUCCESS": "Your password has been reset successfully.", "SIGNIN_SUCCESS": "User successfully logged in.", "LOGOUT_SUCCESS": "User successfully logged out", "ADD_CHILD_SUCCESS": "Child added successfully", "ADD_ORGANIZATION_SUCCESS": "Organization added successfully", "USER_EXISTS_WITH_EMAIL": "A user with the email %s already exists", "UPDATE_ORGANIZATION_SUCCESS": "Organization updated successfully", "ORGANIZATION_DOES_NOT_EXIST": "Organization with given id does not exist", "DELETE_ORGANIZATION_SUCCESS": "Organization has been deleted successfully", "DELETE_ORGANIZATION_ERROR": "Organization can not be deleted", "ORGANIZATION_STATUS_CHANGE_SUCCESS": "Organization status has been changed successfully", "ORGANIZATION_STATUS_CHANGE_ERROR": "Organization can not be disabled", "ALREADY_ONBOARDED": "This organization is already onboarded on stripe connect", "ADD_ORG_USER_SUCCESS": "User added to organization successfully", "ORGANIZATION_USER_EXISTS": "User is already added to organization", "ROLE_NOT_ALLOWED": "You cannot add super admin to organization", "ORGANIZATION_USER_DOES_NOT_EXIST": "User is not added to organization", "UPDATE_ORG_USER_SUCCESS": "User updated successfully", "ORGANIZATION_USER_CANNOT_BE_UPDATED": "User can not be updated", "DELETE_ORG_USER_SUCCESS": "User deleted successfully", "ORGANIZATION_USER_CANNOT_BE_DELETED": "User can not be deleted", "NO_CHILD_FOUND_WITH_ID": "No child found with given %s id", "CANT_FOLLOW_OWN_CHILD": "You can not follow your own child", "FOLLOW_REQUEST_SUCCESS": "Follow request sent successfully", "ALREADY_FOLLOWING_CHILD": "You are already following this child", "INVALID_ENUM_VALUE": "%s field can contains value from %s", "FOLLOW_REQUEST_NOT_FOUND": "Follow request not found", "FOLLOW_REQUEST_STATUS_CHANGED": "Follow request status changed successfully", "ALREADY_FOLLOWED_BY_CHILD": "This child is already followed by you", "ALREADY_FOLLOW_REQUEST_SENT": "You have already sent follow request to this child", "CANT_FOLLOW_CHILD": "You can not follow this child", "ALREADY_REQUEST_ACCEPTED": "You have already accepted follow request from this child", "ALREADY_FOLLOW_REQUEST_PENDING": "Request is already pending", "CANT_FOLLOW_CHILD_FROM_OTHER_SCHOOL": "You can not follow this child from other school", "No child found with given %s id": "No child found with given %s id", "DATE_FORMAT_ERROR": "%s must be in MM/DD/YYYY format", "HOMEROOM_DOES_NOT_EXIST": "Homeroom does not exist", "SCHOOL_DOES_NOT_EXIST": "School does not exist", "CANT_UNFOLLOW": "You can not unfollow this child as request is not accepted yet", "CHILD_UNFOLLOW_SUCCESS": "Child unfollowed successfully", "%s must be in MM/DD/YYYY format": "%s must be in MM/DD/YYYY format", "CHILD_NOT_FOUND": "Child not found!", "CHILD_ALREADY_EXISTS": "Child already exists", "PARENT_REINVITE_SUCCESS": "<PERSON><PERSON> re-invited successfully", "PARTNER_ACCEPTED": "Partner accepted successfully", "PARTNER_REJECTED": "Partner rejected successfully", "FOLLOWER_REMOVED_SUCCESS": "Follower removed successfully", "PARTNER_EXISTS": "This partner has already been invited with this child.", "PARTNER_INVITE_SUCCESS": "Partner invited successfully", "CHILD_ALREADY_ADDED": "Child already added to this partner", "Child not found!": "Child not found!", "CONNECTION_REQUEST_SUCCESS": "Connection request sent successfully", "CANT_CONNECT_OWN_CHILD": "You can not connect your own child", "CANT_CONNECT_CHILD_FROM_OTHER_SCHOOL": "You can not connect this child from other school", "ALREADY_SENT_CONNECTION_REQUEST": "You have already sent connection request to this child", "ALREADY_CONNECTED": "You are already connected with this child", "CANT_DISCONNECT": "You can not disconnect this child as request is not accepted yet", "CANT_CONNECT_CHILD": "You can not connect with this child", "ALREADY_RECEIVED_CONNECTION_REQUEST": "You have already received connection request from this child", "CONNECTION_REQUEST_STATUS_CHANGED": "Connection request status changed successfully", "CONNECTION_REQUEST_NOT_FOUND": "Connection request not found", "CONNECTION_REMOVED_SUCCESS": "Connection removed successfully", "INVITE_NOT_FOUND": "Invite not found", "PAYMENT_TYPE_REQUIRED": "Please select atleast one payment type", "INVALID_OLD_PASSWORD": "Current password provided is incorrect", "SUCCESS_CHANGE_PASSWORD": "You have successfully changed your password", "OLD_NEW_PASSWORD_SAME": "Old password and new password can not be same", "Old password and new password can not be same": "Old password and new password can not be same", "SUCCESS_UPDATE_PROFILE": "Your profile updated successfully", "CANNOT_MODIFY_PLATFORM_FEE": "Organization can not modify platform fee", "INVALID_PLATFORM_FEE": "Platform fee should be between 0 to 100", "INVALID_PLATFORM_FEE_COVERED_BY": "Platform fee covered by either of 'organization', 'parent' or 'optional'", "MISSING_PLATFORM_FEE_COVERED_BY": "Platform fee covered by is required", "%s can't be blank": "%s can't be blank", "PARTNER_ALREADY_INVITED": "Partner already invited", "CHILD_ARRAY_EMPTY": "Child ids array can not be empty", "CHILDREN_IDS_REQUIRED": "Please select a child to send invite", "ALL_CHILDREN_ALREADY_INVITED": "All children are already invited", "CANNOT_REMOVE_MEMBER": "You can not remove this member. Please remove the children first.", "REMOVE_MEMBER_SUCCESS": "Member removed successfully", "MEMBERS_UPDATED_SUCCESS": "Member's invite updated successfully", "CANNOT_INVITE_YOURSELF": "You can not invite yourself", "CANT_ACCEPT_DENY_OWN_CHILD": "You can not accept/deny connection request of your own child", "CASH_INSTRUCTION": "Thank you for choosing to pay with cash. Please hand over the cash amount to the event organizer. Please note that your sign up will be confirmed upon successful receipt of the cash amount.", "USER_ASSOCIATED_ORGANIZATION": "User is associated with an organization please contact administrator to delete the account.", "ADD_CHILDREN_ORGANIZATION": "Changes applied successfully for the selected children", "INVALID_FILE_FORMAT": "Invalid file format"}