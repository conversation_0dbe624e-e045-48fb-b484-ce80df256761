const dynamoose = require('dynamoose');
const { v4: uuidv4 } = require('uuid');

const reactionsSchema = new dynamoose.Schema({
    reactionId: {
        hashKey: true,
        type: String,
        default: () => uuidv4()
    },
    messageId: {
        type: String,
        required: true
    },
    userId: {
        type: String,
        required: true
    },
    reaction: {
        type: String,
        required: true,
        enum: ['thumbs-down', 'thumbs-up', 'heart']
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('Reactions', reactionsSchema);
