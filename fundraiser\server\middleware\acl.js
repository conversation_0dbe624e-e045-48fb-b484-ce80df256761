const Utils = require('../util/utilFunctions');
const HTTPStatus = require('../util/http-status');

module.exports = function (req, res, next) {
    const accessList = {
        'app': [{ method: 'POST', path: '/fundraiser/register' }],
        'org_app': [
            { method: 'POST', path: '/fundraiser' },
            { method: 'GET', path: '/fundraiser' },
            { method: 'PUT', path: '/fundraiser' },
            { method: 'DELETE', path: '/fundraiser' },
            { method: 'GET', path: '/fundraiser/list' },
            { method: 'GET', path: '/fundraiser/participant' },
            { method: 'PUT', path: '/fundraiser/signup-status' },
            { method: 'PATCH', path: '/fundraiser/publish' },
            { method: 'GET', path: '/fundraiser/orgBooster/donations' },
            { method: 'POST', path: '/fundraiser/orgBooster/donations' },
            { method: 'PATCH', path: '/fundraiser/signup-fulfilled-status' },
            { method: 'GET', path: '/fundraiser/generate-presigned-url' }
        ],
        'root': [
            { method: 'POST', path: '/fundraiser' },
            { method: 'GET', path: '/fundraiser' },
            { method: 'PUT', path: '/fundraiser' },
            { method: 'DELETE', path: '/fundraiser' },
            { method: 'GET', path: '/fundraiser/list' },
            { method: 'GET', path: '/fundraiser/participant' },
            { method: 'PUT', path: '/fundraiser/signup-status' },
            { method: 'PATCH', path: '/fundraiser/publish' },
            { method: 'GET', path: '/fundraiser/orgBooster/donations' },
            { method: 'POST', path: '/fundraiser/orgBooster/donations' },
            { method: 'PATCH', path: '/fundraiser/signup-fulfilled-status' },
            { method: 'GET', path: '/fundraiser/generate-presigned-url' }
        ]
    };
    const accessLevel = res.locals.user.accessLevel;
    const isAllowed = _.find(accessList[accessLevel], { method: req.method, path: req.originalUrl.split('?')[0] });
    if (isAllowed) {
        next();
    } else {
        const responseObject = Utils.errorResponse();
        responseObject.message = res.__('ACCESS_DENIED');
        res.status(HTTPStatus.NOT_ACCEPTABLE).send(responseObject);
        return;
    }
};
