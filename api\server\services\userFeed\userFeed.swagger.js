/**
 *  routes and schema for User feeds
 */

/**
 * @openapi
 * components:
 *  schemas:
 *      successUserFeeds:
 *          type: object
 *          properties:
 *              status:
 *                  type: boolean
 *                  description: status if user is present
 *              data:
 *                  type: string
 *                  description: feeds of the user
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data:
 *                  docs : [{
 *                          id:
 *                              id,
 *                          title:
 *                              title,
 *                          details:
 *                              details,
 *                          imageURL:
 *                              imageURL,
 *                          createdBy:
 *                              createdBy,
 *                          updatedBy:
 *                              updatedBy,
 *                          orgainzer:
 *                              orgainzer,
 *                          status:
 *                              status
 *                          }]
 *                  currentPage: 1
 *                  pageSize: 1
 *                  total: 1
 *                  totalPages: 1
 *                  hasPrevPage: false
 *                  hasNextPage: false
 *              message: Success
 *
 */

/**
 * @openapi
 *  /user/feeds:
 *      get:
 *          security:
 *              - bearerAuth: []
 *          tags: [User]
 *          summary: User Feeds
 *          parameters:
 *              - in: query
 *                name: page
 *                description: current page
 *              - in: query
 *                name: pageSize
 *          responses:
 *              200:
 *                  description: User feeds
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#/components/schemas/successUserFeeds'
 *              400:
 *                  description: Invalid Request
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#/components/schemas/validationError'
 *              401:
 *                  description: Unauthorised Access
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#/components/schemas/unauthorisedAccessUser'
 *              500:
 *                  description: internal server error
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#/components/schemas/unexpectedError'
 *
 *
 *
 */
