const handler = require('../index');
const AWS = require('aws-sdk');
const AWSMock = require('jest-aws-sdk-mock');
const sinon = require('sinon');
const Organization = require('../server/models/organization.model');
const ChildOrganizationMapping = require('../server/models/childOrganizationMapping.model');
const Child = require('../server/models/child.model');
const Redis = require('ioredis');
const RedisUtil = require('../server/redisUtil');
const { afterEach, beforeEach } = require('mocha');
const CONSOLE_LOGGER = require('../server/logger');
const { SQSClient } = require('@aws-sdk/client-sqs');
const ConstantModel = require('../server/models/constant.model');
const CONSTANTS = require('../server/constants');

describe('Insert post', () => {
    let stubRedisConnect;
    let pipelineExecStub;
    let pipelineStub;
    let sandbox;

    before(() => {
        AWSMock.setSDKInstance(AWS);
        sandbox = sinon.createSandbox();
    });

    after(() => {
        AWSMock.restore();
        sinon.restore();
        pipelineStub.restore();
        sandbox.restore();
    });

    beforeEach(() => {
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
        pipelineExecStub = sinon.stub();
        pipelineStub = sinon.stub(Redis.prototype, 'pipeline').returns({ exec: pipelineExecStub });
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        sinon.stub(Organization, 'get').resolves({ name: 'Test' });
        sinon.stub(ConstantModel, 'get').withArgs(CONSTANTS.FEED_VERSION_PREFIX).resolves({ value: 'v1' });
    });

    afterEach(() => {
        sinon.restore();
    });

    try {
        it('should process INSERT posts', async () => {
            pipelineExecStub.resolves();
            const mockedResponse = [
                { childId: 'child1', associatedOrganizations: ['123'] }
            ];

            const queryStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({ eq: eqStub.returns({ using: usingStub.returns({ exec: execStub.resolves(mockedResponse) }) }) });

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([]);

            sinon.stub(Child, 'batchGet').resolves([
                { id: 'child1' }
            ]);

            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Posts/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test Post Title' },
                        subTitle: { S: 'Test Post SubTitle' },
                        content: { S: 'Test Post Content' },
                        status: { S: 'published' },
                        publishedDate: { S: '2024-01-01' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };
            const sendStub = sandbox.stub(SQSClient.prototype, 'send');
            sendStub.resolves();

            await handler.handler(event);

            sinon.assert.called(pipelineExecStub);
        });

        it('should process INSERT posts if status isn\'t published', async () => {
            pipelineExecStub.resolves();
            const mockedResponse = [
                { childId: 'child1', associatedOrganizations: ['123'] }
            ];

            const queryStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({ eq: eqStub.returns({ using: usingStub.returns({ exec: execStub.resolves(mockedResponse) }) }) });

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([]);

            sinon.stub(Child, 'batchGet').resolves([{ id: 'child1' }]);

            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Posts/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test Post Title' },
                        subTitle: { S: 'Test Post SubTitle' },
                        content: { S: 'Test Post Content' },
                        status: { S: 'draft' },
                        publishedDate: { S: '2024-01-01' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(pipelineExecStub);
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Update post', () => {
    let sandbox;
    let stubRedisConnect;
    let pipelineExecStub;
    let pipelineStub;

    before(() => {
        AWSMock.setSDKInstance(AWS);
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
        pipelineExecStub = sinon.stub();
        pipelineStub = sinon.stub(Redis.prototype, 'pipeline').returns({ exec: pipelineExecStub });
        sandbox = sinon.createSandbox();
    });

    after(() => {
        AWSMock.restore();
        sinon.restore();
        pipelineStub.restore();
        sandbox.restore();
    });

    afterEach(() => {
        sinon.restore();
    });

    beforeEach(() => {
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        sinon.stub(Organization, 'get').resolves({ name: 'Test' });
        sinon.stub(ConstantModel, 'get').withArgs(CONSTANTS.FEED_VERSION_PREFIX).resolves({ value: 'v1' });
    });

    try {
        it('should process MODIFY posts', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN:
                    'arn:aws:dynamodb:us-east-2:464376718462:table/Posts/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test Post Title' },
                        subTitle: { S: 'Test Post SubTitle' },
                        content: { S: 'Test Post Content' },
                        status: { S: 'published' },
                        publishedDate: { S: '2024-01-01' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    },
                    NewImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test Post Title' },
                        subTitle: { S: 'Test Post SubTitle' },
                        content: { S: 'Test Post Content' },
                        status: { S: 'published' },
                        publishedDate: { S: '2024-01-01' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            pipelineExecStub.resolves();
            const mockedResponse = [
                { childId: 'child1', associatedOrganizations: ['123'] }
            ];

            const queryStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({
                eq: eqStub.returns({
                    using: usingStub.returns({ exec: execStub.resolves(mockedResponse) })
                })
            });

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([
                JSON.stringify({
                    postId: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                })
            ]);

            sinon.stub(Child, 'batchGet').resolves([]);

            await handler.handler(event);

            sinon.assert.called(pipelineExecStub);
        });

        it('should process MODIFY post and soft delete', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Posts/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test Post Title' },
                        subTitle: { S: 'Test Post SubTitle' },
                        content: { S: 'Test Post Content' },
                        status: { S: 'published' },
                        publishedDate: { S: '2024-01-01' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' },
                        isDeleted: { N: 0 }
                    },
                    NewImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test Post Title' },
                        subTitle: { S: 'Test Post SubTitle' },
                        content: { S: 'Test Post Content' },
                        status: { S: 'published' },
                        publishedDate: { S: '2024-01-01' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' },
                        isDeleted: { N: 1 }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            sinon.stub(ChildOrganizationMapping, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ childId: 'child1', associatedOrganizations: ['123'] }])
                    })
                })
            });

            sinon.stub(Child, 'batchGet').resolves([
                { id: 'child1' }
            ]);

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([
                JSON.stringify({
                    postId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                    childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    isSignedUp: false, status: ''
                }),
                JSON.stringify({
                    eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eeg',
                    child: { id: 'ace0149e-447d-4f7f-9402-95a96963de9b', associatedImageOrColor: '#FACD01' }, isSignedUp: false, status: ''
                })]);

            await handler.handler(event);
        });

        it('should process MODIFY post and remove post from feed if status is changes to draft from published', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Posts/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test Post Title' },
                        subTitle: { S: 'Test Post SubTitle' },
                        content: { S: 'Test Post Content' },
                        status: { S: 'published' },
                        publishedDate: { S: '2024-01-01' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' },
                        isDeleted: { N: 0 }
                    },
                    NewImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test Post Title' },
                        subTitle: { S: 'Test Post SubTitle' },
                        content: { S: 'Test Post Content' },
                        status: { S: 'draft' },
                        publishedDate: { S: '2024-01-01' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' },
                        isDeleted: { N: 0 }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            sinon.stub(ChildOrganizationMapping, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ childId: 'child1', associatedOrganizations: ['123'] }])
                    })
                })
            });

            sinon.stub(Child, 'batchGet').resolves([
                { id: 'child1', guardians: ['userId1', 'userId2'] }
            ]);

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([
                JSON.stringify({
                    postId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                    childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    isSignedUp: false, status: ''
                }),
                JSON.stringify({
                    eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eeg',
                    childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    isSignedUp: false, status: ''
                })]);

            await handler.handler(event);
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
