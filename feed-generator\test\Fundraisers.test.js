const handler = require('../index');
const AWS = require('aws-sdk');
const AWSMock = require('jest-aws-sdk-mock');
const sinon = require('sinon');
const Organization = require('../server/models/organization.model');
const ChildOrganizationMapping = require('../server/models/childOrganizationMapping.model');
const Child = require('../server/models/child.model');
const Redis = require('ioredis');
const RedisUtil = require('../server/redisUtil');
const { afterEach, beforeEach } = require('mocha');
const CONSOLE_LOGGER = require('../server/logger');
const constants = require('../server/constants');
const FundraiserSignup = require('../server/models/fundraiserSignup.model');
const User = require('../server/models/user.model');
const ConstantModel = require('../server/models/constant.model');
const CONSTANTS = require('../server/constants');

describe('Insert fundraiser', () => {
    let stubRedisConnect;
    let pipelineExecStub;
    let pipelineStub;

    before(() => {
        AWSMock.setSDKInstance(AWS);
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
        pipelineExecStub = sinon.stub();
        pipelineStub = sinon.stub(Redis.prototype, 'pipeline').returns({ exec: pipelineExecStub });
    });

    after(() => {
        AWSMock.restore();
        sinon.restore();
        pipelineStub.restore();
    });

    beforeEach(() => {
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        sinon.stub(ConstantModel, 'get').withArgs(CONSTANTS.FEED_VERSION_PREFIX).resolves({ value: 'v1' });
    });

    afterEach(() => {
        sinon.restore();
    });

    try {
        it('should process INSERT fundraiser', async () => {
            sinon.stub(Organization, 'get').resolves({ name: 'Test' });
            stubRedisConnect.callsFake(async function () {
                this.setStatus('connect');
            });
            pipelineExecStub.resolves();
            const mockedResponse = [
                { id: 'child1', associatedOrganizations: ['123'] }
            ];

            const queryStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({ eq: eqStub.returns({ using: usingStub.returns({ exec: execStub.resolves(mockedResponse) }) }) });

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([]);

            sinon.stub(Child, 'batchGet').resolves([
                {
                    id: 'childId1',
                    guardians: ['guardianId1']
                },
                {
                    id: 'childId2',
                    guardians: ['guardianId2']
                }
            ]);

            const record = {
                eventName: 'INSERT',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Fundraisers/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    NewImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test fundraiser' },
                        startDate: { S: '2020-01-01T00:00:00.000Z' },
                        organizationId: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        eventType: { S: 'Test event type' },
                        eventScope: { S: 'Test event scope' },
                        sheetUrl: { S: 'Test sheet url' },
                        fee: { S: 'Test fee' },
                        status: { S: 'published' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            await handler.handler(event);

            sinon.assert.called(pipelineExecStub);
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Update fundraiser', () => {
    let sandbox;
    let stubRedisConnect;
    let pipelineExecStub;
    let pipelineStub;

    before(() => {
        AWSMock.setSDKInstance(AWS);
        sandbox = sinon.createSandbox();
    });

    after(() => {
        AWSMock.restore();
        sinon.restore();
        pipelineStub.restore();
        sandbox.restore();
    });

    afterEach(() => {
        sinon.restore();
    });

    beforeEach(() => {
        stubRedisConnect = sinon.stub(Redis.prototype, 'connect');
        pipelineExecStub = sinon.stub();
        pipelineStub = sinon.stub(Redis.prototype, 'pipeline').returns({ exec: pipelineExecStub });
        stubRedisConnect.callsFake(async function () {
            this.setStatus('connect');
        });
        sinon.stub(Organization, 'get').resolves({ name: 'Test' });
        sinon.stub(ConstantModel, 'get').withArgs(CONSTANTS.FEED_VERSION_PREFIX).resolves({ value: 'v1' });
    });

    try {
        it('should process MODIFY fundraiser events', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Fundraisers/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test fundraiser' },
                        startDate: { S: '2020-01-01T00:00:00.000Z' },
                        endDate: { S: '2020-01-01T00:00:00.000Z' },
                        organizationId: {
                            S: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                        },
                        fundraiserType: { S: constants.FUNDRAISER_TYPES.MEMBERSHIP },
                        eventScope: { S: 'Test event scope' },
                        status: { S: 'unpublished' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    },
                    NewImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test fundraiser' },
                        startDate: { S: '2020-01-01T00:00:00.000Z' },
                        endDate: { S: '2020-01-02T00:00:00.000Z' },
                        organizationId: {
                            S: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                        },
                        fundraiserType: { S: constants.FUNDRAISER_TYPES.MEMBERSHIP },
                        eventScope: { S: 'Test event scope' },
                        status: { S: 'draft' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: {
                            S: '2020-01-01T00:00:00.000Z'
                        },
                        updatedAt: {
                            S: '2020-01-01T00:00:00.000Z'
                        }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            sinon.stub(RedisUtil, 'getHashValue').resolves(JSON.stringify({
                id: '0fd6a871-a6f5-4690-b06a-d786f1361eef', title: 'Test fundraiser',
                startDate: '2020-01-01T00:00:00.000Z', organizationId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                eventType: 'Test event type', eventScope: 'Test event scope', status: 'unpublished',
                photoURL: 'Test photo url', createdAt: '2020-01-01T00:00:00.000Z', updatedAt: '2020-01-01T00:00:00.000Z'
            }));
            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([JSON.stringify({
                fundraiserSignupId: '470f3434-7cb5-402f-81fc-ef3db68b73ce', fundraiserId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                childId: 'ace0149e-447d-4f7f-9402-95a96963de9b', isSignedUp: false, status: ''
            })]);

            sinon.stub(FundraiserSignup, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        attributes: sinon.stub().returns({
                            exec: sinon.stub().resolves([
                                {
                                    id: 'fundraiserSignupId',
                                    parentId: 'parentId',
                                    paymentDetails: {
                                        paymentStatus: 'approved'
                                    },
                                    purchasedProducts: JSON.stringify([{ membershipType: 'family' }])
                                },
                                {
                                    id: 'fundraiserSignupId-2',
                                    parentId: 'parentId-2',
                                    paymentDetails: {
                                        paymentStatus: 'pending'
                                    },
                                    purchasedProducts: JSON.stringify([{ membershipType: 'child' }])
                                },
                                {
                                    id: 'fundraiserSignupId-3',
                                    parentId: 'parentId-3',
                                    paymentDetails: {
                                        paymentStatus: 'approved'
                                    },
                                    purchasedProducts: JSON.stringify([{ membershipType: 'child' }])
                                }
                            ])
                        })
                    })
                })
            });

            sinon.stub(User, 'query').returns({
                exec: sinon.stub().resolves([{
                    id: 'parentId',
                    children: ['childId1'],
                    membershipsPurchased: [
                        {
                            fundraiserSignupId: 'fundraiserSignupId',
                            membershipType: constants.MEMBERSHIP_TYPES.FAMILY
                        },
                        {
                            fundraiserSignupId: 'fundraiserSignupId-2'
                        },
                        {
                            fundraiserSignupId: 'fundraiserSignupId',
                            startDate: '2020-01-01T00:00:00.000Z',
                            endDate: '2020-01-02T00:00:00.000Z',
                            membershipType: constants.MEMBERSHIP_TYPES.FAMILY
                        }
                    ],
                    save: () => { }
                }])
            });

            sinon.stub(Child, 'batchGet').resolves([
                {
                    id: 'childId1',
                    membershipsPurchased: [
                        {
                            fundraiserSignupId: 'fundraiserSignupId',
                            membershipType: constants.MEMBERSHIP_TYPES.FAMILY
                        },
                        {
                            fundraiserSignupId: 'fundraiserSignupId',
                            startDate: '2020-01-01T00:00:00.000Z',
                            endDate: '2020-01-02T00:00:00.000Z',
                            membershipType: constants.MEMBERSHIP_TYPES.FAMILY
                        },
                        {
                            fundraiserSignupId: 'fundraiserSignupId',
                            startDate: '2020-01-01T00:00:00.000Z',
                            endDate: '2020-01-02T00:00:00.000Z',
                            membershipType: constants.MEMBERSHIP_TYPES.CHILD
                        }
                    ],
                    save: () => { }
                }
            ]);

            sinon.stub(Child, 'get').resolves({
                id: 'childId1',
                membershipsPurchased: [
                    {
                        fundraiserSignupId: 'fundraiserSignupId-3',
                        membershipType: constants.MEMBERSHIP_TYPES.CHILD
                    },
                    {
                        fundraiserSignupId: 'fundraiserSignupId-3',
                        startDate: '2020-01-01T00:00:00.000Z',
                        endDate: '2020-01-02T00:00:00.000Z',
                        membershipType: constants.MEMBERSHIP_TYPES.CHILD
                    },
                    {
                        fundraiserSignupId: 'fundraiserSignupId-3',
                        startDate: '2020-01-01T00:00:00.000Z',
                        endDate: '2020-01-02T00:00:00.000Z',
                        membershipType: constants.MEMBERSHIP_TYPES.FAMILY
                    }
                ],
                save: () => { }
            });

            await handler.handler(event);
            sinon.assert.called(pipelineExecStub);
        });

        it('should process MODIFY fundraiser events and soft delete', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Fundraisers/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test fundraiser' },
                        startDate: { S: '2020-01-01T00:00:00.000Z' },
                        organizationId: {
                            S: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                        },
                        eventType: { S: 'Test event type' },
                        eventScope: { S: 'Test event scope' },
                        status: { S: 'unpublished' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' },
                        isDeleted: { N: 0 }
                    },
                    NewImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test fundraiser' },
                        startDate: { S: '2020-01-01T00:00:00.000Z' },
                        organizationId: {
                            S: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                        },
                        eventType: { S: 'Test event type' },
                        eventScope: { S: 'Test event scope' },
                        status: { S: 'draft' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: {
                            S: '2020-01-01T00:00:00.000Z'
                        },
                        updatedAt: {
                            S: '2020-01-01T00:00:00.000Z'
                        },
                        isDeleted: { N: 1 }
                    }
                }
            };

            const mockedResponse = [
                { childId: 'childId1', associatedOrganizations: ['123'] },
                { childId: 'childId3', associatedOrganizations: ['123'] }
            ];

            const queryStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({
                eq: eqStub.returns({
                    using: usingStub.returns({ exec: execStub.resolves(mockedResponse) })
                })
            });

            sinon.stub(Child, 'batchGet').resolves([
                {
                    id: 'childId1',
                    guardians: ['guardianId1']
                },
                {
                    id: 'childId2',
                    guardians: ['guardianId2']
                }
            ]);

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([
                JSON.stringify({
                    fundraiserId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                    childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    isSignedUp: false, status: ''
                }),
                JSON.stringify({
                    eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eeg',
                    childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    isSignedUp: false, status: ''
                })
            ]);

            const event = {
                Records: [record]
            };

            sinon.stub(User, 'query').
                resolves({ fcmToken: '0fd6a871-a6f5-4690-b06a-d786f1361eef' });

            await handler.handler(event);

            sinon.assert.called(Child.batchGet);
        });

        it('should process MODIFY membership fundraiser events and soft delete', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Fundraisers/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test fundraiser' },
                        startDate: { S: '2020-01-01T00:00:00.000Z' },
                        organizationId: {
                            S: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                        },
                        fundraiserType: { S: 'membershipFundraiser' },
                        eventScope: { S: 'Test event scope' },
                        status: { S: 'unpublished' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' },
                        isDeleted: { N: 0 }
                    },
                    NewImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test fundraiser' },
                        startDate: { S: '2020-01-01T00:00:00.000Z' },
                        organizationId: {
                            S: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                        },
                        fundraiserType: { S: 'membershipFundraiser' },
                        eventScope: { S: 'Test event scope' },
                        status: { S: 'draft' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: {
                            S: '2020-01-01T00:00:00.000Z'
                        },
                        updatedAt: {
                            S: '2020-01-01T00:00:00.000Z'
                        },
                        isDeleted: { N: 1 }
                    }
                }
            };

            const mockedResponse = [
                { id: 'child1', associatedOrganizations: ['123'] }
            ];

            const queryStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({
                eq: eqStub.returns({
                    using: usingStub.returns({ exec: execStub.resolves(mockedResponse) })
                })
            });

            sinon.stub(FundraiserSignup, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        attributes: sinon.stub().returns({
                            exec: sinon.stub().resolves([
                                {
                                    id: 'fundraiserSignupId1',
                                    parentId: 'parentId1',
                                    childId: 'childId1',
                                    purchasedProducts: JSON.stringify([{ membershipType: constants.MEMBERSHIP_TYPES.FAMILY }])
                                },
                                {
                                    id: 'fundraiserSignupId2',
                                    parentId: 'parentId2',
                                    childId: 'childId1',
                                    purchasedProducts: JSON.stringify([{ membershipType: constants.MEMBERSHIP_TYPES.FAMILY }])
                                },
                                {
                                    id: 'fundraiserSignupId3',
                                    parentId: 'parentId3',
                                    childId: 'childId2',
                                    purchasedProducts: JSON.stringify([{ membershipType: constants.MEMBERSHIP_TYPES.FAMILY }])
                                },
                                {
                                    id: 'fundraiserSignupId4',
                                    parentId: 'parentId4',
                                    childId: 'childId4',
                                    purchasedProducts: JSON.stringify([{ membershipType: constants.MEMBERSHIP_TYPES.CHILD }])
                                },
                                {
                                    id: 'fundraiserSignupId5',
                                    parentId: 'parentId4',
                                    childId: 'childId5',
                                    purchasedProducts: JSON.stringify([{ membershipType: constants.MEMBERSHIP_TYPES.CHILD }])
                                }
                            ])
                        })
                    })
                })
            });

            const childGetStub = sinon.stub(Child, 'get');
            childGetStub.withArgs('childId4').resolves({
                save: () => { },
                membershipsPurchased: [{ fundraiserSignupId: 'fundraiserSignupId4', membershipType: constants.MEMBERSHIP_TYPES.FAMILY }]
            });
            childGetStub.withArgs('childId5').resolves(null);

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([
                JSON.stringify({
                    fundraiserId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                    childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    isSignedUp: false, status: ''
                }),
                JSON.stringify({
                    eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eeg',
                    childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    isSignedUp: false, status: ''
                })
            ]);

            const event = {
                Records: [record]
            };

            const userQueryStub = sinon.stub(User, 'query');

            userQueryStub.withArgs({ id: 'parentId1' }).returns({
                exec: sinon.stub().resolves([
                    {
                        children: ['childId3'], save: () => { },
                        membershipsPurchased: [{
                            fundraiserSignupId: 'fundraiserSignupId1',
                            membershipType: constants.MEMBERSHIP_TYPES.FAMILY
                        }]
                    }
                ])
            });

            userQueryStub.withArgs({ id: 'parentId2' }).returns({
                exec: sinon.stub().resolves([
                    {
                        children: [], save: () => { }
                    }
                ])
            });

            userQueryStub.withArgs({ id: 'parentId3' }).returns({
                exec: sinon.stub().resolves([])
            });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'childId1'
            }]);

            await handler.handler(event);

            sinon.assert.called(queryStub);
        });

        it('should process MODIFY fundraiser if status is changed to publish', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Fundraisers/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test fundraiser' },
                        startDate: { S: '2020-01-01T00:00:00.000Z' },
                        organizationId: {
                            S: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                        },
                        eventType: { S: 'Test event type' },
                        eventScope: { S: 'Test event scope' },
                        status: { S: 'published' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: { S: '2020-01-01T00:00:00.000Z' },
                        updatedAt: { S: '2020-01-01T00:00:00.000Z' }
                    },
                    NewImage: {
                        id: { S: '0fd6a871-a6f5-4690-b06a-d786f1361eef' },
                        title: { S: 'Test fundraiser' },
                        startDate: { S: '2020-01-01T00:00:00.000Z' },
                        organizationId: {
                            S: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                        },
                        eventType: { S: 'Test event type' },
                        eventScope: { S: 'Test event scope' },
                        status: { S: 'published' },
                        photoURL: { S: 'Test photo url' },
                        createdAt: {
                            S: '2020-01-01T00:00:00.000Z'
                        },
                        updatedAt: {
                            S: '2020-01-01T00:00:00.000Z'
                        }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            const mockedResponse = [
                { id: 'child1', associatedOrganizations: ['123'] }
            ];

            const queryStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({ eq: eqStub.returns({ using: usingStub.returns({ exec: execStub.resolves(mockedResponse) }) }) });

            sinon.stub(User, 'query').
                resolves({ fcmToken: '0fd6a871-a6f5-4690-b06a-d786f1361eef' });

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([JSON.stringify({
                fundraiserId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                isSignedUp: false, status: ''
            }), JSON.stringify({
                eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eeg',
                childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                isSignedUp: false, status: ''
            })]);

            sinon.stub(Child, 'batchGet').resolves([
                {
                    id: 'childId1',
                    guardians: ['guardianId1']
                },
                {
                    id: 'childId2',
                    guardians: ['guardianId2']
                }
            ]);

            await handler.handler(event);

            sinon.assert.called(pipelineExecStub);
        });

        it('should process MODIFY fundraiser and update existing feeds', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Fundraisers/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: {
                            S: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                        },
                        title: {
                            S: 'Test fundraiser'
                        },
                        startDate: {
                            S: '2020-01-01T00:00:00.000Z'
                        },
                        organizationId: {
                            S: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                        },
                        eventType: {
                            S: 'Test event type'
                        },
                        eventScope: {
                            S: 'Test event scope'
                        },
                        status: {
                            S: 'published'
                        },
                        photoURL: {
                            S: 'Test photo url'
                        },
                        createdAt: {
                            S: '2020-01-01T00:00:00.000Z'
                        },
                        updatedAt: {
                            S: '2020-01-01T00:00:00.000Z'
                        }
                    },
                    NewImage: {
                        id: {
                            S: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                        },
                        title: {
                            S: 'Test fundraiser'
                        },
                        startDate: {
                            S: '2020-01-01T00:00:00.000Z'
                        },
                        organizationId: {
                            S: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                        },
                        eventType: {
                            S: 'Test event type'
                        },
                        eventScope: {
                            S: 'Test event scope'
                        },
                        status: {
                            S: 'draft'
                        },
                        photoURL: {
                            S: 'Test photo url'
                        },
                        createdAt: {
                            S: '2020-01-01T00:00:00.000Z'
                        },
                        updatedAt: {
                            S: '2020-01-01T00:00:00.000Z'
                        }
                    }
                }

            };

            const event = {
                Records: [record]
            };

            const mockedResponse = [
                { childId: 'childId1', associatedOrganizations: ['123'] }
            ];

            sinon.stub(Child, 'batchGet').resolves([
                {
                    id: 'childId1',
                    guardians: ['guardianId1']
                },
                {
                    id: 'childId2',
                    guardians: ['guardianId2']
                }
            ]);

            const queryStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({ eq: eqStub.returns({ using: usingStub.returns({ exec: execStub.resolves(mockedResponse) }) }) });

            sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore').resolves([
                JSON.stringify({
                    fundraiserId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                    childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    isSignedUp: false, status: ''
                }),
                JSON.stringify({
                    eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eeg',
                    childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                    isSignedUp: false, status: ''
                })
            ]);

            sinon.stub(User, 'query').
                resolves({ fcmToken: '0fd6a871-a6f5-4690-b06a-d786f1361eef' });

            await handler.handler(event);

            sinon.assert.called(pipelineExecStub);
        });

        it('should process MODIFY fundraiser and update registered existing feeds', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Fundraisers/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: {
                            S: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                        },
                        title: {
                            S: 'Test fundraiser'
                        },
                        startDate: {
                            S: '2020-01-01T00:00:00.000Z'
                        },
                        organizationId: {
                            S: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                        },
                        eventType: {
                            S: 'Test event type'
                        },
                        eventScope: {
                            S: 'Test event scope'
                        },
                        status: {
                            S: 'published'
                        },
                        photoURL: {
                            S: 'Test photo url'
                        },
                        createdAt: {
                            S: '2020-01-01T00:00:00.000Z'
                        },
                        updatedAt: {
                            S: '2020-01-01T00:00:00.000Z'
                        }
                    },
                    NewImage: {
                        id: {
                            S: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                        },
                        title: {
                            S: 'Test fundraiser'
                        },
                        startDate: {
                            S: '2020-01-01T00:00:00.000Z'
                        },
                        organizationId: {
                            S: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                        },
                        eventType: {
                            S: 'Test event type'
                        },
                        eventScope: {
                            S: 'Test event scope'
                        },
                        status: {
                            S: 'published'
                        },
                        photoURL: {
                            S: 'Test photo url'
                        },
                        createdAt: {
                            S: '2020-01-01T00:00:00.000Z'
                        },
                        updatedAt: {
                            S: '2020-01-01T00:00:00.000Z'
                        }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            const mockedResponse = [
                { childId: 'childId1', associatedOrganizations: ['123'] }
            ];

            sinon.stub(Child, 'batchGet').resolves([
                {
                    id: 'childId1',
                    guardians: ['guardianId1']
                },
                {
                    id: 'childId2',
                    guardians: ['guardianId2']
                }
            ]);

            const queryStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({ eq: eqStub.returns({ using: usingStub.returns({ exec: execStub.resolves(mockedResponse) }) }) });

            const userQueryStub = sinon.stub(User, 'query').
                resolves({ fcmToken: '0fd6a871-a6f5-4690-b06a-d786f1361eef' });

            const getElementsOfSortedSetByScoreStub = sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore');
            getElementsOfSortedSetByScoreStub.onCall(0).resolves([]);
            getElementsOfSortedSetByScoreStub.onCall(1).resolves([JSON.stringify({
                eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                fundraiserId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                isSignedUp: false,
                status: ''
            })]);

            await handler.handler(event);

            userQueryStub.restore();
            queryStub.restore();
            RedisUtil.getElementsOfSortedSetByScore.restore();
        });

        it('should process MODIFY fundraiser and update registered existing feeds if fundraiser type is booster', async () => {
            const record = {
                eventName: 'MODIFY',
                eventSourceARN: 'arn:aws:dynamodb:us-east-2:464376718462:table/Fundraisers/stream/2023-10-27T13:01:18.061',
                dynamodb: {
                    OldImage: {
                        id: {
                            S: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                        },
                        title: {
                            S: 'Test fundraiser'
                        },
                        startDate: {
                            S: '2020-01-01T00:00:00.000Z'
                        },
                        organizationId: {
                            S: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                        },
                        eventType: {
                            S: 'Test event type'
                        },
                        eventScope: {
                            S: 'Test event scope'
                        },
                        status: {
                            S: 'published'
                        },
                        photoURL: {
                            S: 'Test photo url'
                        },
                        createdAt: {
                            S: '2020-01-01T00:00:00.000Z'
                        },
                        updatedAt: {
                            S: '2020-01-01T00:00:00.000Z'
                        }
                    },
                    NewImage: {
                        id: {
                            S: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                        },
                        title: {
                            S: 'Test fundraiser'
                        },
                        startDate: {
                            S: '2020-01-01T00:00:00.000Z'
                        },
                        organizationId: {
                            S: '0fd6a871-a6f5-4690-b06a-d786f1361eef'
                        },
                        fundraiserType: {
                            S: constants.FUNDRAISER_TYPES.BOOSTER
                        },
                        eventScope: {
                            S: 'Test event scope'
                        },
                        status: {
                            S: 'published'
                        },
                        photoURL: {
                            S: 'Test photo url'
                        },
                        createdAt: {
                            S: '2020-01-01T00:00:00.000Z'
                        },
                        updatedAt: {
                            S: '2020-01-01T00:00:00.000Z'
                        }
                    }
                }
            };

            const event = {
                Records: [record]
            };

            const mockedResponse = [
                { childId: 'child1', associatedOrganizations: ['123'] }
            ];

            sinon.stub(Child, 'batchGet').resolves([
                {
                    id: 'childId1',
                    guardians: ['guardianId1']
                },
                {
                    id: 'childId2',
                    guardians: ['guardianId2']
                }
            ]);

            const queryStub = sinon.stub(ChildOrganizationMapping, 'query');
            const eqStub = sinon.stub();
            const usingStub = sinon.stub();
            const execStub = sinon.stub();
            queryStub.returns({ eq: eqStub.returns({ using: usingStub.returns({ exec: execStub.resolves(mockedResponse) }) }) });

            const userQueryStub = sinon.stub(User, 'query').
                resolves({ fcmToken: '0fd6a871-a6f5-4690-b06a-d786f1361eef' });

            const getElementsOfSortedSetByScoreStub = sinon.stub(RedisUtil, 'getElementsOfSortedSetByScore');
            getElementsOfSortedSetByScoreStub.onCall(0).resolves([]);
            getElementsOfSortedSetByScoreStub.onCall(1).resolves([JSON.stringify({
                eventId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                fundraiserId: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
                childId: 'ace0149e-447d-4f7f-9402-95a96963de9b',
                isSignedUp: false,
                status: ''
            })]);

            await handler.handler(event);

            userQueryStub.restore();
            queryStub.restore();
            RedisUtil.getElementsOfSortedSetByScore.restore();
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
