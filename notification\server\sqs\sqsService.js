const { SQSClient, DeleteMessageCommand } = require('@aws-sdk/client-sqs');
const Notification = require('../util/notification');
const CONSTANTS = require('../util/constants');
const config = { region: process.env.AWS_REGION };
const PushNotificationService = require('../pushNotification/pushNotificationService');
const ChildModel = require('../models/child.model');
const sqsClient = new SQSClient(config);

class SqsService {
    static async pullEventFromQueue (event) {
        try {
            for (const record of event.Records) {
                if (record.eventSource === 'aws:sqs') {
                    const body = JSON.parse(record.body);
                    CONSOLE_LOGGER.info('Received message from SQS: body ', body);
                    await this.processNotification(body, record.receiptHandle);
                    await this.deleteMessage(record.receiptHandle);
                }
            }
        } catch (error) {
            CONSOLE_LOGGER.error(error.message, 'Error sending notification');
        }
    }

    static async processNotification (message) {
        const { trigger, childId, title, message: msg, topicKey, eventSignUp, eventDetails, score, postDetails, isPost } = message;
        CONSOLE_LOGGER.info(' process SQS Notification :: ', trigger);
        if (trigger === CONSTANTS.TRIGGER.REQUESTED_BY) {
            const notificationObject = Notification.createNotificationObject({
                title,
                topic: topicKey,
                body: msg,
                data: { childId },
                clickAction: 'vaalee://routeName=requestedBy'
            });
            await Notification.sendNotification(notificationObject);
            CONSOLE_LOGGER.info('Sending notification for requestedBy with object: ', notificationObject);
            await PushNotificationService.pushInNotificationTable({
                userId: eventSignUp.parentId,
                associatedChildId: childId,
                notificationAction: 'vaalee://routeName=requestedBy'
            }, { score, childId, eventId: eventSignUp.eventId }, msg);
            CONSOLE_LOGGER.info('Notification sent for requestedBy with object: ', notificationObject);
        }
        if (trigger === CONSTANTS.TRIGGER.EVENT_SIGNUP) {
            const child = await ChildModel.get({ id: childId });
            const msg = `Your ${eventDetails.title} event request for ${child.firstName} ${child.lastName} is now complete`;
            const clickAction = 'vaalee://routeName=eventDetails';
            const notificationData = {
                details: JSON.stringify({
                    associatedChild: { id: childId },
                    id: eventSignUp.eventId,
                    startDateTime: eventDetails.startDateTime,
                    endDateTime: eventDetails.endDateTime,
                    score
                }),
                child: JSON.stringify({
                    id: child.id,
                    firstName: child.firstName,
                    lastName: child.lastName,
                    photoURL: child.photoURL,
                    associatedColor: child.associatedColor
                }),
                route: 'eventDetails'
            };
            const notificationObject = Notification.createNotificationObject({
                clickAction,
                title: 'Event Confirmation!',
                topic: topicKey,
                body: msg,
                data: notificationData
            });
            CONSOLE_LOGGER.info('Sending notification for event signup with object: ', notificationObject);
            await Notification.sendNotification(notificationObject);
            await PushNotificationService.pushInNotificationTable({
                associatedChildId: childId,
                notificationAction: clickAction,
                title: 'Event Confirmation!'
            }, notificationData, msg);
            CONSOLE_LOGGER.info('Notification sent for event signup with object: ', notificationObject);
        }
        if (trigger === CONSTANTS.TRIGGER.FUNDRAISER_SIGNUP) {
            const childDetails = await ChildModel.get({ id: childId });
            // eslint-disable-next-line max-len
            const message = `Your ${eventDetails.title} fundraiser request for ${childDetails.firstName} ${childDetails.lastName} is now complete.`;
            const clickAction = 'vaalee://routeName=fundraiserDetails';
            const notificationDataObj = {
                details: JSON.stringify({
                    associatedChild: { id: childId },
                    id: eventSignUp.eventId,
                    isFundraiser: true,
                    fundraiserSignupId: eventSignUp.id,
                    startDateTime: eventDetails.startDateTime,
                    endDateTime: eventDetails.endDateTime,
                    score
                }),
                child: JSON.stringify({
                    id: childDetails.id,
                    firstName: childDetails.firstName,
                    lastName: childDetails.lastName,
                    photoURL: childDetails.photoURL,
                    associatedColor: childDetails.associatedColor
                }),
                route: 'fundraiserDetails'
            };
            const createdNotificationObject = Notification.createNotificationObject({
                clickAction,
                title: 'Fundraiser Confirmation!',
                topic: topicKey,
                body: message,
                data: notificationDataObj
            });
            CONSOLE_LOGGER.info('Sending notification for fundraiser signup with object: ', createdNotificationObject);
            await Notification.sendNotification(createdNotificationObject);
            await PushNotificationService.pushInNotificationTable({
                associatedChildId: childId,
                notificationAction: clickAction,
                title: 'Fundraiser Confirmation!'
            }, notificationDataObj, message);
            CONSOLE_LOGGER.info('Notification sent for fundraiser signup with object: ', createdNotificationObject);
        }
        if (trigger === CONSTANTS.TRIGGER.BOOSTER_DONATION) {
            const child = await ChildModel.get({ id: childId });
            const clickAction = 'vaalee://routeName=fundraiserDetails';
            const notificationData = {
                details: JSON.stringify({
                    associatedChild: { id: childId },
                    id: eventSignUp?.fundraiserId,
                    isFundraiser: true,
                    fundraiserSignupId: eventSignUp?.fundraiserSignupId,
                    startDateTime: eventDetails?.startDate,
                    endDateTime: eventDetails?.endDate,
                    score
                }),
                child: JSON.stringify({
                    id: child.id,
                    firstName: child.firstName,
                    lastName: child.lastName,
                    photoURL: child.photoURL,
                    associatedColor: child.associatedColor
                }),
                route: 'fundraiserDetails'
            };
            const notificationObject = Notification.createNotificationObject({
                clickAction,
                title,
                topic: topicKey,
                body: msg,
                data: notificationData
            });
            CONSOLE_LOGGER.info('Sending notification for booster donation with object: ', notificationObject);
            await Notification.sendNotification(notificationObject);
            await PushNotificationService.pushInNotificationTable({
                associatedChildId: childId,
                notificationAction: clickAction,
                title
            }, notificationData, msg);
            CONSOLE_LOGGER.info('Notification sent for booster donation with object: ', notificationObject);
        }
        if (trigger === CONSTANTS.TRIGGER.POST_CREATED) {
            const child = await ChildModel.get({ id: childId });
            const clickAction = 'vaalee://routeName=postDetails';
            const notificationData = {
                details: JSON.stringify({
                    associatedChild: { id: childId },
                    id: postDetails.id,
                    publishedDate: postDetails.publishedDate,
                    isPost,
                    score
                }),
                child: JSON.stringify({
                    id: child.id,
                    firstName: child.firstName,
                    lastName: child.lastName,
                    photoURL: child.photoURL,
                    associatedColor: child.associatedColor
                }),
                route: 'postDetails'
            };
            const notificationObject = Notification.createNotificationObject({
                clickAction,
                title,
                topic: topicKey,
                body: msg,
                data: notificationData
            });
            CONSOLE_LOGGER.info('Sending notification for post published with object: ', { notificationObject });
            // await Notification.sendNotification(notificationObject);
            await PushNotificationService.pushInNotificationTable({
                associatedChildId: childId,
                notificationAction: clickAction,
                title
            }, notificationData, msg, trigger);
            CONSOLE_LOGGER.info('Notification sent for post published with object: ', notificationObject);
        }
        if (trigger === CONSTANTS.TRIGGER.EVENT_UPDATED) {
            const child = await ChildModel.get({ id: childId });
            const msg = `${title} is updated for your child ${child.firstName} ${child.lastName}.`;
            const clickAction = 'vaalee://routeName=eventDetails';
            const notificationData = {
                details: JSON.stringify({
                    associatedChild: { id: childId },
                    id: eventSignUp.eventId,
                    startDateTime: eventDetails.startDateTime,
                    endDateTime: eventDetails.endDateTime,
                    score
                }),
                child: JSON.stringify({
                    id: childId,
                    firstName: child.firstName,
                    lastName: child.lastName,
                    photoURL: child.photoURL,
                    associatedColor: child.associatedColor
                }),
                route: 'eventDetails'
            };
            const notificationObject = Notification.createNotificationObject({
                clickAction,
                title: 'Event Updated!',
                topic: topicKey,
                body: msg,
                data: notificationData
            });
            CONSOLE_LOGGER.info('Sending notification for event updated with object: ', notificationObject);
            await Notification.sendNotification(notificationObject);
            await PushNotificationService.pushInNotificationTable({
                associatedChildId: childId,
                notificationAction: clickAction,
                title: 'Event Updated!'
            }, notificationData, msg);
            CONSOLE_LOGGER.info('Notification sent for event updated with object: ', notificationObject);
        }
        if (trigger === CONSTANTS.TRIGGER.CONVERSATION) {
            CONSOLE_LOGGER.info('Start sending notification for conversation');
            const { messageText, senderName, groupName, topicKey, groupId } = message;
            const clickAction = 'vaalee://routeName=conversation';
            const notificationData = {
                details: JSON.stringify({
                    actionText: 'Message Received!',
                    groupId
                }),
                route: 'conversation'
            };
            const notificationObject = Notification.createNotificationObject({
                clickAction,
                topic: topicKey,
                body: messageText,
                title: senderName + ' to ' + groupName,
                data: notificationData
            });
            const resp = await Notification.sendNotification(notificationObject);
            if (resp.isError) {
                throw new Error('Error sending notification');
            } else {
                CONSOLE_LOGGER.info('Notification sent for conversation');
            }
        }
        if (trigger === CONSTANTS.TRIGGER.PERSONAL_CONVERSATION) {
            CONSOLE_LOGGER.info('Start sending notification for personal conversation');
            const { messageText, senderName, topicKey, conversationId, receiverId, senderId } = message;
            const clickAction = 'vaalee://routeName=personalConversation';
            const notificationData = {
                details: JSON.stringify({
                    actionText: 'Personal Message Received!',
                    conversationId,
                    receiverId,
                    senderId
                }),
                route: 'personalConversation'
            };
            const notificationObject = Notification.createNotificationObject({
                clickAction,
                topic: topicKey,
                body: messageText,
                title: senderName,
                data: notificationData
            });
            const resp = await Notification.sendNotification(notificationObject);
            if (resp.isError) {
                throw new Error('Error sending notification');
            } else {
                CONSOLE_LOGGER.info('Notification sent for personal conversation');
            }
        }
    }

    static async deleteMessage (receiptHandle) {
        const deleteParams = {
            QueueUrl: process.env.QUEUE_URL,
            ReceiptHandle: receiptHandle
        };
        CONSOLE_LOGGER.info('Deleting message from queue with params: ', deleteParams);
        const command = new DeleteMessageCommand(deleteParams);
        return await sqsClient.send(command);
    }
}

module.exports = SqsService;
