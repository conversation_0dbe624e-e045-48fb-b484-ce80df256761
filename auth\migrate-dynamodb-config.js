const AWS = require('aws-sdk');
const dynamoose = require('dynamoose');

const dotenv = require('dotenv');
const env = process.env.NODE_ENV || 'local';
dotenv.config({ path: env + '.env' });

AWS.config.update({
    region: process.env.AWS_DB_REGION,
    endpoint: process.env.DB_HOST,
    accessKeyId: process.env.ACCESS_KEY_ID,
    secretAccessKey: process.env.SECRET_ACCESS_KEY
});

dynamoose.aws.ddb.set(AWS);

module.exports = {
    dynamodb: dynamoose
};
