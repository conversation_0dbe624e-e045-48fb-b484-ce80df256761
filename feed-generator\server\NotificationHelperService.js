const PendingPushNotification = require('./models/pendingPushNotification.model');
const EventSignups = require('./models/eventSignup.model');
const MOMENT = require('moment');
const CONSOLE_LOGGER = require('./logger');
const { SQSClient, SendMessageCommand } = require('@aws-sdk/client-sqs');
const CONSTANTS = require('./constants');

const config = { region: process.env.AWS_REGION };

const sqsClient = new SQSClient(config);

class NotificationHelperService {
    /**
     * @description Update pending push notifications
     * <AUTHOR>
     * @param {Object} event - The event object
     * @returns {Promise<number>} - Returns the number of notifications updated
    */
    static async updatePendingPushNotifications (event) {
        const notifications = await PendingPushNotification.query('eventId')
            .eq(event.id)
            .using('eventId-index')
            .exec();

        CONSOLE_LOGGER.info(
            'Pending push notifications for event',
            event.id,
            notifications
        );

        const keys = notifications?.map((notification) => ({
            id: notification.id
        }));

        if (keys && keys.length > 0) {
            for (const key of keys) {
                await PendingPushNotification.delete(key);
            }
        }

        CONSOLE_LOGGER.info(
            'Pending push notifications deleted for event',
            event.id
        );

        const payload = { ...event };
        delete payload.participants;

        const signups = await EventSignups.query('eventId')
            .eq(event.id)
            .using('eventId-index')
            .attributes(['childId'])
            .exec();

        CONSOLE_LOGGER.info(
            'Sending push notifications for updated event',
            event.id,
            { signups }
        );

        for (const signup of signups) {
            await this.addNotificationToPendingPushNotifications(
                payload,
                event.id,
                signup.childId,
                MOMENT(event.startDateTime).valueOf()
            );
        }

        return notifications.length;
    }

    /**
     * @description Add notification to pending push notifications
     * <AUTHOR>
     * @param {Object} feed - The feed object
     * @param {string} eventId - The event id
     * @param {string} childId - The child id
     * @param {number} score - The score
     * @returns {Promise<number>} - Returns the number of notifications added
    */
    static async addNotificationToPendingPushNotifications (
        feed,
        eventId,
        childId,
        score
    ) {
        const currentTime = MOMENT().utc();
        const pushNotificationTime72 = MOMENT(feed.startDateTime).subtract(
            72,
            'hours'
        );

        if (pushNotificationTime72.isAfter(currentTime)) {
            await PendingPushNotification.create({
                eventId,
                pushNotificationTime: pushNotificationTime72.toDate(),
                associatedChildId: childId,
                notificationAction: 'vaalee://routeName=eventDetails',
                payload: { ...feed, score, triggerAt: '72' }
            });
        }

        const pushNotificationTime24 = MOMENT(feed.startDateTime).subtract(
            24,
            'hours'
        );

        if (pushNotificationTime24.isAfter(currentTime)) {
            await PendingPushNotification.create({
                eventId,
                pushNotificationTime: pushNotificationTime24.toDate(),
                associatedChildId: childId,
                notificationAction: 'vaalee://routeName=eventDetails',
                payload: { ...feed, score, triggerAt: '24' }
            });
        }

        return currentTime;
    }

    /**
     * @description Send post notification
     * <AUTHOR>
     * @param {Object} post - The post object
     * @param {Object} childDetails - The child details object
     * @param {string} organizationName - The organization name
     * @returns {Promise<number>} - Returns the number of notifications added
     */
    static async sendPostNotification (post, childDetails, organizationName) {
        return await this.sendNotificationInQueue({
            topicKey: `childId-${childDetails.id}`,
            trigger: 'postCreated',
            childId: childDetails.id,
            title: `New post published by ${organizationName}`,
            message: `${post.title}`,
            postDetails: post,
            score: MOMENT(post.publishedDate).valueOf(),
            isPost: true,
            childDetails
        });
    }

    /**
     * @description Send notification in queue
     * <AUTHOR>
     * @param {Object} notificationObject - The notification object
     * @returns {Promise<number>} - Returns the number of notifications added
     */
    static async sendNotificationInQueue (notificationObject) {
        const input = {
            QueueUrl: process.env.QUEUE_URL,
            MessageBody: JSON.stringify(notificationObject)
        };
        const command = new SendMessageCommand(input);
        await sqsClient.send(command);
    }

    /**
     * @description Send event notification
     * <AUTHOR>
     * @param {Object} eventSignUp - The event sign up object
     * @param {Object} feed - The feed object
     * @param {Object} childDetails - The child details object
     * @returns {Promise<number>} - Returns the number of notifications added
     */
    static async sendEventNotification (eventSignUp, feed, childDetails) {
        const payload = { ...feed };
        delete payload.participants;

        await this.sendNotificationInQueue({
            topicKey: `childId-${eventSignUp.childId}`,
            trigger: 'eventSignup',
            childId: eventSignUp.childId,
            title: 'Event Confirmation!',
            message: 'You have successfully registered for this event.',
            eventDetails: payload,
            score: MOMENT(feed.startDateTime).valueOf(),
            childDetails,
            eventSignUp
        });

        return await this.addNotificationToPendingPushNotifications(
            payload,
            eventSignUp.eventId,
            eventSignUp.childId,
            MOMENT(feed.startDateTime).valueOf()
        );
    }

    /**
     * @description Send fundraiser notification
     * <AUTHOR>
     * @param {Object} fundraiserSignup - The fundraiser signup object
     * @param {Object} feed - The feed object
     * @returns {Promise<number>} - Returns the number of notifications added
     */
    static async sendFundraiserNotification (fundraiserSignup, feed) {
        return await NotificationHelperService.sendNotificationInQueue({
            topicKey: `childId-${fundraiserSignup.childId}`,
            trigger: 'fundraiserSignup',
            childId: fundraiserSignup.childId,
            title: 'Fundraiser Confirmation!',
            message: 'Your order for the fundraiser has been confirmed.',
            eventDetails: feed,
            score: MOMENT(feed.startDate).valueOf(),
            eventSignUp: fundraiserSignup
        });
    }

    /**
     * @description Send notification for booster donation
     * <AUTHOR>
     * @param {Object} fundraiserSignup - The fundraiser signup object
     * @param {Object} fundraiserFeed - The fundraiser feed object
     * @param {number} totalAmountRaised - The total amount raised
     * @param {Object} orgManagedFundraiserBoosterDonation - The org managed fundraiser booster donation object
     * @param {number} score - The score
     */
    static async sendNotificationForBoosterDonation (
        fundraiserSignup,
        fundraiserFeed,
        totalAmountRaised,
        orgManagedFundraiserBoosterDonation,
        score
    ) {
        fundraiserFeed = fundraiserFeed ? JSON.parse(fundraiserFeed) : {};
        // eslint-disable-next-line max-len
        const message = `${orgManagedFundraiserBoosterDonation.donorName} donated $${orgManagedFundraiserBoosterDonation.amount} to ${fundraiserFeed.title}. Total amount raised so far is $${totalAmountRaised}`;
        // eslint-disable-next-line max-len
        const messageWithoutFundraiserName = `${orgManagedFundraiserBoosterDonation.donorName} donated $${orgManagedFundraiserBoosterDonation.amount}. Total amount raised so far is $${totalAmountRaised}`;

        await NotificationHelperService.sendNotificationInQueue({
            topicKey: `childId-${orgManagedFundraiserBoosterDonation?.childId}`,
            trigger: CONSTANTS.TRIGGER.BOOSTER_DONATION,
            childId: orgManagedFundraiserBoosterDonation?.childId,
            title: 'Donation Received!',
            message: fundraiserFeed.title ? message : messageWithoutFundraiserName,
            eventDetails: fundraiserFeed,
            eventSignUp: fundraiserSignup,
            score
        });
    }
}

module.exports = NotificationHelperService;
