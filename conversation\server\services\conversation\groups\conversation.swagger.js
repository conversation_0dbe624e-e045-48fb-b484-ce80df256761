/**
 * Swagger schema for conversation routes
 */

/**
 * @openapi
 * components:
 *      schemas:
 *          successGetGroupConversationList:
 *              type: object
 *              properties:
 *                  status:
 *                      $ref: '#/components/messageDefinition/properties/status'
 *                  message:
 *                      $ref: '#/components/messageDefinition/properties/message'
 *                  data:
 *                      type: Array
 *                      description: Array of group conversations
 *              example:
 *                  status: 1
 *                  message: Success
 *                  data:
 *                      - groupId: 1
 *                        groupName: Group 1
 *                        groupImage: Test image
 *                        isAdmin: true
 *                        childrenIds: [1, 2, 3]
 *                        organizationId: 1
 *          successGetGroupMembers:
 *              type: object
 *              properties:
 *                  status:
 *                      $ref: '#/components/messageDefinition/properties/status'
 *                  message:
 *                      $ref: '#/components/messageDefinition/properties/message'
 *                  data:
 *                      type: Array
 *                      description: Array of group members
 *              example:
 *                  status: 1
 *                  message: Success
 *                  data:
 *                      - userId: 1
 *                        groupId: 1
 *                        status: active
 *                        muteConversation: false
 *                        childrenIds: [1, 2, 3]
 *                        isAdmin: true
 *                        id: 1
 *                        userName: <PERSON>
 *                        role: Admin
 *                        children: [
 *                          {
 *                              id: 1,
 *                              name: Child 1
 *                          }
 *                        ]
 *          successGeneratePresignedUrl:
 *              type: object
 *              properties:
 *                  status:
 *                      $ref: '#/components/messageDefinition/properties/status'
 *                  message:
 *                      $ref: '#/components/messageDefinition/properties/message'
 *                  data:
 *                      type: object
 *                      properties:
 *                          presignedUrl:
 *                              type: string
 *                              description: Presigned url
 *                          fileName:
 *                              type: string
 *                              description: File name
 *              example:
 *                  status: 1
 *                  message: Success
 *                  data:
 *                      presignedUrl: https://example.com/presigned-url
 *                      fileName: example.png
 */


/**
 * @openapi
 * /conversation/group-list:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Conversation]
 *      summary: Get group conversation list
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successGetGroupConversationList'
 *          400:
 *              description: Bad Request
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 *          401:
 *              description: Unauthorized
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /conversation/group-members:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Conversation]
 *      summary: Get group members
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successGetGroupMembers'
 *          400:
 *              description: Bad Request
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 *          401:
 *              description: Unauthorized
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /conversation/generate-presigned-url:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Conversation]
 *      summary: Generate presigned url
 *      parameters:
 *          - in: query
 *            name: isGroupMessage
 *            schema:
 *              type: string
 *            description: Is group message
 *          - in: query
 *            name: conversationId
 *            schema:
 *              type: string
 *            description: Conversation id
 *          - in: query
 *            name: messageId
 *            schema:
 *              type: string
 *            description: Message id
 *          - in: query
 *            name: isThumbnail
 *            schema:
 *              type: string
 *            description: Is thumbnail
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successGeneratePresignedUrl'
 *          400:
 *              description: Bad Request
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 *          401:
 *              description: Unauthorized
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */
