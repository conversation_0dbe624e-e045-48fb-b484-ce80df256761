module.exports = {
    getBoosterDetails: [
        {
            it: 'As a user I should validate if fundraiserId is not passed',
            options: {
                fundraiserId: ''
            },
            status: 0
        },
        {
            it: 'As a user I should validate if fundraiserId is not uuid',
            options: {
                fundraiserId: '1234567890'
            },
            status: 0
        }
    ],
    addManualDonations: [
        {
            it: 'As a user I should validate if donations array is not empty',
            options: {
                donations: []
            },
            status: 0
        },
        {
            it: 'As a user I should validate if fundraiserBoosterId is not passed',
            options: {
                donations: [
                    {}
                ]
            },
            status: 0
        },
        {
            it: 'As a user I should validate if fundraiserBoosterId is not uuid',
            options: {
                donations: [
                    {}
                ],
                fundraiserBoosterId: '1234567890'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if childName is not passed',
            options: {
                donations: [
                    {}
                ],
                fundraiserBoosterId: 'cf972fee-97a9-401c-a84a-6d2da501d21e'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if donor<PERSON><PERSON> is not passed',
            options: {
                donations: [
                    {
                        fundraiserBoosterId: 'cf972fee-97a9-401c-a84a-6d2da501d21e',
                        childName: 'childName'
                    }
                ]
            },
            status: 0
        },
        {
            it: 'As a user I should validate if donorMessage is not passed',
            options: {
                donations: [
                    {
                        childName: 'childName',
                        donorName: 'donorName'
                    }
                ],
                fundraiserBoosterId: 'cf972fee-97a9-401c-a84a-6d2da501d21e'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if amount is not passed',
            options: {
                donations: [
                    {
                        childName: 'childName',
                        donorName: 'donorName',
                        donorMessage: 'donorMessage'
                    }
                ],
                fundraiserBoosterId: 'cf972fee-97a9-401c-a84a-6d2da501d21e'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if amount is not a number',
            options: {
                donations: [
                    {
                        childName: 'childName',
                        donorName: 'donorName',
                        donorMessage: 'donorMessage',
                        amount: 'amount'
                    }
                ],
                fundraiserBoosterId: 'cf972fee-97a9-401c-a84a-6d2da501d21e'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if expectDonorMatch is not a boolean',
            options: {
                donations: [
                    {
                        childName: 'childName',
                        donorName: 'donorName',
                        donorMessage: 'donorMessage',
                        amount: 100,
                        expectDonorMatch: 'expectDonorMatch'
                    }
                ],
                fundraiserBoosterId: 'cf972fee-97a9-401c-a84a-6d2da501d21e'
            },
            status: 0
        }
    ]
};
