const Organization = require('../server/models/organization.model');
const CONSOLE_LOGGER = require('../server/util/logger');

exports.up = async function () {
    const organizationsData = [
        {
            id: '0fd6a871-a6f5-4690-b06a-d786f1361eef',
            name: 'Rainbow Elementary School',
            zipCode: '12345',
            applicableAgeRange: 'Kindergarten to Grade 5',
            organizationType: 'Public',
            category: 'School',
            address: '123, Main Street',
            country: 'USA',
            state: 'New York',
            city: 'New York',
            
        },
        {
            id: 'eeaab921-ff0b-4b91-9cb6-473ef8a11908',
            name: 'Sunny Day Preschool',
            zipCode: '67890',
            applicableAgeRange: '2 years to 5 years',
            organizationType: 'Private', category: 'School', 
            address: '123, Main Street',
            country: 'USA',
            state: 'New York',
            city: 'New York', },
        {
            id: 'd0bd7969-b661-4eac-911c-20dcc2523853',
            name: 'Maplewood Middle School',
            zipCode: '54321',
            applicableAgeRange: 'Grade 6 to Grade 8',
            organizationType: 'Public',
            category: 'School',
            address: '123, Main Street',
            country: 'USA',
            state: 'New York',
            city: 'New York', },
        {
            id: '1996eabf-1a94-4689-a3c5-8b913744e314',
            name: 'Little Explorers Montessori',
            zipCode: '98765',
            applicableAgeRange: '18 months to 6 years',
            organizationType: 'Montessori/Private',
            category: 'School',
            address: '123, Main Street',
            country: 'USA',
            state: 'New York',
            city: 'New York',
        }
    ];
    try {
        await Promise.all(organizationsData.map(async userData => await Organization.create(userData)));
        CONSOLE_LOGGER.debug('All organizations created successfully');
    } catch (error) {
        CONSOLE_LOGGER.error('Error creating organizations:', error);
    }
};

exports.down = async function () {
    try {
        const organizationIds = [
            '0fd6a871-a6f5-4690-b06a-d786f1361eef',
            'eeaab921-ff0b-4b91-9cb6-473ef8a11908',
            'd0bd7969-b661-4eac-911c-20dcc2523853',
            '1996eabf-1a94-4689-a3c5-8b913744e314'
        ];
        await Promise.all(organizationIds.map(async id => await Organization.delete(id)));
        CONSOLE_LOGGER.debug('Organizations deleted successfully');
    } catch (error) {
        CONSOLE_LOGGER.error('Error deleting users:', error);
    }
};
