const chai = require('chai');
const chaiHttp = require('chai-http');
const User = require('../../../models/user.model');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
const TestCase = require('./testcaseForgotPassword');
const sinon = require('sinon');
const Utils = require('../../../util/utilFunctions');
chai.use(chaiHttp);

Utils.addCommonReqTokenForHMac(request);
describe('Forgot Password', () => {
    TestCase.forgotPassword.forEach((data) => {
        it(data.it, (done) => {
            request(process.env.BASE_URL)
                .post('/auth/forgot-password')
                .send(data.options)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });
    });

    it('As a user I should send otp to valid user', (done) => {
        const data = {
            email: '<EMAIL>'
        };
        const scanStub = sinon.stub();
        const eqStub = sinon.stub();
        const execStub = sinon.stub();
        const saveStub = sinon.stub().resolves();
        scanStub.returns({ eq: eqStub });
        eqStub.returns({ exec: execStub });
        execStub.resolves({
            count: 1, toJSON: () => [{ id: '**********', status: 'active' }]
        });
        sinon.replace(User, 'query', scanStub);
        sinon.replace(User.prototype, 'save', saveStub);
        sinon.stub(User, 'update').resolves();
        request(process.env.BASE_URL)
            .post('/auth/forgot-password')
            .send(data)
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
                sinon.restore();
                done();
            });
    });

    it('As a user I should not send otp if user is invalid', (done) => {
        const data = {
            email: '<EMAIL>'
        };

        const scanStub = sinon.stub();
        const eqStub = sinon.stub();
        const execStub = sinon.stub();
        const saveStub = sinon.stub().resolves();
        scanStub.returns({ eq: eqStub });
        eqStub.returns({ exec: execStub });
        execStub.resolves({ count: 0 });
        sinon.replace(User, 'query', scanStub);
        sinon.replace(User.prototype, 'save', saveStub);
        sinon.stub(User, 'update').resolves();
        request(process.env.BASE_URL)
            .post('/auth/forgot-password')
            .send(data)
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 404);
                sinon.restore();
                done();
            });
    });

    it('As a user I should show error if the user is deleted', (done) => {
        const data = {
            email: '<EMAIL>'
        };

        const scanStub = sinon.stub();
        const eqStub = sinon.stub();
        const execStub = sinon.stub();
        const saveStub = sinon.stub().resolves();
        scanStub.returns({ eq: eqStub });
        eqStub.returns({ exec: execStub });
        execStub.resolves({
            count: 1, toJSON: () => [{ id: '**********', status: 'active', isDeleted: 1 }]
        });
        sinon.replace(User, 'query', scanStub);
        sinon.replace(User.prototype, 'save', saveStub);
        sinon.stub(User, 'update').resolves();
        request(process.env.BASE_URL)
            .post('/auth/forgot-password')
            .send(data)
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 403);
                sinon.restore();
                done();
            });
    });

    it('As a user I should show error if the user is not active', (done) => {
        const data = {
            email: '<EMAIL>'
        };

        const scanStub = sinon.stub();
        const eqStub = sinon.stub();
        const execStub = sinon.stub();
        const saveStub = sinon.stub().resolves();
        scanStub.returns({ eq: eqStub });
        eqStub.returns({ exec: execStub });
        execStub.resolves({
            count: 1, toJSON: () => [{ id: '**********', status: 'inactive', isDeleted: 0 }]
        });
        sinon.replace(User, 'query', scanStub);
        sinon.replace(User.prototype, 'save', saveStub);
        sinon.stub(User, 'update').resolves();
        request(process.env.BASE_URL)
            .post('/auth/forgot-password')
            .send(data)
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 403);
                sinon.restore();
                done();
            });
    });
});

describe('Verify otp for forgot password', () => {
    it('As a user, I should verify otp for forgot password', (done) => {
        const verifyAccount = {
            email: '<EMAIL>',
            otp: 123456
        };
        const scanStub = sinon.stub();
        const eqStub = sinon.stub();
        const execStub = sinon.stub();
        const saveStub = sinon.stub().resolves();
        scanStub.returns({ eq: eqStub });
        eqStub.returns({ exec: execStub });
        execStub.resolves({
            count: 1, toJSON: () => [{ otp: 123456, id: '**********', otpExpiryDate: MOMENT().add(CONSTANTS.OTP_EXPIRY_DURATION, 'minutes').utc().toDate() }]
        });
        sinon.replace(User, 'query', scanStub);
        sinon.replace(User.prototype, 'save', saveStub);
        request(process.env.BASE_URL)
            .post('/auth/verify-otp')
            .send(verifyAccount)
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
                sinon.restore();
                done();
            });
    });

    it('As a user, I should not verify invalid otp for forgot password', (done) => {
        const verifyAccount = {
            email: '<EMAIL>',
            otp: 123456
        };

        const scanStub = sinon.stub();
        const eqStub = sinon.stub();
        const execStub = sinon.stub();
        const saveStub = sinon.stub().resolves();
        scanStub.returns({ eq: eqStub });
        eqStub.returns({ exec: execStub });
        execStub.resolves({
            count: 1, toJSON: () => [{ otp: 123456, id: '**********', otpExpiryDate: '*************' }]
        });
        sinon.replace(User, 'query', scanStub);
        sinon.replace(User.prototype, 'save', saveStub);
        request(process.env.BASE_URL)
            .post('/auth/verify-otp')
            .send(verifyAccount)
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                sinon.restore();
                done();
            });
    });

    it('As a user, I should not verify if user invalid', (done) => {
        const verifyAccount = {
            email: '<EMAIL>',
            otp: 123456
        };

        const scanStub = sinon.stub();
        const eqStub = sinon.stub();
        const execStub = sinon.stub();
        const saveStub = sinon.stub().resolves();
        scanStub.returns({ eq: eqStub });
        eqStub.returns({ exec: execStub });
        execStub.resolves({ count: 0 });
        sinon.replace(User, 'query', scanStub);
        sinon.replace(User.prototype, 'save', saveStub);
        request(process.env.BASE_URL)
            .post('/auth/verify-otp')
            .send(verifyAccount)
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 404);
                sinon.restore();
                done();
            });
    });
});

describe('Reset password', () => {
    it('As a user, I should reset password', (done) => {
        const resetData = {
            email: '<EMAIL>',
            password: 'Test@123',
            otp: 123456
        };
        const scanStub = sinon.stub();
        const eqStub = sinon.stub();
        const execStub = sinon.stub();
        const saveStub = sinon.stub().resolves();
        scanStub.returns({ eq: eqStub });
        eqStub.returns({ exec: execStub });
        execStub.resolves({
            count: 1, toJSON: () => [{ otp: 123456, id: '**********' }]
        });
        sinon.replace(User, 'query', scanStub);
        sinon.replace(User.prototype, 'save', saveStub);
        sinon.stub(User, 'update').resolves();
        request(process.env.BASE_URL)
            .post('/auth/reset-password')
            .send(resetData)
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
                sinon.restore();
                done();
            });
    });

    it('As a user, I should not reset password if user is invalid', (done) => {
        const resetData = {
            email: '<EMAIL>',
            password: 'Test@123',
            otp: 123456
        };
        const scanStub = sinon.stub();
        const eqStub = sinon.stub();
        const execStub = sinon.stub();
        const saveStub = sinon.stub().resolves();
        scanStub.returns({ eq: eqStub });
        eqStub.returns({ exec: execStub });
        execStub.resolves({
            count: 0
        });
        sinon.replace(User, 'query', scanStub);
        sinon.replace(User.prototype, 'save', saveStub);
        sinon.stub(User, 'update').resolves();
        request(process.env.BASE_URL)
            .post('/auth/reset-password')
            .send(resetData)
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 404);
                sinon.restore();
                done();
            });
    });
});
