/**
 * This file contains routes used for register.
 * Created by Growexx on 09/11/2023.
 * @name registerRoutes
 */
const router = require('express').Router();

const FundRaiserRegisterController = require('../services/register/fundraiserRegisterController');
const AuthMiddleware = require('../middleware/auth');

router.post('/webhook', FundRaiserRegisterController.stripeWebhook);
router.post('/', AuthMiddleware, FundRaiserRegisterController.registerUserAndPayment);
router.patch('/', AuthMiddleware, FundRaiserRegisterController.patchChildBoosterDetails);

module.exports = router;
