/**
 * This file contains service for AI Companion Group.
 * <AUTHOR>
 * @since 11/06/2025
 * @name aiCompanionGroupService
 */

const AiCompanionGroupFeedbackModel = require('../../models/aiCompanionGroupFeedback.model');
const MessageModel = require('../../models/message.model');
const GeneralError = require('../../util/GeneralError');
const AiCompanionGroupValidator = require('./aiCompanionGroupValidator');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents service for AI Companion Group.
 */
class AiCompanionGroupService {
    /**
     * @desc This function is being used to give feedback for AI Companion Group
     * <AUTHOR>
     * @since 11/06/2025
     * @param {Object} req Request
     * @param {Object} user User
     * @param {Function} locale Translation function
     * @returns {Object} Data
     */
    static async addUpdateAiCompanionGroupFeedback (req, user, locale) {
        const validator = new AiCompanionGroupValidator({ body: req.body, locale });
        validator.validateAddUpdateAiCompanionGroupFeedback();

        const { aiResponseMessageId, userQueryMessageId, groupId, feedback } = req.body;

        const messages = await MessageModel.batchGet([aiResponseMessageId, userQueryMessageId]);
        this.validateMessages({ messages, groupId, locale, userQueryMessageId, aiResponseMessageId });

        const aiCompanionGroupFeedback = await AiCompanionGroupFeedbackModel.get({
            aiResponseMessageId,
            userId: user.id
        });

        if (aiCompanionGroupFeedback) {
            aiCompanionGroupFeedback.feedback = feedback;
            await Utils.addMetricForAccuracy(feedback);
            return await aiCompanionGroupFeedback.save();
        }

        await Utils.addMetricForAccuracy(feedback);
        return await AiCompanionGroupFeedbackModel.create({
            userId: user.id,
            aiResponseMessageId,
            userQueryMessageId,
            groupId,
            feedback
        });
    }

    /**
     * @desc This function is being used to validate messages
     * <AUTHOR>
     * @since 11/06/2025
     * @param {Object} messages Messages
     * @param {String} groupId Group Id
     * @param {Function} locale Translation function
     * @param {String} userQueryMessageId User Query Message Id
     * @param {String} aiResponseMessageId AI Response Message Id
     * @returns {Object} Data
     */
    static validateMessages ({ messages, groupId, locale, userQueryMessageId, aiResponseMessageId }) {
        const userQueryMessage = messages?.find((message) => message.id === userQueryMessageId);
        const aiResponseMessage = messages?.find((message) => message.id === aiResponseMessageId);

        if (messages?.length !== 2) {
            throw new GeneralError(locale(MESSAGES.MESSAGE_NOT_FOUND), 400);
        }
        if (userQueryMessage.groupId !== groupId || aiResponseMessage.groupId !== groupId) {
            throw new GeneralError(locale(MESSAGES.MESSAGE_NOT_FOUND), 400);
        }
        if (aiResponseMessage.userQueryMessageId !== userQueryMessage.id) {
            throw new GeneralError(locale(MESSAGES.MESSAGE_NOT_FOUND), 400);
        }
        if (aiResponseMessage.senderId !== CONSTANTS.AI_COMPANION_SENDER_ID) {
            throw new GeneralError(locale(MESSAGES.MESSAGE_NOT_FOUND), 400);
        }
    }

    /**
     * @desc This function is being used to get page size
     * <AUTHOR>
     * @since 11/06/2025
     * @param {String} pageSize Page Size
     * @returns {Number} Page
     */
    static getPageSize (pageSize) {
        return isNaN(Number(pageSize)) ? CONSTANTS.DEFAULT_PAGE_SIZE : Number(pageSize);
    }

    /**
     * @desc This function is being used to get feedback list for AI Companion Group
     * <AUTHOR>
     * @since 11/06/2025
     * @param {Object} req Request
     * @param {Object} user User
     * @param {Function} locale Translation function
     * @returns {Object} Data
     */
    static async getAiCompanionGroupFeedbackList (req, _, locale) {
        const validator = new AiCompanionGroupValidator({ query: req.query, locale });
        validator.validateGetAiCompanionGroupFeedbackList();

        const { groupId, startAiResponseMessageId, startUserId, pageSize } = req.query;
        const page = this.getPageSize(pageSize);

        const query = AiCompanionGroupFeedbackModel
            .query('groupId')
            .eq(groupId)
            .using('groupId-index')
            .limit(page);

        if (startAiResponseMessageId && startUserId) {
            query.startAt({
                aiResponseMessageId: startAiResponseMessageId,
                userId: startUserId,
                groupId: groupId
            });
        }

        const data = await query.exec();

        if (data.length === 0) {
            return {
                startAiResponseMessageId: null,
                startUserId: null,
                feedbackList: []
            };
        }

        const feedbackList = await this.formatFeedbackResponse(data);

        let nextStartAt = null;

        if (data.lastKey) {
            nextStartAt = {
                aiResponseMessageId: data.lastKey.aiResponseMessageId,
                userId: data.lastKey.userId
            };
        }

        return {
            startAiResponseMessageId: nextStartAt?.aiResponseMessageId ?? null,
            startUserId: nextStartAt?.userId ?? null,
            feedbackList
        };
    }

    /**
     * @desc This function is being used to format feedback response
     * <AUTHOR>
     * @since 11/06/2025
     * @param {Array} feedbackList Feedback List
     * @returns {Object} Data
     */
    static async formatFeedbackResponse (feedbackList) {
        const formattedFeedbackList = [];
        const uniqueMessageIds = new Set();
        const messageMap = new Map();

        feedbackList.forEach((feedback) => {
            uniqueMessageIds.add(feedback.aiResponseMessageId);
            uniqueMessageIds.add(feedback.userQueryMessageId);
        });

        const messages = await MessageModel.batchGet(
            Array.from(uniqueMessageIds),
            { attributes: ['id', 'senderId', 'userQueryMessageId', 'message', 'contexts', 'createdAt', 'updatedAt'] }
        );

        messages.forEach((message) => {
            messageMap.set(message.id, message);
        });

        for (const feedback of feedbackList) {
            const userQueryMessage = messageMap.get(feedback.userQueryMessageId);
            const aiResponseMessage = messageMap.get(feedback.aiResponseMessageId);

            formattedFeedbackList.push({
                userQueryMessage,
                aiResponseMessage,
                feedback: feedback.feedback
            });
        }
        return formattedFeedbackList;
    }
}

module.exports = AiCompanionGroupService;
