const validation = require('../../../util/validation');

/**
 * Class represents validations for Messages.
 */
class MessageValidator extends validation {
    constructor (req, locale) {
        super(locale);
        this.mediaFiles = req.files;
        this.body = req.body;
        this.query = req.query;
    }

    /**
     * @desc This function is being used to validate send message with media
     * <AUTHOR>
     * @since 25/10/2024
     */
    validateSendMessageWithMedia () {
        const { messageId, groupId, senderId, replyMessage, organizationId } = this.body;

        super.field(messageId, 'Message Id');
        super.uuid(messageId, 'Message Id');
        super.field(groupId, 'Group Id');
        super.uuid(groupId, 'Group Id');
        super.field(organizationId, 'Organization Id');
        super.uuid(organizationId, 'Organization Id');
        super.field(senderId, 'Sender Id');
        if (replyMessage) {
            super.field(replyMessage.messageId, 'Reply Message Id');
            super.uuid(replyMessage.messageId, 'Reply Message Id');
            super.field(replyMessage.senderId, 'Reply Message Sender Id');
        }
    }

    /**
     * @desc This function is being used to validate send personal message with media
     * <AUTHOR>
     * @since 20/12/2024
     */
    validateSendPersonalMessageWithMedia () {
        const { messageId, senderId, replyMessage, receiverId } = this.body;

        super.field(messageId, 'Message Id');
        super.uuid(messageId, 'Message Id');
        super.field(senderId, 'Sender Id');
        super.field(receiverId, 'Receiver Id');
        if (replyMessage) {
            super.field(replyMessage.messageId, 'Reply Message Id');
            super.uuid(replyMessage.messageId, 'Reply Message Id');
            super.field(replyMessage.senderId, 'Reply Message Sender Id');
        }
    }

    /**
     * @desc This function is being used to validate flag message request object
     * <AUTHOR>
     * @since 24/12/2024
     */
    validateFlagMessage () {
        const { messageId, reasons } = this.body;
        super.field(messageId, 'Message Id');
        super.uuid(messageId, 'Message Id');
        super.field(reasons, 'Reasons');
        super.array(reasons, 'Reasons');
        for (const reason of reasons) {
            super.enum(reason, Object.values(CONSTANTS.FLAG_MESSAGE_REASON), 'Reason');
        }
    }

    /**
     * @desc This function is being used to validate get flag message list request object
     * <AUTHOR>
     * @since 26/12/2024
     */
    validateGetFlagMessageList () {
        const { orgId } = this.query;
        super.field(orgId, 'Org Id');
        super.uuid(orgId, 'Org Id');
    }

    /**
     * @desc This function is being used to validate update flag message status request object
     * <AUTHOR>
     * @since 30/12/2024
     */
    validateUpdateFlagMessageStatus () {
        const { messageId, status, senderId } = this.body;
        super.field(messageId, 'Message Id');
        super.uuid(messageId, 'Message Id');
        super.field(status, 'Status');
        super.enum(status, Object.values(CONSTANTS.FLAG_MESSAGE_STATUS), 'Status');
        super.field(senderId, 'Sender Id');
    }

    /**
     * @desc This function is being used to validate get flag message reasons request object
     * <AUTHOR>
     * @since 02/01/2025
     */
    validateGetFlagMessageReasons () {
        const { messageId, organizationId } = this.query;
        super.field(messageId, 'Message Id');
        super.field(organizationId, 'Organization Id');
    }

    /**
     * @desc This function is being used to validate get user children list request object
     * <AUTHOR>
     * @since 02/01/2025
     */
    validateGetUserChildrenList () {
        const { userId, groupId } = this.query;
        super.field(userId, 'User Id');
        super.field(groupId, 'Group Id');
        super.uuid(groupId, 'Group Id');
    }

    /**
     * @desc This function is being used to validate get disabled commenter list request object
     * <AUTHOR>
     * @since 02/01/2025
     */
    validateGetDisabledCommenterList () {
        const { organizationId } = this.query;
        super.field(organizationId, 'Organization Id');
    }

    validateEnableCommenter () {
        const { userId, groupId } = this.body;
        super.field(userId, 'User Id');
        super.field(groupId, 'Group Id');
        super.uuid(groupId, 'Group Id');
    }
}

module.exports = MessageValidator;
