const Connection = require('../connection');

module.exports = async function (req, res, next) {
    if (process.env.NODE_ENV !== 'testing') {
        const startTime = Date.now();
        CONSOLE_LOGGER.info(MOMENT().format('YYYY-MM-DD HH:mm:ss'), 'Connection.connectDB : before db connection');
        await Connection.connectToDB();
        const endTime = Date.now();
        CONSOLE_LOGGER.info(MOMENT().format('YYYY-MM-DD HH:mm:ss'), 'Connection.connectDB : after db connection');
        const timeDifference = endTime - startTime;
        CONSOLE_LOGGER.info('Time difference for db middleware:', timeDifference);
    }
    next();
};
