/**
 * This file contains routes used for donating in org managed fundraiser booster.
 * Created by Incubyte on 13/09/2024.
 * @name OrgManagedFundraiserBoosterDonationRoutes
 */
const router = require('express').Router();
const AuthMiddleware = require('../middleware/auth');
const AclMiddleware = require('../middleware/acl');
const HmacMiddleware = require('../middleware/hmac');

const OrgManagedFundraiserBoosterDonationRoutes =
    require('../services/fundraiser/donation/orgManaged/orgFundraiserBoosterDonationController');

router.post('/webhook', OrgManagedFundraiserBoosterDonationRoutes.addDonation);
router.post('/donations', HmacMiddleware, AuthMiddleware, AclMiddleware, OrgManagedFundraiserBoosterDonationRoutes.addManualDonations);
router.get('/details', OrgManagedFundraiserBoosterDonationRoutes.getBoosterDetails);
router.post('/stripeSession', OrgManagedFundraiserBoosterDonationRoutes.createStripeSession);
router.get('/donations', HmacMiddleware, AuthMiddleware, AclMiddleware, OrgManagedFundraiserBoosterDonationRoutes.getDonations);
router.get('/stripeSession', OrgManagedFundraiserBoosterDonationRoutes.getSessionDetails);
module.exports = router;
