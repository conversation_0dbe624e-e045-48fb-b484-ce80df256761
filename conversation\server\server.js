/**
 * @name Server Configuration
 */

const compression = require('compression');
const express = require('express');
const cookieParser = require('cookie-parser');
const app = express();
const bodyParser = require('body-parser');
const swaggerUi = require('swagger-ui-express');
const swaggerDoc = require('swagger-jsdoc');
const swaggerDef = require('./public/swagger');
const cors = require('cors');
const methodOverride = require('method-override');
const i18n = require('i18n');
const morgan = require('morgan');
const helmet = require('helmet');
const HmacMiddleware = require('./middleware/hmac');
const conversationRoutes = require('./routes/conversationRoutes');
const aiCompanionGroupRoutes = require('./routes/aiCompanionGroupRoutes');
const aiCompanionScreenRoutes = require('./routes/aiCompanionScreenRoutes');

// Global Variables
global.CONSOLE_LOGGER = require('./util/logger');
global.CONSTANTS = require('./util/constants');
global.MESSAGES = require('./locales/en.json');
global.MOMENT = require('moment');
global._ = require('lodash');

if (process.env.LOCAL === 'true') {
    app.use(express.static('../jsdocs/jsdocs'));
    app.use(
        '/auth/coverage',
        express.static(`${__dirname}/../coverage/lcov-report`)
    );
}

// Configure i18n for multilingual
i18n.configure({
    locales: ['en'],
    directory: `${__dirname}/locales`,
    extension: '.json',
    prefix: '',
    logDebugFn (msg) {
        if (process.env.LOCAL === 'true') {
            CONSOLE_LOGGER.debug(`i18n::${CONSTANTS.LOG_LEVEL}`, msg);
        }
    }
});

app.use(compression());
app.use(helmet());
app.use(i18n.init);
app.use(cookieParser());

app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));
app.use(
    bodyParser.json({
        limit: '50mb',
        extended: true,
        verify: (req, res, buf) => {
            req.rawBody = buf.toString();
        }
    })
);
app.use(cors({
    origin: true,
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true,
    exposedHeaders: ['x-auth-token']
}));

app.use(morgan('dev'));
app.use(methodOverride());

const spec = swaggerDoc(swaggerDef);

if (process.env.NODE_ENV !== 'production') {
    app.use('/conversation/api-docs/', swaggerUi.serve, swaggerUi.setup(spec));
}

app.use('/conversation', HmacMiddleware, conversationRoutes);
app.use('/conversation/ai-companion-group', HmacMiddleware, aiCompanionGroupRoutes);
app.use('/conversation/ai-companion-screen', HmacMiddleware, aiCompanionScreenRoutes);

module.exports = app;
