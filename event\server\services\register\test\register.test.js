/* eslint-disable max-len */
const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
const sinon = require('sinon');
chai.use(chaiHttp);
const jwt = require('jsonwebtoken');
const User = require('../../../models/user.model');
const Organization = require('../../../models/organization.model');
const OrganizationMembers = require('../../../models/organizationMember.model');
const Child = require('../../../models/child.model');
const Event = require('../../../models/event.model');
const Stripe = require('../../../util/Stripe');
const EventSignup = require('../../../models/eventSignup.model');
const TestCase = require('./testcaseRegister');
const Utils = require('../../../util/utilFunctions');
const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
/* User Token */
const user = {
    id: '95285616-3769-4b2e-b36b-898597b8146e',
    email: '<EMAIL>'
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'testingjwt', tokenOptionalInfo)
};
const eventSignupObject = {
    organizationId: 'orgId',
    eventId: 'eventId',
    fee: 100,
    parentId: 'parentId',
    childId: 'childId'
};
Utils.addCommonReqTokenForHMac(request);
describe('Event Payment service', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            getStub.restore();
            sinon.restore();
        });

        TestCase.registerForEvent.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({ status: 'active', isVerified: 1, role: 1 });
                request(process.env.BASE_URL)
                    .post('/event/register')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should handle error if token is not passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .post('/event/register')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and correct token is passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .post('/event/register')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and is verified', (done) => {
            getStub.resolves({ status: 'active', isVerified: 0, isDeleted: 0, role: 1 });
            request(process.env.BASE_URL)
                .post('/event/register')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 423);
                    done();
                });
        });

        it('As a user I should validate the role before creating event', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1 });
            request(process.env.BASE_URL)
                .post('/event')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 406);
                    done();
                });
        });

        it('As a user I should throw error if user is already registered', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, role: 1 });
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const scanStub = sinon.stub(EventSignup, 'query');
            scanStub.returns({
                eq: eqStub.withArgs(eventSignupObject.eventId).returns({
                    where: whereStub.withArgs('childId').returns({
                        eq: eqStub.withArgs(eventSignupObject.childId).returns({
                            exec: execStub.resolves({ count: 1, toJSON: () => [{ paymentDetails: { paymentStatus: 'approved' } }] })
                        })
                    })
                })
            });
            sinon.stub(Event, 'get').resolves({ participantsLimit: 1, details: { startDateTime: '2027-11-16T12:00:00Z' } });

            request(process.env.BASE_URL)
                .post('/event/register')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...eventSignupObject, paymentType: 'free', fee: 0 })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 403);
                    scanStub.restore();
                    Event.get.restore();
                    done();
                });
        });

        it('As a user I should throw error if user is already registered but payment is pending with cash', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, role: 1 });
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const scanStub = sinon.stub(EventSignup, 'query');
            scanStub.returns({
                eq: eqStub.withArgs(eventSignupObject.eventId).returns({
                    where: whereStub.withArgs('childId').returns({
                        eq: eqStub.withArgs(eventSignupObject.childId).returns({
                            exec: execStub.resolves({
                                count: 1, toJSON: () =>
                                    [{ paymentDetails: { paymentStatus: 'pending', paymentType: 'cash' } }]
                            })
                        })
                    })
                })
            });
            sinon.stub(Event, 'get').resolves({ participantsLimit: 1, details: { startDateTime: '2027-11-16T12:00:00Z' }, membershipBenefitDetails: { benefitDiscount: '[]' } });
            sinon.stub(Child, 'get').resolves({ id: 'childId1' });

            request(process.env.BASE_URL)
                .post('/event/register')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...eventSignupObject, paymentType: 'cash' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 403);
                    scanStub.restore();
                    Event.get.restore();
                    Child.get.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
describe('Event Payment service', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            getStub.restore();
            sinon.restore();
        });

        it('As a user I should throw error if the limit exceeds for that event', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1 });
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const eventSignupScanStub = sinon.stub(EventSignup, 'query');
            eventSignupScanStub.returns({
                eq: eqStub.withArgs(eventSignupObject.eventId).returns({
                    where: whereStub.withArgs('childId').returns({
                        eq: eqStub.withArgs(eventSignupObject.childId).returns({
                            exec: execStub.resolves({ count: 1, toJSON: () => [{ paymentDetails: {} }] })
                        })
                    }),
                    exec: execStub.resolves({ count: 10, toJSON: () => [{ paymentDetails: {}, fee: 0, participantsLimit: 0 }] })
                })
            });
            sinon.stub(Event, 'get').resolves({ participantsLimit: 1, details: { startDateTime: '2027-11-16T12:00:00Z' }, membershipBenefitDetails: { benefitDiscount: '[]' } });
            sinon.stub(Child, 'get').resolves({ id: 'childId1' });
            request(process.env.BASE_URL)
                .post('/event/register')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...eventSignupObject, paymentType: 'free', quantity: 5 })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 403);
                    eventSignupScanStub.restore();
                    Event.get.restore();
                    Child.get.restore();
                    sinon.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
describe('Event Payment service', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            getStub.restore();
            sinon.restore();
        });

        it('As a user I should participate in that event for free event', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 1, email: 'loggedin email' });
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const eventSignupScanStub = sinon.stub(EventSignup, 'query');
            const eventScanStub = sinon.stub(Event, 'get');
            eventSignupScanStub.returns({
                eq: eqStub.withArgs(eventSignupObject.eventId).returns({
                    where: whereStub.withArgs('childId').returns({
                        eq: eqStub.withArgs(eventSignupObject.childId).returns({
                            exec: execStub.resolves({
                                count: 0, toJSON: () => [
                                    { paymentDetails: { paymentStatus: '' }, fee: 0 }]
                            })
                        })
                    }),
                    exec: execStub.resolves({ count: 1, toJSON: () => [{ paymentDetails: { paymentStatus: '' }, fee: 0 }] })
                })
            });
            eventScanStub.returns({
                participantsLimit: 15,
                participantsCount: 0,
                id: 'id',
                title: 'title',
                details: {
                    startDateTime: '11/15/2027 09:00',
                    endDateTime: '11/15/2027 09:00',
                    venue: 'venue'
                },
                membershipBenefitDetails: { benefitDiscount: '[]' },
                save: () => Promise.resolve()
            });
            sinon.stub(EventSignup, 'create');
            const childScanStub = sinon.stub(Child, 'get');
            childScanStub.returns({
                id: 'userId',
                firstName: 'firstName',
                lastName: 'lastName'
            });
            request(process.env.BASE_URL)
                .post('/event/register')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...eventSignupObject, paymentType: 'free' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    eventSignupScanStub.restore();
                    sinon.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
describe('Event Payment service', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            getStub.restore();
            sinon.restore();
        });

        it('As a user I should get error if the organization is not active for stripe transaction', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, email: 'loggedin email', id: 'id', firstName: 'firstName', lastName: 'lastName'
            });
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const eventSignupScanStub = sinon.stub(EventSignup, 'query');
            const eventScanStub = sinon.stub(Event, 'get');
            eventSignupScanStub.returns({
                eq: eqStub.withArgs(eventSignupObject.eventId).returns({
                    where: whereStub.withArgs('childId').returns({
                        eq: eqStub.withArgs(eventSignupObject.childId).returns({
                            exec: execStub.resolves({
                                count: 0, toJSON: () => [
                                    { paymentDetails: { paymentStatus: '' }, fee: 0 }]
                            })
                        })
                    }),
                    exec: execStub.resolves({ count: 1, toJSON: () => [{ paymentDetails: { paymentStatus: '' }, fee: 0 }] })
                })
            });
            eventScanStub.returns({
                participantsLimit: 15,
                participantsCount: 0,
                id: 'id',
                title: 'title',
                details: {
                    startDateTime: '11/15/2027 09:00',
                    endDateTime: '11/15/2027 09:00',
                    venue: 'venue'
                },
                membershipBenefitDetails: { benefitDiscount: '[]' },
                save: () => Promise.resolve()
            });
            const createCustomerStub = sinon.stub(Stripe, 'createCustomer');
            createCustomerStub.resolves({ client_secret: 'mocked_client_secret', id: 'stripeCustomeId' });
            sinon.stub(User, 'update').resolves();
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.returns({
                paymentDetails: {
                    stripeConnectAccountId: 'stripeConnectAccountId'
                }
            });
            const createPaymentIntentStub = sinon.stub(Stripe, 'createPaymentIntent');
            createPaymentIntentStub.resolves({ client_secret: 'mocked_client_secret', stripeFee: 29 });
            sinon.stub(EventSignup, 'create');
            sinon.stub(Child, 'get').resolves({ id: 'childId1' });

            request(process.env.BASE_URL)
                .post('/event/register')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...eventSignupObject, paymentType: 'stripe' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 403);
                    eventSignupScanStub.restore();
                    eventScanStub.restore();
                    createCustomerStub.restore();
                    User.update.restore();
                    orgScanStub.restore();
                    createPaymentIntentStub.restore();
                    EventSignup.create.restore();
                    Child.get.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
describe('Event Payment service', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            getStub.restore();
            sinon.restore();
        });

        it('As a user I should participate in that event for paid event and create customer on stripe', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, email: 'loggedin email', id: 'id', firstName: 'firstName', lastName: 'lastName'
            });
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const eventSignupScanStub = sinon.stub(EventSignup, 'query');
            const eventScanStub = sinon.stub(Event, 'get');
            eventSignupScanStub.returns({
                eq: eqStub.withArgs(eventSignupObject.eventId).returns({
                    where: whereStub.withArgs('childId').returns({
                        eq: eqStub.withArgs(eventSignupObject.childId).returns({
                            exec: execStub.resolves({
                                count: 0, toJSON: () => [
                                    { paymentDetails: { paymentStatus: '' }, fee: 0 }]
                            })
                        })
                    }),
                    exec: execStub.resolves({ count: 1, toJSON: () => [{ paymentDetails: { paymentStatus: '' }, fee: 0 }] })
                })
            });
            eventScanStub.returns({
                participantsLimit: 15,
                participantsCount: 0,
                id: 'id',
                title: 'title',
                details: {
                    startDateTime: '11/15/2027 09:00',
                    endDateTime: '11/15/2027 09:00',
                    venue: 'venue'
                },
                membershipBenefitDetails: { benefitDiscount: '[]' },
                save: () => Promise.resolve()
            });
            const createCustomerStub = sinon.stub(Stripe, 'createCustomer');
            createCustomerStub.resolves({ client_secret: 'mocked_client_secret', id: 'stripeCustomeId' });
            sinon.stub(User, 'update').resolves();
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.returns({
                paymentDetails: {
                    stripeConnectAccountId: 'stripeConnectAccountId',
                    stripeOnboardingStatus: 'active'
                }
            });
            const createPaymentIntentStub = sinon.stub(Stripe, 'createPaymentIntent');
            createPaymentIntentStub.resolves({
                client_secret: 'mocked_client_secret', stripeFee: 29,
                paymentIntent: { id: 'paymentIntentId' }
            });
            sinon.stub(EventSignup, 'create');
            sinon.stub(Child, 'get').resolves({ id: 'childId1' });

            request(process.env.BASE_URL)
                .post('/event/register')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...eventSignupObject, paymentType: 'stripe' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    eventSignupScanStub.restore();
                    eventScanStub.restore();
                    createCustomerStub.restore();
                    User.update.restore();
                    orgScanStub.restore();
                    createPaymentIntentStub.restore();
                    EventSignup.create.restore();
                    Child.get.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
describe('Event Payment service', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            getStub.restore();
            sinon.restore();
        });

        it('As a user I should participate in that event for paid event and create customer on stripe and platformFeeCoveredBy is optional and isCoveredByUser is false', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, email: 'loggedin email', id: 'id', firstName: 'firstName', lastName: 'lastName'
            });
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const eventSignupScanStub = sinon.stub(EventSignup, 'query');
            const eventScanStub = sinon.stub(Event, 'get');
            eventSignupScanStub.returns({
                eq: eqStub.withArgs(eventSignupObject.eventId).returns({
                    where: whereStub.withArgs('childId').returns({
                        eq: eqStub.withArgs(eventSignupObject.childId).returns({
                            exec: execStub.resolves({
                                count: 0, toJSON: () => [
                                    { paymentDetails: { paymentStatus: '' }, fee: 0 }]
                            })
                        })
                    }),
                    exec: execStub.resolves({ count: 1, toJSON: () => [{ paymentDetails: { paymentStatus: '' }, fee: 0 }] })
                })
            });
            eventScanStub.returns({
                participantsLimit: 15,
                participantsCount: 0,
                id: 'id',
                title: 'title',
                details: {
                    startDateTime: '11/15/2027 09:00',
                    endDateTime: '11/15/2027 09:00',
                    venue: 'venue'
                },
                membershipBenefitDetails: { benefitDiscount: '[]' },
                save: () => Promise.resolve()
            });
            const createCustomerStub = sinon.stub(Stripe, 'createCustomer');
            createCustomerStub.resolves({ client_secret: 'mocked_client_secret', id: 'stripeCustomeId' });
            sinon.stub(User, 'update').resolves();
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.returns({
                paymentDetails: {
                    stripeConnectAccountId: 'stripeConnectAccountId',
                    stripeOnboardingStatus: 'active'
                },
                platformFeeCoveredBy: 'optional'
            });
            const createPaymentIntentStub = sinon.stub(Stripe, 'createPaymentIntent');
            createPaymentIntentStub.resolves({
                client_secret: 'mocked_client_secret', stripeFee: 29,
                paymentIntent: { id: 'paymentIntentId' }
            });
            sinon.stub(EventSignup, 'create');
            sinon.stub(Child, 'get').resolves({ id: 'childId1' });

            request(process.env.BASE_URL)
                .post('/event/register')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...eventSignupObject, paymentType: 'stripe', isCoveredByUser: false })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    eventSignupScanStub.restore();
                    eventScanStub.restore();
                    createCustomerStub.restore();
                    User.update.restore();
                    orgScanStub.restore();
                    createPaymentIntentStub.restore();
                    EventSignup.create.restore();
                    Child.get.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});

describe('Event Payment service', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            getStub.restore();
            sinon.restore();
        });

        it('As a user I should participate in that event for paid event and create customer on stripe and platformFeeCoveredBy is optional and isCoveredByUser is true', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, email: 'loggedin email', id: 'id', firstName: 'firstName', lastName: 'lastName'
            });
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const eventSignupScanStub = sinon.stub(EventSignup, 'query');
            const eventScanStub = sinon.stub(Event, 'get');
            eventSignupScanStub.returns({
                eq: eqStub.withArgs(eventSignupObject.eventId).returns({
                    where: whereStub.withArgs('childId').returns({
                        eq: eqStub.withArgs(eventSignupObject.childId).returns({
                            exec: execStub.resolves({
                                count: 0, toJSON: () => [
                                    { paymentDetails: { paymentStatus: '' }, fee: 0 }]
                            })
                        })
                    }),
                    exec: execStub.resolves({ count: 1, toJSON: () => [{ paymentDetails: { paymentStatus: '' }, fee: 0 }] })
                })
            });
            eventScanStub.returns({
                participantsLimit: 15,
                participantsCount: 0,
                id: 'id',
                title: 'title',
                details: {
                    startDateTime: '11/15/2027 09:00',
                    endDateTime: '11/15/2027 09:00',
                    venue: 'venue'
                },
                membershipBenefitDetails: { benefitDiscount: '[]' },
                save: () => Promise.resolve()
            });
            const createCustomerStub = sinon.stub(Stripe, 'createCustomer');
            createCustomerStub.resolves({ client_secret: 'mocked_client_secret', id: 'stripeCustomeId' });
            sinon.stub(User, 'update').resolves();
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.returns({
                paymentDetails: {
                    stripeConnectAccountId: 'stripeConnectAccountId',
                    stripeOnboardingStatus: 'active'
                },
                platformFeeCoveredBy: 'optional'
            });
            const createPaymentIntentStub = sinon.stub(Stripe, 'createPaymentIntent');
            createPaymentIntentStub.resolves({
                client_secret: 'mocked_client_secret', stripeFee: 29,
                paymentIntent: { id: 'paymentIntentId' }
            });
            sinon.stub(EventSignup, 'create');
            sinon.stub(Child, 'get').resolves({ id: 'childId1' });

            request(process.env.BASE_URL)
                .post('/event/register')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...eventSignupObject, paymentType: 'stripe', isCoveredByUser: true })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    eventSignupScanStub.restore();
                    eventScanStub.restore();
                    createCustomerStub.restore();
                    User.update.restore();
                    orgScanStub.restore();
                    createPaymentIntentStub.restore();
                    EventSignup.create.restore();
                    Child.get.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
describe('Event Payment service', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            getStub.restore();
            sinon.restore();
        });

        it('As a user I should participate in that event for paid event and create event signup via cash', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, email: 'loggedin email', id: 'id', firstName: 'firstName', lastName: 'lastName'
            });
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const eventSignupScanStub = sinon.stub(EventSignup, 'query');
            const eventScanStub = sinon.stub(Event, 'get');
            eventSignupScanStub.returns({
                eq: eqStub.withArgs(eventSignupObject.eventId).returns({
                    where: whereStub.withArgs('childId').returns({
                        eq: eqStub.withArgs(eventSignupObject.childId).returns({
                            exec: execStub.resolves({
                                count: 0, toJSON: () => [
                                    { paymentDetails: { paymentStatus: '' }, fee: 0 }]
                            })
                        })
                    }),
                    exec: execStub.resolves({
                        count: 1, toJSON: () => [{ paymentDetails: { paymentStatus: '', paymentType: 'stripe' }, fee: 0 }]
                    })
                })
            });
            eventScanStub.returns({
                participantsLimit: 15,
                participantsCount: 0,
                id: 'id',
                title: 'title',
                details: {
                    startDateTime: '11/15/2027 09:00',
                    endDateTime: '11/15/2027 09:00',
                    venue: 'venue'
                },
                membershipBenefitDetails: { benefitDiscount: '[]' },
                save: () => Promise.resolve()
            });

            sinon.stub(EventSignup, 'create');
            sinon.stub(Child, 'get').resolves({ id: 'childId1' });

            request(process.env.BASE_URL)
                .post('/event/register')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...eventSignupObject, paymentType: 'cash' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    eventSignupScanStub.restore();
                    Event.get.restore();
                    Child.get.restore();
                    done();
                });
        });

        it('As a user I should get error if I try to register for past event', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, email: 'loggedin email', id: 'id', firstName: 'firstName', lastName: 'lastName'
            });
            const eventScanStub = sinon.stub(Event, 'get');
            eventScanStub.returns({
                participantsLimit: 15,
                id: 'id',
                title: 'title',
                details: {
                    startDateTime: '11/15/2022 09:00',
                    endDateTime: '11/15/2022 09:00',
                    venue: 'venue'
                }
            });

            sinon.stub(Child, 'get').resolves({ id: 'childId1' });

            request(process.env.BASE_URL)
                .post('/event/register')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...eventSignupObject, paymentType: 'cash' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    sinon.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});

describe('Event Payment service', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            sinon.restore();
        });

        it('As a user I should participate in that event for paid event and get family membership discount', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, email: 'loggedin email', id: 'id', firstName: 'firstName', lastName: 'lastName',
                membershipsPurchased: [{ organizationId: 'org-id', membershipType: 'family', startDate: '11/18/2000', endDate: '11/18/2027' }]
            });
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const eventSignupScanStub = sinon.stub(EventSignup, 'query');
            const eventScanStub = sinon.stub(Event, 'get');
            eventSignupScanStub.returns({
                eq: eqStub.withArgs(eventSignupObject.eventId).returns({
                    where: whereStub.withArgs('childId').returns({
                        eq: eqStub.withArgs(eventSignupObject.childId).returns({
                            exec: execStub.resolves({
                                count: 0, toJSON: () => []
                            })
                        })
                    }),
                    exec: execStub.resolves({
                        count: 1, toJSON: () => [{ paymentDetails: { paymentStatus: '', paymentType: 'stripe' }, fee: 10 }]
                    })
                })
            });
            eventScanStub.returns({
                participantsLimit: 15,
                participantsCount: 0,
                id: 'id',
                title: 'title',
                details: {
                    startDateTime: '11/15/2027 09:00',
                    endDateTime: '11/15/2027 09:00',
                    venue: 'venue'
                },
                organizationId: 'org-id',
                fee: 10,
                isMembershipEnabled: true,
                membershipBenefitDetails: { benefitDiscount: '[]' },
                save: () => Promise.resolve()
            });

            sinon.stub(EventSignup, 'create');
            sinon.stub(Child, 'get').resolves({
                id: 'childId1', membershipsPurchased:
                    [{ organizationId: 'org-id', membershipType: 'family', startDate: '11/18/2000', endDate: '11/18/2027' }]
            });

            request(process.env.BASE_URL)
                .post('/event/register')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...eventSignupObject, paymentType: 'cash' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    eventSignupScanStub.restore();
                    Event.get.restore();
                    EventSignup.create.restore();
                    Child.get.restore();
                    done();
                });
        });

        it('As a user I should participate in that event for paid event and get child membership discount', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, email: 'loggedin email', id: 'id', firstName: 'firstName', lastName: 'lastName',
                membershipsPurchased: [{ organizationId: 'org-id', membershipType: 'child', startDate: '11/18/2000', endDate: '11/18/2027', childId: 'childId' }]
            });
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const eventSignupScanStub = sinon.stub(EventSignup, 'query');
            const eventScanStub = sinon.stub(Event, 'get');
            eventSignupScanStub.returns({
                eq: eqStub.withArgs(eventSignupObject.eventId).returns({
                    where: whereStub.withArgs('childId').returns({
                        eq: eqStub.withArgs(eventSignupObject.childId).returns({
                            exec: execStub.resolves({
                                count: 0, toJSON: () => []
                            })
                        })
                    }),
                    exec: execStub.resolves({
                        count: 1, toJSON: () => [{ paymentDetails: { paymentStatus: '', paymentType: 'stripe' }, fee: 10 }]
                    })
                })
            });
            eventScanStub.returns({
                participantsLimit: 15,
                participantsCount: 0,
                id: 'id',
                title: 'title',
                details: {
                    startDateTime: '11/15/2027 09:00',
                    endDateTime: '11/15/2027 09:00',
                    venue: 'venue'
                },
                organizationId: 'org-id',
                fee: 10,
                isMembershipEnabled: true,
                membershipBenefitDetails: { benefitDiscount: '[]' },
                save: () => Promise.resolve()
            });

            sinon.stub(EventSignup, 'create');
            sinon.stub(Child, 'get').resolves({
                id: 'childId1', membershipsPurchased:
                    [{ organizationId: 'org-id', membershipType: 'family', startDate: '11/18/2000', endDate: '11/18/2027' }]
            });
            request(process.env.BASE_URL)
                .post('/event/register')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...eventSignupObject, paymentType: 'cash' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    eventSignupScanStub.restore();
                    Event.get.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});

describe('Event Payment service', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            sinon.restore();
        });

        it('As a user I should participate in that event for User first paid with stripe now trying to pay with cash/check', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, email: 'loggedin email', id: 'id', firstName: 'firstName', lastName: 'lastName'
            });
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const eventSignupScanStub = sinon.stub(EventSignup, 'query');
            const eventScanStub = sinon.stub(Event, 'get');
            eventSignupScanStub.returns({
                eq: eqStub.withArgs(eventSignupObject.eventId).returns({
                    where: whereStub.withArgs('childId').returns({
                        eq: eqStub.withArgs(eventSignupObject.childId).returns({
                            exec: execStub.resolves({
                                count: 1, toJSON: () => [
                                    { paymentDetails: { paymentStatus: 'payment-initiated', paymentType: 'stripe' }, fee: 10 }]
                            })
                        })
                    }),
                    exec: execStub.resolves({
                        count: 1, toJSON: () => [{ paymentDetails: { paymentStatus: 'payment-initiated', paymentType: 'stripe' }, fee: 10 }]
                    })
                })
            });
            eventScanStub.returns({
                participantsLimit: 15,
                participantsCount: 0,
                id: 'id',
                title: 'title',
                details: {
                    startDateTime: '11/15/2027 09:00',
                    endDateTime: '11/15/2027 09:00',
                    venue: 'venue'
                },
                membershipBenefitDetails: { benefitDiscount: '[]' },
                save: () => Promise.resolve()
            });
            const eventSignupGet = sinon.stub(EventSignup, 'get').resolves({
                paymentDetails: { paymentStatus: 'approved' }, save: () => { }, parentId: 'parentId'
            });
            const createCustomerStub = sinon.stub(Stripe, 'createCustomer');
            createCustomerStub.resolves({ client_secret: 'mocked_client_secret', id: 'stripeCustomeId' });
            sinon.stub(User, 'update').resolves();
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.returns({
                paymentDetails: {
                    stripeConnectAccountId: 'stripeConnectAccountId'
                }
            });
            const createPaymentIntentStub = sinon.stub(Stripe, 'createPaymentIntent');
            createPaymentIntentStub.resolves({ client_secret: 'mocked_client_secret' });
            const eventCreate = sinon.stub(EventSignup, 'create');
            sinon.stub(Child, 'get').resolves({ id: 'childId1' });
            request(process.env.BASE_URL)
                .post('/event/register')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...eventSignupObject, paymentType: 'cash' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    eventSignupScanStub.restore();
                    Event.get.restore();
                    eventSignupGet.restore();
                    eventScanStub.restore();
                    createPaymentIntentStub.restore();
                    createCustomerStub.restore();
                    orgScanStub.restore();
                    eventCreate.restore();
                    Child.get.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
describe('Event Payment service', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            sinon.restore();
        });

        it('As a user I should participate in that event for User first paid with stripe now trying to pay with stripe again', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, email: 'loggedin email', id: 'id', firstName: 'firstName', lastName: 'lastName'
            });
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const eventSignupScanStub = sinon.stub(EventSignup, 'query');
            const eventScanStub = sinon.stub(Event, 'get');
            eventSignupScanStub.returns({
                eq: eqStub.withArgs(eventSignupObject.eventId).returns({
                    where: whereStub.withArgs('childId').returns({
                        eq: eqStub.withArgs(eventSignupObject.childId).returns({
                            exec: execStub.resolves({
                                count: 1, toJSON: () => [
                                    { paymentDetails: { paymentStatus: 'payment-initiated', paymentType: 'stripe' }, fee: 10 }]
                            })
                        })
                    }),
                    exec: execStub.resolves({
                        count: 1, toJSON: () => [{ paymentDetails: { paymentStatus: 'payment-initiated', paymentType: 'stripe' }, fee: 10 }]
                    })
                })
            });
            eventScanStub.returns({
                participantsLimit: 15,
                participantsCount: 0,
                id: 'id',
                title: 'title',
                details: {
                    startDateTime: '11/15/2027 09:00',
                    endDateTime: '11/15/2027 09:00',
                    venue: 'venue'
                },
                membershipBenefitDetails: { benefitDiscount: '[]' },
                save: () => Promise.resolve()
            });
            const eventSignupGet = sinon.stub(EventSignup, 'get').resolves({
                paymentDetails: { paymentStatus: 'approved' }, save: () => { }, parentId: 'parentId'
            });
            const createCustomerStub = sinon.stub(Stripe, 'createCustomer');
            createCustomerStub.resolves({ client_secret: 'mocked_client_secret', id: 'stripeCustomeId' });
            sinon.stub(User, 'update').resolves();
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.returns({
                paymentDetails: {
                    stripeConnectAccountId: 'stripeConnectAccountId',
                    stripeOnboardingStatus: 'active'
                }
            });
            const createPaymentIntentStub = sinon.stub(Stripe, 'createPaymentIntent');
            createPaymentIntentStub.resolves({ client_secret: 'mocked_client_secret', paymentIntent: { id: 'paymentIntentId' } });
            const eventCreate = sinon.stub(EventSignup, 'create');
            sinon.stub(Child, 'get').resolves({ id: 'childId1' });
            request(process.env.BASE_URL)
                .post('/event/register')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...eventSignupObject, paymentType: 'stripe' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    eventSignupScanStub.restore();
                    Event.get.restore();
                    eventSignupGet.restore();
                    eventScanStub.restore();
                    createPaymentIntentStub.restore();
                    createCustomerStub.restore();
                    orgScanStub.restore();
                    eventCreate.restore();
                    Child.get.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
describe('Event Payment service', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            sinon.restore();
        });

        it('As a user I should participate in that event for User first paid with stripe now trying to pay with stripe again but org is inactive', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, email: 'loggedin email', id: 'id', firstName: 'firstName', lastName: 'lastName'
            });
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const eventSignupScanStub = sinon.stub(EventSignup, 'query');
            const eventScanStub = sinon.stub(Event, 'get');
            eventSignupScanStub.returns({
                eq: eqStub.withArgs(eventSignupObject.eventId).returns({
                    where: whereStub.withArgs('childId').returns({
                        eq: eqStub.withArgs(eventSignupObject.childId).returns({
                            exec: execStub.resolves({
                                count: 1, toJSON: () => [
                                    { paymentDetails: { paymentStatus: 'payment-initiated', paymentType: 'stripe' }, fee: 10 }]
                            })
                        })
                    }),
                    exec: execStub.resolves({
                        count: 1, toJSON: () => [{ paymentDetails: { paymentStatus: 'payment-initiated', paymentType: 'stripe' }, fee: 10 }]
                    })
                })
            });
            eventScanStub.returns({
                participantsLimit: 15,
                participantsCount: 0,
                id: 'id',
                title: 'title',
                details: {
                    startDateTime: '11/15/2027 09:00',
                    endDateTime: '11/15/2027 09:00',
                    venue: 'venue'
                },
                membershipBenefitDetails: { benefitDiscount: '[]' },
                save: () => Promise.resolve()
            });
            const eventSignupGet = sinon.stub(EventSignup, 'get').resolves({
                paymentDetails: { paymentStatus: 'approved' }, save: () => { }, parentId: 'parentId'
            });
            sinon.stub(User, 'update').resolves();
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.returns({
                paymentDetails: {
                    stripeConnectAccountId: 'stripeConnectAccountId',
                    stripeOnboardingStatus: 'inactive'
                }
            });
            sinon.stub(Child, 'get').resolves({ id: 'childId1' });
            request(process.env.BASE_URL)
                .post('/event/register')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...eventSignupObject, paymentType: 'stripe' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 403);
                    eventSignupScanStub.restore();
                    Event.get.restore();
                    eventSignupGet.restore();
                    eventScanStub.restore();
                    orgScanStub.restore();
                    Child.get.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
describe('Event Payment service', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            sinon.restore();
        });

        it('As a user I should participate in that event for User first paid with stripe now trying to pay with stripe again and platformFeeCoveredBy is optional and isCoveredByUser is not passed', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, email: 'loggedin email', id: 'id', firstName: 'firstName', lastName: 'lastName'
            });
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const eventSignupScanStub = sinon.stub(EventSignup, 'query');
            const eventScanStub = sinon.stub(Event, 'get');
            eventSignupScanStub.returns({
                eq: eqStub.withArgs(eventSignupObject.eventId).returns({
                    where: whereStub.withArgs('childId').returns({
                        eq: eqStub.withArgs(eventSignupObject.childId).returns({
                            exec: execStub.resolves({
                                count: 1, toJSON: () => [
                                    { paymentDetails: { paymentStatus: 'payment-initiated', paymentType: 'stripe' }, fee: 10 }]
                            })
                        })
                    }),
                    exec: execStub.resolves({
                        count: 1, toJSON: () => [{ paymentDetails: { paymentStatus: 'payment-initiated', paymentType: 'stripe' }, fee: 10 }]
                    })
                })
            });
            eventScanStub.returns({
                participantsLimit: 15,
                participantsCount: 0,
                id: 'id',
                title: 'title',
                details: {
                    startDateTime: '11/15/2027 09:00',
                    endDateTime: '11/15/2027 09:00',
                    venue: 'venue'
                },
                save: () => Promise.resolve()
            });
            const eventSignupGet = sinon.stub(EventSignup, 'get').resolves({
                paymentDetails: { paymentStatus: 'approved' }, save: () => { }, parentId: 'parentId'
            });
            sinon.stub(User, 'update').resolves();
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.returns({
                paymentDetails: {
                    stripeConnectAccountId: 'stripeConnectAccountId',
                    stripeOnboardingStatus: 'active'
                },
                platformFeeCoveredBy: 'optional'
            });
            sinon.stub(Child, 'get').resolves({ id: 'childId1' });
            request(process.env.BASE_URL)
                .post('/event/register')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...eventSignupObject, paymentType: 'stripe' })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    eventSignupScanStub.restore();
                    Event.get.restore();
                    eventSignupGet.restore();
                    eventScanStub.restore();
                    orgScanStub.restore();
                    Child.get.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
describe('Event Payment service', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            sinon.restore();
        });

        it('As a user I should participate in that event for User first paid with stripe now trying to pay with stripe again and platformFeeCoveredBy is optional and isCoveredByUser is false', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, email: 'loggedin email', id: 'id', firstName: 'firstName', lastName: 'lastName'
            });
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const eventSignupScanStub = sinon.stub(EventSignup, 'query');
            const eventScanStub = sinon.stub(Event, 'get');
            eventSignupScanStub.returns({
                eq: eqStub.withArgs(eventSignupObject.eventId).returns({
                    where: whereStub.withArgs('childId').returns({
                        eq: eqStub.withArgs(eventSignupObject.childId).returns({
                            exec: execStub.resolves({
                                count: 1, toJSON: () => [
                                    { paymentDetails: { paymentStatus: 'payment-initiated', paymentType: 'stripe' }, fee: 10 }]
                            })
                        })
                    }),
                    exec: execStub.resolves({
                        count: 1, toJSON: () => [{ paymentDetails: { paymentStatus: 'payment-initiated', paymentType: 'stripe' }, fee: 10 }]
                    })
                })
            });
            eventScanStub.returns({
                participantsLimit: 15,
                participantsCount: 0,
                id: 'id',
                title: 'title',
                details: {
                    startDateTime: '11/15/2027 09:00',
                    endDateTime: '11/15/2027 09:00',
                    venue: 'venue'
                },
                membershipBenefitDetails: { benefitDiscount: '[]' },
                save: () => Promise.resolve()
            });
            const eventSignupGet = sinon.stub(EventSignup, 'get').resolves({
                paymentDetails: { paymentStatus: 'approved' }, save: () => { }, parentId: 'parentId'
            });
            sinon.stub(User, 'update').resolves();
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.returns({
                paymentDetails: {
                    stripeConnectAccountId: 'stripeConnectAccountId',
                    stripeOnboardingStatus: 'active'
                },
                platformFeeCoveredBy: 'optional'
            });
            sinon.stub(Child, 'get').resolves({ id: 'childId1' });
            request(process.env.BASE_URL)
                .post('/event/register')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...eventSignupObject, paymentType: 'stripe', isCoveredByUser: false })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    eventSignupScanStub.restore();
                    Event.get.restore();
                    eventSignupGet.restore();
                    eventScanStub.restore();
                    orgScanStub.restore();
                    Child.get.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
describe('Event Payment service', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });
        after(async () => {
            sinon.restore();
        });

        it('As a user I should participate in that event for User first paid with stripe now trying to pay with stripe again and platformFeeCoveredBy is optional and isCoveredByUser is true', (done) => {
            getStub.resolves({
                status: 'active', isVerified: 1, role: 1, email: 'loggedin email', id: 'id', firstName: 'firstName', lastName: 'lastName'
            });
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const eventSignupScanStub = sinon.stub(EventSignup, 'query');
            const eventScanStub = sinon.stub(Event, 'get');
            eventSignupScanStub.returns({
                eq: eqStub.withArgs(eventSignupObject.eventId).returns({
                    where: whereStub.withArgs('childId').returns({
                        eq: eqStub.withArgs(eventSignupObject.childId).returns({
                            exec: execStub.resolves({
                                count: 1, toJSON: () => [
                                    { paymentDetails: { paymentStatus: 'payment-initiated', paymentType: 'stripe' }, fee: 10 }]
                            })
                        })
                    }),
                    exec: execStub.resolves({
                        count: 1, toJSON: () => [{ paymentDetails: { paymentStatus: 'payment-initiated', paymentType: 'stripe' }, fee: 10 }]
                    })
                })
            });
            eventScanStub.returns({
                participantsLimit: 15,
                participantsCount: 0,
                id: 'id',
                title: 'title',
                details: {
                    startDateTime: '11/15/2027 09:00',
                    endDateTime: '11/15/2027 09:00',
                    venue: 'venue'
                },
                membershipBenefitDetails: { benefitDiscount: '[]' },
                save: () => Promise.resolve()
            });
            const eventSignupGet = sinon.stub(EventSignup, 'get').resolves({
                paymentDetails: { paymentStatus: 'approved' }, save: () => { }, parentId: 'parentId'
            });
            sinon.stub(User, 'update').resolves();
            const orgScanStub = sinon.stub(Organization, 'get');
            orgScanStub.returns({
                paymentDetails: {
                    stripeConnectAccountId: 'stripeConnectAccountId',
                    stripeOnboardingStatus: 'active'
                },
                platformFeeCoveredBy: 'optional'
            });
            sinon.stub(Child, 'get').resolves({ id: 'childId1' });

            request(process.env.BASE_URL)
                .post('/event/register')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...eventSignupObject, paymentType: 'stripe', isCoveredByUser: true })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    eventSignupScanStub.restore();
                    Event.get.restore();
                    eventSignupGet.restore();
                    eventScanStub.restore();
                    orgScanStub.restore();
                    Child.get.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
describe('Stripe connect webhooks', () => {
    try {
        let stripeConstructWebhookEventStub;

        before(async () => {
            stripeConstructWebhookEventStub = sinon.stub(Stripe, 'constructWebhookEvent');
        });

        after(async () => {
            stripeConstructWebhookEventStub.restore();
            sinon.restore();
        });

        it('As a super admin, should watch for payment_intent.canceled and delete the event signup', async () => {
            stripeConstructWebhookEventStub.returns({
                type: 'payment_intent.canceled',
                data: {
                    object: {
                        id: 'payment_intent_id'
                    }
                }
            });

            const eventSignupDeleteStub = sinon.stub();
            const eventSignupGetStub = sinon.stub(EventSignup, 'get').returns({
                delete: eventSignupDeleteStub
            });

            const eventSignupQueryStub = sinon.stub(EventSignup, 'query').returns({
                eq: () => {
                    return {
                        exec: () => Promise.resolve({
                            count: 1,
                            toJSON: () => [{ id: 'event_signup_id' }]
                        })
                    };
                }
            });

            const res = await request(process.env.BASE_URL)
                .post('/event/register/webhook')
                .send();

            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 200);
            eventSignupQueryStub.restore();
            eventSignupGetStub.restore();
        });

        it('As a super admin, should watch for payment_intent.canceled and do not delete the event signup if id not found', async () => {
            stripeConstructWebhookEventStub.returns({
                type: 'payment_intent.canceled',
                data: {
                    object: {
                        id: 'payment_intent_id'
                    }
                }
            });

            const eventSignupDeleteStub = sinon.stub();
            const eventSignupGetStub = sinon.stub(EventSignup, 'get').returns({
                delete: eventSignupDeleteStub
            });

            const eventSignupQueryStub = sinon.stub(EventSignup, 'query').returns({
                eq: () => {
                    return {
                        exec: () => Promise.resolve({
                            count: 0,
                            toJSON: () => []
                        })
                    };
                }
            });

            const res = await request(process.env.BASE_URL)
                .post('/event/register/webhook')
                .send();

            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 200);
            eventSignupQueryStub.restore();
            eventSignupGetStub.restore();
        });

        it('As a super admin, should watch for payment_intent.succeeded and send mail', async () => {

            stripeConstructWebhookEventStub.returns({
                type: 'payment_intent.succeeded',
                data: {
                    id: 'account_1',
                    object: {
                        payment: { id: 'id' }
                    }
                }
            });

            const execStub = sinon.stub();
            const eventSignupScanStub = sinon.stub(EventSignup, 'query');
            eventSignupScanStub.returns({
                eq: () => {
                    return {
                        exec: execStub.resolves({
                            count: 10, toJSON: () => [{
                                id: 'eventSignupid',
                                parentId: 'parentId',
                                childId: 'childId',
                                organizationId: 'organizationId',
                                eventId: 'eventId'
                            }]
                        })
                    };
                }
            });
            sinon.stub(EventSignup, 'get').resolves({
                paymentDetails: { paymentStatus: 'approved' }, save: () => { }, parentId: 'parentId'
            });

            const saveStub = sinon.stub().resolves();
            sinon.replace(EventSignup.prototype, 'save', saveStub).resolves();
            const userScanStub = sinon.stub(User, 'query');
            userScanStub.returns({
                eq: () => {
                    return {
                        exec: execStub.resolves({
                            count: 10, toJSON: () => [{
                                id: 'userId',
                                email: 'email'
                            }]
                        })
                    };
                }
            });
            const childScanStub = sinon.stub(Child, 'get');
            childScanStub.returns({
                id: 'userId',
                firstName: 'firstName',
                lastName: 'lastName'
            });
            const eventScanStub = sinon.stub(Event, 'get');
            eventScanStub.returns({
                id: 'userId',
                title: 'title',
                details: {
                    startDateTime: '11/15/2023 09:00',
                    endDateTime: '11/15/2023 09:00',
                    venue: 'venue'
                },
                save: () => Promise.resolve()
            });
            const res = await request(process.env.BASE_URL)
                .post('/event/register/webhook')
                .send();
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 200);
            eventSignupScanStub.restore();
            EventSignup.get.restore();
            eventScanStub.restore();
        });

        it('As a super admin, should watch for payment_intent.failed and update status accordingly', async () => {

            stripeConstructWebhookEventStub.returns({
                type: 'payment_intent.payment_failed',
                data: {
                    id: 'account_1',
                    object: {
                        payment: { id: 'id' }
                    }
                }
            });

            const execStub = sinon.stub();
            const eventSignupScanStub = sinon.stub(EventSignup, 'query');
            eventSignupScanStub.returns({
                eq: () => {
                    return {
                        exec: execStub.resolves({
                            count: 10, toJSON: () => [{
                                id: 'eventSignupid',
                                parentId: 'parentId',
                                childId: 'childId',
                                organizationId: 'organizationId',
                                eventId: 'eventId'
                            }]
                        })
                    };
                }
            });
            sinon.stub(EventSignup, 'get').resolves({ delete: () => {} });
            const res = await request(process.env.BASE_URL)
                .post('/event/register/webhook')
                .send();
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 200);
        });

        it('Invalid stripe signature', async () => {
            stripeConstructWebhookEventStub.throws(new Error('error'));

            const res = await request(process.env.BASE_URL)
                .post('/event/register/webhook')
                .send();
            expect(res.body.status).to.be.status;
            assert.equal(res.body.status, 0);
            assert.equal(res.statusCode, 400);
        });

    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
