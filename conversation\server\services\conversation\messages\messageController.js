/**
 * This file contains controller for Messages.
 * Created by Incubyte on 24/10/2024.
 * @name messageController
 */

const Utils = require('../../../util/utilFunctions');
const MessageService = require('./messageService');

class MessageController {
    /**
     * @desc This function is being used to send message with media
     * <AUTHOR>
     * @since 24/10/2024
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async sendMessageWithMedia (req, res) {
        try {
            const data = await MessageService.sendMessageWithMedia(req);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in send message', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to send personal message with media
     * <AUTHOR>
     * @since 20/12/2024
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async sendPersonalMessageWithMedia (req, res) {
        try {
            const data = await MessageService.sendPersonalMessageWithMedia(req);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in send personal message with media', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to flag message
     * <AUTHOR>
     * @since 24/12/2024
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async flagMessage (req, res) {
        try {
            const data = await MessageService.flagMessage(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in flag message', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to get flag message list
     * <AUTHOR>
     * @since 26/12/2024
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async getFlagMessageList (req, res) {
        try {
            const data = await MessageService.getFlagMessageList(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get flag message list', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to update flag message status
     * <AUTHOR>
     * @since 30/12/2024
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async updateFlagMessageStatus (req, res) {
        try {
            const data = await MessageService.updateFlagMessageStatus(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in update flag message status', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to get flag message reasons
     * <AUTHOR>
     * @since 02/01/2025
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async getFlagMessageReasons (req, res) {
        try {
            const data = await MessageService.getFlagMessageReasons(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get flag message reasons', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to get user children list
     * <AUTHOR>
     * @since 02/01/2025
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async getUserChildrenList (req, res) {
        try {
            const data = await MessageService.getUserChildrenList(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get user children list', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to get disabled commenter list
     * <AUTHOR>
     * @since 02/01/2025
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async getDisabledCommenterList (req, res) {
        try {
            const data = await MessageService.getDisabledCommenterList(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in get disabled commenter list', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to enable disabled commenter
     * <AUTHOR> Girbide
     * @since 07/01/2025
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async enableCommenter (req, res) {
        try {
            const data = await MessageService.enableCommenter(req, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in enable disabled commenter', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = MessageController;
