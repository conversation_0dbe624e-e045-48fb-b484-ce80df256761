const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const request = require('supertest');
chai.use(chaiHttp);
const sinon = require('sinon');
const assert = sinon.assert;
const jwt = require('jsonwebtoken');
const Utils = require('../../../util/utilFunctions');
const User = require('../../../models/user.model');
const CONSTANTS = require('../../../util/constants');
const testcaseAiCompanionGroup = require('./testcaseAiCompanionGroup');
const MessageModel = require('../../../models/message.model');
const AiCompanionGroupFeedbackModel = require('../../../models/aiCompanionGroupFeedback.model');
const { beforeEach, afterEach } = require('mocha');
const { CloudWatchClient, PutMetricDataCommand } = require('@aws-sdk/client-cloudwatch');

const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
/* User Token */
const user = {
    id: '95285616-3769-4b2e-b36b-898597b8146e',
    sub: '95285616-3769-4b2e-b36b-898597b8146e',
    email: '<EMAIL>',
    isVerified: 1,
    role: 1,
    status: 'active',
    isDeleted: 0
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'testToken', tokenOptionalInfo)
};

Utils.addCommonReqTokenForHMac(request);

const payload = {
    aiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
    userQueryMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c608',
    groupId: '38325e0b-fed4-42a9-8d45-36f9a445c607',
    feedback: 'positive'
};

const payloadGetFeedbackList = {
    groupId: '38325e0b-fed4-42a9-8d45-36f9a445c607',
    startAiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
    startUserId: 1
};

describe('Add/Update Feedback', () => {
    try {
        let getStub;
        let addMetricForQueryTimeStub;

        before(async () => {
            getStub = sinon.stub(User, 'get');
        });


        beforeEach(() => {
            addMetricForQueryTimeStub = sinon.stub(CloudWatchClient.prototype, 'send').resolves();
        });

        afterEach(() => {
            addMetricForQueryTimeStub.restore();
        });

        after(async () => {
            sinon.restore();
        });

        testcaseAiCompanionGroup.addUpdateAiCompanionGroupFeedback.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                    accessLevel: CONSTANTS.ACCESS_LEVEL.APP
                });

                request(process.env.BASE_URL)
                    .post('/conversation/ai-companion-group/add-update-feedback')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        done();
                    });
            });
        });

        it('should validate if the user query or ai response message is not found', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP
            });

            const batchGetStub = sinon.stub(MessageModel, 'batchGet').resolves(null);

            request(process.env.BASE_URL)
                .post('/conversation/ai-companion-group/add-update-feedback')
                .set({ Authorization: requestPayloadUser.token })
                .send(payload)
                .end((err, res) => {
                    assert.calledOnceWithMatch(batchGetStub, [payload.aiResponseMessageId, payload.userQueryMessageId]);

                    expect(res.body.status).to.be.equal(0);
                    expect(res.statusCode).to.be.equal(400);
                    expect(res.body.message).to.be.equal('AI Response Message or User Query Message not found');

                    batchGetStub.restore();
                    done();
                });
        });

        it('should validate if the user query and ai response message is not in the same group', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP
            });

            const batchGetStub = sinon.stub(MessageModel, 'batchGet').resolves([
                { id: payload.aiResponseMessageId, groupId: '38325e0b-fed4-42a9-8d45-36f9a445c608' },
                { id: payload.userQueryMessageId, groupId: '38325e0b-fed4-42a9-8d45-36f9a445c607' }
            ]);

            request(process.env.BASE_URL)
                .post('/conversation/ai-companion-group/add-update-feedback')
                .set({ Authorization: requestPayloadUser.token })
                .send(payload)
                .end((err, res) => {
                    assert.calledOnceWithMatch(batchGetStub, [payload.aiResponseMessageId, payload.userQueryMessageId]);

                    expect(res.body.status).to.be.equal(0);
                    expect(res.statusCode).to.be.equal(400);
                    expect(res.body.message).to.be.equal('AI Response Message or User Query Message not found');

                    batchGetStub.restore();
                    done();
                });
        });

        it('should validate if the ai message user query id is not same as user query message id', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP
            });

            const batchGetStub = sinon.stub(MessageModel, 'batchGet').resolves([
                {
                    id: payload.aiResponseMessageId,
                    groupId: payload.groupId,
                    userQueryMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
                },
                { id: payload.userQueryMessageId, groupId: payload.groupId }
            ]);

            request(process.env.BASE_URL)
                .post('/conversation/ai-companion-group/add-update-feedback')
                .set({ Authorization: requestPayloadUser.token })
                .send(payload)
                .end((err, res) => {
                    assert.calledOnceWithMatch(batchGetStub, [payload.aiResponseMessageId, payload.userQueryMessageId]);

                    expect(res.body.status).to.be.equal(0);
                    expect(res.statusCode).to.be.equal(400);
                    expect(res.body.message).to.be.equal('AI Response Message or User Query Message not found');

                    batchGetStub.restore();
                    done();
                });
        });

        it('should validate if ai message sender id is not ai companion sender id', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP
            });

            const batchGetStub = sinon.stub(MessageModel, 'batchGet').resolves([
                {
                    id: payload.aiResponseMessageId,
                    groupId: payload.groupId,
                    userQueryMessageId: payload.userQueryMessageId,
                    senderId: 'userId1'
                },
                { id: payload.userQueryMessageId, groupId: payload.groupId, senderId: 'userId' }
            ]);

            request(process.env.BASE_URL)
                .post('/conversation/ai-companion-group/add-update-feedback')
                .set({ Authorization: requestPayloadUser.token })
                .send(payload)
                .end((err, res) => {
                    assert.calledOnceWithMatch(batchGetStub, [payload.aiResponseMessageId, payload.userQueryMessageId]);

                    expect(res.body.status).to.be.equal(0);
                    expect(res.statusCode).to.be.equal(400);
                    expect(res.body.message).to.be.equal('AI Response Message or User Query Message not found');

                    batchGetStub.restore();
                    done();
                });
        });

        it('should update the feedback if feedback with same ai response message id and user id is already present', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP
            });

            const batchGetStub = sinon.stub(MessageModel, 'batchGet').resolves([
                {
                    id: payload.aiResponseMessageId,
                    groupId: payload.groupId,
                    userQueryMessageId: payload.userQueryMessageId,
                    senderId: CONSTANTS.AI_COMPANION_SENDER_ID
                },
                {
                    id: payload.userQueryMessageId,
                    groupId: payload.groupId,
                    senderId: 1
                }
            ]);

            const saveStub = sinon.stub();
            saveStub.resolves({
                aiResponseMessageId: payload.aiResponseMessageId,
                userId: 1,
                userQueryMessageId: payload.userQueryMessageId,
                groupId: payload.groupId,
                feedback: 'negative',
                createdAt: new Date(),
                updatedAt: new Date()
            });

            const aiGroupFeedbackStub = sinon.stub(AiCompanionGroupFeedbackModel, 'get').resolves({
                id: payload.aiResponseMessageId,
                groupId: payload.groupId,
                userQueryMessageId: payload.userQueryMessageId,
                senderId: CONSTANTS.AI_COMPANION_SENDER_ID,
                feedback: 'negative',
                save: saveStub
            });

            request(process.env.BASE_URL)
                .post('/conversation/ai-companion-group/add-update-feedback')
                .set({ Authorization: requestPayloadUser.token })
                .send(payload)
                .end((err, res) => {
                    assert.calledOnceWithMatch(batchGetStub, [payload.aiResponseMessageId, payload.userQueryMessageId]);
                    assert.calledOnceWithMatch(aiGroupFeedbackStub, { aiResponseMessageId: payload.aiResponseMessageId, userId: 1 });

                    assert.calledOnce(saveStub);

                    assert.match(res.body.data, {
                        aiResponseMessageId: payload.aiResponseMessageId,
                        userId: 1,
                        userQueryMessageId: payload.userQueryMessageId,
                        groupId: payload.groupId,
                        feedback: 'negative'
                    });

                    expect(res.body.status).to.be.equal(1);
                    expect(res.statusCode).to.be.equal(200);
                    expect(res.body.message).to.be.equal('Success');

                    assert.calledOnce(addMetricForQueryTimeStub);
                    const commandArg = addMetricForQueryTimeStub.firstCall.args[0];
                    expect(commandArg).to.be.instanceOf(PutMetricDataCommand);
                    assert.match(commandArg.input.Namespace, 'Vaalee/AICompanion');
                    assert.match(commandArg.input.MetricData[0].MetricName, CONSTANTS.CLOUDWATCH_METRIC_NAMES.AI_COMPANION_ACCURACY);
                    assert.match(commandArg.input.MetricData[0].Unit, 'Count');
                    assert.match(commandArg.input.MetricData[0].Value, 1);

                    batchGetStub.restore();
                    aiGroupFeedbackStub.restore();
                    done();
                });
        });

        it('should create the feedback if feedback with same ai response message id and user id is not present', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP
            });

            const batchGetStub = sinon.stub(MessageModel, 'batchGet').resolves([
                {
                    id: payload.aiResponseMessageId,
                    groupId: payload.groupId,
                    userQueryMessageId: payload.userQueryMessageId,
                    senderId: CONSTANTS.AI_COMPANION_SENDER_ID
                },
                {
                    id: payload.userQueryMessageId,
                    groupId: payload.groupId,
                    senderId: 1
                }
            ]);

            const aiGroupFeedbackStub = sinon.stub(AiCompanionGroupFeedbackModel, 'get').resolves(null);
            const aiGroupFeedBackCreateStub = sinon.stub(AiCompanionGroupFeedbackModel, 'create').resolves({
                aiResponseMessageId: payload.aiResponseMessageId,
                groupId: payload.groupId,
                userQueryMessageId: payload.userQueryMessageId,
                userId: 1,
                feedback: 'negative',
                createdAt: new Date(),
                updatedAt: new Date()
            });

            request(process.env.BASE_URL)
                .post('/conversation/ai-companion-group/add-update-feedback')
                .set({ Authorization: requestPayloadUser.token })
                .send({ ...payload, feedback: 'negative' })
                .end((err, res) => {
                    assert.calledOnceWithMatch(batchGetStub, [payload.aiResponseMessageId, payload.userQueryMessageId]);
                    assert.calledOnceWithMatch(aiGroupFeedbackStub, { aiResponseMessageId: payload.aiResponseMessageId, userId: 1 });
                    assert.calledOnceWithMatch(aiGroupFeedBackCreateStub, {
                        aiResponseMessageId: payload.aiResponseMessageId,
                        groupId: payload.groupId,
                        userQueryMessageId: payload.userQueryMessageId,
                        userId: 1,
                        feedback: 'negative'
                    });

                    assert.match(res.body.data, {
                        aiResponseMessageId: payload.aiResponseMessageId,
                        userId: 1,
                        userQueryMessageId: payload.userQueryMessageId,
                        groupId: payload.groupId,
                        feedback: 'negative'
                    });

                    expect(res.body.status).to.be.equal(1);
                    expect(res.statusCode).to.be.equal(200);
                    expect(res.body.message).to.be.equal('Success');

                    assert.calledOnce(addMetricForQueryTimeStub);
                    const commandArg = addMetricForQueryTimeStub.firstCall.args[0];
                    expect(commandArg).to.be.instanceOf(PutMetricDataCommand);
                    assert.match(commandArg.input.Namespace, 'Vaalee/AICompanion');
                    assert.match(commandArg.input.MetricData[0].MetricName, CONSTANTS.CLOUDWATCH_METRIC_NAMES.AI_COMPANION_ACCURACY);
                    assert.match(commandArg.input.MetricData[0].Unit, 'Count');
                    assert.match(commandArg.input.MetricData[0].Value, 0);

                    batchGetStub.restore();
                    aiGroupFeedbackStub.restore();
                    aiGroupFeedBackCreateStub.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Get Feedback List', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        testcaseAiCompanionGroup.getAiCompanionGroupFeedbackList.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                    accessLevel: CONSTANTS.ACCESS_LEVEL.APP
                });

                request(process.env.BASE_URL)
                    .get('/conversation/ai-companion-group/feedback-list')
                    .set({ Authorization: requestPayloadUser.token })
                    .query(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        done();
                    });
            });
        });

        it('should return empty array if no feedback is present', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP
            });

            const aiGroupFeedbackStub = sinon.stub(AiCompanionGroupFeedbackModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        limit: sinon.stub().returns({
                            exec: sinon.stub().resolves([])
                        })
                    })
                })
            });

            request(process.env.BASE_URL)
                .get('/conversation/ai-companion-group/feedback-list')
                .set({ Authorization: requestPayloadUser.token })
                .query({ groupId: payloadGetFeedbackList.groupId })
                .end((err, res) => {
                    assert.match(res.body.data, {
                        startAiResponseMessageId: null,
                        startUserId: null,
                        feedbackList: []
                    });

                    expect(res.body.status).to.be.equal(1);
                    expect(res.statusCode).to.be.equal(200);
                    expect(res.body.message).to.be.equal('Success');

                    aiGroupFeedbackStub.restore();
                    done();
                });
        });

        it('should return feedback list if feedback is present with last key', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP
            });

            const resData = [
                {
                    aiResponseMessageId: payload.aiResponseMessageId,
                    userId: 1,
                    userQueryMessageId: payload.userQueryMessageId,
                    groupId: payload.groupId,
                    feedback: 'positive',
                    createdAt: new Date(),
                    updatedAt: new Date()
                },
                {
                    aiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                    userId: 2,
                    userQueryMessageId: payload.userQueryMessageId,
                    groupId: payload.groupId,
                    feedback: 'negative',
                    createdAt: new Date(),
                    updatedAt: new Date()
                }
            ];

            resData.lastKey = {
                aiResponseMessageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                userId: 2
            };

            const execStub = sinon.stub().resolves(resData);

            const aiGroupFeedbackStub = sinon.stub(AiCompanionGroupFeedbackModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        limit: sinon.stub().returns({
                            startAt: sinon.stub().returns({
                                exec: execStub
                            }),
                            exec: execStub
                        })
                    })
                })
            });

            const messageBatchGetStub = sinon.stub(MessageModel, 'batchGet').resolves([
                { id: payload.aiResponseMessageId, groupId: payload.groupId },
                { id: payload.userQueryMessageId, groupId: payload.groupId }
            ]);

            request(process.env.BASE_URL)
                .get('/conversation/ai-companion-group/feedback-list')
                .set({ Authorization: requestPayloadUser.token })
                .query(payloadGetFeedbackList)
                .end((err, res) => {
                    assert.calledOnceWithMatch(messageBatchGetStub, [payload.aiResponseMessageId, payload.userQueryMessageId]);

                    assert.match(res.body.data, {
                        startAiResponseMessageId: payload.aiResponseMessageId,
                        startUserId: 2,
                        feedbackList: [
                            {
                                userQueryMessage: {
                                    id: payload.userQueryMessageId,
                                    groupId: payload.groupId
                                },
                                aiResponseMessage: {
                                    id: payload.aiResponseMessageId,
                                    groupId: payload.groupId
                                },
                                feedback: 'positive'
                            },
                            {
                                userQueryMessage: {
                                    id: payload.userQueryMessageId,
                                    groupId: payload.groupId
                                },
                                aiResponseMessage: {
                                    id: payload.aiResponseMessageId,
                                    groupId: payload.groupId
                                },
                                feedback: 'negative'
                            }
                        ]
                    });


                    expect(res.body.status).to.be.equal(1);
                    expect(res.statusCode).to.be.equal(200);
                    expect(res.body.message).to.be.equal('Success');

                    aiGroupFeedbackStub.restore();
                    messageBatchGetStub.restore();
                    done();
                });
        });

        it('should return feedback list if feedback is present without last key', (done) => {
            getStub.resolves({
                status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 1,
                accessLevel: CONSTANTS.ACCESS_LEVEL.APP
            });

            const resData = [
                {
                    aiResponseMessageId: payload.aiResponseMessageId,
                    userId: 1,
                    userQueryMessageId: payload.userQueryMessageId,
                    groupId: payload.groupId,
                    feedback: 'positive',
                    createdAt: new Date(),
                    updatedAt: new Date()
                }
            ];

            const execStub = sinon.stub().resolves(resData);

            const aiGroupFeedbackStub = sinon.stub(AiCompanionGroupFeedbackModel, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        limit: sinon.stub().returns({
                            exec: execStub
                        })
                    })
                })
            });

            const messageBatchGetStub = sinon.stub(MessageModel, 'batchGet').resolves([
                { id: payload.aiResponseMessageId, groupId: payload.groupId },
                { id: payload.userQueryMessageId, groupId: payload.groupId }
            ]);

            request(process.env.BASE_URL)
                .get('/conversation/ai-companion-group/feedback-list')
                .set({ Authorization: requestPayloadUser.token })
                .query({ groupId: payloadGetFeedbackList.groupId, pageSize: 2 })
                .end((err, res) => {
                    assert.calledOnceWithMatch(messageBatchGetStub, [payload.aiResponseMessageId, payload.userQueryMessageId]);

                    assert.match(res.body.data, {
                        startAiResponseMessageId: null,
                        startUserId: null,
                        feedbackList: [
                            {
                                userQueryMessage: {
                                    id: payload.userQueryMessageId,
                                    groupId: payload.groupId
                                },
                                aiResponseMessage: {
                                    id: payload.aiResponseMessageId,
                                    groupId: payload.groupId
                                },
                                feedback: 'positive'
                            }
                        ]
                    });

                    expect(res.body.status).to.be.equal(1);
                    expect(res.statusCode).to.be.equal(200);
                    expect(res.body.message).to.be.equal('Success');

                    aiGroupFeedbackStub.restore();
                    messageBatchGetStub.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
