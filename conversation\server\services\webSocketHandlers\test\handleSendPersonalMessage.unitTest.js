const chai = require('chai');
const expect = chai.expect;
const assert = chai.assert;
const sinon = require('sinon');
const handleSendPersonalMessage = require('../handleSendPersonalMessage');
const PersonalMessage = require('../../../models/personalMessage.model');
const SendMessageService = require('../../sendSocketMessageService');
const UploadService = require('../../../util/uploadService');
const PersonalConversation = require('../../../models/personalConversation.model');
const dynamoose = require('dynamoose');
const SQSPushNotificationService = require('../../sqsPushNotificationService');
const { afterEach, beforeEach } = require('mocha');
const websocketRoutes = require('../../../webSocketHandler');
const userModel = require('../../../models/user.model');
const MessageReactionsModel = require('../../../models/messageReactions.model');

describe('Send Personal Message', () => {
    try {
        let createMessageStub;
        let sendMessagesToUsersStub;
        let personalConversationUpdateStub;
        let sqsPushNotificationServiceStub;
        let getPersonalConversationStub;
        let personalConversationCreateStub;
        let userQueryStub;
        let dynamooseTransactionStub;
        beforeEach(() => {
            dynamooseTransactionStub = sinon.stub(dynamoose, 'transaction');
            createMessageStub = sinon.stub(PersonalMessage, 'create');
            sendMessagesToUsersStub = sinon.stub(SendMessageService, 'sendMessagesToUsers');
            personalConversationUpdateStub = sinon.stub(PersonalConversation, 'update');
            sqsPushNotificationServiceStub = sinon.stub(SQSPushNotificationService, 'sendPersonalMessage');
            getPersonalConversationStub = sinon.stub(PersonalConversation, 'get');
            personalConversationCreateStub = sinon.stub(PersonalConversation.transaction, 'create')
                .callsFake((data) => data);
            userQueryStub = sinon.stub(userModel, 'query');
        });

        afterEach(() => {
            sinon.restore();
        });

        it('should throw 400 error if the event is not valid', async () => {
            const event = {
                body: {
                    action: 'sendPersonalMessage',
                    actionType: 'INVALID_ACTION_TYPE',
                    userId: 'userId1',
                    message: 'message1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(400);
        });

        it('should handle error in send personal message', async () => {
            const event = {
                body: {
                    actionType: 'NEW_PERSONAL_MESSAGE',
                    messageId: 'test_message_id',
                    message: 'Hello, how are you?',
                    senderId: 'test_sender_id',
                    receiverId: 'test_receiver_id',
                    conversationId: 'test_conversation_id',
                    replyMessage: {
                        messageId: 'test_reply_message_id',
                        senderId: 'test_receiver_id',
                        message: 'I am fine, thank you!',
                        mediaUrl: 'test_media_url'
                    }
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const getReplyMessageStub = sinon.stub(PersonalMessage, 'get');
            getReplyMessageStub.resolves();
            createMessageStub.rejects(new Error('test error'));

            const result = await handleSendPersonalMessage(event);
            expect(result.statusCode).to.equal(500);
        });

        it('Should send personal message', async () => {
            const event = {
                body: {
                    actionType: 'NEW_PERSONAL_MESSAGE',
                    messageId: 'test_message_id',
                    message: 'Hello, how are you?',
                    senderId: 'test_sender_id',
                    receiverId: 'test_receiver_id',
                    conversationId: 'test_conversation_id',
                    mediaThumbnailName: 'test_media_thumbnail_name'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const savedMessage = {
                messageId: 'test_message_id',
                conversationId: 'test_conversation_id',
                senderId: 'test_sender_id',
                message: 'Hello, how are you?',
                createdAt: '2024-12-17T12:34:56.789Z',
                mediaThumbnailName: 'test_media_thumbnail_name'
            };
            const lastReadMessage = { messageId: 'test_message_id', createdAt: '2024-12-17T12:34:56.789Z' };
            createMessageStub.resolves(savedMessage);
            personalConversationUpdateStub.resolves(lastReadMessage);
            sendMessagesToUsersStub.resolves();
            sqsPushNotificationServiceStub.resolves();
            getPersonalConversationStub.resolves({ isMuted: false });
            const result = await handleSendPersonalMessage(event);

            expect(result.statusCode).to.equal(200);
            expect(result.message).to.equal('Personal message sent successfully!');
            expect(result.actionType).to.equal('NEW_PERSONAL_MESSAGE');
            expect(result.action).to.equal('sendPersonalMessage');
            const parsedData = result.data;
            expect(parsedData.messageId).to.equal('test_message_id');
            expect(parsedData.senderId).to.equal('test_sender_id');
            expect(parsedData.conversationId).to.equal('test_conversation_id');
            expect(parsedData.message).to.equal('Hello, how are you?');

            sinon.assert.calledOnceWithExactly(createMessageStub, {
                messageId: 'test_message_id',
                conversationId: 'test_conversation_id',
                senderId: 'test_sender_id',
                message: 'Hello, how are you?',
                mediaName: undefined,
                mediaType: undefined,
                mediaDisplayName: undefined,
                mediaThumbnailName: 'test_media_thumbnail_name',
                replyMessage: undefined
            });

            sinon.assert.calledOnceWithExactly(personalConversationUpdateStub,
                { userAId: 'test_sender_id', userBId: 'test_receiver_id' },
                { lastReadMessage });

            sinon.assert.calledOnceWithExactly(sendMessagesToUsersStub, ['test_sender_id', 'test_receiver_id'], {
                data: { ...savedMessage, mediaThumbnailName: undefined, mediaThumbnailUrl: undefined },
                actionType: 'NEW_PERSONAL_MESSAGE'
            });
            sinon.assert.calledOnceWithExactly(sqsPushNotificationServiceStub,
                'test_receiver_id', 'test_sender_id', undefined, 'test_conversation_id', 'Hello, how are you?');
        });

        it('Should send personal message with reply message', async () => {
            const event = {
                body: {
                    actionType: 'NEW_PERSONAL_MESSAGE',
                    messageId: 'test_message_id',
                    message: 'Hello, how are you?',
                    senderId: 'test_sender_id',
                    receiverId: 'test_receiver_id',
                    conversationId: 'test_conversation_id',
                    replyMessage: {
                        messageId: 'test_reply_message_id',
                        senderId: 'test_receiver_id',
                        message: 'I am fine, thank you!',
                        mediaUrl: 'test_media_url'
                    }
                },
                requestContext: { connectionId: 'connectionId1' }
            };
            const savedMessage = {
                messageId: 'test_message_id',
                conversationId: 'test_conversation_id',
                senderId: 'test_sender_id',
                message: 'Hello, how are you?',
                replyMessage: {
                    messageId: 'test_reply_message_id',
                    senderId: 'test_receiver_id',
                    message: 'I am fine, thank you!',
                    mediaName: 'test_media_name',
                    mediaType: 'test_media_type',
                    mediaDisplayName: 'test_media_display_name'
                },
                createdAt: '2024-12-17T12:34:56.789Z'
            };
            const replyMessage = {
                messageId: 'test_reply_message_id',
                conversationId: 'test_conversation_id',
                senderId: 'test_receiver_id',
                message: 'I am fine, thank you!',
                mediaName: 'test_media_name',
                mediaType: 'test_media_type',
                mediaDisplayName: 'test_media_display_name'
            };
            const lastReadMessage = { messageId: 'test_message_id', createdAt: '2024-12-17T12:34:56.789Z' };

            const getReplyMessageStub = sinon.stub(PersonalMessage, 'get');
            const getSignedUrlForAttachmentsStub = sinon.stub(UploadService, 'getSignedUrlForAttachments');
            getReplyMessageStub.resolves(replyMessage);
            createMessageStub.resolves(savedMessage);
            sendMessagesToUsersStub.resolves();
            getSignedUrlForAttachmentsStub
                .withArgs('test_media_name').resolves('test_media_name_url');
            sqsPushNotificationServiceStub.resolves();
            getPersonalConversationStub.resolves({ isMuted: true });

            const result = await handleSendPersonalMessage(event);

            expect(result.statusCode).to.equal(200);
            expect(result.message).to.equal('Personal message sent successfully!');
            expect(result.actionType).to.equal('NEW_PERSONAL_MESSAGE');
            expect(result.action).to.equal('sendPersonalMessage');
            const parsedData = result.data;
            expect(parsedData.messageId).to.equal('test_message_id');
            expect(parsedData.senderId).to.equal('test_sender_id');
            expect(parsedData.conversationId).to.equal('test_conversation_id');
            expect(parsedData.message).to.equal('Hello, how are you?');
            expect(parsedData.replyMessage).to.deep.equal({
                messageId: 'test_reply_message_id',
                senderId: 'test_receiver_id',
                message: 'I am fine, thank you!',
                mediaUrl: 'test_media_name_url',
                mediaType: 'test_media_type',
                mediaDisplayName: 'test_media_display_name',
                mediaName: undefined
            });

            sinon.assert.calledOnceWithExactly(createMessageStub, {
                messageId: 'test_message_id',
                conversationId: 'test_conversation_id',
                senderId: 'test_sender_id',
                message: 'Hello, how are you?',
                mediaName: undefined,
                mediaType: undefined,
                mediaDisplayName: undefined,
                mediaThumbnailName: undefined,
                replyMessage: {
                    messageId: 'test_reply_message_id',
                    conversationId: 'test_conversation_id',
                    senderId: 'test_receiver_id',
                    message: 'I am fine, thank you!',
                    mediaName: 'test_media_name',
                    mediaType: 'test_media_type',
                    mediaDisplayName: 'test_media_display_name'
                }
            });

            sinon.assert.calledOnceWithExactly(personalConversationUpdateStub,
                { userAId: 'test_sender_id', userBId: 'test_receiver_id' },
                { lastReadMessage });

            sinon.assert.calledOnceWithExactly(sendMessagesToUsersStub, ['test_sender_id', 'test_receiver_id'], {
                data: { ...savedMessage },
                actionType: 'NEW_PERSONAL_MESSAGE'
            });
            sinon.assert.notCalled(sqsPushNotificationServiceStub);
        });

        it('Should send personal message and start personal conversation if conversation does not exist', async () => {
            const event = {
                body: {
                    actionType: 'NEW_PERSONAL_MESSAGE',
                    messageId: 'test_message_id',
                    message: 'Hello, how are you?',
                    senderId: 'test_sender_id',
                    receiverId: 'test_receiver_id'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const savedMessage = {
                messageId: 'test_message_id',
                conversationId: 'test_conversation_id',
                senderId: 'test_sender_id',
                message: 'Hello, how are you?',
                createdAt: '2024-12-17T12:34:56.789Z'
            };
            const lastReadMessage = { messageId: 'test_message_id', createdAt: '2024-12-17T12:34:56.789Z' };
            createMessageStub.resolves(savedMessage);
            personalConversationUpdateStub.resolves(lastReadMessage);
            sendMessagesToUsersStub.resolves();
            sqsPushNotificationServiceStub.resolves();
            getPersonalConversationStub.resolves({ isMuted: false });
            userQueryStub.returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub()
                            .onFirstCall().resolves([{ id: 'test_sender_id' }])
                            .onSecondCall().resolves([{ id: 'test_receiver_id' }])
                    })
                })
            });
            dynamooseTransactionStub.resolves();

            const result = await handleSendPersonalMessage(event);

            expect(result.statusCode).to.equal(200);
            expect(result.message).to.equal('Conversation started successfully!');
            expect(result.actionType).to.equal('START_PERSONAL_CONVERSATION');
            expect(result.action).to.equal('sendPersonalMessage');
            const parsedData = result.data;
            expect(parsedData.messageId).to.equal('test_message_id');
            expect(parsedData.senderId).to.equal('test_sender_id');
            expect(parsedData.conversationId).to.equal('test_conversation_id');
            expect(parsedData.message).to.equal('Hello, how are you?');

            sinon.assert.calledOnceWithExactly(personalConversationUpdateStub,
                { userAId: 'test_sender_id', userBId: 'test_receiver_id' },
                { lastReadMessage });
            sinon.assert.calledOnce(dynamooseTransactionStub);
            sinon.assert.calledTwice(personalConversationCreateStub);
        });

        it('Should handle error in send personal message and start personal conversation if conversation does not exist', async () => {
            const event = {
                body: {
                    actionType: 'NEW_PERSONAL_MESSAGE',
                    messageId: 'test_message_id',
                    message: 'Hello, how are you?',
                    senderId: 'test_sender_id',
                    receiverId: 'test_receiver_id'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            dynamooseTransactionStub.rejects(new Error('test error'));

            const result = await handleSendPersonalMessage(event);

            expect(result.statusCode).to.equal(500);
            expect(result.message).to.equal('Failed to start personal conversation!');
            expect(result.actionType).to.equal('START_PERSONAL_CONVERSATION');
            expect(result.action).to.equal('sendPersonalMessage');
            const parsedData = result.data;
            expect(parsedData.messageId).to.equal('test_message_id');

            sinon.assert.notCalled(personalConversationUpdateStub);
            sinon.assert.calledOnce(dynamooseTransactionStub);
            sinon.assert.calledTwice(personalConversationCreateStub);
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});

describe('Start Personal Message', () => {
    try {
        let dynamooseTransactionStub;
        let personalConversationCreateStub;
        let userQueryStub;
        let sendMessagesToUsersStub;
        beforeEach(() => {
            dynamooseTransactionStub = sinon.stub(dynamoose, 'transaction');
            personalConversationCreateStub = sinon.stub(PersonalConversation.transaction, 'create')
                .callsFake((data) => data);
            userQueryStub = sinon.stub(userModel, 'query');
            sendMessagesToUsersStub = sinon.stub(SendMessageService, 'sendMessagesToUsers');
        });

        afterEach(() => {
            sinon.restore();
        });

        it('should handle error in user query', async () => {
            const conversationId = 'test_conversation_id';
            const senderId = 'test_sender_id';
            const receiverId = 'test_receiver_id';
            userQueryStub.throws(new Error('test error'));

            const result = await handleSendPersonalMessage({
                body: {
                    conversationId,
                    senderId,
                    receiverId,
                    actionType: 'START_PERSONAL_CONVERSATION'
                },
                requestContext: { connectionId: 'connectionId1' }
            });

            expect(result.statusCode).to.equal(500);
        });

        it('Should start personal message', async () => {
            const conversationId = 'test_conversation_id';
            const senderId = 'test_sender_id';
            const receiverId = 'test_receiver_id';
            userQueryStub.returns({
                eq: sinon.stub().returns({
                    attributes: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ id: 'test_user_id' }])
                    })
                })
            });
            sendMessagesToUsersStub.resolves();

            const result = await handleSendPersonalMessage({
                body: {
                    conversationId,
                    senderId,
                    receiverId,
                    actionType: 'START_PERSONAL_CONVERSATION'
                },
                requestContext: { connectionId: 'connectionId1' }
            });

            sinon.assert.calledOnce(dynamooseTransactionStub);
            sinon.assert.calledTwice(personalConversationCreateStub);

            expect(result.statusCode).to.equal(200);
            expect(result.message).to.equal('Conversation started successfully!');
            expect(result.actionType).to.equal('START_PERSONAL_CONVERSATION');
            expect(result.action).to.equal('sendPersonalMessage');
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Edit Personal Message', () => {
    try {
        afterEach(() => {
            sinon.restore();
        });

        it('Should handle error in edit personal message', async () => {
            const senderId = 'test_sender_id';
            const receiverId = 'test_receiver_id';
            const messageId = 'test_message_id';
            const event = {
                body: {
                    actionType: 'EDIT_PERSONAL_MESSAGE',
                    messageId,
                    message: 'Hello, how are you?',
                    senderId,
                    receiverId
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            sinon.stub(PersonalMessage, 'update').rejects(new Error('test error'));

            const result = await handleSendPersonalMessage(event);
            expect(result.statusCode).to.equal(500);
        });

        it('Should edit personal message', async () => {
            const senderId = 'test_sender_id';
            const receiverId = 'test_receiver_id';
            const messageId = 'test_message_id';
            const event = {
                body: {
                    actionType: 'EDIT_PERSONAL_MESSAGE',
                    messageId,
                    message: 'Hello, how are you?',
                    senderId,
                    receiverId
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const updatedMessage = {
                messageId,
                conversationId: 'test_conversation_id',
                senderId,
                message: 'Hello, how are you?',
                isEdited: true
            };
            const activeUsers = [ senderId, receiverId ];
            const sendMessagesToUsersStub = sinon.stub(SendMessageService, 'sendMessagesToUsers');
            const updateMessageStub = sinon.stub(PersonalMessage, 'update').resolves(updatedMessage);

            const result = await handleSendPersonalMessage(event);

            expect(result.statusCode).to.equal(200);
            expect(result.message).to.equal('Personal message edited successfully!');
            expect(result.actionType).to.equal('EDIT_PERSONAL_MESSAGE');
            expect(result.action).to.equal('sendPersonalMessage');
            expect(result.data).to.deep.equal(updatedMessage);

            sinon.assert.calledOnceWithExactly(updateMessageStub, { messageId }, { message: 'Hello, how are you?', isEdited: true });
            sinon.assert.calledOnceWithExactly(sendMessagesToUsersStub, activeUsers,
                { data: updatedMessage, actionType: 'EDIT_PERSONAL_MESSAGE' });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Delete Personal Message', () => {
    try {
        afterEach(() => {
            sinon.restore();
        });

        it('Should handle error in delete personal message', async () => {
            const senderId = 'test_sender_id';
            const receiverId = 'test_receiver_id';
            const messageId = 'test_message_id';
            const event = {
                body: {
                    actionType: 'DELETE_PERSONAL_MESSAGE',
                    messageId,
                    message: 'Hello, how are you?',
                    senderId,
                    receiverId
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            sinon.stub(PersonalMessage, 'update').rejects(new Error('test error'));

            const result = await handleSendPersonalMessage(event);
            expect(result.statusCode).to.equal(500);
        });

        it('Should delete personal message', async () => {
            const senderId = 'test_sender_id';
            const receiverId = 'test_receiver_id';
            const messageId = 'test_message_id';
            const event = {
                body: {
                    actionType: 'DELETE_PERSONAL_MESSAGE',
                    messageId,
                    message: 'Hello, how are you?',
                    senderId,
                    receiverId
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const updatedMessage = {
                messageId,
                conversationId: 'test_conversation_id',
                senderId,
                message: 'Hello, how are you?',
                isDeleted: true
            };
            const activeUsers = [ senderId, receiverId ];
            const sendMessagesToUsersStub = sinon.stub(SendMessageService, 'sendMessagesToUsers');
            const updateMessageStub = sinon.stub(PersonalMessage, 'update').resolves(updatedMessage);

            const result = await handleSendPersonalMessage(event);

            expect(result.statusCode).to.equal(200);
            expect(result.message).to.equal('Personal message deleted successfully!');
            expect(result.actionType).to.equal('DELETE_PERSONAL_MESSAGE');
            expect(result.action).to.equal('sendPersonalMessage');
            expect(result.data).to.deep.equal(updatedMessage);

            sinon.assert.calledOnceWithExactly(updateMessageStub, { messageId }, { isDeleted: true });
            sinon.assert.calledOnceWithExactly(sendMessagesToUsersStub, activeUsers,
                { data: updatedMessage, actionType: 'DELETE_PERSONAL_MESSAGE' });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Mute Personal Conversation Handler', () => {
    afterEach(() => {
        sinon.restore();
    });

    it('should throw error if user is not a member of the group', async () => {
        const event = {
            body: {
                actionType: 'MUTE_PERSONAL_CONVERSATION',
                conversationId: '123',
                muteConversation: true,
                userId: '456'
            },
            requestContext: { connectionId: 'connectionId1' }
        };

        sinon.stub(PersonalConversation, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            exec: sinon.stub().resolves([])
                        })
                    })
                })
            })
        });

        const response = await handleSendPersonalMessage(event);
        assert.equal(response.statusCode, 404);
    });

    it('should handle error while updating personal conversation status', async () => {
        const event = {
            body: { actionType: 'MUTE_PERSONAL_CONVERSATION', conversationId: '123', muteConversation: false, userId: '456' },
            requestContext: { connectionId: 'connectionId1' }
        };

        sinon.stub(PersonalConversation, 'update').rejects(new Error('Failed to update personal conversation status'));

        sinon.stub(PersonalConversation, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            exec: sinon.stub().resolves([{ conversationId: '123', userAId: '456', userBId: '789' }])
                        })
                    })
                })
            })
        });

        const response = await handleSendPersonalMessage(event);
        assert.equal(response.statusCode, 500);
    });

    it('should return success response and send event to user if personal conversation is updated successfully', async () => {
        const event = {
            body: { actionType: 'MUTE_PERSONAL_CONVERSATION', conversationId: '123', muteConversation: true, userId: '456' },
            requestContext: { connectionId: 'connectionId1' }
        };

        sinon.stub(PersonalConversation, 'update').resolves();
        sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();

        sinon.stub(PersonalConversation, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            exec: sinon.stub().resolves([{ conversationId: '123', userAId: '456', userBId: '789' }])
                        })
                    })
                })
            })
        });

        const response = await handleSendPersonalMessage(event);
        assert.equal(response.statusCode, 200);
        assert(SendMessageService.sendMessagesToUsers.calledOnce);
    });
});

describe('Handle Send Group Message, BLOCK_PERSONAL_CONVERSATION', () => {
    afterEach(() => {
        sinon.restore();
    });

    it('should throw error if personal conversation is not found', async () => {
        const event = {
            body: { actionType: 'BLOCK_PERSONAL_CONVERSATION', receiverId: '123', isBlocked: true, userId: '456' },
            requestContext: { connectionId: 'connectionId1' }
        };

        sinon.stub(PersonalConversation, 'get').resolves(null);

        const response = await handleSendPersonalMessage(event);
        assert.equal(response.statusCode, 404);
    });

    it('should handle error while updating personal conversation status', async () => {
        const event = {
            body: { actionType: 'BLOCK_PERSONAL_CONVERSATION', receiverId: '123', isBlocked: false, userId: '456' },
            requestContext: { connectionId: 'connectionId1' }
        };

        sinon.stub(PersonalConversation, 'update').rejects(new Error('Failed to update personal conversation status'));
        sinon.stub(PersonalConversation, 'get').resolves({ userAId: '456', userBId: '123', conversationId: '123' });

        const response = await handleSendPersonalMessage(event);
        assert.equal(response.statusCode, 500);
    });

    it('should return success response and send event to user if personal conversation is updated successfully', async () => {
        const event = {
            body: { actionType: 'BLOCK_PERSONAL_CONVERSATION', receiverId: '123', isBlocked: true, userId: '456' },
            requestContext: { connectionId: 'connectionId1' }
        };

        sinon.stub(PersonalConversation, 'update').resolves();
        sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();
        sinon.stub(PersonalConversation, 'get').resolves({ userAId: '456', userBId: '123', conversationId: '123' });

        const response = await handleSendPersonalMessage(event);
        assert.equal(response.statusCode, 200);
        sinon.assert.calledOnceWithExactly(
            SendMessageService.sendMessagesToUsers,
            ['456', '123'],
            {
                data: { userAId: '456', userBId: '123', isBlocked: true, conversationId: '123' },
                actionType: 'BLOCK_PERSONAL_CONVERSATION'
            }
        );
    });
});

describe('Handle Send Group Message, READ_PERSONAL_MESSAGE', () => {
    afterEach(() => {
        sinon.restore();
    });

    it('should handle error while updating personal conversation status', async () => {
        const event = {
            body: { actionType: 'READ_PERSONAL_MESSAGE', conversationId: '123', userId: '456' },
            requestContext: { connectionId: 'connectionId1' }
        };

        sinon.stub(PersonalConversation, 'query').rejects(new Error('Failed to query personal conversation'));

        const response = await handleSendPersonalMessage(event);
        assert.equal(response.statusCode, 500);
    });

    it('should return 404 error if personal conversation is not found', async () => {
        const event = {
            body: { actionType: 'READ_PERSONAL_MESSAGE', conversationId: '123', userId: '456' },
            requestContext: { connectionId: 'connectionId1' }
        };

        sinon.stub(PersonalConversation, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({ exec: sinon.stub().resolves([]) })
                    })
                })
            })
        });

        const response = await handleSendPersonalMessage(event);
        assert.equal(response.statusCode, 404);
    });

    it('should return success response and send event to user if personal conversation is updated successfully', async () => {
        const conversationId = 'test_conversation_id';
        const userId = 'test_user_id';
        const messageId = 'test_message_id';
        const createdAt = new Date().toISOString();
        const saveStub = sinon.stub();

        const event = {
            body: { actionType: 'READ_PERSONAL_MESSAGE', conversationId, userId, messageId, createdAt },
            requestContext: { connectionId: 'connectionId1' }
        };

        sinon.stub(PersonalConversation, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            exec: sinon.stub().resolves(
                                [{ conversationId: '123', userAId: '456', userBId: '789', save: saveStub }])
                        })
                    })
                })
            })
        });

        saveStub.resolves();

        const response = await handleSendPersonalMessage(event);
        const parsedData = response.data;
        assert.equal(response.statusCode, 200);
        assert.equal(parsedData.conversationId, conversationId);
        assert.equal(parsedData.userId, userId);
        sinon.assert.calledOnce(saveStub);
    });
});

describe('Reactions for messages', () => {
    try {
        it('should throw error if invalid reaction is passed', async () => {
            const event = {
                body: {
                    action: 'sendPersonalMessage',
                    actionType: 'ADD_PERSONAL_MESSAGE_REACTION',
                    messageId: 'messageId1',
                    reaction: 'reaction1'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(400);
            expect(result.message).to.equal('Invalid reaction');
        });

        it('should handle error in adding reaction for a message', async () => {
            const event = {
                body: {
                    action: 'sendPersonalMessage',
                    actionType: 'ADD_PERSONAL_MESSAGE_REACTION',
                    messageId: 'messageId1',
                    userId: 'userId',
                    reaction: CONSTANTS.ALLOWED_REACTIONS[0],
                    receiverId: 'receiverId',
                    conversationId: 'conversationId'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            sinon.stub(MessageReactionsModel, 'update').rejects(new Error('Something went wrong!'));

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(500);
            expect(result.message).to.equal('Failed to add reaction');

            MessageReactionsModel.update.restore();
        });

        it('should handle success in adding reaction for a message', async () => {
            const messageId = 'messageId1';
            const userId = 'userId';
            const reaction = CONSTANTS.ALLOWED_REACTIONS[0];
            const groupId = 'groupId';
            const event = {
                body: {
                    action: 'sendPersonalMessage',
                    actionType: 'ADD_PERSONAL_MESSAGE_REACTION',
                    messageId,
                    userId,
                    reaction,
                    groupId
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            sinon.stub(MessageReactionsModel, 'update').resolves();

            sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);
            expect(result.message).to.equal('Reaction added successfully');
            expect(result.data).to.deep.equal(JSON.stringify({
                messageId,
                userId,
                reaction,
                groupId
            }));

            MessageReactionsModel.update.restore();
            SendMessageService.sendMessagesToUsers.restore();
        });

        it('should handle error in updating reaction for a message', async () => {
            const event = {
                body: {
                    action: 'sendPersonalMessage',
                    actionType: 'UPDATE_PERSONAL_MESSAGE_REACTION',
                    messageId: 'messageId1',
                    userId: 'userId',
                    reaction: CONSTANTS.ALLOWED_REACTIONS[0],
                    groupId: 'groupId'
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            sinon.stub(MessageReactionsModel, 'update').rejects(new Error('Something went wrong!'));

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(500);
            expect(result.message).to.equal('Failed to update reaction');

            MessageReactionsModel.update.restore();
        });

        it('should handle success in UPDATE_PERSONAL_MESSAGE_REACTION websocket route', async () => {
            const messageId = 'messageId1';
            const userId = 'userId';
            const reaction = CONSTANTS.ALLOWED_REACTIONS[0];
            const groupId = 'groupId';
            const event = {
                body: {
                    action: 'sendPersonalMessage',
                    actionType: 'UPDATE_PERSONAL_MESSAGE_REACTION',
                    messageId,
                    userId,
                    reaction,
                    groupId
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            sinon.stub(MessageReactionsModel, 'update').resolves();

            sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);
            expect(result.message).to.equal('Reaction updated successfully');
            expect(result.data).to.deep.equal(JSON.stringify({
                messageId,
                userId,
                reaction,
                groupId
            }));

            MessageReactionsModel.update.restore();
            SendMessageService.sendMessagesToUsers.restore();
        });

        it('should handle error in REMOVE_PERSONAL_MESSAGE_REACTION websocket route', async () => {
            const messageId = 'messageId1';
            const userId = 'userId';
            const groupId = 'groupId';
            const event = {
                body: {
                    action: 'sendPersonalMessage',
                    actionType: 'REMOVE_PERSONAL_MESSAGE_REACTION',
                    messageId,
                    userId,
                    groupId
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            sinon.stub(MessageReactionsModel, 'delete').rejects(new Error('Something went wrong!'));

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(500);
            expect(result.message).to.equal('Failed to remove reaction');

            MessageReactionsModel.delete.restore();
        });

        it('should handle success in REMOVE_PERSONAL_MESSAGE_REACTION websocket route', async () => {
            const messageId = 'messageId1';
            const userId = 'userId';
            const groupId = 'groupId';
            const event = {
                body: {
                    action: 'sendPersonalMessage',
                    actionType: 'REMOVE_PERSONAL_MESSAGE_REACTION',
                    messageId,
                    userId,
                    groupId
                },
                requestContext: { connectionId: 'connectionId1' }
            };

            sinon.stub(MessageReactionsModel, 'delete').resolves();

            sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();

            const result = await websocketRoutes(event);
            expect(result.statusCode).to.equal(200);
            expect(result.message).to.equal('Reaction removed successfully');
            expect(result.data).to.deep.equal(JSON.stringify({
                messageId,
                userId,
                groupId
            }));

            MessageReactionsModel.delete.restore();
            SendMessageService.sendMessagesToUsers.restore();
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Handle Send Group Message, DELETE_PERSONAL_CONVERSATION', () => {
    afterEach(() => {
        sinon.restore();
    });

    it('should throw error if personal conversation is not found', async () => {
        const event = {
            body: {
                actionType: 'DELETE_PERSONAL_CONVERSATION',
                senderId: 'senderId', receiverId: 'receiverId', conversationId: 'conversationId', lastMessageId: 'lastMessageId'
            },
            requestContext: { connectionId: 'connectionId1' }
        };

        sinon.stub(PersonalConversation, 'get').resolves(null);

        const response = await handleSendPersonalMessage(event);
        assert.equal(response.statusCode, 404);
    });

    it('Should handle error in deleting personal conversation', async () => {
        const event = {
            body: {
                actionType: 'DELETE_PERSONAL_CONVERSATION', senderId: 'senderId', receiverId: 'receiverId',
                conversationId: 'conversationId', lastMessageId: 'lastMessageId'
            },
            requestContext: { connectionId: 'connectionId1' }
        };

        sinon.stub(PersonalConversation, 'get').rejects(new Error('Something went wrong!'));

        const response = await handleSendPersonalMessage(event);
        assert.equal(response.statusCode, 500);
    });

    it('Should handle success in deleting personal conversation', async () => {
        const event = {
            body: {
                actionType: 'DELETE_PERSONAL_CONVERSATION', senderId: 'senderId',
                receiverId: 'receiverId', conversationId: 'conversationId', lastMessageId: 'lastMessageId'
            },
            requestContext: { connectionId: 'connectionId1' }
        };

        const saveStub = sinon.stub();

        sinon.stub(PersonalConversation, 'get').resolves({
            lastMessageIdBeforeDeleted: 'lastMessageIdBeforeDeleted',
            save: saveStub,
            conversationId: 'conversationId'
        });

        sinon.stub(SendMessageService, 'sendMessagesToUsers').resolves();

        const response = await handleSendPersonalMessage(event);
        assert.equal(response.statusCode, 200);
        sinon.assert.calledOnceWithExactly(saveStub);
        sinon.assert.calledOnceWithExactly(SendMessageService.sendMessagesToUsers, ['senderId'], {
            data: {
                userAId: 'senderId', userBId: 'receiverId',
                conversationId: 'conversationId', lastMessageIdBeforeDeleted: 'lastMessageId'
            },
            actionType: 'DELETE_PERSONAL_CONVERSATION'
        });
    });
});
