const GeneralError = require('../../util/GeneralError');
const validation = require('../../util/validation');
const REQUIRED = 'FIELD_REQUIRED';
const INVALID = 'FIELD_NOT_VALID';

/**
 * Class represents validations for payment.
 */
class PaymentValidator extends validation {
    constructor (body, locale, query) {
        super(locale);
        this.NOT_VALID = INVALID;
        this.REQUIRED = REQUIRED;
        this.body = body;
        this.query = query;
    }

    /**
     * @desc This function is being used to validate request for payment
     * <AUTHOR>
     * @since 17/11/2023
     */
    validate () {
        const {
            eventId,
            childId,
            paymentType,
            quantity,
            donationAmount,
            isGuestSignup,
            userEmail,
            childFirstName,
            childLastName
        } = this.body;

        super.field(eventId, 'Event Id');
        super.field(paymentType, 'Payment Type');
        this.verifyPaymentType();
        quantity && super.field(quantity, 'Quantity');
        quantity && this.isNumber(quantity, 'Quantity');
        if (donationAmount !== undefined &&
            !CONSTANTS.REGEX.DONATION_AMOUNT.test(donationAmount.trim())) {
            throw new GeneralError(this.__(INVALID, 'Donation Amount'), 400);
        }

        const parsedIsGuestSignup = isGuestSignup?.toString()?.toLowerCase() === 'true';
        this.body.isGuestSignup = parsedIsGuestSignup;
        if (!parsedIsGuestSignup) {
            super.field(childId, 'Child Id');
        } else {
            super.email(userEmail);
            super.name(childFirstName, 'Child First Name');
            super.name(childLastName, 'Child Last Name');
        }
    }

    verifyPaymentType () {
        const { paymentType } = this.body;
        if (!['stripe', 'cash', 'cheque', 'venmo', 'free'].includes(paymentType)) {
            throw new GeneralError(this.__(INVALID, 'Payment Type'), 400);
        }
    }

    isNumber () {
        const { quantity } = this.body;
        if (isNaN(quantity)) {
            throw new GeneralError(this.__(INVALID, 'Quantity'), 400);
        }
    }

    validateOptionalFee () {
        const { isCoveredByUser } = this.body;
        if (typeof isCoveredByUser !== 'boolean') {
            throw new GeneralError(this.__(INVALID, 'Fees covered by user'), 400);
        }
    }
}


module.exports = PaymentValidator;
