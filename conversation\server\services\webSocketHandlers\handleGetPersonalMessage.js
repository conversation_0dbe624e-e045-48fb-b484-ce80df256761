const UploadService = require('../../util/uploadService');
const PersonalMessage = require('../../models/personalMessage.model');
const User = require('../../models/user.model');
const Child = require('../../models/child.model');
const Organization = require('../../models/organization.model');
const { getUserConnectionsList } = require('./handlers/getUserConnectionsListHandler');
const EnrichmentService = require('../enrichmentService');
const MessageReactionsHandler = require('./handlers/messageReactionsHandler');
const CONSTANTS = require('../../util/constants');
const PersonalConversationModel = require('../../models/personalConversation.model');
const { filterMessagesFromDeletedMessage, getCalculatedLastEvaluatedKey } = require('./conversationUtils');

module.exports = async (event) => {
    switch (event.body.actionType) {
        case CONSTANTS.ACTION_TYPES.GET_PERSONAL_MESSAGES.LAZY_LOAD_PERSONAL_MESSAGES:
            return await getLazyLoadPersonalMessages(event);
        case CONSTANTS.ACTION_TYPES.GET_PERSONAL_MESSAGES.GET_USER_CONNECTED_CHILDREN:
            return await getUserConnectedChildren(event);
        case CONSTANTS.ACTION_TYPES.GET_PERSONAL_MESSAGES.GET_USER_CONNECTIONS_LIST:
            return await getUserConnectionsList(event);
        default:
            return {
                statusCode: 400,
                body: 'Invalid action type'
            };
    }
};

async function getLazyLoadPersonalMessages (event) {
    try {
        const conversationId = event.body.conversationId;
        const lastEvaluatedKey = event.body.lastEvaluatedKey;
        const clientLastSyncedMessage = event.body.clientLastSyncedMessage;
        const limit = event.body.limit;
        const senderId = event.body.senderId;
        const receiverId = event.body.receiverId;
        const result = await syncPersonalMessages({
            conversationId, lastEvaluatedKey, clientLastSyncedMessage,
            limit, senderId, receiverId
        });
        const processedResult = { ...result };
        const messagesWithSignedUrlPromise = processedResult.messages.map(
            async (message) => await EnrichmentService.enrichMessageWithMediaUrls(message)
        );
        const messagesWithSignedUrl = await Promise.all(messagesWithSignedUrlPromise);
        processedResult.messages = messagesWithSignedUrl;
        return {
            statusCode: 200,
            message: 'Get personal messages successful!',
            data: processedResult,
            actionType: 'LAZY_LOAD_PERSONAL_MESSAGES',
            action: 'getPersonalMessage'
        };
    } catch (error) {
        CONSOLE_LOGGER.error('--> Error while get lazy load messages --> ', error);
        if (error.statusCode) {
            return error;
        }

        return {
            statusCode: 500,
            message: 'Failed to get personal message!',
            actionType: 'LAZY_LOAD_PERSONAL_MESSAGES',
            action: 'getPersonalMessage'
        };
    }

    async function syncPersonalMessages ({
        conversationId,
        lastEvaluatedKey,
        clientLastSyncedMessage,
        senderId,
        receiverId,
        limit = CONSTANTS.MESSAGE_LAZY_LOADING_LIMIT
    }) {
        let messages = [];
        let newLastEvaluatedKey;
        let isRecentFetchedMessageFound = false;
        const recentFetchedMessage = clientLastSyncedMessage?.recentFetchedMessage;
        const lastFetchedMessage = clientLastSyncedMessage?.lastFetchedMessage;

        const personalConversation = await PersonalConversationModel.get({ userAId: senderId, userBId: receiverId });

        if (!personalConversation) {
            throw {
                statusCode: 404,
                message: 'Personal conversation not found',
                actionType: 'LAZY_LOAD_PERSONAL_MESSAGES',
                action: 'getPersonalMessage'
            };
        }

        const lastMessageIdBeforeDeleted = personalConversation.lastMessageIdBeforeDeleted;

        let query = PersonalMessage.query('conversationId').eq(conversationId)
            .using('conversationId-createdAt-index')
            .sort('descending')
            .limit(limit);

        if (lastEvaluatedKey) {
            query = query.startAt(lastEvaluatedKey);
        }

        const result = await query.exec();
        messages = result;

        const { filteredMessages } = filterMessagesFromDeletedMessage({
            lastMessageIdBeforeDeleted,
            messages
        });

        const { calculatedLastEvaluatedKey } = getCalculatedLastEvaluatedKey({
            messages: filteredMessages,
            isPersonalConversation: true,
            conversationId
        });

        messages = filteredMessages;

        const messageIds = messages.map(message => message.messageId);

        const reactions = await MessageReactionsHandler.getReactionsForMessage({ messageIds });

        if (!recentFetchedMessage) {
            newLastEvaluatedKey = calculatedLastEvaluatedKey;
        }
        else {
            isRecentFetchedMessageFound = messages.find(message => message.messageId === recentFetchedMessage.messageId);
            isRecentFetchedMessageFound = !!isRecentFetchedMessageFound;
            const derivedLastEvaluatedKey = lastFetchedMessage ?
                { messageId: lastFetchedMessage.messageId, createdAt: lastFetchedMessage.createdAt, conversationId }
                : undefined;
            newLastEvaluatedKey = isRecentFetchedMessageFound ? derivedLastEvaluatedKey : calculatedLastEvaluatedKey;
        }

        return {
            messages,
            reactions,
            conversationId,
            lastEvaluatedKey: newLastEvaluatedKey,
            isRecentFetchedMessageFound: recentFetchedMessage ? isRecentFetchedMessageFound : undefined
        };
    }
}

async function getUserConnectedChildren (event) {
    const { senderId, receiverId } = event.body;
    try {
        const users = (
            await Promise.all([
                User.query('id').eq(senderId).attributes(['id', 'children']).exec(),
                User.query('id').eq(receiverId).attributes(['id', 'children']).exec()
            ])
        ).flat();
        const senderUserDetails = users.find((user) => user.id === senderId);
        const receiverUserDetails = users.find((user) => user.id === receiverId);
        const receiverChildsDetails = receiverUserDetails?.children ?
            await Child.batchGet(receiverUserDetails.children,
                { attributes: ['id', 'firstName', 'lastName', 'connections', 'associatedColor', 'photoURL', 'school', 'homeRoom'] })
            : [];
        const connectedChildIds = findConnectedChildIds(senderUserDetails.children, receiverChildsDetails);
        const connectedChildsDetails = await getConnectedChildDetails(connectedChildIds, receiverChildsDetails);

        return {
            statusCode: 200,
            message: 'Get user children list successful!',
            data: connectedChildsDetails,
            actionType: 'GET_USER_CONNECTED_CHILDREN',
            action: 'getPersonalMessage'
        };
    } catch (error) {
        CONSOLE_LOGGER.error('--> Error while get user children list --> ', error);
        return {
            statusCode: 500,
            message: 'Failed to get user children list!',
            actionType: 'GET_USER_CONNECTED_CHILDREN',
            action: 'getPersonalMessage'
        };
    }

    function findConnectedChildIds (senderChildIds, receiversChildsDetails) {
        const connectedChilds = receiversChildsDetails
            .filter(receiverChildDetails => receiverChildDetails?.connections
                ?.some(receiverChild => senderChildIds.includes(receiverChild.childId)))
            .map(item => item.id);
        return [...new Set(connectedChilds)];
    }

    async function getConnectedChildDetails (connectedChildIds, receiverChildsDetails) {
        const orgIds = new Set();
        const connectedChildsDetailsPromise = connectedChildIds.map(async (childId) => {
            const connectedChild = receiverChildsDetails.find((child) => child.id === childId);
            if (connectedChild?.photoURL) {
                connectedChild.photoURL = await UploadService.getSignedUrl(connectedChild.photoURL);
            }
            connectedChild?.school && orgIds.add(connectedChild.school);
            connectedChild?.homeRoom && orgIds.add(connectedChild.homeRoom);
            return connectedChild;
        }).filter(Boolean);

        const connectedChildsDetails = await Promise.all(connectedChildsDetailsPromise);
        const organizations = orgIds.size > 0 ? await Organization.batchGet([...orgIds], { attributes: ['id', 'name'] }) : [];
        return populateSchoolHomeroomName(connectedChildsDetails, organizations);
    }

    function populateSchoolHomeroomName (connectedChildsDetails, organizations) {
        return connectedChildsDetails.map((child) => {
            child.connections = undefined;
            child.school = organizations.find((org) => org.id === child.school)?.name;
            child.homeRoom = organizations.find((org) => org.id === child.homeRoom)?.name;
            return child;
        });
    }
}
