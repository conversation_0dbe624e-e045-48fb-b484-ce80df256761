const dynamoose = require('dynamoose');
const { v4: uuidv4 } = require('uuid');

const NotificationSchema = new dynamoose.Schema({
    id: {
        hashKey: true,
        type: String,
        default: () => uuidv4()
    },
    userId: {
        type: String,
        index: {
            name: 'userId-index',
            global: true,
            rangeKey: 'createdAt'
        }
    },
    title: {
        type: String
    },
    description: {
        type: String
    },
    readStatus: {
        type: Boolean,
        default: false
    },
    associatedChildId: {
        type: String
    },
    notificationAction: {
        type: String
    },
    payload: {
        type: Object
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    },
    saveUnknown: true
});

module.exports = dynamoose.model('Notification', NotificationSchema);
