const chai = require('chai');
const sinon = require('sinon');
const Stripe = require('../Stripe');
const expect = chai.expect;
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

describe('Stripe', () => {
    after(() => {
        sinon.restore();
    });

    it('should create a stripe account for the connect user', async () => {
        const createAccountStub = sinon.stub(stripe.accounts, 'create');
        createAccountStub.resolves({ id: 'test_account_id' });

        const result = await Stripe.createAccount(stripe);
        expect(result).to.deep.equal({ id: 'test_account_id' });

        createAccountStub.restore();
    });

    it('should create an onboarding link for the connect user', async () => {
        const account = { id: 'test_account_id' };
        const createOnboardingLinkStub = sinon.stub(stripe.accountLinks, 'create');
        createOnboardingLinkStub.resolves({ url: 'onboarding_url' });

        const result = await Stripe.createOnboardingLink(stripe, account);
        expect(result).to.deep.equal({ url: 'onboarding_url' });

        createOnboardingLinkStub.restore();
    });

    it('should create a customer in Stripe', async () => {
        const customerInfo = { id: 'test_customer_id' };
        const email = '<EMAIL>';
        const name = 'Test User';

        const createCustomerStub = sinon.stub(stripe.customers, 'create');
        createCustomerStub.resolves(customerInfo);

        const result = await Stripe.createCustomer(email, name, stripe);
        expect(result).to.deep.equal(customerInfo);

        createCustomerStub.restore();
    });


    it('should create a payment intent in Stripe with platformFeeCoveredBy', async () => {
        const paymentIntentInfo = { id: 'test_payment_intent_id' };
        const eventCost = 100;
        const ptoId = 'test_pto_id';
        const customerId = 'test_customer_id';
        const organization = { platformFee: 10 };
        const isCoveredByUser = true;

        const createPaymentIntentStub = sinon.stub(stripe.paymentIntents, 'create');
        createPaymentIntentStub.resolves(paymentIntentInfo);

        const result = await Stripe.createPaymentIntent(stripe, eventCost, ptoId, customerId, organization, isCoveredByUser);
        expect(result.paymentIntent).to.deep.equal(paymentIntentInfo);

        createPaymentIntentStub.restore();
    });

    it('should create a payment intent in Stripe with platformFeeCoveredBy as parent', async () => {
        const paymentIntentInfo = { id: 'test_payment_intent_id' };
        const eventCost = 100;
        const ptoId = 'test_pto_id';
        const customerId = 'test_customer_id';
        const organization = { platformFee: 10, platformFeeCoveredBy: 'parent' };
        const isCoveredByUser = true;

        const createPaymentIntentStub = sinon.stub(stripe.paymentIntents, 'create');
        createPaymentIntentStub.resolves(paymentIntentInfo);

        const result = await Stripe.createPaymentIntent(stripe, eventCost, ptoId, customerId, organization, isCoveredByUser);
        expect(result.paymentIntent).to.deep.equal(paymentIntentInfo);

        createPaymentIntentStub.restore();
    });

    it('should create a payment intent in Stripe with platformFeeCoveredBy as optional and isCoveredByUser as true', async () => {
        const paymentIntentInfo = { id: 'test_payment_intent_id' };
        const eventCost = 100;
        const ptoId = 'test_pto_id';
        const customerId = 'test_customer_id';
        const organization = { platformFee: 10, platformFeeCoveredBy: 'optional' };
        const isCoveredByUser = true;

        const createPaymentIntentStub = sinon.stub(stripe.paymentIntents, 'create');
        createPaymentIntentStub.resolves(paymentIntentInfo);

        const result = await Stripe.createPaymentIntent(stripe, eventCost, ptoId, customerId, organization, isCoveredByUser);
        expect(result.paymentIntent).to.deep.equal(paymentIntentInfo);

        createPaymentIntentStub.restore();
    });

    it('should create a payment intent in Stripe with platformFeeCoveredBy as optional and isCoveredByUser as false', async () => {
        const paymentIntentInfo = { id: 'test_payment_intent_id' };
        const eventCost = 100;
        const ptoId = 'test_pto_id';
        const customerId = 'test_customer_id';
        const organization = { platformFee: 10, platformFeeCoveredBy: 'optional' };
        const isCoveredByUser = false;

        const createPaymentIntentStub = sinon.stub(stripe.paymentIntents, 'create');
        createPaymentIntentStub.resolves(paymentIntentInfo);

        const result = await Stripe.createPaymentIntent(stripe, eventCost, ptoId, customerId, organization, isCoveredByUser);
        expect(result.paymentIntent).to.deep.equal(paymentIntentInfo);

        createPaymentIntentStub.restore();
    });

    it('should create a payment intent in Stripe with platformFeeCoveredBy as organization', async () => {
        const paymentIntentInfo = { id: 'test_payment_intent_id' };
        const eventCost = 100;
        const ptoId = 'test_pto_id';
        const customerId = 'test_customer_id';
        const organization = { platformFee: 10, platformFeeCoveredBy: 'organization' };
        const isCoveredByUser = true;

        const createPaymentIntentStub = sinon.stub(stripe.paymentIntents, 'create');
        createPaymentIntentStub.resolves(paymentIntentInfo);

        const result = await Stripe.createPaymentIntent(stripe, eventCost, ptoId, customerId, organization, isCoveredByUser);
        expect(result.paymentIntent).to.deep.equal(paymentIntentInfo);

        createPaymentIntentStub.restore();
    });


    it('should handle webhook events', async () => {
        const constructWebhookEventStub = sinon.stub(stripe.webhooks, 'constructEvent');
        const body = {};
        const signature = 'test_signature';

        constructWebhookEventStub.resolves({ type: 'mock_event_type' });

        const result = await Stripe.constructWebhookEvent(body, signature, stripe);

        sinon.assert.calledOnce(constructWebhookEventStub);
        sinon.assert.calledWith(
            constructWebhookEventStub,
            body,
            signature,
            process.env.STRIPE_WEBHOOK_SECRET
        );

        expect(result).to.deep.equal({ type: 'mock_event_type' });

        constructWebhookEventStub.restore();
    });
});
