const Child = require('../../../models/child.model');
const Organization = require('../../../models/organization.model');
const PersonalConversation = require('../../../models/personalConversation.model');
const User = require('../../../models/user.model');
const CONSTANTS = require('../../../util/constants');
const UploadService = require('../../../util/uploadService');

/**
 * @description Get the user connections list.
 * <AUTHOR>
 * @since 17/12/2024
 * @param {Object} event - The event object containing the user ID.
 * @returns {Promise<Object>} - A promise that resolves to an object containing the user connections list.
 */
module.exports.getUserConnectionsList = async (event) => {
    try {
        const { userId } = event.body;

        const user = await getUser(userId);

        const childrenIds = user.children;

        const connectionsChildIds = await getConnectionsChildIds(childrenIds);

        const { childConnectionsMap, guardianChildrenMap, uniqueOrganizationIds } =
            await getConnectionsDetails(connectionsChildIds, userId);

        const connectionsGuardianIdsSet = guardianChildrenMap.keys();

        const connectionsGuardians = await getConnectionsGuardians(connectionsGuardianIdsSet);

        const organizationMap = await getOrganizationMap(uniqueOrganizationIds);

        const connections = await getConnections({
            userId,
            connectionsGuardians,
            guardianChildrenMap,
            childConnectionsMap,
            organizationMap
        });

        return {
            statusCode: 200,
            message: 'User connections list fetched successfully',
            action: 'getUserConnectionsList',
            actionType: 'GET_USER_CONNECTIONS_LIST',
            data: JSON.stringify({ connections })
        };
    } catch (error) {
        if (error.statusCode) {
            return error;
        }

        return {
            statusCode: 500,
            message: 'Failed to get user connections list',
            action: 'getUserConnectionsList',
            actionType: 'GET_USER_CONNECTIONS_LIST'
        };
    }
};


/**
 * @description Batch get items in chunks.
 * <AUTHOR>
 * @since 17/12/2024
 * @param {Array} ids - The array of ids to batch get.
 * @returns {Array} - The array of chunks.
 */
const batchGetItemsInChunks = (ids) => {
    const chunks = [];
    for (let i = 0; i < ids.length; i += 100) {
        chunks.push(ids.slice(i, i + 100));
    }
    return chunks;
};


/**
 * @description Get the user.
 * <AUTHOR>
 * @since 17/12/2024
 * @param {String} userId - The user id.
 * @returns {Promise<Object>} - A promise that resolves to an object containing the user.
 */
const getUser = async (userId) => {
    const user = await User.query('id').eq(userId).exec();
    if (!user.length) {
        throw {
            statusCode: 404,
            message: 'User not found',
            action: 'getUserConnectionsList',
            actionType: 'GET_USER_CONNECTIONS_LIST'
        };
    }

    return user[0];
};


/**
 * @description Get the connections child ids.
 * <AUTHOR>
 * @since 17/12/2024
 * @param {Array} childrenIds - The array of children ids.
 * @returns {Promise<Array>} - A promise that resolves to an array of connections child ids.
 */
const getConnectionsChildIds = async (childrenIds) => {
    if (!childrenIds?.length) {
        throw {
            statusCode: 200,
            message: 'User has no children',
            action: 'getUserConnectionsList',
            actionType: 'GET_USER_CONNECTIONS_LIST',
            data: JSON.stringify({
                connections: []
            })
        };
    }

    const childrenIdsChunks = batchGetItemsInChunks(childrenIds);

    const children = [];
    for (const childrenIdsChunk of childrenIdsChunks) {
        children.push(...await Child.batchGet(childrenIdsChunk));
    }

    const connectionsChildIds = children.map(child => {
        const connections = child.connections?.filter(connection => connection.status === CONSTANTS.CONNECTION_STATUS.CONNECTED);
        return connections?.map(connection => connection.childId);
    }).flat();

    return [...new Set(connectionsChildIds)].filter(Boolean);
};

/**
 * @description Get the connections details.
 * <AUTHOR>
 * @since 17/12/2024
 * @param {Array} connectionsChildIds - The array of connections child ids.
 * @returns {Promise<Object>} - A promise that resolves to an object containing the connections details.
 */
const getConnectionsDetails = async (connectionsChildIds, userId) => {
    if (!connectionsChildIds?.length) {
        throw {
            statusCode: 200,
            message: 'User has no connections',
            action: 'getUserConnectionsList',
            actionType: 'GET_USER_CONNECTIONS_LIST',
            data: JSON.stringify({
                connections: []
            })
        };
    }

    const childConnectionsChunks = batchGetItemsInChunks(connectionsChildIds);

    const childConnections = [];
    for (const childConnectionsChunk of childConnectionsChunks) {
        childConnections.push(...await Child.batchGet(childConnectionsChunk));
    }

    const childConnectionsMap = new Map();
    const guardianChildrenMap = new Map();
    const uniqueOrganizationIds = new Set();

    childConnections.forEach(childConnection => {
        childConnectionsMap.set(childConnection.id, childConnection);
        uniqueOrganizationIds.add(childConnection.school);
        childConnection.homeRoom && uniqueOrganizationIds.add(childConnection.homeRoom);

        const guardians = childConnection.guardians ?? [];
        guardians
            .filter(guardian => guardian !== userId)
            .forEach(guardian => {
                guardianChildrenMap.set(guardian, [...(guardianChildrenMap.get(guardian) ?? []), childConnection.id]);
            });
    });

    return {
        childConnectionsMap,
        guardianChildrenMap,
        uniqueOrganizationIds: Array.from(uniqueOrganizationIds)
    };
};

/**
 * @description Get the connections guardians.
 * <AUTHOR>
 * @since 17/12/2024
 * @param {Set} connectionsGuardianIdsSet - The set of connections guardian ids.
 * @returns {Promise<Array>} - A promise that resolves to an array of connections guardians.
 */
const getConnectionsGuardians = async (connectionsGuardianIdsSet) => {
    const connectionsGuardians = [];

    for (const guardianId of connectionsGuardianIdsSet) {
        const guardian = await User.query('id').eq(guardianId).exec();
        if (guardian.length) {
            connectionsGuardians.push(guardian[0]);
        }
    }

    return connectionsGuardians;
};

/**
 * @description Get the organization map.
 * <AUTHOR>
 * @since 17/12/2024
 * @param {Set} uniqueOrganizationIds - The set of unique organization ids.
 * @returns {Promise<Map>} - A promise that resolves to a map containing the organizations.
 */
const getOrganizationMap = async (uniqueOrganizationIds) => {
    const uniqueOrganizationIdsChunks = batchGetItemsInChunks(uniqueOrganizationIds);
    const organizations = [];

    for (const uniqueOrganizationIdsChunk of uniqueOrganizationIdsChunks) {
        organizations.push(...await Organization.batchGet(uniqueOrganizationIdsChunk));
    }

    const organizationMap = new Map();
    organizations.forEach(organization => {
        organizationMap.set(organization.id, organization);
    });

    return organizationMap;
};

/**
 * @description Get the connection id.
 * <AUTHOR>
 * @since 17/12/2024
 * @param {String} userId - The user id.
 * @param {String} guardianId - The guardian id.
 * @returns {String} - The connection id.
 */
const getConnectionId = async (userId, guardianId) => {
    const connection = await PersonalConversation.get({ userAId: userId, userBId: guardianId });
    return connection?.conversationId;
};

/**
 * @description Get the connections.
 * <AUTHOR>
 * @since 17/12/2024
 * @param {Object} params - The parameters object.
 * @param {String} params.userId - The user id.
 * @param {Array} params.connectionsGuardians - The array of connections guardians.
 * @param {Map} params.guardianChildrenMap - The map of guardian children.
 * @param {Map} params.childConnectionsMap - The map of child connections.
 * @param {Map} params.organizationMap - The map of organizations.
 * @returns {Promise<Array>} - A promise that resolves to an array of connections.
 */
const getConnections = async ({ userId, connectionsGuardians, guardianChildrenMap, childConnectionsMap, organizationMap }) => {
    const connections = [];

    await Promise.all(connectionsGuardians.map(async guardian => {
        connections.push({
            conversationId: await getConnectionId(userId, guardian.id),
            id: guardian.id,
            firstName: guardian.firstName,
            lastName: guardian.lastName,
            children: await Promise.all(
                guardianChildrenMap
                    .get(guardian.id)
                    .map(async childId => {
                        const child = childConnectionsMap.get(childId);
                        return {
                            id: child.id,
                            firstName: child.firstName,
                            lastName: child.lastName,
                            school: organizationMap.get(child.school)?.name,
                            homeRoom: child.homeRoom ? organizationMap.get(child.homeRoom)?.name : null,
                            photoURL: child.photoURL ? await UploadService.getSignedUrl(child.photoURL) : null,
                            associatedColor: child.associatedColor
                        };
                    })
            )
        });
    }));

    return connections;
};
