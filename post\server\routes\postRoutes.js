/**
 * This file contains routes used for parent user.
 * Created by Growexx on 19/10/2023.
 * @name parentRoutes
 */
const router = require('express').Router();

const PostController = require('../services/post/postController');

const AuthMiddleware = require('../middleware/auth');
const AclMiddleware = require('../middleware/acl');
const UploadMiddleWare = require('../middleware/upload');
const HmacMiddleware = require('../middleware/hmac');

router.post('/', HmacMiddleware, AuthMiddleware, AclMiddleware, UploadMiddleWare.any(), PostController.addPost);
router.delete('/', HmacMiddleware, AuthMiddleware, AclMiddleware, PostController.deletePost);
router.patch('/publish', HmacMiddleware, AuthMiddleware, AclMiddleware, PostController.publishPost);

router.get('/list', HmacMiddleware, AuthMiddleware, AclMiddleware, PostController.getPostList);
router.get('/', HmacMiddleware, AuthMiddleware, AclMiddleware, PostController.getPostDetails);
router.put('/', HmacMiddleware, AuthMiddleware, AclMiddleware, UploadMiddleWare.any(), PostController.updatePost);


module.exports = router;
