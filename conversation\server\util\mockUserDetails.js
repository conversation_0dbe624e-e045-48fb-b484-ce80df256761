module.exports = {
    'email': '<EMAIL>',
    'id': 'b4d8c4f8-b001-70ac-d939-2f48d186a5da',
    'children': [
        {
            'id': '90c639c6-2e61-4586-b142-929ac5661600',
            'firstName': 'Ayan',
            'lastName': 'T.',
            'associatedOrganizations': [
                {
                    'id': '9b8ca05f-8fa2-4fcf-a381-24d42841a7ec',
                    'name': 'James Madison Primary School',
                    'category': 'school'
                },
                {
                    'id': 'd9f47b96-9e58-4b61-abfc-39b45fa3d708',
                    'name': 'Vaalee',
                    'category': 'super organization'
                },
                {
                    'id': 'b84a49d2-3e6e-4f3b-8577-623ca7b8cd33',
                    'name': 'James Madison Primary School PTO',
                    'category': 'pto'
                }
            ]
        },
        {
            'id': 'fb5c0f67-34d1-4cca-8803-51c8fdd678e7',
            'firstName': 'Shrey',
            'lastName': 'T.',
            'associatedOrganizations': [
                {
                    'id': '0752966c-908c-4b98-8331-13025fb499e4',
                    'name': 'James Madison Intermediate School',
                    'category': 'school'
                },
                {
                    'id': 'd9f47b96-9e58-4b61-abfc-39b45fa3d708',
                    'name': 'Vaalee',
                    'category': 'super organization'
                },
                {
                    'id': '6258b7a6-dc03-4dd8-af46-78510c7e231b',
                    'name': 'James Madison Intermediate School PTO',
                    'category': 'pto'
                }
            ]
        }
    ],
    'firstName': 'Dixit',
    'lastName': 'Tilaji'
};
