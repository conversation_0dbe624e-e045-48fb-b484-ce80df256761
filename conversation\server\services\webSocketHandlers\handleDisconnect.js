// handleConnect.js
const SocketConnections = require('../../models/socketConnections.model');

module.exports = async (event) => {
    const connectionId = event.requestContext.connectionId;
    try {
        await SocketConnections.delete({ id: connectionId });
        return {
            statusCode: 200,
            body: 'Disconnected'
        };
    } catch (error) {
        CONSOLE_LOGGER.error('Error deleting connection --> ConnectionId: %s & Error: %s', connectionId, error);
        return {
            statusCode: 500,
            body: 'Error deleting connection'
        };
    }
};
