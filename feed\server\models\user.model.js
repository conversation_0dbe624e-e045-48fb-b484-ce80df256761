const dynamoose = require('dynamoose');
const Child = require('./child.model');

const userSchema = new dynamoose.Schema({
    id: {
        hashKey: true,
        required: true,
        type: String
    },
    email: {
        type: String,
        required: true,
        rangeKey: true,
        index: {
            name: 'email-index',
            global: true,
            project: true
        }
    },
    firstName: {
        type: String
    },
    lastName: {
        type: String
    },
    phoneNumber: {
        type: String
    },
    countryCode: {
        type: String
    },
    photoURL: {
        type: String
    },
    isVerified: {
        type: Number,
        enum: [0, 1],
        default: 0
    },
    status: {
        type: String,
        enum: ['active', 'inactive'],
        default: 'inactive'
    },
    stripeCustomerId: {
        type: String
    },
    // 1 - deleted
    isDeleted: {
        type: Number,
        enum: [0, 1],
        default: 0
    },
    otp: {
        type: Number
    },
    otpExpiryDate: {
        type: Date
    },
    children: {
        type: Array,
        schema: [Child],
        default: []
    },
    associatedOrganizations: {
        type: Array,
        schema: [{
            type: Object,
            schema: {
                organizationId: {
                    type: String,
                    required: true
                },
                role: {
                    type: String,
                    required: true
                }
            }
        }],
        default: []
    },
    partnerInvites: {
        type: Array,
        schema: [{
            type: Object,
            schema: {
                children: {
                    type: Array,
                    schema: [{
                        type: Object,
                        schema: {
                            childId: { type: Child, required: true },
                            invitedAt: { type: Date, default: Date.now }
                        }
                    }],
                    default: []
                },
                inviterPartnerId: {
                    type: String,
                    required: true
                },
                inviterPartnerEmail: {
                    type: String,
                    required: true
                }
            }
        }],
        default: []
    },
    sendInvites: {
        type: Array,
        schema: [{
            type: Object,
            schema: {
                invitedPartnerEmail: {
                    type: String,
                    required: true
                },
                children: {
                    type: Array,
                    schema: [{
                        type: Object,
                        schema: {
                            childId: { type: Child, required: true },
                            status: {
                                type: String,
                                enum: ['pending', 'accepted'],
                                default: 'pending'
                            }
                        }
                    }],
                    default: []
                }
            }
        }],
        default: []
    },
    membershipsPurchased: {
        type: Array,
        schema: [{
            type: Object,
            schema: {
                organizationId: {
                    type: String,
                    required: true
                },
                fundraiserSignupId: {
                    type: String,
                    required: true
                },
                membershipType: {
                    type: String,
                    required: true,
                    enum: ['family']
                },
                startDate: {
                    type: Date,
                    required: true
                },
                membershipId: {
                    type: String,
                    required: true
                },
                endDate: {
                    type: Date,
                    required: true
                }
            }
        }],
        default: []
    },
    // 1 - user, 3 - org admin, 4 - admin
    role: {
        type: Number,
        enum: [1, 2, 3, 4],
        default: 1,
        index: {
            global: true,
            name: 'role-index',
            project: true
        }
    },
    accessLevel: {
        type: String,
        enum: ['app', 'org_app', 'root'],
        default: 'app',
        index: {
            global: true,
            name: 'accessLevel-index',
            project: true
        }
    },
    fcmToken: {
        type: String
    },
    token: {
        type: String
    },
    refreshToken: {
        type: String
    },
    provider: {
        type: String,
        default: 'email'
    },
    isFeedsGenerated: {
        type: Boolean
    },
    hasReadChatGuidelines: {
        type: Boolean,
        default: false
    },
    isGuestUser: {
        type: Boolean
    },
    createdBy: {
        type: String
    },
    updatedBy: {
        type: String
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('User', userSchema);
