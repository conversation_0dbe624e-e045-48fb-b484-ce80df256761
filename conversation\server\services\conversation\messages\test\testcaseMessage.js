module.exports = {
    sendPersonalMessageWithMedia: [
        {
            it: 'As a user I should validate if message id is not passed',
            options: {},
            status: 0
        },
        {
            it: 'As a user I should validate if sender id is not passed',
            options: {
                messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if conversation id is not passed',
            options: {
                messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                senderId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if receiver id is not passed',
            options: {
                messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                senderId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                conversationId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if replyMessageId is not passed',
            options: {
                messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                senderId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                conversationId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                receiverId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                replyMessage: '{}'
            },
            status: 0
        },
        {
            it: 'As a user I should validate if replyMessageSenderId is not passed',
            options: {
                messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                senderId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                conversationId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                receiverId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                replyMessage: JSON.stringify({
                    messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
                })
            },
            status: 0
        }
    ],
    flagMessage: [
        {
            it: 'should throw error when reason is not passed',
            options: {
                messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
            },
            status: 400
        },
        {
            it: 'should throw error when reason passed is not valid',
            options: {
                messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                reasons: ['spam']
            },
            status: 400
        },
        {
            it: 'should throw error when reason is not passed as array',
            options: {
                messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                reasons: 'Spam'
            },
            status: 400
        },
        {
            it: 'should be able to flag message',
            options: {
                messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                reasons: ['Spam']
            },
            status: 200
        }
    ],
    sendMessageWithMedia: [
        {
            it: 'should throw error when message id is not passed',
            options: {},
            status: 400
        },
        {
            it: 'should throw error when message id is not uuid',
            options: {
                messageId: 'message-id'
            },
            status: 400
        },
        {
            it: 'should throw error when group id is not passed',
            options: {
                messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
            },
            status: 400
        },
        {
            it: 'should throw error when group id is not uuid',
            options: {
                messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                groupId: 'group-id'
            },
            status: 400
        },
        {
            it: 'should throw error when organization id is not passed',
            options: {
                messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
            },
            status: 400
        },
        {
            it: 'should throw error when sender id is not passed',
            options: {
                messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                organizationId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
            },
            status: 400
        },
        {
            it: 'should throw error when reply message does not have message id',
            options: {
                messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                organizationId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                senderId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                replyMessage: JSON.stringify({})
            },
            status: 400
        },
        {
            it: 'should throw error when reply message id is not uuid',
            options: {
                messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                organizationId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                senderId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                replyMessage: JSON.stringify({
                    messageId: 'message-id'
                })
            },
            status: 400
        },
        {
            it: 'should throw error when reply message sender id is not passed',
            options: {
                messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                groupId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                organizationId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                senderId: '38325e0b-fed4-42a9-8d45-36f9a445c609',
                replyMessage: JSON.stringify({
                    messageId: '38325e0b-fed4-42a9-8d45-36f9a445c609'
                })
            },
            status: 400
        }
    ]
};
