const dotenv = require('dotenv');
const env = process.env.NODE_ENV || 'testing';
dotenv.config({ path: process.env.PWD + '/' + env + '.env' });
global.logger = require('../server/util/logger');
const chai = require('chai');
const chaiHttp = require('chai-http');
const app = require('../index');
chai.use(chaiHttp);
const request = require('supertest');
request(app);

// Start testing
require('./init.test');
require('../server/services/notification/test/notification.test');
require('../server/sqs/test/sqs.test');
require('../server/pushNotification/test/pushNotification.test');
require('../server/updateFeeds/test/updateChildFeeds.test');
require('../server/conversation/test/conversationService.test');

describe('Stop server in end', () => {
    it('Server should stop manually to get code coverage', done => {
        app.close();
        done();
    });
});
