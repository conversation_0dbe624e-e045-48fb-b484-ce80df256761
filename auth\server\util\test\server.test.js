const sinon = require('sinon');
const AWS = require('aws-sdk');
const Utils = require('../utilFunctions');

describe('Warmup lambda', () => {
    let invokeStub;
    let lambda;

    beforeEach(() => {
        invokeStub = sinon.stub().returns({
            promise: () => Promise.resolve({})
        });
        lambda = {
            invoke: invokeStub
        };
    });

    afterEach(() => {
        sinon.restore();
    });
});
