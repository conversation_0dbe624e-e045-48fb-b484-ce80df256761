const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
const User = require('../../../models/user.model');
const sinon = require('sinon');
chai.use(chaiHttp);
const jwt = require('jsonwebtoken');
const Utils = require('../../../util/utilFunctions');
const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
/* User Token */
const user = {
    id: '95285616-3769-4b2e-b36b-898597b8146e',
    email: '<EMAIL>'
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'testingjwt', tokenOptionalInfo)
};
Utils.addCommonReqTokenForHMac(request);
describe('Logout User', () => {
    try {
        let updateStub; let getStub;
        before(async () => {
            updateStub = sinon.stub(User, 'update');
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });
        it('As a User, I should able to logout', async () => {
            getStub.resolves({ status: 'active', isVerified: 1 });
            const res = await request(process.env.BASE_URL)
                .post('/user/signout')
                .set({ Authorization: requestPayloadUser.token });
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 200);
        });
        it('As a User, I should get unauthorized if user is invalid', async () => {
            getStub.resolves();
            const res = await request(process.env.BASE_URL)
                .post('/user/signout')
                .set({ Authorization: requestPayloadUser.token });
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 401);
        });
        it('As a User, I should get error if I am not active', async () => {
            getStub.resolves({ status: 'inactive', isVerified: 1 });
            const res = await request(process.env.BASE_URL)
                .post('/user/signout')
                .set({ Authorization: requestPayloadUser.token });
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 423);
        });
        it('As a User, I should get error if I am not authorized by jwt', async () => {
            const res = await request(process.env.BASE_URL)
                .post('/user/signout');
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 401);
        });
        it('As a User, I should get error if request is invalid', async () => {
            getStub.resolves({ status: 'active', isVerified: 1 });
            updateStub.rejects();
            const res = await request(process.env.BASE_URL)
                .post('/user/signout')
                .set({ Authorization: requestPayloadUser.token });
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 400);
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
