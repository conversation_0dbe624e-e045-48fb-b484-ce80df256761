const dynamoose = require('dynamoose');

const personalConversationSchema = new dynamoose.Schema({
    userAId: {
        hashKey: true,
        type: String,
        required: true,
        index: {
            name: 'userAId-index',
            global: true,
            project: true
        }
    },
    userBId: {
        type: String,
        required: true,
        rangeKey: true
    },
    conversationId: {
        type: String,
        required: true,
        index: {
            name: 'conversationId-index',
            global: true,
            project: true
        }
    },
    lastReadMessage: {
        type: Object,
        schema: {
            messageId: {
                type: String
            },
            createdAt: {
                type: Date
            }
        },
        default: null
    },
    isMuted: {
        type: Boolean,
        default: false
    },
    isBlocked: {
        type: Boolean,
        default: false
    },
    lastMessageIdBeforeDeleted: {
        type: String,
        default: null
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('PersonalConversation', personalConversationSchema);
