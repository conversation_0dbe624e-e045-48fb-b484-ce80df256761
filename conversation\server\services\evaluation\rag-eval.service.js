const { Client } = require('langsmith');
const { evaluate } = require('langsmith/evaluation');
const { z } = require('zod');
const path = require('path');
const fs = require('fs');
const { ChatOpenAI } = require('@langchain/openai');
const { HumanMessage, SystemMessage } = require('@langchain/core/messages');

const {
    correctnessInstructions,
    relevancyInstructions,
    groundnessInstructions,
    retrievalInstructions
} = require('./rag-eval.instructions');
const { handleNewCompanionMessage } = require('../webSocketHandlers/handleCompanionMessage');

class LangsmithEvaluatorService {
    constructor () {
        this.GOLDENS_FILENAME = 'goldens.json';
        // The dataset name should be changed when goldens are updated.
        this.DATASET_NAME = process.env.DATASET_NAME || 'rag-eval-dataset';
        this.systemPrompt = '';

        this.chatModel = new ChatOpenAI({
            apiKey: process.env.OPENAI_API_KEY || '',
            model: CONSTANTS.AI_MODELS.GPT_41_NANO
        });

        this.client = new Client({
            apiKey: process.env.LANGSMITH_API_KEY || ''
        });
    }

    async target (query) {
        const response = await handleNewCompanionMessage(
            { senderId: 'b4d8c4f8-b001-70ac-d939-2f48d186a5da', message: query, context: [] }
            , '');
        const retrievedContext = JSON.parse(response.data.contexts);

        return {
            answer: response.data.message,
            retrievedContext: JSON.stringify(retrievedContext),
            rewriteQuery: response.data.childQuestions
        };
    }

    async runEval () {
        try {
            const jsonPath = path.join(__dirname, this.GOLDENS_FILENAME);
            const jsonString = fs.readFileSync(jsonPath, 'utf-8');
            const goldens = JSON.parse(jsonString);

            const ResponseSchema = z.object({
                score: z.boolean()
            });

            const extractScore = (res) => {
                const raw = res.content?.trim();
                const normalized = `{ "score": ${raw?.toLowerCase()} }`;
                return ResponseSchema.parse(JSON.parse(normalized)).score;
            };


            const inputs = goldens.map((g) => ({ question: g.input }));
            const outputs = goldens.map((g) => ({ answer: g.expected_output }));

            let dataset;

            dataset = await this.client.hasDataset({ datasetName: this.DATASET_NAME });

            if (!dataset) {
                dataset = await this.client.createDataset(this.DATASET_NAME);
                await this.client.createExamples({
                    inputs,
                    outputs,
                    datasetName: dataset?.name || this.DATASET_NAME
                });
            }

            const correctnessEvaluator = async ({ outputs, referenceOutputs }) => {
                const messages = [
                    new SystemMessage(correctnessInstructions),
                    new HumanMessage(`Ground Truth answer: ${referenceOutputs?.answer || ''}`),
                    new HumanMessage(`Actual Answer: ${outputs?.answer || ''}`)
                ];

                const res = await this.chatModel.invoke(messages);
                return { key: 'correctness', score: extractScore(res) };
            };

            const relevancyEvaluator = async ({ inputs, outputs }) => {
                const messages = [
                    new SystemMessage(relevancyInstructions),
                    new HumanMessage(`Question: ${inputs?.question || ''}`),
                    new HumanMessage(`Actual Answer: ${outputs?.answer || ''}`)
                ];

                const res = await this.chatModel.invoke(messages);
                return { key: 'relevancy', score: extractScore(res) };
            };

            const groundnessEvaluator = async ({ outputs }) => {
                const messages = [
                    new SystemMessage(groundnessInstructions),
                    new HumanMessage(`Facts: ${outputs?.retrievedContext || ''}`),
                    new HumanMessage(`Actual Answer: ${outputs?.answer || ''}`)
                ];

                const res = await this.chatModel.invoke(messages);
                return { key: 'groundness', score: extractScore(res) };
            };

            const retrievalEvaluator = async ({ inputs, outputs }) => {
                const messages = [
                    new SystemMessage(retrievalInstructions),
                    new HumanMessage(`Question: ${inputs?.question || ''}`),
                    new HumanMessage(`Facts: ${outputs?.retrievedContext || ''}`)
                ];

                const res = await this.chatModel.invoke(messages);
                return { key: 'retrieval', score: extractScore(res) };
            };

            await evaluate(
                (input) => {
                    return this.target(input.question);
                },
                {
                    data: this.DATASET_NAME,
                    evaluators: [
                        correctnessEvaluator,
                        relevancyEvaluator,
                        groundnessEvaluator,
                        retrievalEvaluator
                    ],
                    maxConcurrency: parseInt(process.env.EVAL_MAX_CONCURRENCY) || 1
                }
            );

        } catch (error) {
            CONSOLE_LOGGER.error('Error in runEval:', error);
        }
    }
}

module.exports = { LangsmithEvaluatorService };
