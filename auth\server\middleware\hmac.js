const Utils = require('../util/utilFunctions');
const crypto = require('crypto');
const HTTPStatus = require('../util/http-status');
const secretKey = process.env.HMAC_SECRET_KEY;
const url = require('url');

/**
 * @desc This function is being used to hmac authenticate each request
 * <AUTHOR>
 * @since 19/10/2023
 * @param {Object} req Request req.headers  req.headers.reqtoken
 * @param {Object} res Response
 * @param {function} next exceptionHandler Calls exceptionHandler
 */
module.exports = function (req, res, next) {
    try {
        const currentDate = MOMENT().utc().format('MMYYYYDD'); // 03202426
        const reqURL = url.parse(req.originalUrl).pathname; // /feed/path
        const message = currentDate + reqURL;
        const receivedHmac = req.headers.reqtoken;
        const calculatedHmac = crypto.createHmac('sha256', secretKey).update(message).digest('hex');
        if (calculatedHmac === receivedHmac) {
            next();
        } else {
            throw 'invalid hmac';
        }
    } catch (err) {
        const responseObject = Utils.errorResponse();
        responseObject.message = res.__('ACCESS_DENIED');
        res.status(HTTPStatus.UNAUTHORIZED).send(responseObject);
    }
};
