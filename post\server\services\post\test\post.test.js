const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const sinon = require('sinon');
const User = require('../../../models/user.model');
const Post = require('../../../models/post.model');
const TestCase = require('./testcasePost');
const jwt = require('jsonwebtoken');
const organizationModel = require('../../../models/organization.model');
const OrganizationMembers = require('../../../models/organizationMember.model');
const Utils = require('../../../util/utilFunctions');


const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
/* User Token */
const user = {
    id: '95285616-3769-4b2e-b36b-898597b8146e',
    email: '<EMAIL>'
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'testingjwt', tokenOptionalInfo)
};

const data = {
    title: 'Test',
    subTitle: 'Some SubTitle',
    publishedDate: '',
    organizationId: '123',
    content: 'Some Content',
    status: 'draft'
};

const postList = [{
    'title': 'Test',
    'subTitle': 'Some SubTitle',
    'publishedDate': '',
    'content': 'Some Content',
    'id': '54404ec7-15af-4ab8-afb3-8db63203e05e',
    'status': 'draft',
    'organizationId': '5f5f7e5f7e5f7e5f7e5f7e5f'

},
{
    'title': 'Test',
    'subTitle': 'Some SubTitle',
    'publishedDate': '',
    'content': 'Some Content',
    'id': '54404ec7-15af-4ab8-afb3-8db63203e05e',
    'status': 'published',
    'organizationId': '5f5f7e5f7e5f7e5f7e5f7e5f'

},
{
    'title': 'Test',
    'subTitle': 'Some SubTitle',
    'publishedDate': '',
    'content': 'Some Content',
    'id': '54404ec7-15af-4ab8-afb3-8db63203e05e',
    'status': 'published',
    'organizationId': '5f5f7e5f7e5f7e5f7e5f7e5k'

}];


Utils.addCommonReqTokenForHMac(request);
describe('Add Post', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });
        TestCase.addPost.forEach((data) => {
            it(data.it, (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                    accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                    associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });
                request(process.env.BASE_URL)
                    .post('/post')
                    .set({ Authorization: requestPayloadUser.token })
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should handle error if token is not passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .post('/post')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and correct token is passed', (done) => {
            getStub.resolves(null);
            request(process.env.BASE_URL)
                .post('/post')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 401);
                    done();
                });
        });

        it('As a user I should validate the that i am registered to app and is verified', (done) => {
            getStub.resolves({ status: 'active', isVerified: 0, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });
            request(process.env.BASE_URL)
                .post('/post')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 423);
                    done();
                });
        });

        it('As a user I should validate the role before creating post', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, isDeleted: 0, role: 1,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });
            request(process.env.BASE_URL)
                .post('/post')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 406);
                    done();
                });
        });

        it('As a user I should validate that I am associated with an organization', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            const orgGetStub = sinon.stub(organizationModel, 'get').resolves(null);
            sinon.stub(Post, 'create').resolves();
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });
            request(process.env.BASE_URL)
                .post('/post')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('subTitle', data.subTitle)
                .field('publishDate', data.publishedDate)
                .field('content', data.content)
                .field('status', data.status)
                .field('organizationId', '5f5f7e5f7e5f7e5f7e5f7e5g')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    orgGetStub.restore();
                    Post.create.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });


    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});


describe('Edit Post', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should validate that post id is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            request(process.env.BASE_URL)
                .put('/post')
                .set({ Authorization: requestPayloadUser.token })
                .field('postId', 'abcd')
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('Post with provided post id must exist', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Post, 'get').resolves(null);

            request(process.env.BASE_URL)
                .put('/post')
                .set({ Authorization: requestPayloadUser.token })
                .field('postId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('subTitle', data.subTitle)
                .field('publishDate', data.publishedDate)
                .field('content', data.content)
                .field('postStatus', data.status)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 404);
                    Post.get.restore();
                    done();
                });
        });



        it('As a user I should check if title is passed', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Post, 'get').resolves({ subTitle: '1.png', content: 'some content', save: () => {} });

            request(process.env.BASE_URL)
                .put('/post')
                .set({ Authorization: requestPayloadUser.token })
                .field('postId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('subTitle', data.subTitle)
                .field('publishDate', data.publishedDate)
                .field('content', data.content)
                .field('postStatus', data.status)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Post.get.restore();
                    done();
                });
        });

        it('As a user I should check if subtitle passed is correct', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Post, 'get').resolves({ photoURL: '1.png', isPaid: true,
                details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });

            request(process.env.BASE_URL)
                .put('/post')
                .set({ Authorization: requestPayloadUser.token })
                .field('postId', '7230a94d-260d-4fbf-a168-8a483b0990bf')
                .field('title', data.title)
                .field('publishDate', data.publishedDate)
                .field('content', data.content)
                .field('postStatus', data.status)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Post.get.restore();
                    done();
                });
        });

        it('As a user I should check if content is passed', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }]
            });
            sinon.stub(Post, 'get').resolves({ photoURL: '1.png', isPaid: true, volunteerRequired: true,
                details: { documentURLs: new Set(['1.jpg']) }, save: () => {} });

            request(process.env.BASE_URL)
                .put('/post')
                .set({ Authorization: requestPayloadUser.token })
                .field('title', data.title)
                .field('subTitle', data.subTitle)
                .field('publishDate', data.publishedDate)
                .field('postStatus', data.status)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Post.get.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Get Post List', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get post list that are created under my organization', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            const queryStub = sinon.stub(Post, 'query');
            const usingStub = sinon.stub();
            const attributesStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: eqStub.withArgs('organizationId').returns({ using: usingStub }) });
            usingStub.returns({ attributes: attributesStub });
            attributesStub.returns({ exec: execStub });
            execStub.resolves(postList);

            request(process.env.BASE_URL)
                .get('/post/list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    queryStub.restore();
                    done();
                });
        });

        it('As a user I should get post list that are created under my organization with the passed status', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', role: 'admin' }]
            });

            const queryStub = sinon.stub(Post, 'query');
            const usingStub = sinon.stub();
            const attributesStub = sinon.stub();
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: eqStub.withArgs('organizationId').returns({ using: usingStub }) });
            usingStub.returns({ attributes: attributesStub });
            attributesStub.returns({ where: whereStub.returns({ eq: eqStub.withArgs('draft').returns({ exec: execStub }) }) });
            execStub.resolves(postList);

            request(process.env.BASE_URL)
                .get('/post/list?status=draft')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    queryStub.restore();
                    done();
                });
        });

        it('As a user I should get empty post list if none exists', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            const queryStub = sinon.stub(Post, 'query');
            const usingStub = sinon.stub();
            const attributesStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: eqStub.withArgs('organizationId').returns({ using: usingStub }) });
            usingStub.returns({ attributes: attributesStub });
            attributesStub.returns({ exec: execStub });
            execStub.resolves([]);

            request(process.env.BASE_URL)
                .get('/post/list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    queryStub.restore();
                    done();
                });
        });

        it('As a user I should post list for an organization', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', role: 'admin' }]
            });

            const queryStub = sinon.stub(Post, 'query');
            const usingStub = sinon.stub();
            const attributesStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: eqStub.withArgs('organizationId').returns({ using: usingStub }) });
            usingStub.returns({ attributes: attributesStub });
            attributesStub.returns({ exec: execStub });
            execStub.resolves([]);

            request(process.env.BASE_URL)
                .get('/post/list?organizationId=5f5f7e5f7e5f7e5f7e5f7e5f')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    queryStub.restore();
                    done();
                });
        });

        it('As a user I should post list for an organization with the passed status', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f', role: 'admin' }]
            });

            const queryStub = sinon.stub(Post, 'query');
            const usingStub = sinon.stub();
            const attributesStub = sinon.stub();
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: eqStub.withArgs('organizationId').returns({ using: usingStub }) });
            usingStub.returns({ attributes: attributesStub });
            attributesStub.returns({ where: whereStub.returns({ eq: eqStub.withArgs('draft').returns({ exec: execStub }) }) });
            execStub.resolves(postList);

            request(process.env.BASE_URL)
                .get('/post/list?status=draft&organizationId=5f5f7e5f7e5f7e5f7e5f7e5f')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    queryStub.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a user I should get error is passed organizationId is not associated with me', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ organizationId: '5f5f7e5f7e5f7e5f7e5f7e5a', role: 'admin' }]
            });

            sinon.stub(OrganizationMembers, 'get').resolves(null);

            const queryStub = sinon.stub(Post, 'query');
            const usingStub = sinon.stub();
            const attributesStub = sinon.stub();
            const whereStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: eqStub.withArgs('organizationId').returns({ using: usingStub }) });
            usingStub.returns({ attributes: attributesStub });
            attributesStub.returns({ where: whereStub.returns({ eq: eqStub.withArgs('draft').returns({ exec: execStub }) }) });
            execStub.resolves(postList);

            request(process.env.BASE_URL)
                .get('/post/list?status=draft&organizationId=5f5f7e5f7e5f7e5f7e5f7e5f')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    queryStub.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Handle Error in getting list', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get error if something went wrong', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            const queryStub = sinon.stub();
            const inStub = sinon.stub();
            const attributesStub = sinon.stub();
            const execStub = sinon.stub();

            queryStub.returns({ eq: inStub });
            inStub.returns({ attributes: attributesStub });
            attributesStub.returns({ exec: execStub });
            execStub.rejects();
            sinon.replace(Post, 'query', queryStub);

            request(process.env.BASE_URL)
                .get('/post/list')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Get Post Details', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should validate the postId passed', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            request(process.env.BASE_URL)
                .get('/post?postId=abcd')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    done();
                });
        });

        it('As a user I should get error message if post doesnt exists', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(Post, 'get').resolves(null);

            request(process.env.BASE_URL)
                .get('/post?postId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Post.get.restore();
                    done();
                });
        });

        it('As a user I should get the post details', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(Post, 'get').resolves(postList[0]);

            request(process.env.BASE_URL)
                .get('/post?postId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Post.get.restore();
                    done();
                });
        });


        it('As a user I should get error message if post doesn\'t exists, is deleted or if it doesn\'t belong to organization id',
            (done) => {
                getStub.resolves({
                    status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3,
                    accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                    associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5' }] });
                OrganizationMembers.get.restore();
                sinon.stub(OrganizationMembers, 'get').resolves(null);

                sinon.stub(Post, 'get').resolves(postList[0]);

                request(process.env.BASE_URL)
                    .get('/post?postId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                    .set({ Authorization: requestPayloadUser.token })
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.statusCode, 400);
                        Post.get.restore();
                        done();
                    });
            });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Publish Post', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should be able to publish post', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            sinon.stub(Post, 'get').resolves({ ...postList[0], save: () => Promise.resolve(postList[0]) });

            request(process.env.BASE_URL)
                .patch('/post/publish?postId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Post.get.restore();
                    done();
                });
        });

        it('As a user I should get message if post is already published', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(Post, 'get').resolves({ ...postList[1], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .patch('/post/publish?postId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Post.get.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a user I should get error message if I am not associated with organization', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5a' }] });

            sinon.stub(Post, 'get').resolves({ ...postList[0], save: () => Promise.resolve() });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'deleted' }]
            });

            request(process.env.BASE_URL)
                .patch('/post/publish?postId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Post.get.restore();
                    done();
                });
        });

        it('As a user I should get error if post doesn\'t exists, is deleted or if it doesn\'t belong to organization id', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5' }] });

            sinon.stub(Post, 'get').resolves(null);

            request(process.env.BASE_URL)
                .patch('/post/publish?postId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Post.get.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Delete Post', () => {
    try {
        let getStub;
        before(async () => {
            getStub = sinon.stub(User, 'get');
        });

        after(async () => {
            sinon.restore();
        });

        it('As a user I should get be able to delete post', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            sinon.stub(Post, 'get').resolves({ ...postList[0], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .delete('/post?postId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Post.get.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As an editor I should delete post', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: 'editor', status: 'active' }]
            });

            sinon.stub(Post, 'get').resolves({ ...postList[0], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .delete('/post?postId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    Post.get.restore();
                    done();
                });
        });

        it('As a editor I should get error message if I try to delete published post', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(Post, 'get').resolves({ ...postList[1], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .delete('/post?postId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 403);
                    Post.get.restore();
                    OrganizationMembers.get.restore();
                    done();
                });
        });

        it('As a admin I should get error message if post is already published', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(OrganizationMembers, 'get').resolves({
                users: [{ id: 1, email: '<EMAIL>', associatedOrgRole: CONSTANTS.ORG_ROLE.SUPER_ADMIN, status: 'active' }]
            });

            sinon.stub(Post, 'get').resolves({ ...postList[1], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .delete('/post?postId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Post.get.restore();
                    done();
                });
        });

        it('As a user I should get error if post doesn\'t exists, is deleted or if it doesn\'t belong to organization id', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5f' }] });

            sinon.stub(Post, 'get').resolves({ ...postList[2], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .delete('/post?postId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Post.get.restore();
                    done();
                });
        });

        it('As a user I should get error I do not belong to that organization while deleting post', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5a' }] });

            sinon.stub(Post, 'get').resolves({ ...postList[2], save: () => Promise.resolve() });

            request(process.env.BASE_URL)
                .delete('/post?postId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Post.get.restore();
                    done();
                });
        });

        it('As a user I should get error if post doesnt exists', (done) => {
            getStub.resolves({ status: 'active', id: 1, isVerified: 1, isDeleted: 0, role: 3, accessLevel: CONSTANTS.ACCESS_LEVEL.ORG_APP,
                associatedOrganizations: [{ role: CONSTANTS.ORG_ROLE.SUPER_ADMIN, organizationId: '5f5f7e5f7e5f7e5f7e5f7e5a' }] });

            sinon.stub(Post, 'get').resolves(null);

            request(process.env.BASE_URL)
                .delete('/post?postId=54404ec7-15af-4ab8-afb3-8db63203e05e')
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    Post.get.restore();
                    done();
                });
        });

    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

