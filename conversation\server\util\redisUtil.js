const Redis = require('ioredis');
const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);

class RedisUtil {
    /**
     * <AUTHOR>
     * @description Get all the values of a field in a hash
     * @param {string} key - The key of the hash
     * @returns {Promise<string>} The value of the field
     */
    static async getAllHashValues (key) {
        if (process.env.NODE_ENV !== 'testing') {
            return await redis.hgetall(key);
        } else {
            return Promise.resolve();
        }
    }

    /**
     * <AUTHOR>
     * @description Get the value of a field in a hash
     * @param {string} key - The key of the hash
     * @param {string} field - The field of the hash
     * @returns {Promise<string>} The value of the field
     */
    static async getHashValue (key, field) {
        if (process.env.NODE_ENV !== 'testing') {
            return await redis.hget(key, field);
        } else {
            return Promise.resolve();
        }
    }
}

module.exports = RedisUtil;
