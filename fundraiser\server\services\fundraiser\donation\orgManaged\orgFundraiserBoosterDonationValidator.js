const validation = require('../../../../util/validation');

/**
 * Class represents validations for Organization managed fundraiser booster donation.
 */
class OrgFundraiserBoosterDonationValidator extends validation {
    constructor (body, query, locale) {
        super(locale);
        this.body = body;
        this.query = query;
    }

    /**
     * @desc This function is being used to validate request for add donations for fundraiser booster.
     * <AUTHOR>
     * @since 08/11/2023
     */
    validate () {
        super.field(this.body.fundraiserBoosterId, 'Fundraiser Booster Id');
        super.uuid(this.body.fundraiserBoosterId, 'Fundraiser Booster Id');
        super.field(this.body.childName, 'Child Name');
        super.field(this.body.donorName, 'Donor Name');
        super.field(this.body.donorMessage, 'Donor Message');
        super.field(this.body.amount, 'Donation Amount');
        super.number(this.body.amount, 'Donation Amount');
        this.validateBoolean(this.body.expectDonorMatch);
        super.field(this.body.paymentType, 'Payment Type');
    }

    /**
     * @desc This function is being used to validate request for get organization managed fundraiser booster details.
     * <AUTHOR>
     * @since 08/11/2023
     */
    validateGetBoosterDetails () {
        super.field(this.query.fundraiserId, 'Fundraiser Id');
        super.uuid(this.query.fundraiserId, 'Fundraiser Id');
    }

    validateBoolean (value) {
        if (typeof value === 'boolean') {
            return value;
        } else {
            throw {
                message: 'Invalid value for Expect Donor Match',
                statusCode: 400
            };
        }
    }
}


module.exports = OrgFundraiserBoosterDonationValidator;
