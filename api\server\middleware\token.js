const jwt = require('jsonwebtoken');
const Utils = require('../util/utilFunctions');
const HTTPStatus = require('../util/http-status');

/**
* @desc This function is being used to authenticate each private request
* <AUTHOR>
* @since 19/10/2023
* @param {Object} req Request req.headers RequestBody req.headers.accessToken accessToken
* @param {Object} res Response
* @param {function} next exceptionHandler Calls exceptionHandler
*/
const checkUser = (me, res, next) => {
    const responseObject = Utils.errorResponse();
    if (!me) {
        responseObject.message = res.__('ACCESS_DENIED');
        res.status(HTTPStatus.UNAUTHORIZED).send(responseObject);
        return;
    } else if (me.status !== CONSTANTS.STATUS.ACTIVE || !me.isVerified) {
        responseObject.data = {
            status: me.status,
            message: res.__('DEACTIVATE_ACCOUNT_BY_ADMIN')
        };
        res.status(HTTPStatus.ACCOUNT_SUSPENDED).send(responseObject);
        return;
    } else {
        // Do nothing...
    }
    res.locals.user = me;
    next();
};

module.exports = function (req, res, next) {
    const token = req.headers.authorization.split(' ')[1];

    jwt.verify(token, process.env.JWT_SECRET, (err, tokenDetail) => {
        if (err) {
            const responseObject = Utils.errorResponse();
            responseObject.message = res.__('ACCESS_DENIED');
            res.status(HTTPStatus.UNAUTHORIZED).send(responseObject);
        } else {
            checkUser(tokenDetail, res, next);
        }
    });
};
