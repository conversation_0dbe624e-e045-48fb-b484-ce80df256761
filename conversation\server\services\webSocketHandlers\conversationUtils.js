const moment = require('moment');

const filterMessagesFromDeletedMessage = ({ messages, lastMessageIdBeforeDeleted }) => {
    const lastMessageDeletedIndex = messages.findIndex(message => message.messageId === lastMessageIdBeforeDeleted);

    let filteredMessages = messages;

    if (lastMessageDeletedIndex !== -1) {
        filteredMessages = messages.slice(0, lastMessageDeletedIndex);
    }

    return { filteredMessages };
};

const getCalculatedLastEvaluatedKey = ({ messages, conversationId, isPersonalConversation, groupId }) => {
    const lastMessage = messages?.length > 0 ? messages[messages.length - 1] : null;
    const createdAt = lastMessage?.createdAt ? moment(lastMessage.createdAt).valueOf() : null;

    const lastKeyCalculatedOnLastMessage = {
        createdAt: createdAt
    };

    if (isPersonalConversation) {
        lastKeyCalculatedOnLastMessage.conversationId = conversationId;
        lastKeyCalculatedOnLastMessage.messageId = lastMessage?.messageId;
    } else {
        lastKeyCalculatedOnLastMessage.groupId = groupId;
        lastKeyCalculatedOnLastMessage.id = lastMessage?.id;
    }

    const calculatedLastEvaluatedKey = lastMessage ? lastKeyCalculatedOnLastMessage : null;

    return { calculatedLastEvaluatedKey };
};

module.exports = {
    filterMessagesFromDeletedMessage,
    getCalculatedLastEvaluatedKey
};
