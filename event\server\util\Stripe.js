/**
 * This class represents all the stripe services functions
 */
class Stripe {
    /**
     * @desc This function is being used to create a stripe account for the connect user
     * <AUTHOR>
     * @since 08/11/2023
     */
    static async createAccount (stripe) {
        return await stripe.accounts.create({
            'type': 'express',
            'country': 'US'
        });
    }

    /**
     * @desc This function is being used to create a onboarding link for the connect user
     * <AUTHOR>
     * @since 08/11/2023
     * @param {Object} account account
     */
    static async createOnboardingLink (stripe, account) {
        return await stripe.accountLinks.create({
            account: account.id,
            // failure
            refresh_url: process.env.FE_URL,
            // success url
            return_url: process.env.FE_URL,
            type: 'account_onboarding'
        });
    }

    /**
     * @desc This function is being used to create a new instance of customer in stripe
     * <AUTHOR>
     * @since 15/11/2023
     * @param {String} email
     */
    static async createCustomer (email, name, stripe) {
        return await stripe.customers.create({ email, name });
    }

    /**
     * @desc This function is being used to create a payment intent in stripe
     * <AUTHOR>
     * @since 15/11/2023
     * @param {String} email
     */
    static async createPaymentIntent (stripe, eventCost, ptoId, customerId, organization, isCoveredByUser, donationAmount) {
        const calculatedPlatformFee = Math.round((eventCost * (organization.platformFee / 100)) * 100) / 100;
        const platformFee = Math.max(
            calculatedPlatformFee,
            (organization.minPlatformFeeAmount ?? CONSTANTS.DEFAULT_MIN_PLATFORM_FEE_AMOUNT)
        );
        let amount = Math.round((eventCost + donationAmount) * 100);
        const applicationFeeAmount = Math.round(platformFee * 100);

        if (organization.platformFeeCoveredBy === 'parent' || (organization.platformFeeCoveredBy === 'optional' && isCoveredByUser)) {
            amount += applicationFeeAmount;
        }

        const paymentIntent = await stripe.paymentIntents.create({
            amount,
            currency: 'usd',
            automatic_payment_methods: {
                enabled: true
            },
            application_fee_amount: applicationFeeAmount,
            transfer_data: {
                destination: ptoId
            },
            customer: customerId
        });

        return { paymentIntent, stripeFee: applicationFeeAmount };
    }

    /**
     * @desc This function is being used to create stripe webhooks
     * <AUTHOR>
     * @since 16/11/2023
     * @param {Object} body
     * @param {String} signature
     */
    static async constructWebhookEvent (body, signature, stripe) {
        return await stripe.webhooks.constructEvent(body, signature, process.env.STRIPE_WEBHOOK_SECRET);
    }
}

module.exports = Stripe;
