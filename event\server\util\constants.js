/* eslint-disable max-len */
module.exports = {
    // For AES, this is always 16
    IV_LENGTH: 16,
    LOG_LEVEL: 'debug',
    EVENT_IMAGE: {
        ALLOWED_TYPE: ['image/jpg', 'image/jpeg', 'image/png']
    },
    USER_DOCUMENT_FILE: {
        ALLOWED_TYPE: ['image/jpg', 'image/jpeg', 'image/png', 'application/pdf']
    },
    REGEX: {
        EMAIL: /^[A-Za-z0-9](\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\.([a-z]{1,6}\.)?[a-z]{2,6}$/,
        NAME: /^[a-zA-Z0-9,'~._^ -]{3,100}$/,
        ALPHA_ONLY: /^[a-zA-Z']*$/,
        ALPHA_SPECIAL_CHAR: /^[ A-Za-z0-9_@./#&+-]*$/,
        ALPHA_SPECIAL_CHAR_EXCEPT_NUMBER: /^[ A-Za-z_@./#&+-]*$/,
        FULL_ACCESS: /^[^<> ?//\\]+$/,
        ALPHA_NUMARIC: /^[\w@ ]+$/,
        templateName: /^[ A-Za-z0-9_@./#&+-]*$/,
        SUBJECT: /^[ A-Za-z0-9_@./#&+-]*$/,
        URL: /(http(s)?:\/\/www\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\.[a-z]{2,4}\b([-a-zA-Z0-9@:%+.~#?&//=]*)/,
        MOBILE: /^([+]\d{1,2})?\d{10}$/,
        PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
        UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
        DONATION_AMOUNT: /^([0-9]\d{0,5})(\.\d{0,2})?$/
    },
    OTPLENGTH: 6,
    OTP_EXPIRY_DURATION: 30,
    VERIFIED: {
        PENDING: 0,
        ACTIVE: 1
    },
    EMAIL_TEMPLATE: {
        REGISTER: 'verificationOtpMail.html',
        FORGOTPASSWORD: 'forgotPassword.html'
    },
    OTP_TYPE: ['register', 'forgotPassword'],
    PAYMENT_TYPES: {
        FREE: 'free',
        STRIPE: 'stripe',
        CASH: 'cash',
        CHECK: 'cheque',
        VENMO: 'venmo',
        PAYMENT_INITIATED: 'payment-initiated'
    },
    PAYMENT_STATUS: {
        PENDING: 'pending',
        APPROVED: 'approved'
    },
    STATUS: {
        INACTIVE: 'inactive',
        ACTIVE: 'active',
        SUSPENDED: 'suspended',
        FREEZED: 'freezed',
        PUBLISHED: 'published'
    },
    EVENT_TYPE: ['event', 'post', 'calendar'],
    QUANTITY_TYPE: ['People', 'Items'],
    ENVIRONMENT: {
        TESTING: 'testing',
        LOCAL: 'local',
        DEV: 'dev',
        PRODUCTION: 'production'
    },
    DEVELOPERS_EMAIL: '<EMAIL>',
    SES_HOST: 'email-smtp.us-east-2.amazonaws.com',
    CLIENT_INFO: {
        EMAIL: '<EMAIL>',
        HELP_EMAIL: '<EMAIL>'
    },
    APP_NAME: 'Vaalee',
    ROLE: {
        USER: 1,
        ORG_ADMIN: 3,
        ADMIN: 4
    },
    ACCESS_LEVEL: {
        APP: 'app',
        ORG_APP: 'org_app',
        ROOT: 'root'
    },
    ORG_ROLE: {
        SUPER_ADMIN: 'super admin'
    },
    OPEN_SEARCH: {
        COLLECTION: {
            EVENT: 'events',
            CHILD: 'children'
        },
        SEARCHABLE_FIELDS: {
            'events': ['title', 'details.details'],
            'children': ['firstName', 'lastName']
        },
        INDICES: {
            'events': 'eventSchema',
            'children': 'childSchema'
        }
    },
    MEMBERSHIP_TYPES: {
        CHILD: 'child',
        FAMILY: 'family'
    },
    ALLOWED_EVENT_STATUS: ['published', 'draft', 'cancelled'],
    ALLOWED_SIGNUP_STATUS: ['pending', 'approved', 'cancelled'],
    AWS_S3_PUBLIC_BUCKET: 'vaalee-assets',
    COMPRESSION_QUALITY: 60,
    DEFAULT_MIN_PLATFORM_FEE_AMOUNT: 1,
    MIN_FILE_SIZE_FOR_CHUNK_UPLOAD: 5 * 1024 * 1024
};
