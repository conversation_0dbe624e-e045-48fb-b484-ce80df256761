const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const sinon = require('sinon');
const EmailService = require('../../../util/sendEmail');
const Cognito = require('../../../util/cognito');
const User = require('../../../models/user.model');
const TestCase = require('./testcaseSignup');
const Crypt = require('../../../util/crypt');
const PendingParentInvite = require('../../../models/pendingPartnerInvite.model');
const Utils = require('../../../util/utilFunctions');

Utils.addCommonReqTokenForHMac(request);
describe('Signup', () => {
    try {
        let emailStub;
        before(async () => {
            emailStub = sinon.stub(EmailService, 'prepareAndSendEmail');
        });

        after(async () => {
            emailStub.restore();
        });

        TestCase.registerAccount.forEach((data) => {
            it(data.it, (done) => {
                request(process.env.BASE_URL)
                    .post('/auth/signup')
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.body.status, data.status);
                        done();
                    });
            });
        });

        it('As a user I should validate if email is already registered', (done) => {
            const registerUser = {
                email: '<EMAIL>',
                role: 1,
                phoneNumber: '**********',
                countryCode: '+91',
                firstName: 'Test',
                lastName: 'User',
                password: 'Test@123'
            };

            const scanStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const saveStub = sinon.stub().resolves();

            scanStub.returns({ eq: eqStub });
            eqStub.returns({ exec: execStub });
            execStub.resolves({ count: 1 });
            sinon.replace(User, 'query', scanStub);
            sinon.replace(User.prototype, 'save', saveStub);
            sinon.stub(Cognito, 'signup').resolves();
            sinon.stub(Cognito, 'updateConfirmStatus').resolves();
            sinon.stub(Cognito, 'login').returns({ AuthenticationResult: { IdToken: 'id-token', RefreshToken: 'refresh-token' } });
            sinon.stub(Crypt, 'getUserToken').returns({ token: '123' });
            request(process.env.BASE_URL)
                .post('/auth/signup')
                .send(registerUser)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 422);
                    sinon.restore();
                    done();
                });
        });

        it('As a user I should register as user', (done) => {
            const registerUser = {
                email: '<EMAIL>',
                role: 1,
                phoneNumber: '**********',
                countryCode: '+91',
                firstName: 'Test',
                lastName: 'User',
                password: 'Test@123'
            };
            const scanStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const saveStub = sinon.stub().resolves();

            scanStub.returns({ eq: eqStub });
            eqStub.returns({ exec: execStub });
            execStub.resolves({ count: 0 });
            sinon.replace(User, 'query', scanStub);
            sinon.replace(User.prototype, 'save', saveStub);
            sinon.stub(Cognito, 'signup').resolves({ userSub: 'id' });
            sinon.stub(Cognito, 'updateConfirmStatus').resolves();
            sinon.stub(Cognito, 'login').returns({ AuthenticationResult: { IdToken: 'id-token', RefreshToken: 'refresh-token' } });
            sinon.stub(Crypt, 'getUserToken').returns({ token: '123' });
            request(process.env.BASE_URL)
                .post('/auth/signup')
                .send(registerUser)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    sinon.restore();
                    done();
                });
        });

        it('Cognito account should be deleted if if there is some error in saving to db', (done) => {
            const registerUser = {
                email: '<EMAIL>',
                role: 1,
                phoneNumber: '**********',
                countryCode: '+91',
                firstName: 'Test',
                lastName: 'User',
                password: 'Test@123'
            };
            const scanStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const saveStub = sinon.stub().rejects();

            scanStub.returns({ eq: eqStub });
            eqStub.returns({ exec: execStub });
            execStub.resolves({ count: 0 });
            sinon.replace(User, 'query', scanStub);
            sinon.replace(User.prototype, 'save', saveStub);
            sinon.stub(Cognito, 'signup').resolves({ userSub: 'id' });
            sinon.stub(Cognito, 'updateConfirmStatus').resolves();
            sinon.stub(Cognito, 'deleteUser').resolves();
            sinon.stub(Cognito, 'login').returns({ AuthenticationResult: { IdToken: 'id-token', RefreshToken: 'refresh-token' } });

            request(process.env.BASE_URL)
                .post('/auth/signup')
                .send(registerUser)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    sinon.restore();
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Verify Account', () => {
    TestCase.verifyAccount.forEach((data) => {
        it(data.it, (done) => {
            request(process.env.BASE_URL)
                .post('/auth/verify-account')
                .send(data.options)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.body.status, data.status);
                    done();
                });
        });
    });

    it('As a user I should validate if email is already registered', (done) => {
        const verifyAccount = {
            email: '<EMAIL>',
            otp: '123456',
            fcmToken: 'test-token'
        };

        const scanStub = sinon.stub();
        const eqStub = sinon.stub();
        const execStub = sinon.stub();

        scanStub.returns({ eq: eqStub });
        eqStub.returns({ exec: execStub });
        execStub.resolves({
            count: 0, toJSON: () => {
                return [
                    { otp: 123456, id: '**********', otpExpiryDate: '*************', email: '<EMAIL>' }];
            }
        });
        sinon.replace(User, 'query', scanStub);
        sinon.stub(User, 'update').returns({
            email: '<EMAIL>',
            role: 1,
            countryCode: '+91',
            firstName: 'Test',
            lastName: 'User'
        });
        sinon.stub(Cognito, 'updateUserToActive').resolves();

        request(process.env.BASE_URL)
            .post('/auth/verify-account')
            .send(verifyAccount)
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                sinon.restore();
                done();
            });
    });

    it('As a user I should validate if correct otp is passed', (done) => {
        const verifyAccount = {
            email: '<EMAIL>',
            otp: 123456,
            fcmToken: 'test-token'
        };

        const scanStub = sinon.stub();
        const eqStub = sinon.stub();
        const execStub = sinon.stub();

        scanStub.returns({ eq: eqStub });
        eqStub.returns({ exec: execStub });
        execStub.resolves({
            count: 1, toJSON: () => {
                return [
                    {
                        otp: 123456, id: '**********', otpExpiryDate: MOMENT().add(CONSTANTS.OTP_EXPIRY_DURATION, 'minutes').utc().toDate(),
                        email: '<EMAIL>'
                    }];
            }
        });
        sinon.replace(User, 'query', scanStub);
        sinon.stub(User, 'update').returns({
            email: '<EMAIL>',
            role: 1,
            countryCode: '+91',
            firstName: 'Test',
            lastName: 'User'
        });
        sinon.stub(Crypt, 'getUserToken').returns({ token: '123' });
        sinon.stub(Cognito, 'updateUserToActive').resolves();
        const pendingParentInviteStub = sinon.stub(PendingParentInvite, 'query');
        const pendingParentInviteDeleteStub = sinon.stub(PendingParentInvite, 'delete').resolves({});
        pendingParentInviteStub.withArgs('invitedPartner')
            .returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([{
                            inviterPartnerId: '124',
                            inviterPartnerEmail: '<EMAIL>',
                            children: ['59985bdc-77ba-4911-ba98-d49230566444']
                        }])
                    })
                })
            });
        request(process.env.BASE_URL)
            .post('/auth/verify-account')
            .send(verifyAccount)
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
                sinon.restore();
                pendingParentInviteStub.restore();
                pendingParentInviteDeleteStub.restore();
                done();
            });
    });

    it('As a user I should validate if otp is incorrect', (done) => {
        const verifyAccount = {
            email: '<EMAIL>',
            otp: 123457,
            fcmToken: 'test-token'
        };

        const scanStub = sinon.stub();
        const eqStub = sinon.stub();
        const execStub = sinon.stub();

        scanStub.returns({ eq: eqStub });
        eqStub.returns({ exec: execStub });
        execStub.resolves({
            count: 1, toJSON: () => {
                return [
                    {
                        otp: 123456, id: '**********', otpExpiryDate: MOMENT().add(CONSTANTS.OTP_EXPIRY_DURATION, 'minutes').utc().toDate(),
                        email: '<EMAIL>'
                    }];
            }
        });
        sinon.replace(User, 'query', scanStub);
        sinon.stub(User, 'update').returns({
            email: '<EMAIL>',
            role: 1,
            countryCode: '+91',
            firstName: 'Test',
            lastName: 'User'
        });
        sinon.stub(Cognito, 'updateUserToActive').resolves();

        request(process.env.BASE_URL)
            .post('/auth/verify-account')
            .send(verifyAccount)
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                sinon.restore();
                done();
            });
    });
});

describe('Resend OTP', () => {
    it('As a user I should validate if otp type is invalid', (done) => {
        const verifyAccount = {
            email: '<EMAIL>',
            otpType: 'feed'
        };

        const scanStub = sinon.stub();
        const eqStub = sinon.stub();
        const execStub = sinon.stub();

        scanStub.returns({ eq: eqStub });
        eqStub.returns({ exec: execStub });
        execStub.resolves({ count: 0 });
        sinon.replace(User, 'query', scanStub);
        sinon.stub(User, 'update').returns({
            email: '<EMAIL>',
            role: 1,
            countryCode: '+91',
            firstName: 'Test',
            lastName: 'User'
        });

        request(process.env.BASE_URL)
            .post('/auth/resend-otp')
            .send(verifyAccount)
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                sinon.restore();
                done();
            });
    });
    it('As a user I should validate if user is registered', (done) => {
        const verifyAccount = {
            email: '<EMAIL>',
            otpType: 'register'
        };

        const scanStub = sinon.stub();
        const eqStub = sinon.stub();
        const execStub = sinon.stub();

        scanStub.returns({ eq: eqStub });
        eqStub.returns({ exec: execStub });
        execStub.resolves({ count: 0 });
        sinon.replace(User, 'query', scanStub);
        sinon.stub(User, 'update').returns({
            email: '<EMAIL>',
            role: 1,
            countryCode: '+91',
            firstName: 'Test',
            lastName: 'User'
        });

        request(process.env.BASE_URL)
            .post('/auth/resend-otp')
            .send(verifyAccount)
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 404);
                sinon.restore();
                done();
            });
    });
    it('As a user I should validate if user is registered', (done) => {
        const verifyAccount = {
            email: '<EMAIL>',
            otpType: 'register'
        };

        const scanStub = sinon.stub();
        const eqStub = sinon.stub();
        const execStub = sinon.stub();

        scanStub.returns({ eq: eqStub });
        eqStub.returns({ exec: execStub });
        execStub.resolves({
            count: 1, toJSON: () => {
                return [
                    {
                        otp: 123456, id: '**********', countryCode: '+11', firstName: 'John',
                        lastName: 'Doe', otpExpiryDate: MOMENT().add(CONSTANTS.OTP_EXPIRY_DURATION, 'minutes').utc().toDate(),
                        email: '<EMAIL>'
                    }];
            }
        });
        sinon.replace(User, 'query', scanStub);
        sinon.stub(User, 'update').returns({
            email: '<EMAIL>',
            role: 1,
            countryCode: '+91',
            firstName: 'Test',
            lastName: 'User'
        });

        request(process.env.BASE_URL)
            .post('/auth/resend-otp')
            .send(verifyAccount)
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
                sinon.restore();
                done();
            });
    });
    it('As a user I should validate if user is registered with otp type forgot password', (done) => {
        const verifyAccount = {
            email: '<EMAIL>',
            otpType: 'forgotPassword'
        };

        const scanStub = sinon.stub();
        const eqStub = sinon.stub();
        const execStub = sinon.stub();

        scanStub.returns({ eq: eqStub });
        eqStub.returns({ exec: execStub });
        execStub.resolves({
            count: 1, toJSON: () => {
                return [
                    {
                        otp: 123456, id: '**********', countryCode: '+11', firstName: 'John',
                        lastName: 'Doe', otpExpiryDate: MOMENT().add(CONSTANTS.OTP_EXPIRY_DURATION, 'minutes').utc().toDate(),
                        email: '<EMAIL>'
                    }];
            }
        });
        sinon.replace(User, 'query', scanStub);
        sinon.stub(User, 'update').returns({
            email: '<EMAIL>',
            role: 1,
            countryCode: '+91',
            firstName: 'Test',
            lastName: 'User'
        });

        request(process.env.BASE_URL)
            .post('/auth/resend-otp')
            .send(verifyAccount)
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
                sinon.restore();
                done();
            });
    });
});
