const AuthMiddleware = require('./auth');
const ReCAPTCHAMiddleware = require('./recaptcha');

/**
 * This middleware is used to check if the user is a guest or authenticated.
 * If the user is a guest, it will check if the reCAPTCHA token is valid.
 * If the user is not a guest, it will check if the user is authenticated.
 * @param {Object} req - The request object.
 * @param {Object} res - The response object.
 * @param {Function} next - The next function.
 */
module.exports = function (req, res, next) {
    if (req.body.isGuestSignup?.toString()?.toLowerCase() === 'true') {
        return ReCAPTCHAMiddleware(req, res, next);
    }
    return AuthMiddleware(req, res, next);
};
