const DBModelHelperService = require('./DBModelHelperService');
const RedisUtil = require('./redisUtil');
const CONSTANTS = require('./constants');
const MOMENT = require('moment');
const CONSOLE_LOGGER = require('./logger');
const MembershipHelperService = require('./MembershipHelperService');
const Utils = require('./Utils');

class FeedHelperService {
    /**
     * @description Generates a feed
     * <AUTHOR>
     * @param {Object} event - The event object
     * @param {Array} participants - The participants array
     * @param {String} orgName - The organization name
     * @returns {Promise<Object>} The feed object
     */
    static async generateFeed (event, participants) {
        const {
            id, title, details, photoURL, eventType, eventScope,
            volunteerRequired, volunteerSignupUrl, isPaid, isDonatable, fee, membershipBenefitDetails,
            status, quantityType, quantityInstruction, participantsCount, isRecurring, recurringFrequency,
            documentURLs, products, organizationId
        } = event;

        return {
            startDateTime: details.startDateTime,
            endDateTime: details.endDateTime,
            description: details.details,
            venue: details.venue,
            organizationId,
            participants,
            id,
            title,
            photoURL,
            eventType,
            eventScope,
            volunteerRequired,
            volunteerSignupUrl,
            isPaid,
            isDonatable,
            fee,
            membershipBenefitDetails,
            status,
            quantityType,
            quantityInstruction,
            participantsCount,
            isRecurring,
            recurringFrequency,
            documentURLs,
            products
        };
    }

    /**
     * @description Generates a post feed
     * <AUTHOR>
     * @param {Object} post - The post object
     * @returns {Promise<Object>} The post feed object
     */
    static async generatePostFeed (post) {
        const {
            id, title, subTitle, content, publishedDate, status, organizationId
        } = post;

        return {
            organizationId,
            id,
            title,
            subTitle,
            content,
            publishedDate,
            status,
            isPost: true
        };
    }

    /**
     * @description Generates a fundraiser feed
     * <AUTHOR>
     * @param {Object} fundraiser - The fundraiser object
     * @returns {Promise<Object>} The fundraiser feed object
     */
    static async generateFundraiserFeed (fundraiser) {
        const { organizationId, id, title, description,
            startDate, endDate, fundraiserType, imageURL, products,
            membershipBenefitDetails, status, boosterGoal,
            boosterGoalForChild, boosterMessageForChild
        } = fundraiser;

        return {
            organizationId,
            id,
            title,
            description,
            startDate,
            endDate,
            fundraiserType,
            products,
            membershipBenefitDetails,
            status,
            boosterGoal,
            boosterGoalForChild,
            boosterMessageForChild,
            photoURL: imageURL,
            isFundraiser: true
        };
    }

    /**
     * @description Deletes all event references
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} event - The event object
     * @param {Boolean} shouldDeleteRegisteredEvents - Whether to delete registered events
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<Number>} The number of children deleted
     */
    static async deleteAllEventReferences ({
        redis,
        event,
        shouldDeleteRegisteredEvents,
        isCalendarEvent,
        versionPrefix
    }) {
        const { organizationId, id: eventId } = event;

        const score = MOMENT(event.details.startDateTime).toDate().getTime();
        const children = await DBModelHelperService.getChildOrganizationMapping(organizationId);
        const childrenGuardiansMap = await Utils.getChildrenDetailsMapWithAttributes(children, ['id', 'guardians']);

        await Promise.all(
            children.map(async (child) => {
                const { childId } = child;
                const childGuardians = childrenGuardiansMap[childId]?.guardians ?? [];

                const keys = isCalendarEvent
                    ? [Utils.getChildCalendarKey({ versionPrefix, childId })]
                    : [Utils.getChildKey({ versionPrefix, childId })];
                childGuardians.forEach((guardian) => {
                    const key = isCalendarEvent
                        ? Utils.getCalendarUserKey({ versionPrefix, shouldGenerateKeyPrefix: true })
                        : Utils.getUserKey({ versionPrefix, shouldGenerateKeyPrefix: true });
                    keys.push(`${key}:${guardian}`);
                });

                if (shouldDeleteRegisteredEvents && !isCalendarEvent) {
                    keys.push(Utils.getRegisteredChildKey({ versionPrefix, childId }));
                    childGuardians.forEach((guardian) => {
                        keys.push(Utils.getRegisteredUserKey({ versionPrefix, userId: guardian }));
                    });
                }
                for (const key of keys) {
                    const events = await RedisUtil.getElementsOfSortedSetByScore(
                        redis,
                        key,
                        score,
                        CONSTANTS.PLUS_INF
                    );
                    for (const childEvent of events) {
                        if (JSON.parse(childEvent).eventId === eventId) {
                            await RedisUtil.removeMemberFromSortedSet(redis, key, childEvent);
                        }
                    }
                }
            })
        );
        return children.length;
    }

    /**
     * @description Deletes all post references
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} post - The post object
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<Number>} The number of children deleted
     */
    static async deleteAllPostReferences (redis, post, versionPrefix) {
        const { organizationId, id: postId, publishedDate } = post;

        const children = await DBModelHelperService.getChildOrganizationMapping(organizationId);
        const score = MOMENT(publishedDate).toDate().getTime();

        const childrenGuardiansMap = await Utils.getChildrenDetailsMapWithAttributes(children, ['id', 'guardians']);

        CONSOLE_LOGGER.debug('Deleting post references', score, children.length);

        await Promise.all(
            children.map(async (child) => {
                const { childId } = child;
                const childGuardians = childrenGuardiansMap[childId]?.guardians ?? [];

                const keys = [Utils.getChildKey({ versionPrefix, childId })];
                childGuardians.forEach((guardian) => {
                    keys.push(Utils.getUserKey({ versionPrefix, userId: guardian }));
                });

                for (const key of keys) {
                    const posts = await RedisUtil.getElementsOfSortedSetByScore(
                        redis,
                        key,
                        score,
                        CONSTANTS.PLUS_INF
                    );
                    for (const childPost of posts) {
                        if (JSON.parse(childPost).postId === postId) {
                            await RedisUtil.removeMemberFromSortedSet(redis, key, childPost);
                        }
                    }
                }
            })
        );
        return children.length;
    }

    /**
     * @description Deletes all fundraiser references
     * <AUTHOR>
     * @param {Redis} redis - The Redis client
     * @param {Object} fundraiser - The fundraiser object
     * @param {Boolean} shouldDeleteRegisteredEvents - Whether to delete registered events
     * @param {String} versionPrefix - The version prefix
     * @returns {Promise<Number>} The number of children deleted
     */
    static async deleteAllFundraiserReferences ({
        redis,
        fundraiser,
        shouldDeleteRegisteredEvents,
        versionPrefix
    }) {
        const { organizationId, startDate, id: fundraiserId } = fundraiser;

        const children = await DBModelHelperService.getChildOrganizationMapping(organizationId);
        const score = MOMENT(startDate).toDate().getTime();

        const childrenGuardiansMap = await Utils.getChildrenDetailsMapWithAttributes(children, ['id', 'guardians']);

        await Promise.all(
            children.map(async (child) => {
                const { childId } = child;
                const childGuardians = childrenGuardiansMap[childId]?.guardians ?? [];

                const keys = [Utils.getChildKey({ versionPrefix, childId })];
                childGuardians.forEach((guardian) => {
                    keys.push(Utils.getUserKey({ versionPrefix, userId: guardian }));
                });

                if (shouldDeleteRegisteredEvents) {
                    keys.push(Utils.getRegisteredChildKey({ versionPrefix, childId }));
                    childGuardians.forEach((guardian) => {
                        keys.push(Utils.getRegisteredUserKey({ versionPrefix, userId: guardian }));
                    });
                }

                for (const key of keys) {
                    const fundraisers = await RedisUtil.getElementsOfSortedSetByScore(
                        redis,
                        key,
                        score,
                        CONSTANTS.PLUS_INF
                    );

                    for (const childFundraiser of fundraisers) {
                        if (JSON.parse(childFundraiser).fundraiserId === fundraiserId) {
                            await RedisUtil.removeMemberFromSortedSet(
                                redis,
                                key,
                                childFundraiser
                            );
                        }
                    }
                }
            })
        );
        if (shouldDeleteRegisteredEvents) {
            await MembershipHelperService.deleteAllMemberships(fundraiser);
        }
        return children.length;
    }
}

module.exports = FeedHelperService;
