const chai = require('chai');
const sinon = require('sinon');
const Stripe = require('../Stripe');
const expect = chai.expect;
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

describe('Stripe', () => {
    after(() => {
        sinon.restore();
    });

    it('should create a stripe account for the connect user', async () => {
        const createAccountStub = sinon.stub(stripe.accounts, 'create');
        createAccountStub.resolves({ id: 'test_account_id' });

        const result = await Stripe.createAccount(stripe);
        expect(result).to.deep.equal({ id: 'test_account_id' });

        createAccountStub.restore();
    });

    it('should create an onboarding link for the connect user', async () => {
        const account = { id: 'test_account_id' };
        const createOnboardingLinkStub = sinon.stub(stripe.accountLinks, 'create');
        createOnboardingLinkStub.resolves({ url: 'onboarding_url' });

        const result = await Stripe.createOnboardingLink(stripe, account);
        expect(result).to.deep.equal({ url: 'onboarding_url' });

        createOnboardingLinkStub.restore();
    });

    it('should handle webhook events', async () => {
        const constructWebhookEventStub = sinon.stub(stripe.webhooks, 'constructEvent');
        const body = {};
        const signature = 'test_signature';

        constructWebhookEventStub.resolves({ type: 'mock_event_type' });

        const result = await Stripe.constructWebhookEvent(body, signature, stripe);

        sinon.assert.calledOnce(constructWebhookEventStub);
        sinon.assert.calledWith(
            constructWebhookEventStub,
            body,
            signature,
            process.env.STRIPE_WEBHOOK_SECRET_CONNECT
        );

        expect(result).to.deep.equal({ type: 'mock_event_type' });

        constructWebhookEventStub.restore();
    });
});
