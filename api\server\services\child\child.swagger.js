/**
 *  routes and schema for Child routes
 */

/**
 * @openapi
 * components:
 *  schemas:
 *      followChild:
 *          type: object
 *          required:
 *              - followerChildId
 *              - followingChildId
 *          properties:
 *              followerChildId:
 *                type: string
 *                description: follower child id
 *              followingChildId:
 *                type: string
 *                description: following child id
 *          example:
 *              followerChildId: uuid of child on behalf of whom the parent is sending the follow request
 *              followingChildId: uuid of child to whom the parent is sending the follow request
 *
 *      successFollowChild:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *
 *
 *      connectChild:
 *          type: object
 *          required:
 *              - requesterChildId
 *              - requestedChildId
 *          properties:
 *              requesterChildId:
 *                type: string
 *                description: requester child id
 *              requestedChildId:
 *                type: string
 *                description: requested child id
 *          example:
 *              requesterChildId: uuid of child on behalf of whom the parent is sending the connection request
 *              requestedChildId: uuid of child to whom the parent is sending the connection request
 *
 *      successConnectChild:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Connection request sent successfully
 *
 *
 *      changeFollowRequestStatus:
 *          type: object
 *          required:
 *              - followerChildId
 *              - followingChildId
 *              - status
 *          properties:
 *              followerChildId:
 *                  type: string
 *                  description: follower child id
 *              followingChildId:
 *                  type: string
 *                  description: following child id
 *              status:
 *                  type: string
 *                  description: status of follow request either of accepted or rejected
 *          example:
 *              followerChildId: uuid of child on behalf of whom the parent is sending the follow request
 *              followingChildId: uuid of child to whom the parent is sending the follow request
 *              status: accepted
 *
 *      successChangeFollowRequestStatus:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: object
 *                  description: array of relationships and followers and followings count
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data: {
 *                      list :
 *                        [
 *                          {
 *                            "id": "uuid of child",
 *                            "firstName": "first name of child",
 *                            "lastName": "last name of child",
 *                            "photoURL": "profile picture of child",
 *                            "school": "school of child",
 *                            "homeroom": "homeroom of child",
 *                            "associatedColor": "#2772ED"
 *                          }
 *                        ],
 *                      followersCount: 1,
 *                      followingCount: 2
 *                    }
 *              message: Follow request status changed successfully
 *
 *      changeConnectionRequestStatus:
 *          type: object
 *          required:
 *              - requesterChildId
 *              - requestedChildId
 *              - status
 *          properties:
 *              requesterChildId:
 *                  type: string
 *                  description: requester child id
 *              requestedChildId:
 *                  type: string
 *                  description: requested child id
 *              status:
 *                  type: string
 *                  description: status of connection request either of connected or rejected
 *          example:
 *              requesterChildId: uuid of child on behalf of whom the parent is sending the connection request
 *              requestedChildId: uuid of child to whom the parent is sending the connection request
 *              status: connected
 *
 *      successChangeConnectionRequestStatus:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: object
 *                  description: array of connections and connections count where status is connected
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data: {
 *                      list :
 *                        [
 *                          {
 *                            "id": "uuid of child",
 *                            "firstName": "first name of child",
 *                            "lastName": "last name of child",
 *                            "photoURL": "profile picture of child",
 *                            "school": "school of child",
 *                            "homeroom": "homeroom of child",
 *                            "associatedColor": "#2772ED"
 *                          }
 *                        ],
 *                      connectionsCount: 1
 *                    }
 *              message: Connection request status changed successfully
 *
 *
 *      getChildDetails:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Success
 *              data:
 *                    id: uuid
 *                    firstName: firstName
 *                    lastName: lastName
 *                    dob: dob
 *                    associatedColor: associatedColor
 *                    schoolId: schoolId
 *                    followersCount: 1
 *                    followingCount: 2
 *                    schoolName: school name
 *                    homeroom: homeroom name
 *                    homeroomId: homeroomId
 *                    photoURL: photoURL
 *
 *      unfollowChild:
 *          type: object
 *          required:
 *              - followerChildId
 *              - followingChildId
 *          properties:
 *              followerChildId:
 *                type: string
 *                description: follower child id
 *              followingChildId:
 *                type: string
 *                description: following child id
 *          example:
 *              followerChildId: uuid of child on behalf of whom the parent is sending the follow request
 *              followingChildId: uuid of child to whom the parent is sending the follow request
 *
 *      successUnfollowChild:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: object
 *                  description: array of relationships and followers and followings count
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data: {
 *                      list :
 *                        [
 *                          {
 *                            "id": "uuid of child",
 *                            "firstName": "first name of child",
 *                            "lastName": "last name of child",
 *                            "photoURL": "profile picture of child",
 *                            "school": "school of child",
 *                            "homeroom": "homeroom of child",
 *                            "associatedColor": "#2772ED"
 *                          }
 *                        ],
 *                      followersCount: 1,
 *                      followingCount: 2
 *                    }
 *              message: Child unfollowed successfully
 *
 *      successRemoveFollower:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: object
 *                  description: array of relationships and followers and followings count
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data: {
 *                      list :
 *                        [
 *                          {
 *                            "id": "uuid of child",
 *                            "firstName": "first name of child",
 *                            "lastName": "last name of child",
 *                            "photoURL": "profile picture of child",
 *                            "school": "school of child",
 *                            "homeroom": "homeroom of child",
 *                            "associatedColor": "#2772ED"
 *                          }
 *                        ],
 *                      followersCount: 1,
 *                      followingCount: 2
 *                    }
 *              message: Follower removed successfully
 *
 *      removeChildConnection:
 *          type: object
 *          required:
 *              - requesterChildId
 *              - requestedChildId
 *          properties:
 *              requesterChildId:
 *                type: string
 *                description: requester child id
 *              requestedChildId:
 *                type: string
 *                description: requested child id
 *          example:
 *              requesterChildId: uuid of child whom the parent wants to remove from connections
 *              requestedChildId: uuid of child of the parent who wants to remove from connections
 *
 *      successRemoveConnection:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: object
 *                  description: array of connections and connections count where status is connected
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data: {
 *                      list :
 *                        [
 *                          {
 *                            "id": "uuid of child",
 *                            "firstName": "first name of child",
 *                            "lastName": "last name of child",
 *                            "photoURL": "profile picture of child",
 *                            "school": "school of child",
 *                            "homeroom": "homeroom of child",
 *                            "associatedColor": "#2772ED"
 *                          }
 *                        ],
 *                      connectionsCount: 1
 *                    }
 *              message: Connection removed successfully
 *
 *      successSearchChild:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: array
 *                  description: array of search results
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data: [
 *                  {
 *                      "id": "uuid of child",
 *                      "firstName": "first name of child",
 *                      "lastName": "last name of child",
 *                      "photoURL": "profile picture of child",
 *                      "school": "school of child",
 *                      "homeroom": "homeroom of child",
 *                      "associatedColor": "#2772ED",
 *                      "status": "either of Follow, Request Sent, Following"
 *                  }
 *              ]
 *              message: Success
 *
 *
 *      successSearchOrgChild:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: array
 *                  description: array of search results
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data: [
 *                  {
 *                      "id": "uuid of child",
 *                      "firstName": "first name of child",
 *                      "lastName": "last name of child",
 *                      "photoURL": "profile picture of child",
 *                      "school": "school of child",
 *                      "homeroom": "homeroom of child",
 *                      "associatedColor": "#2772ED",
 *                      "status": "either of not-connected, accept/deny, pending, connected"
 *                  }
 *              ]
 *              message: Success
 *
 *
 *      successGetAllRelationships:
 *          type: object
 *          properties:
 *              status:
 *                $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: object
 *                  description: array of relationships and followers and followings count
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data: {
 *                      list :
 *                        [
 *                          {
 *                            "id": "uuid of child",
 *                            "firstName": "first name of child",
 *                            "lastName": "last name of child",
 *                            "photoURL": "profile picture of child",
 *                            "school": "school of child",
 *                            "homeroom": "homeroom of child",
 *                            "associatedColor": "#2772ED"
 *                          }
 *                        ],
 *                      followersCount: 1,
 *                      followingCount: 2
 *                    }
 *              message: Success
 *
 *      successGetAllChildConnections:
 *          type: object
 *          properties:
 *              status:
 *                $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: object
 *                  description: array of connections and connections count where status is connected
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data: {
 *                      list :
 *                        [
 *                          {
 *                            "id": "uuid of child",
 *                            "firstName": "first name of child",
 *                            "lastName": "last name of child",
 *                            "photoURL": "profile picture of child",
 *                            "school": "school of child",
 *                            "homeroom": "homeroom of child",
 *                            "associatedColor": "#2772ED"
 *                          }
 *                        ],
 *                      connectionsCount: 1
 *                    }
 *              message: Success
 *
 *      updateChild:
 *          type: object
 *          required:
 *              - childId
 *              - firstName
 *              - lastName
 *              - dob
 *              - schoolId
 *              - zipCode
 *              - associatedColor
 *          properties:
 *              childId:
 *                  type: string
 *                  description: child id
 *              firstName:
 *                  type: string
 *                  description: first name of child
 *              lastName:
 *                  type: string
 *                  description: last name of child
 *              dob:
 *                  type: string
 *                  description: date of birth of child
 *              schoolId:
 *                  type: string
 *                  description: school id of child
 *              homeroomId:
 *                  type: string
 *                  description: home room id of child
 *              zipCode:
 *                  type: string
 *                  description: zip code of child
 *              associatedColor:
 *                  type: string
 *                  description: associated color of child
 *              photo:
 *                  type: string
 *                  description: photo of child
 *                  format: binary
 *
 *      successUpdateChild:
 *          type: object
 *          properties:
 *            status:
 *                $ref: '#/components/messageDefinition/properties/status'
 *            message:
 *              $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Child updated successfully
 *
 *      successGetAllAssociatedOrganizations:
 *          type: object
 *          properties:
 *            status:
 *                $ref: '#/components/messageDefinition/properties/status'
 *            data:
 *                type: array
 *                description: array of associated organizations
 *            message:
 *                $ref: '#/components/messageDefinition/properties/message'
 *            example:
 *                status: 1
 *                data: [
 *                        {
 *                          "organization":
 *                            {
 *                              "id": "uuid of organization",
 *                              "name": "name of organization",
 *                              "zipCode": "zip code of organization",
 *                              "category": "category of organization",
 *                              "address": "address of organization",
 *                              "country": "country of organization",
 *                              "state": "state of organization",
 *                              "city": "city of organization"
 *                            },
 *                          "child":
 *                            {
 *                              "id": "uuid of child",
 *                              "firstName": "first name of child",
 *                              "lastName": "last name of child",
 *                              "associatedColor": "#2772ED",
 *                              "photoURL": "profile picture of child",
 *                              "associatedOrganizations": "associated organizations of child"
 *                            }
 *                        }
 *                      ]
 *                message: Success
 *
 *      successGeneratePresignedUrl:
 *          type: object
 *          properties:
 *              presignedUrl:
 *                  type: string
 *                  description: presigned url for child
 *              fileName:
 *                  type: string
 *                  description: file name for child
 *          example:
 *              presignedUrl: presigned url for child
 *              fileName: file name for child
 */


/**
 * @openapi
 * /child:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Child]
 *      summary: get child details
 *      parameters:
 *          - in: query
 *            name: childId
 *            schema:
 *                type: string
 *            description: uuid of the child
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/getChildDetails'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /child/follow:
 *  post:
 *      security:
 *        - bearerAuth: []
 *      tags: [Child]
 *      summary: follow child
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/followChild'
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successFollowChild'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /child/connect:
 *  post:
 *      security:
 *        - bearerAuth: []
 *      tags: [Child Connection]
 *      summary: connect with child
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/connectChild'
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successConnectChild'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /child/follow-request:
 *  put:
 *      security:
 *        - bearerAuth: []
 *      tags: [Child]
 *      summary: Change the status of a follow request
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/changeFollowRequestStatus'
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successChangeFollowRequestStatus'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /child/connection-request:
 *  put:
 *      security:
 *        - bearerAuth: []
 *      tags: [Child Connection]
 *      summary: Change the status of a connection request
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/changeConnectionRequestStatus'
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successChangeConnectionRequestStatus'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /child/remove-follower:
 *  put:
 *      security:
 *        - bearerAuth: []
 *      tags: [Child]
 *      summary: Remove a follower
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/followChild'
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successRemoveFollower'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /child/remove-connection:
 *  put:
 *      security:
 *        - bearerAuth: []
 *      tags: [Child Connection]
 *      summary: Remove a connection
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/removeChildConnection'
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successRemoveConnection'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /child/search:
 *   get:
 *     security:
 *        - bearerAuth: []
 *     tags: [Child]
 *     summary: Search child by parent within same school of parent's child
 *     description: This function is being used to search child by parent within same school of parent's child
 *     parameters:
 *       - in: query
 *         name: childId
 *         schema:
 *           type: string
 *         required: true
 *         description: The child ID
 *       - in: query
 *         name: searchValue
 *         schema:
 *           type: string
 *         required: true
 *         description: The search value
 *     responses:
 *       '200':
 *         description: Search operation successful
 *         content:
 *           application/json:
 *             schema:
 *                 $ref: '#/components/schemas/successSearchChild'
 *       '400':
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/validationError'
 *       '401':
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/unauthorisedAccessUser'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /child/org-child-search:
 *  get:
 *     security:
 *        - bearerAuth: []
 *     tags: [Child]
 *     summary: Search child by parent within same school of parent's child
 *     description: This function is being used to search child by parent within same school of parent's child
 *     parameters:
 *       - in: query
 *         name: childId
 *         schema:
 *           type: string
 *         required: true
 *         description: The child ID
 *       - in: query
 *         name: searchValue
 *         schema:
 *           type: string
 *         required: true
 *         description: The search value
 *     responses:
 *       '200':
 *         description: Search operation successful
 *         content:
 *           application/json:
 *             schema:
 *                 $ref: '#/components/schemas/successSearchOrgChild'
 *       '400':
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/validationError'
 *       '401':
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/unauthorisedAccessUser'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /child/unfollow:
 *  post:
 *      security:
 *        - bearerAuth: []
 *      tags: [Child]
 *      summary: unfollow child
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/unfollowChild'
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successUnfollowChild'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /child/relationships:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Child]
 *      summary: Get all relationships of a child
 *      description: This function is being used to get all relationships of a child
 *      parameters:
 *        - in: query
 *          name: childId
 *          required: true
 *          schema:
 *            type: string
 *          description: The child ID
 *        - in: query
 *          name: relationshipType
 *          required: true
 *          schema:
 *            type: string
 *            enum: [followers, followings, requestedBy, requestedTo]
 *          description: The relationship type
 *      responses:
 *        '200':
 *          description: Get all relationships of a child
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/successGetAllRelationships'
 *        '400':
 *          description: Bad request
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/validationError'
 *        '401':
 *          description: Unauthorized access
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/unauthorisedAccessUser'
 *        '500':
 *          description: Internal server error
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /child/child-connections:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Child Connection]
 *      summary: Get all connections of a child
 *      description: This function is being used to get all connections of a child
 *      parameters:
 *        - in: query
 *          name: childId
 *          required: true
 *          schema:
 *            type: string
 *          description: The child ID
 *        - in: query
 *          name: connectionType
 *          required: true
 *          schema:
 *            type: string
 *            enum: [connected, requestedBy, requestedTo]
 *          description: The connection type
 *      responses:
 *        '200':
 *          description: Get all connections of a child
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/successGetAllChildConnections'
 *        '400':
 *          description: Bad request
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/validationError'
 *        '401':
 *          description: Unauthorized access
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/unauthorisedAccessUser'
 *        '500':
 *          description: Internal server error
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /child/exists:
 *  post:
 *      security:
 *        - bearerAuth: []
 *      tags: [Child]
 *      summary: validate child existence
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/addChild'
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successAddChild'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /child:
 *  put:
 *     security:
 *       - bearerAuth: []
 *     tags: [Child]
 *     summary: Update child
 *     requestBody:
 *       content:
 *         multipart/form-data:
 *           schema:
 *             $ref: '#/components/schemas/updateChild'
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/successUpdateChild'
 *       400:
 *         description: Validation failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/validationError'
 *       401:
 *         description: Unauthorised Access
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/unauthorisedAccessUser'
 *       500:
 *         description: internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/unexpectedError'
 */


/**
 * @openapi
 * /child/organizations:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Child]
 *      summary: Get all associated organizations of a child
 *      description: This function is being used to get all associated organizations of a child
 *      parameters:
 *        - in: query
 *          name: childId
 *          schema:
 *            type: string
 *          description: The child ID
 *      responses:
 *        '200':
 *          description: Get all associated organizations of a child
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/successGetAllAssociatedOrganizations'
 *        '400':
 *          description: Bad request
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/validationError'
 *        '401':
 *          description: Unauthorized access
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/unauthorisedAccessUser'
 *        '500':
 *          description: Internal server error
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /child/generate-presigned-url:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Child]
 *      summary: Generate presigned url for child
 *      description: This function is being used to generate presigned url for child
 *      parameters:
 *        - in: query
 *          name: childId
 *          schema:
 *            type: string
 *          description: The child ID
 *      responses:
 *        '200':
 *          description: Generate presigned url for child
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/successGeneratePresignedUrl'
 *        '400':
 *          description: Bad request
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/validationError'
 *        '401':
 *          description: Unauthorized access
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/unauthorisedAccessUser'
 *        '500':
 *          description: Internal server error
 *          content:
 *            application/json:
 *              schema:
 *                $ref: '#/components/schemas/unexpectedError'
 */
