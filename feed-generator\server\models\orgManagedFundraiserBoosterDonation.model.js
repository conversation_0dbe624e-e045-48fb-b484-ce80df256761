const dynamoose = require('dynamoose');
const { v4: uuidv4 } = require('uuid');

const orgManagedFundraiserBoosterDonationSchema = new dynamoose.Schema({
    id: {
        hashKey: true,
        type: String,
        default: () => uuidv4()
    },
    fundraiserBoosterId: {
        type: String,
        required: true,
        index: {
            name: 'fundraiserBoosterId-index',
            global: true,
            project: true
        }
    },
    childName: {
        type: String,
        required: true
    },
    childId: {
        type: String
    },
    donorName: {
        type: String,
        required: true
    },
    donorMessage: {
        type: String,
        required: true
    },
    amount: {
        type: Number,
        required: true
    },
    expectDonorMatch: {
        type: Boolean,
        required: true
    },
    status: {
        type: String,
        enum: ['unknown', 'success', 'failed'],
        default: 'unknown'
    },
    paymentType: {
        type: String,
        enum: ['online', 'offline'],
        default: 'online'
    },
    platformFeeCoveredBy: {
        type: String,
        enum: ['parent', 'organization']
    },
    stripePaymentIntentId: {
        type: String
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('OrgManagedFundraiserBoosterDonation', orgManagedFundraiserBoosterDonationSchema);
