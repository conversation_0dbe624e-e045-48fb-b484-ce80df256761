version: 0.2
environment_variables:
  plaintext:
     S3_BUCKET: "vaalee-dev-be-artifacts"
     FUNCTION_NAME: "dev-vaalee-notification-function"
     projectKey: "vaalee-notification-be"
     projectVersion: "master"
     projectName: "vaalee-notification-be"
env:
  parameter-store:
     SONAR_TOKEN: 'SONAR_TOKEN'
     SONAR_HOST: 'SONAR_HOST'
phases:
  install:
      runtime-versions:
       nodejs: 18

  pre_build:
    commands:
      - echo install node packages and pre-build commands
      - cd notification  
      - npm install  
      - echo run test 
      - npm test
      - echo test completed
      - pwd  
      - rm -R node_modules/
      - wget https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-3.2.0.1227-linux.zip
      - unzip sonar-scanner-cli-3.2.0.1227-linux.zip
      - cp ../sonar-scanner.properties sonar-scanner-3.2.0.1227-linux/conf/sonar-scanner.properties
      - ./sonar-scanner-3.2.0.1227-linux/bin/sonar-scanner -X -Dsonar.host.url=$SONAR_HOST -Dsonar.login=$SONAR_TOKEN -Dsonar.projectKey=$projectKey -Dsonar.projectVersion=$projectVersion

  build:
    commands:
      - npm install --prod
      - zip -r dev-vaalee-notification-function.zip index.js node_modules emailTemplates server prestart.sh
      - ls -la
      - pwd

  post_build:
    commands:
      - echo Entering Post_Build Phase
      - aws s3 cp dev-vaalee-notification-function.zip s3://$S3_BUCKET/notification/
      - aws lambda update-function-code --function-name "$FUNCTION_NAME" --s3-bucket $S3_BUCKET --s3-key notification/dev-vaalee-notification-function.zip  

cache:
   paths:
    - '/root/.sonar/**/*'
    
artifacts:
   files:
    - '**/*'
